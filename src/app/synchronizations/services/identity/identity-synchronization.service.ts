/* eslint-disable no-await-in-loop */
import {
    ComplianceCheckStatus,
    ComplianceCheckType,
    EmploymentStatus,
    ErrorCode,
} from '@drata/enums';
import {
    Injectable,
    InternalServerErrorException,
    NotFoundException,
    NotImplementedException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ApiClientService } from 'app/api-client/api-client.service';
import { User as UserApi } from 'app/apis/classes/api-data/user.class';
import { IApiServices } from 'app/apis/interfaces/api-services.interface';
import { IHrisIdentityServices } from 'app/apis/interfaces/hris-identity-services.interface';
import { HrisUser } from 'app/apis/interfaces/hris-user.interface';
import {
    IdentityServiceUser,
    IdentityServiceUser as IdentityUser,
} from 'app/apis/interfaces/identity-service-user.interface';
import { IIdentityServices } from 'app/apis/interfaces/identity-services.interface';
import { TData } from 'app/apis/types/data';
import { IdentityServiceUserPhoto } from 'app/apis/types/identity-service-user-photo';
import { AutopilotComplianceCheckUpdatedEvent } from 'app/autopilot/observables/events/autopilot-compliance-check-update.event';
import { AutopilotPersonnelSeperatedEvent } from 'app/autopilot/observables/events/autopilot-personnel-separated.event';
import { AutopilotUserCreatedEvent } from 'app/autopilot/observables/events/autopilot-user-created.event';
import { AutopilotUserUpdatedEvent } from 'app/autopilot/observables/events/autopilot-user-updated.event';
import { UserIdentityPersonnelSyncedEvent } from 'app/autopilot/observables/events/user-identity-personnel-synced.event';
import { ConnectionEntity } from 'app/companies/connections/entities/connection.entity';
import { ConnectionsCoreService } from 'app/companies/connections/services/connections-core.service';
import { CompaniesCoreService } from 'app/companies/services/companies-core.service';
import { DeviceComplianceCheck } from 'app/devices/entities/device-compliance-check.entity';
import { DevicesCoreService } from 'app/devices/services/devices-core.service';
import { IdentityConcurrentMap } from 'app/synchronizations/classes/identity-concurrent.map';
import { SynchronizationService } from 'app/synchronizations/services/synchronization.service';
import { UserIdentity } from 'app/users/entities/user-identity.entity';
import { UserRole } from 'app/users/entities/user-role.entity';
import { User } from 'app/users/entities/user.entity';
import { IUserIdentityEntity } from 'app/users/interfaces/user-identity/user-identity-entity.interface';
import { IUserIdentityProviderRepository } from 'app/users/interfaces/user-identity/user-identity-provider-repository.interface';
import { ComplianceCheck } from 'app/users/personnel/entities/compliance-check.entity';
import { PersonnelData } from 'app/users/personnel/entities/personnel-data.entity';
import { Personnel } from 'app/users/personnel/entities/personnel.entity';
import { AssetPersonnelCreatedEvent } from 'app/users/personnel/observables/events/asset-personnel-created.event';
import { AssetPersonnelUpdatedEvent } from 'app/users/personnel/observables/events/asset-personnel-updated.event';
import { ComplianceChecksCoreService } from 'app/users/personnel/services/compliance-checks-core.service';
import { ComplianceChecksOrchestrationService } from 'app/users/personnel/services/compliance-checks-orchestration.service';
import { DeviceComplianceCheckOrchestrationService } from 'app/users/personnel/services/device-compliance-check-orchestration.service';
import { PersonnelCoreService } from 'app/users/personnel/services/personnel-core.service';
import { UserPoliciesCoreService } from 'app/users/policies/services/user-policies-core.service';
import { UserRoleRepository } from 'app/users/repositories/user-role.repository';
import { UserIdentitiesCoreService } from 'app/users/services/user-identities-core.service';
import { UsersCoreService } from 'app/users/services/users-core.service';
import { Auditor } from 'auditors/entities/auditor.entity';
import { AuditorsCoreService } from 'auditors/services/auditors-core.service';
import { Account } from 'auth/entities/account.entity';
import { Entry } from 'auth/entities/entry.entity';
import { Token } from 'auth/entities/token.entity';
import { AuthService } from 'auth/services/auth.service';
import { EntryCoreService } from 'auth/services/entry-core.service';
import { Benchmark } from 'commons/benchmark/benchmark';
import { ClientType } from 'commons/enums/auth/client-type.enum';
import { ProviderType } from 'commons/enums/auth/provider-type.enum';
import { ConnectionState } from 'commons/enums/auth/state-type.enum';
import { LogIdentifierRecordType } from 'commons/enums/log-identifier-record-type.enum';
import { PolicyScope } from 'commons/enums/users/policies/policy-scope.enum';
import { Role } from 'commons/enums/users/role.enum';
import { UserDocumentType } from 'commons/enums/users/user-document-type.enum';
import { ConflictException } from 'commons/exceptions/conflict.exception';
import { ValidationException } from 'commons/exceptions/validation.exception';
import { checkFrequencyNextExpiration } from 'commons/helpers/check-frequency.helper';
import { complianceCheckFrequency } from 'commons/helpers/compliance-check.helper';
import {
    isFlagEnabled,
    isMultiIdpEntitlementAndFeatureFlagsEnabled,
} from 'commons/helpers/connection.helper';
import { duplicateEntry } from 'commons/helpers/database.helper';
import { hasExpired, isFutureDate } from 'commons/helpers/date.helper';
import { getNumericEnumValues } from 'commons/helpers/enum.helper';
import { isProd } from 'commons/helpers/environment.helper';
import { forEachTokenPage } from 'commons/helpers/pagination/pagination.helper';
import {
    allStrictFormerStatuses,
    getLatestEmploymentStatus,
    getPrimaryHrisUserIdentity,
    getPrimaryIdpUserIdentity,
    getSeparatedAtDate,
    getStartedAtDate,
    isInACurrentStatus,
    isInAFormerStatus,
    isOutOfScope,
    personnelSpecialToCurrentStatus,
    personnelToFormerStatus,
} from 'commons/helpers/personnel.helper';
import { getSha256HashString, omitProperties } from 'commons/helpers/security.helper';
import { fullName as getUserFullName } from 'commons/helpers/user.helper';
import config from 'config';
import { Uploader } from 'dependencies/uploader/uploader';
import { FeatureFlag } from 'feature-flags/feature-flag.enum';
import { FeatureFlagService } from 'feature-flags/feature-flag.service';
import { cloneDeep, find, get, isEmpty, isNil } from 'lodash';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { IsNull, Repository } from 'typeorm';
@Injectable()
export class IdentitySynchronizationService extends SynchronizationService {
    protected map: any[] = [];

    constructor(
        protected readonly provider: ApiClientService,
        protected readonly connectionsCoreService: ConnectionsCoreService,
        protected readonly usersCoreService: UsersCoreService,
        protected readonly userIdentitiesCoreService: UserIdentitiesCoreService,
        protected readonly complianceChecksCoreService: ComplianceChecksCoreService,
        private readonly entryCoreService: EntryCoreService,
        private readonly authService: AuthService,
        private readonly personnelCoreService: PersonnelCoreService,
        private readonly complianceChecksOrchestrationService: ComplianceChecksOrchestrationService,
        private readonly devicesCoreService: DevicesCoreService,
        private readonly deviceComplianceCheckOrchestrationService: DeviceComplianceCheckOrchestrationService,
        private readonly userPoliciesCoreService: UserPoliciesCoreService,
        private readonly companiesCoreService: CompaniesCoreService,
        private readonly uploader: Uploader,
        private readonly auditorsCoreService: AuditorsCoreService,
        private readonly featureFlagService: FeatureFlagService,
        @InjectRepository(Token)
        private readonly tokenRepository: Repository<Token>,
    ) {
        super(
            provider,
            connectionsCoreService,
            usersCoreService,
            userIdentitiesCoreService,
            complianceChecksCoreService,
        );
    }

    getProviderType(): ProviderType {
        return ProviderType.IDENTITY;
    }

    getMapInstance(): IdentityConcurrentMap[] {
        return this.map;
    }

    /**
     * Main entry point from the outside world.
     * Also init resources here
     * adding the entitlement flag picker here
     * should work at create/update/delete connection and AP
     *
     * @param account
     */
    protected async runSync(account: Account): Promise<void> {
        // Multi-IDP synchronization flow
        try {
            await this.cleanupComplianceCheckExclusions(account);
            this.map.push({
                accountId: account.id,
                hrisDirectoryService: null,
                policyDistributionPersonnelList: [],
            });
            await this.syncIdentityPersonnelV2(account);
        } catch (error) {
            this.error(error, account);
            throw error;
        }
    }

    async getConnectionApi<T extends IApiServices>(
        account: Account,
        connection: ConnectionEntity,
    ): Promise<T | null> {
        // connection providerType could only be IDP or HRIS
        const api = await this.provider.api<T>(connection, account);

        await this.runHealthCheck(account, connection, api);

        if (
            !(await api.connectionReady()) ||
            connection.state === ConnectionState.CONFIGURED_PENDING_CONFIRMATION
        ) {
            this.warn('Identity Sync is unavailable for connection at this time.', account);
            return null;
        }

        return api;
    }

    /**
     * Synchronizes user identities from an external identity provider or HRIS system.
     * This function fetches users from the external system, updates existing user identities,
     * creates new ones, and soft deletes those no longer present in the external system.
     * It also tracks and logs metrics for the synchronization process.
     *
     * @param account - The account for which the synchronization is being performed.
     * @param connectionApi - The API client for the identity or HRIS service.
     * @param connection - The connection entity associated with the external system.
     * @param userIdentityMapper - A function that maps remote identity user data to local UserIdentity objects.
     * @returns A promise that resolves when the synchronization is complete.
     * @throws Will throw an error if there's an issue during the synchronization process.
     */
    async syncUserIdentitiesV2(
        account: Account,
        connectionApi: IIdentityServices | IHrisIdentityServices,
        connection: ConnectionEntity,
        userIdentityMapper: (
            remoteIdentityUser: IdentityServiceUser | HrisUser,
            account: Account,
            userIdentity: UserIdentity,
            userAvatar?: string | null,
        ) => UserIdentity,
    ): Promise<boolean> {
        try {
            // Initialize metric tracking
            const metrics = {
                syncedUserIdentities: new Map<string, number>(),
                createdUserIdentities: new Map<string, number>(),
                deletedUserIdentities: new Map<string, number>(),
            };

            const company = await this.companiesCoreService.getCompanyByAccountId(account.id);
            const isMultiDomain = !!company.multiDomain;
            const sourceData: any[] = [];

            // Create a map of existing UserIdentities in the database for this connection
            const uniqueUserIdentityMap =
                await this.userIdentityRepository.getUniqueUserIdentitiesMap(connection.id);

            const processPage = async (pageToken: string | null) => {
                this.log(`Processing users with page token ${pageToken}`, account);

                try {
                    // Fetch users from the external system
                    const benchmark = new Benchmark();
                    const response = await connectionApi.getUsers({
                        domain: account.domain,
                        isMultiDomain,
                        maxResults: config.get('sync.maxResults'),
                        nextPageToken: pageToken,
                    });

                    this.logConnectionSyncProgress(
                        account,
                        connection,
                        LogIdentifierRecordType.USER,
                        {
                            records: response?.data?.length,
                            providerType: connection.providerType,
                            benchmark,
                        },
                    );

                    if (!isNil(response.data)) {
                        // Store the raw response data for later use (e.g., event publishing)
                        sourceData.push(response.data);
                        await Promise.allSettled(
                            response.data.map(async (identityUser: IdentityUser | HrisUser) => {
                                // Process each user from the external system
                                const identityUserId = identityUser.getId();
                                if (isNil(identityUserId)) {
                                    this.log(
                                        `Invalid identity user: No ID found for user with email ${identityUser.getPrimaryEmail()}`,
                                        account,
                                    );
                                    return;
                                }

                                // Check if user has avatar data and if the API supports photo retrieval
                                let avatarPromise: Promise<TData<IdentityServiceUserPhoto> | null> | null =
                                    null;

                                // Get local system user identity (if exists)
                                const uniqueIdentityId = `${identityUserId}-${connection.id}-0`;
                                let localUserIdentity: UserIdentity | null = null;

                                // Check if we have the identity in our map
                                if (uniqueUserIdentityMap.has(uniqueIdentityId)) {
                                    const localUserIdentityId =
                                        uniqueUserIdentityMap.get(uniqueIdentityId);
                                    localUserIdentity = await this.userIdentityRepository.findOne({
                                        where: {
                                            id: localUserIdentityId,
                                            connectionId: connection.id,
                                        },
                                    });
                                }

                                // If not found in map, search for any existing record
                                if (!localUserIdentity) {
                                    // First try to find an active record
                                    localUserIdentity = await this.userIdentityRepository.findOne({
                                        where: {
                                            identityId: identityUserId,
                                            connectionId: connection.id,
                                            deletedAt: IsNull(),
                                        },
                                    });

                                    // If no active record, look for soft-deleted ones
                                    if (!localUserIdentity) {
                                        localUserIdentity =
                                            await this.userIdentityRepository.findOne({
                                                where: {
                                                    identityId: identityUserId,
                                                    connectionId: connection.id,
                                                },
                                                withDeleted: true,
                                            });
                                    }
                                }

                                // Get avatar if we have an existing identity
                                if (localUserIdentity) {
                                    avatarPromise = this.getUserAvatar(
                                        identityUser,
                                        connectionApi,
                                        localUserIdentity,
                                    );
                                }

                                // Create new instance or use existing one
                                const parsedUserIdentity =
                                    cloneDeep(localUserIdentity) || new UserIdentity();
                                if (isNil(localUserIdentity)) {
                                    this.log(
                                        `No existing identity found for user with external id: ${identityUserId}, creating new user identity record`,
                                        account,
                                        {
                                            identifier: this.syncUserIdentitiesV2.name,
                                        },
                                    );
                                    parsedUserIdentity.identityId = identityUserId;
                                    parsedUserIdentity.connectionId = connection.id;
                                } else {
                                    this.log(
                                        `Existing identity found for user with external id: ${identityUserId}, getting user identity from database`,
                                        account,
                                        {
                                            identifier: this.syncUserIdentitiesV2.name,
                                            userIdentity: omitProperties(localUserIdentity, [
                                                'metadata',
                                            ]),
                                        },
                                    );
                                }

                                // Map the external user data to our UserIdentity model
                                let userAvatar: string | null = null;
                                if (!isNil(avatarPromise)) {
                                    const photoPayload = await avatarPromise;
                                    userAvatar = await this.uploadUserAvatar(
                                        identityUser,
                                        account,
                                        photoPayload,
                                    );
                                }

                                const mappedIdentity = userIdentityMapper(
                                    identityUser,
                                    account,
                                    parsedUserIdentity,
                                    userAvatar,
                                );

                                await this.saveUserIdentity(
                                    account,
                                    mappedIdentity,
                                    this.syncUserIdentitiesV2.name,
                                );

                                // Track metrics for synced or created user identities
                                if (localUserIdentity) {
                                    metrics.syncedUserIdentities.set(
                                        identityUserId,
                                        localUserIdentity.id,
                                    );
                                } else {
                                    metrics.createdUserIdentities.set(
                                        identityUserId,
                                        mappedIdentity.id,
                                    );
                                }
                                // Remove the processed user from the unique map to so it doesn't get soft deleted
                                uniqueUserIdentityMap.delete(uniqueIdentityId);
                            }),
                        );
                    }

                    // Return the next page token if available
                    return response.token ?? null;
                } catch (error) {
                    this.log(
                        `Error while processing users with page token ${pageToken}: ${error.message}`,
                        account,
                    );
                    return null;
                }
            };

            // Continue processing pages until there are no more
            let nextPageToken: string | null = null;
            do {
                nextPageToken = await processPage(nextPageToken);
            } while (nextPageToken);

            if (
                connection.providerType === this.getProviderType() &&
                metrics.syncedUserIdentities.size === 0 &&
                metrics.createdUserIdentities.size === 0
            ) {
                this.logMetrics(
                    account,
                    connection,
                    'Created user identities',
                    metrics.createdUserIdentities,
                );
                this.logMetrics(
                    account,
                    connection,
                    'Synced user identities',
                    metrics.syncedUserIdentities,
                );
                this.logMetrics(
                    account,
                    connection,
                    'To be deleted user identities',
                    metrics.deletedUserIdentities,
                );
                this.log(
                    'No user identities were fetched for this account connection, skipping user identity deletion and personnel processing.',
                    account,
                );
                return false;
            }

            // Soft delete any local userIdentities not present in the external system
            if (!isEmpty(uniqueUserIdentityMap)) {
                metrics.deletedUserIdentities = uniqueUserIdentityMap;
                await this.userIdentityRepository.softDelete(
                    Array.from(uniqueUserIdentityMap.values()),
                );
            }

            this.logMetrics(
                account,
                connection,
                'Created user identities',
                metrics.createdUserIdentities,
            );
            this.logMetrics(
                account,
                connection,
                'Synced user identities',
                metrics.syncedUserIdentities,
            );
            this.logMetrics(
                account,
                connection,
                'Deleted user identities',
                metrics.deletedUserIdentities,
            );

            this._eventBus.publish(
                new UserIdentityPersonnelSyncedEvent(
                    account,
                    sourceData ?? [],
                    (metrics.syncedUserIdentities?.size ?? 0) +
                        (metrics.createdUserIdentities?.size ?? 0),
                    connection,
                ),
            );
            return true;
        } catch (error) {
            this.log(`Error in syncUserIdentities: ${error.message}`, account);
            throw error;
        }
    }

    /**
     * Get the user avatar from the identity user
     * @param identityUser - The identity user
     * @param connectionApi - The connection API
     * @returns The user avatar
     */
    private async getUserAvatar(
        identityUser: IdentityUser | HrisUser,
        connectionApi: IIdentityServices | IHrisIdentityServices,
        localUserIdentity: UserIdentity | null,
    ): Promise<TData<IdentityServiceUserPhoto> | null> {
        // Check if the API supports photo retrieval and if user has an avatar URL
        if (!('getUserPhoto' in connectionApi) || !('getAvatarUrl' in identityUser)) {
            return null;
        }

        const avatarUrl = identityUser.getAvatarUrl();
        if (!avatarUrl || avatarUrl === localUserIdentity?.avatarSource) {
            return null;
        }

        try {
            return await connectionApi.getUserPhoto(identityUser.getId());
        } catch (error) {
            // If there's an error getting the photo, return null
            this.logger.warn(
                new PolloMessage(
                    `Failed to get avatar for user ${identityUser.getId()}: ${error.message}`,
                )
                    .setError(error)
                    .setMetadata({
                        identityUserId: identityUser.getId(),
                    }),
            );
            return null;
        }
    }

    /**
     * Upload the user avatar to the CDN and return the key
     * @param identityUser - The identity user
     * @param account - The account
     * @param avatarPromise - The avatar promise
     * @returns The avatar key
     */
    private async uploadUserAvatar(
        identityUser: IdentityUser | HrisUser,
        account: Account,
        photoPayload: TData<IdentityServiceUserPhoto> | null,
    ): Promise<string | null> {
        if (isNil(photoPayload)) {
            return null;
        }

        try {
            if (!isNil(photoPayload)) {
                const { data } = photoPayload;
                const uploaderPayload = await this.uploader.uploadUserAvatarFromData(
                    data.photoData,
                    data.mimeType,
                    `${identityUser.getFirstName()} ${identityUser.getLastName()}`,
                    account.id,
                );
                const { key: avatarKey } = uploaderPayload;
                return avatarKey;
            }
        } catch (error) {
            this.logger.warn(
                new PolloMessage(
                    `Failed to process avatar for user ${identityUser.getId()}: ${error.message}`,
                )
                    .setError(error)
                    .setMetadata({
                        identityUserId: identityUser.getId(),
                        accountId: account.id,
                    }),
            );
        }
        return null;
    }

    /**
     * @param {Account} account
     * @param {ConnectionEntity} connection
     * @param {string} metricName
     * @param {Map<string, number>} metrics
     */
    private logMetrics(
        account: Account,
        connection: ConnectionEntity,
        metricName: string,
        metrics: Map<string, number>,
    ): void {
        this.log(`User Identity Sync Phase Metrics - ${metricName}:`, account, {
            connectionId: connection.id,
            metrics: {
                count: metrics.size,
                records: Array.from(metrics.values()),
            },
        });
    }

    private async clearEntryTokens(entry: Entry): Promise<void> {
        const entryId = get(entry, 'id', null);
        if (isNil(entryId)) {
            return;
        }
        await this.tokenRepository
            .createQueryBuilder('Token')
            .delete()
            .where('fk_entry_id = :entryId', { entryId })
            .execute();
        entry.tokens = null;
    }

    async saveUserIdentity(
        account: Account,
        userIdentity: UserIdentity,
        identifier: string | null = null,
    ): Promise<void> {
        try {
            this.log(`Attempting to save user identity`, account, {
                identifier,
                identityId: userIdentity.id,
                identityEmail: userIdentity.email,
                userId: userIdentity.user?.id,
                userEmail: userIdentity.user?.email,
                connectionId: userIdentity.connection?.id,
            });

            await this.userIdentityRepository.save(userIdentity);

            this.log(`Successfully saved user identity`, account, {
                identifier,
                identityId: userIdentity.id,
                identityEmail: userIdentity.email,
                userId: userIdentity.user?.id,
                connectionId: userIdentity.connection?.id,
            });
        } catch (error) {
            if (error.code === duplicateEntry()) {
                this.error(error, account, {
                    identifier,
                    error: error.message,
                });
                this.log(
                    `Duplicate entry detected while saving user identity. Saving the user identity failed.`,
                    account,
                    userIdentity,
                );
            } else {
                // Unknown error, log it
                this.error(error, account, {
                    identifier,
                    error: error.message,
                });
                // Unknown error, rethrow
                throw error;
            }
        }
    }

    /**
     * Will implement to match other identity sync services
     *
     * @param identity
     * @param account
     */
    protected disconnectIdentity(
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        identity: IUserIdentityEntity,
    ): Promise<void> {
        throw new NotImplementedException();
    }

    /**
     *
     * @param {PersonnelData} personnelData
     * @return {void}
     */
    private setPersonnelDataValues(personnelData: PersonnelData): void {
        // init the os version
        personnelData.osVersion = null;
        // init the serial number
        personnelData.serialNumber = null;
    }

    /**
     * @param {Account} account
     * @return {Personnel[]}
     */
    private getPolicyDistributionPersonnelListFromMap(account: Account): Personnel[] {
        return this.getMap(account)?.policyDistributionPersonnelList ?? [];
    }

    /**
     * @param {Account} account
     * @return {Personnel[]}
     */
    private pushToPolicyDistributionPersonnelListFromMap(
        account: Account,
        personnel: Personnel,
    ): void {
        this.getMap(account)?.policyDistributionPersonnelList.push(personnel);
    }

    /**
     * @param {Account} account
     * @return {Personnel[]}
     */
    private clearPolicyDistributionPersonnelListFromMap(account: Account): void {
        this.getMap(account).policyDistributionPersonnelList = [];
    }

    /**
     *
     * @param account
     */
    protected getMap(account: Account): IdentityConcurrentMap | null {
        const map = find(this.map, ['accountId', account.id]);

        if (isNil(map)) {
            throw new InternalServerErrorException(
                `IdentitySynchronizationService::No map for account ${account.id}`,
            );
        }

        return map;
    }

    getUserIdentityProviderRepositoryForServiceFromMap(): IUserIdentityProviderRepository {
        throw new NotImplementedException();
    }

    /**
     * Will implement to match other identity sync services
     *
     * @param account
     * @param identity
     */
    publishDisconnectedEvent(
        account: Account,
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        identity: IUserIdentityEntity,
    ): void {
        throw new NotImplementedException();
    }

    /**
     * Will implement to match other identity sync services
     *
     * @param account
     * @param identity
     * @param user
     */
    publishUpdatedEvent(
        account: Account,
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        identity: IUserIdentityEntity,
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        user: UserApi,
    ): void {
        throw new NotImplementedException();
    }

    /**
     * Will implement to match other identity sync services
     *
     * @param updates
     * @param account
     */
    protected async updateIdentities(
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        updates: IUserIdentityEntity[],

        account: Account,
    ): Promise<void> {
        throw new NotImplementedException();
    }

    private async completeComplianceDeviceChecks(
        account: Account,
        personnel: Personnel,
    ): Promise<void> {
        await this.deviceComplianceCheckOrchestrationService.updatePersonnelCompliance(
            personnel,
            DeviceComplianceCheck.getDeviceComplianceCheckTypes(),
        );
    }

    private async recomputePersonnelCompliance(
        account: Account,
        personnel: Personnel,
    ): Promise<void> {
        await this.complianceChecksOrchestrationService.computeComplianceChecksForPersonnel(
            account,
            [personnel],
            true,
        );
    }

    /**
     * Identity Sync V2
     */

    /**
     * Synchronizes identity personnel data for multiple identity providers
     * Handles both IDP and HRIS connection synchronization
     *
     * @param account - The account to perform synchronization for
     * @throws Error if no identity providers are found or on synchronization failure
     */
    async syncIdentityPersonnelV2(account: Account): Promise<void> {
        // Start false since will turn true once at least one connection succeeds
        let canProcessPersonnel = false;

        // Process IDP connections
        const idpConnections = await this.connectionsCoreService.getConnectionsByProviderTypes(
            [ProviderType.IDENTITY],
            undefined,
            false,
            true,
        );

        if (isEmpty(idpConnections)) {
            this.warn(`On Domain ${account.domain}: Has no Identity Provider`, account);
            return;
        }

        // Determine the highest ranked connection from IDP connections only
        const highestRankedConnection = idpConnections[0];
        const highestRankedConnectionId = highestRankedConnection?.id || null;
        this.log(
            `Highest ranked IDP connection determined: ${highestRankedConnectionId}`,
            account,
            {
                connectionId: highestRankedConnectionId,
                clientType: highestRankedConnection?.clientType,
                providerType: highestRankedConnection?.providerType,
                rankings: idpConnections.map(c => ({
                    id: c.id,
                    ranking: c.ranking,
                })),
            },
        );

        // Process each IDP connection
        for await (const idpConnection of idpConnections) {
            if (idpConnection.clientType === ClientType.CUSTOM) {
                this.warn(
                    `Identity Connection has been identified as ${
                        ClientType[ClientType.CUSTOM]
                    }. Skipping Identity Sync...`,
                    account,
                );
                return;
            }

            const connectionBenchmark = new Benchmark();

            const idpApi = await this.getConnectionApi<IIdentityServices>(account, idpConnection);

            if (isNil(idpApi)) {
                this.error(
                    new Error(
                        `Could not initialize API for IDP Connection. Skipping Identity Sync...`,
                    ),
                    account,
                );
                return;
            }

            try {
                await this.logConnectionSyncStart(
                    account,
                    idpConnection,
                    connectionBenchmark,
                    LogIdentifierRecordType.USER,
                    ProviderType.IDENTITY,
                );
                const connectionSynced = await this.syncUserIdentitiesV2(
                    account,
                    idpApi,
                    idpConnection,
                    this.mapIdpIdentityUserToPropertiesV2,
                );

                // As long as one connection returns true canProcessPersonnel will be true
                canProcessPersonnel = canProcessPersonnel || connectionSynced;

                await this.processUsersAndEntriesV2(
                    account,
                    idpApi,
                    idpConnection,
                    highestRankedConnectionId,
                );

                // Reload connection from database to get latest metadata
                const freshConnection = await this.connectionsCoreService.getConnectionById(
                    idpConnection.id,
                );
                const metadata = freshConnection.getMetadata();

                metadata.lastSyncedAt = new Date();
                idpConnection.setMetadata(metadata);
                await this.connectionsCoreService.updateNonDeletedConnection(
                    account,
                    idpConnection,
                );
                await this.logConnectionSyncSuccess(
                    account,
                    idpConnection,
                    connectionBenchmark,
                    LogIdentifierRecordType.USER,
                    ProviderType.IDENTITY,
                );
            } catch (error) {
                await this.logConnectionSyncFailure(
                    error,
                    account,
                    idpConnection,
                    connectionBenchmark,
                    LogIdentifierRecordType.USER,
                );
                throw error;
            } finally {
                this.stopObserverIfImplemented(idpApi);
            }
        }

        // If canProcessPersonnel is false, no connections succeeded so no data to process
        if (!canProcessPersonnel) {
            this.warn(
                `No users from IDP connection(s) were found for account ${account.id} and domain ${account.domain}. Skipping Identity Sync...`,
                account,
            );
            return;
        }

        // Process HRIS connections
        const hrisConnections = await this.connectionsCoreService.getConnectionsByProviderTypes([
            ProviderType.HRIS,
        ]);

        if (isEmpty(hrisConnections)) {
            this.log(`On Domain ${account.domain}: Has no HRIS Provider`, account);
        }

        for await (const hrisConnection of hrisConnections) {
            if (hrisConnection.clientType === ClientType.CUSTOM) {
                this.warn(
                    `HRIS Connection has been identified as ${
                        ClientType[ClientType.CUSTOM]
                    }. Skipping Identity Sync...`,
                    account,
                );
                return;
            }

            const connectionBenchmark = new Benchmark();

            const hrisApi = await this.getConnectionApi<IHrisIdentityServices>(
                account,
                hrisConnection,
            );

            if (isNil(hrisApi)) {
                this.error(
                    new Error(
                        `Could not initialize API for HRIS Connection. Skipping Identity Sync...`,
                    ),
                    account,
                );
                return;
            }

            try {
                await this.logConnectionSyncStart(
                    account,
                    hrisConnection,
                    connectionBenchmark,
                    LogIdentifierRecordType.USER,
                    ProviderType.HRIS,
                );

                await this.syncUserIdentitiesV2(
                    account,
                    hrisApi,
                    hrisConnection,
                    this.mapHrisIdentityUserToPropertiesV2,
                );

                await this.resolveManagerNamesForHrisV2(account, hrisApi);

                await this.logConnectionSyncSuccess(
                    account,
                    hrisConnection,
                    connectionBenchmark,
                    LogIdentifierRecordType.USER,
                    ProviderType.HRIS,
                );
            } catch (error) {
                await this.logConnectionSyncFailure(
                    error,
                    account,
                    hrisConnection,
                    connectionBenchmark,
                    LogIdentifierRecordType.USER,
                    ProviderType.HRIS,
                );
                throw error;
            } finally {
                this.stopObserverIfImplemented(hrisApi);
            }
        }

        const hasHrisDirectory = !isEmpty(hrisConnections);
        await this.processHrisUserIdentityLinkingV2(account);

        if (
            await isFlagEnabled(
                account,
                this.featureFlagService,
                FeatureFlag.RELEASE_MULTI_IDP_SEPARATE_USER_PERSONNEL,
            )
        ) {
            await this.processPersonnelV2(account, hasHrisDirectory);
        } else {
            this.log(
                `Feature Flag '${FeatureFlag.RELEASE_MULTI_IDP_SEPARATE_USER_PERSONNEL}'` +
                    `is currently OFF, we are skipping processing any personnel changes.`,
                account,
            );
        }
    }

    /**
     * Processes users and entries for a given account, identity provider API, and connection.
     * Uses pagination and batch processing to efficiently synchronize user data from the identity provider.
     *
     * @param account - The account for which to process users and entries
     * @param idpApi - The identity provider API service
     * @param connection - The connection entity
     */
    async processUsersAndEntriesV2(
        account: Account,
        idpApi: IIdentityServices,
        connection: ConnectionEntity,
        highestRankedConnectionId: number | null,
    ): Promise<void> {
        const benchmark = new Benchmark();
        this.log('User processing has started', account, null, benchmark);

        await forEachTokenPage(
            async (_, page = 0) => {
                const response =
                    await this.userIdentityRepository.getUserIdentitiesForUserAndEntryManagementPhase(
                        connection.id,
                        { page },
                    );

                return {
                    data: {
                        page: response?.data ?? [],
                        nextPageToken: isEmpty(response?.data) ? null : true,
                    },
                };
            },
            async (data: UserIdentity[]) => {
                if (isEmpty(data)) {
                    this.warn(
                        'No IdP user identities were found in current page',
                        account,
                        null,
                        benchmark,
                    );
                    return;
                }

                // Process users sequentially to avoid lock timeouts
                for await (const userIdentity of data) {
                    try {
                        await this.processUserIdentityEntryUserV2(
                            account,
                            userIdentity,
                            highestRankedConnectionId,
                        );
                    } catch (error) {
                        this.error(error, account, { identity: userIdentity });
                    }
                }
            },
        );

        benchmark.end();
        this.log('User processing has finished', account, null, benchmark);
    }

    /**
     * Processes a user identity and creates or updates the corresponding user and entry in the system.
     * This method is responsible for synchronizing user data between the identity provider and the local database.
     *
     * @param account - The account associated with the user identity
     * @param userIdentity - The user identity to process
     * @returns A Promise that resolves to the synchronized User object
     * @throws ValidationException if the user identity is invalid or inactive
     */
    async processUserIdentityEntryUserV2(
        account: Account,
        userIdentity: UserIdentity,
        highestRankedConnectionId: number | null,
    ): Promise<User> {
        // Validate user identity email
        if (isNil(userIdentity.email) || isEmpty(userIdentity.email)) {
            throw new ValidationException(
                `No primary email found for user identity ${userIdentity.identityId}`,
            );
        }
        // Check if user identity is active
        if (!isNil(userIdentity.deletedAt)) {
            throw new ValidationException(
                `Inactive user identity found ${userIdentity.identityId}, skipping user/entry process`,
            );
        }

        // Try to find an existing user
        const existingUser =
            userIdentity.user ??
            (await this.usersCoreService.getUserByEmailNoFail(userIdentity.email));

        let syncedUser: User;
        let userDeltas: string[] = [];
        if (!isNil(existingUser)) {
            // Process existing user
            this.log(
                `Processing sync for existing user with email: ${existingUser.email}`,
                account,
            );
            const entry = await this.entryCoreService.getEntryByEmailWithAccount(
                existingUser.email,
                account.id,
                true, // withDeleted
            );

            // Ensure the entry belongs to the correct account
            await this.entryAccountCheckV2(account, userIdentity, entry);

            // Check if the entry is active
            if (!isNil(entry.deletedAt)) {
                this.warn(`Entry found is already inactive. Skipping user processing`, account, {
                    identity: userIdentity,
                });
                throw new ValidationException(
                    `Found existing inactive entry. Skipping user processing for identity with email: ${userIdentity.email}`,
                );
            }

            // Update the entry with the latest information
            const isHighestRankedConnection = await this.isHighestRankedConnection(
                account,
                userIdentity.connection.id,
                highestRankedConnectionId,
            );
            if (!isNil(highestRankedConnectionId) && !isHighestRankedConnection) {
                this.log(
                    `Not highest ranked connection. Skipping entry and user updates for identity with email: ${userIdentity.email}`,
                    account,
                );
            } else {
                await this.updateEntryV2(account, entry, userIdentity);

                // Update user information if necessary
                userDeltas = this.mapUserValuesV2(existingUser, userIdentity);
                if (userDeltas.length > 0) {
                    await this.usersCoreService.saveUser(existingUser, account.id);
                }
            }

            userIdentity.user = existingUser;
            syncedUser = existingUser;
        } else {
            // Create new user and entry
            const newEntry = await this.createEntryV2(account, userIdentity);
            this.log(`Processing sync for new user with email: ${newEntry.email}`, account);
            const newUser = await this.createUserV2(
                account,
                newEntry,
                userIdentity.email,
                userIdentity.userAvatar,
            );
            userDeltas = this.mapUserValuesV2(newUser, userIdentity);
            syncedUser = await this.usersCoreService.saveUser(newUser, account.id);

            // Create disabled user roles for the new user
            await this.createDisabledUserRolesV2(syncedUser);

            this._eventBus.publish(
                new AutopilotUserCreatedEvent(
                    account,
                    syncedUser,
                    userIdentity,
                    userIdentity.connection,
                ),
            );

            userIdentity.user = syncedUser;
        }

        // Log any changes made to the user
        if (userDeltas.length > 0) {
            this.log(
                `Synced changes to user with email: ${syncedUser.email}: ${userDeltas.join(', ')}`,
                account,
            );
        }

        // Save the synced user identity
        await this.userIdentityRepository.save(userIdentity);
        this.log(
            `Finished processing entry user with email: ${syncedUser.email} of linked user identity with ID: ${userIdentity.identityId}`,
            account,
        );
        return syncedUser;
    }

    /**
     * Maps values from a UserIdentity object to a User object and tracks changes.
     * This function is used to map user data from an external identity provider
     * to the local user entity.
     *
     * @param user - The local User object to be updated
     * @param userIdentity - The UserIdentity object containing potentially new data
     * @param allowUserEmailChange - A boolean flag indicating whether the user's email can be updated (default: true)
     * @returns An array of strings describing the changes made to the user
     */
    mapUserValuesV2(
        user: User,
        userIdentity: UserIdentity,
        allowUserEmailChange: boolean = true,
    ): string[] {
        const userChanges: string[] = [];
        if (
            allowUserEmailChange &&
            !isNil(userIdentity.email) &&
            user.email !== userIdentity.email
        ) {
            userChanges.push(`email ${user.email ?? ''} changed to ${userIdentity.email}`);
            user.email = userIdentity.email;
        }

        if (!isNil(userIdentity.firstName) && user.firstName !== userIdentity.firstName) {
            userChanges.push(
                `firstName ${user.firstName ?? ''} changed to ${userIdentity.firstName}`,
            );
            user.firstName = userIdentity.firstName;
        }

        if (!isNil(userIdentity.lastName) && user.lastName !== userIdentity.lastName) {
            userChanges.push(`lastName ${user.lastName ?? ''} changed to ${userIdentity.lastName}`);
            user.lastName = userIdentity.lastName;
        }

        if (!isNil(userIdentity.jobTitle) && user.jobTitle !== userIdentity.jobTitle) {
            userChanges.push(`jobTitle ${user.jobTitle ?? ''} changed to ${userIdentity.jobTitle}`);
            user.jobTitle = userIdentity.jobTitle;
            user.jobTitleChangedAt = new Date();
        }

        if (!isNil(userIdentity.userAvatar) && user.avatar !== userIdentity.userAvatar) {
            userChanges.push(`avatar ${user.avatar ?? ''} changed to ${userIdentity.userAvatar}`);
            user.avatar = userIdentity.userAvatar;
        }
        return userChanges;
    }

    /**
     * Processes and links HRIS user identities with existing user records.
     * This method synchronizes user data between the HRIS system and the local database,
     * updating user information and creating links between HRIS identities and user records.
     *
     * @param account - The account for which to process HRIS user identities
     * @returns A Promise that resolves when the process is complete
     */
    async processHrisUserIdentityLinkingV2(account: Account): Promise<void> {
        const benchmark = new Benchmark();
        this.log('HRIS linking has started', account, null, benchmark);

        // Fetch HRIS user identities that need to be processed
        const hrisUserIdentities =
            await this.userIdentityRepository.getUserIdentitiesForHrisLinkingPhase();

        if (isEmpty(hrisUserIdentities)) {
            this.log('No HRIS user identities were found', account, null);

            benchmark.end();

            this.log('HRIS linking has finished', account, null, benchmark);
            return;
        }

        const users = await this.usersCoreService.getAllEmployeeUsers();
        const { usersByEmail, usersByFullName } = this.getEmployeeUserMapsV2(users);

        // Process each HRIS user identity
        for await (const hrisUserIdentity of hrisUserIdentities) {
            try {
                // Skip processing if hris identity is separated and has other active identities
                const isSeparated = !isNil(hrisUserIdentity.separatedAt);
                if (isSeparated) {
                    const hasActiveUserWithSameName = await this.hasActiveHrisUserWithSameName(
                        hrisUserIdentity,
                        hrisUserIdentities,
                    );

                    if (hasActiveUserWithSameName) {
                        this.log(
                            `Skipping processing of separated HRIS user identity ${hrisUserIdentity.id} ` +
                                `because an active HRIS user with the same name exists`,
                            account,
                            { identity: hrisUserIdentity },
                        );
                        continue;
                    }
                }

                // Attempt to find a matching user
                const { matchedUser, matchType } = this.findMatchingUserForHrisIdentityV2(
                    hrisUserIdentity,
                    usersByEmail,
                    usersByFullName,
                    account,
                );

                if (!matchedUser) {
                    this.warn(
                        `No HRIS user was found for user identity with ID ${hrisUserIdentity.id}`,
                        account,
                        { identity: hrisUserIdentity },
                    );

                    continue;
                }

                this.log(`HRIS user was found matching by ${matchType}`, account, {
                    identity: hrisUserIdentity,
                    user: matchedUser,
                });

                const user = cloneDeep(matchedUser);
                // Map user updates from HRIS identity
                const userDeltas = this.mapUserValuesV2(user, hrisUserIdentity, false);

                try {
                    if (isEmpty(hrisUserIdentity.email)) {
                        throw new ValidationException(
                            `No primary email found for user identity ${hrisUserIdentity.identityId}`,
                        );
                    }
                    const hashedEmail = getSha256HashString(String(hrisUserIdentity.email));

                    if (user.personalEmail !== hashedEmail) {
                        this.log(
                            'Updating personnel personal email',
                            account,
                            hrisUserIdentity.secondaryEmail,
                        );
                        user.personalEmail = hashedEmail;
                        userDeltas.push('personal email changed');
                    }
                } catch (error) {
                    this.warn('Unable to hash personal email', account, {
                        workEmail: hrisUserIdentity.secondaryEmail,
                        error,
                    });
                }

                if (userDeltas.length > 0) {
                    await this.usersCoreService.saveUser(user, account.id);
                    this.log(
                        `Synced changes to user with email: ${user.email}: ${userDeltas.join(
                            ', ',
                        )}`,
                        account,
                    );
                }

                // Link the HRIS user identity if is unlinked or has change user and save
                if (isNil(hrisUserIdentity.user) || hrisUserIdentity.userId != user.id) {
                    hrisUserIdentity.user = user;
                    await this.userIdentityRepository.save(hrisUserIdentity);
                    this.log(
                        `Finished linking HRIS identity ${
                            hrisUserIdentity.email
                        } for user ${getUserFullName(user)} with email ${
                            user.email
                        } by match type: ${matchType},`,
                        account,
                    );
                }
            } catch (error) {
                // Log any errors and continue with the next identity
                this.error(error, account, { identity: hrisUserIdentity });
                continue;
            }
        }

        benchmark.end();
        this.log('HRIS linking has finished', account, null, benchmark);
    }

    /**
     * Retrieves and organizes employee user data into efficient lookup maps.
     * This function creates two maps:
     * 1. usersByEmail: A map of user email addresses to User objects
     * 2. usersByFullName: A map of user full names to User objects or arrays of User objects
     *
     * These maps are used to optimize the HRIS user linking process by providing
     * quick lookups based on email and full name.
     *
     * @param users - An array of User objects to be organized into maps
     * @returns An object containing two maps: usersByEmail and usersByFullName
     */
    getEmployeeUserMapsV2(users: User[]): {
        usersByEmail: Map<string, User>;
        usersByFullName: Map<string, User[]>;
    } {
        const usersByEmail = new Map<string, User>();
        const usersByFullName = new Map<string, User[]>();

        for (const user of users) {
            if (user.email) {
                usersByEmail.set(user.email.toLowerCase(), user);
            }
            const fullName = `${user.firstName} ${user.lastName}`.trim().toLowerCase();
            if (fullName) {
                const existingUserElement = usersByFullName.get(fullName);
                if (existingUserElement) {
                    existingUserElement.push(user);
                } else {
                    usersByFullName.set(fullName, [user]);
                }
            }
        }

        // Log the sizes of the maps for verification
        this.log(
            `Created user maps: Email (${usersByEmail.size}), Full Name (${usersByFullName.size})`,
        );

        return { usersByEmail, usersByFullName };
    }

    /**
     * Attempts to find a matching user based on the provided HRIS user identity.
     * This function tries to match users using the following criteria, in order:
     * 1. Secondary email (work email)
     * 2. Primary/Alt email (personal email)
     * 3. Full name (only if exactly one match - prevents incorrect merging)
     *
     * @param hrisUserIdentity - The HRIS user identity to match
     * @param usersByEmail - A map of users indexed by their email addresses
     * @param usersByFullName - A map of users indexed by their full names
     * @returns An object containing the matched user (if found) and the type of match
     */
    findMatchingUserForHrisIdentityV2(
        hrisUserIdentity: UserIdentity,
        usersByEmail: Map<string, User>,
        usersByFullName: Map<string, User[]>,
        account: Account,
    ): { matchedUser: User | undefined; matchType: string } {
        let matchedUser: User | undefined;

        if (hrisUserIdentity.secondaryEmail) {
            matchedUser = usersByEmail.get(hrisUserIdentity.secondaryEmail.toLowerCase());
            if (matchedUser) {
                return { matchedUser, matchType: 'Secondary Email' };
            }
        }

        if (hrisUserIdentity.email) {
            matchedUser = usersByEmail.get(hrisUserIdentity.email.toLowerCase());
            if (matchedUser) {
                return { matchedUser, matchType: 'Primary Email' };
            }
        }

        if (hrisUserIdentity.firstName && hrisUserIdentity.lastName) {
            // the usersByFullName map contains trimmed and lowercased user full names for matching
            const fullName = `${hrisUserIdentity.firstName} ${hrisUserIdentity.lastName}`
                .trim()
                .toLowerCase();

            const fullNameMatch = usersByFullName.get(fullName);

            if (fullNameMatch && fullNameMatch.length > 1) {
                // Multiple users with same name - log and return no match to prevent incorrect merging
                this.log(
                    `Full name match failed for HRIS user identity with email ${hrisUserIdentity.email} and name ${fullName}`,
                    account,
                    {
                        hrisEmail: hrisUserIdentity.email,
                        hrisName: fullName,
                        candidateCount: fullNameMatch.length,
                        candidates: fullNameMatch.map(u => ({
                            id: u.id,
                            email: u.email,
                            firstName: u.firstName,
                            lastName: u.lastName,
                        })),
                    },
                );
                return { matchedUser, matchType: 'none' };
            }

            if (fullNameMatch && fullNameMatch.length === 1) {
                matchedUser = fullNameMatch[0];
                if (matchedUser) {
                    return { matchedUser, matchType: 'Full Name' };
                }
            }
        }

        return { matchedUser: undefined, matchType: 'No Match Found' };
    }

    /**
     * Checks if there's an active HRIS user identity with the same first and last name as the given terminated user
     * @param terminatedUserIdentity - The terminated HRIS user identity to check
     * @param allHrisUserIdentities - All HRIS user identities to search through
     * @returns Promise<boolean> - True if an active user with the same name exists
     */
    private async hasActiveHrisUserWithSameName(
        terminatedUserIdentity: UserIdentity,
        allHrisUserIdentities: UserIdentity[],
    ): Promise<boolean> {
        if (!terminatedUserIdentity.firstName || !terminatedUserIdentity.lastName) {
            return false;
        }

        const terminatedFirstName = terminatedUserIdentity.firstName.toLowerCase();
        const terminatedLastName = terminatedUserIdentity.lastName.toLowerCase();

        return allHrisUserIdentities.some(identity => {
            // Skip if this is the same identity
            if (identity.id === terminatedUserIdentity.id) {
                return false;
            }

            // Check if this is an active user (no separatedAt date)
            const isActive = isNil(identity.separatedAt);
            if (!isActive) {
                return false;
            }

            // Check if names match
            if (!identity.firstName || !identity.lastName) {
                return false;
            }

            const activeFirstName = identity.firstName.toLowerCase();
            const activeLastName = identity.lastName.toLowerCase();

            return activeFirstName === terminatedFirstName && activeLastName === terminatedLastName;
        });
    }

    /**
     * Maps identity user data to user identity properties
     * @param idpIdentityServiceUser The identity user data from the provider
     * @param connectionId The ID of the connection
     * @returns Mapped user identity properties
     */
    private mapIdpIdentityUserToPropertiesV2(
        idpIdentityServiceUser: IdentityServiceUser,
        account: Account,
        userIdentity: UserIdentity,
        userAvatar?: string | null,
    ): UserIdentity {
        // Maps IDP properties
        try {
            Object.assign(userIdentity, {
                ...userIdentity,
                email: idpIdentityServiceUser.getPrimaryEmail(),
                username: idpIdentityServiceUser.getUserName(),
                firstName: idpIdentityServiceUser.getFirstName(),
                lastName: idpIdentityServiceUser.getLastName(),
                avatarSource: idpIdentityServiceUser.getAvatarUrl(),
                userAvatar,
                hasMfa: idpIdentityServiceUser.hasMfa(),
                lastCheckedAt: new Date(),
                jobTitle: idpIdentityServiceUser.getJobTitle(),
                startedAt: idpIdentityServiceUser.getStartedAt(),
                separatedAt:
                    isNil(userIdentity.separatedAt) ||
                    isNil(idpIdentityServiceUser.getSeparatedAt())
                        ? idpIdentityServiceUser.getSeparatedAt()
                        : userIdentity.separatedAt,
                isContractor: idpIdentityServiceUser.isContractor(),
                connectedAt: isNil(userIdentity.connectedAt)
                    ? new Date()
                    : userIdentity.connectedAt,
                deletedAt: null,
            });

            return userIdentity;
        } catch (error) {
            this.error(error, account, { idpIdentityServiceUser, userIdentity });
            throw error;
        }
    }

    /**
     * Maps identity user data to user identity properties
     * @param hrisIdentityServiceUser The identity user data from the provider
     * @param connectionId The ID of the connection
     * @returns Mapped user identity properties
     */
    mapHrisIdentityUserToPropertiesV2(
        hrisIdentityServiceUser: HrisUser,
        account: Account,
        userIdentity: UserIdentity,
    ): UserIdentity {
        try {
            // Maps HRIS properties
            Object.assign(userIdentity, {
                ...userIdentity,
                email: !isEmpty(hrisIdentityServiceUser.getPrimaryEmail())
                    ? hrisIdentityServiceUser.getPrimaryEmail()
                    : userIdentity.email,
                secondaryEmail: !isEmpty(hrisIdentityServiceUser.getWorkEmail())
                    ? hrisIdentityServiceUser.getWorkEmail()
                    : userIdentity.secondaryEmail,
                username: !isEmpty(hrisIdentityServiceUser.getUserName())
                    ? hrisIdentityServiceUser.getUserName()
                    : userIdentity.username,
                firstName: hrisIdentityServiceUser.getFirstName(),
                lastName: hrisIdentityServiceUser.getLastName(),
                lastCheckedAt: new Date(),
                jobTitle: hrisIdentityServiceUser.getPrimaryJobTitle(),
                startedAt: hrisIdentityServiceUser.getStartedAt(),
                separatedAt:
                    isNil(userIdentity.separatedAt) ||
                    isNil(hrisIdentityServiceUser.getSeparatedAt())
                        ? hrisIdentityServiceUser.getSeparatedAt()
                        : userIdentity.separatedAt,
                isContractor: hrisIdentityServiceUser.isContractor(),
                managerId: hrisIdentityServiceUser.getManagerId(),
                managerName: hrisIdentityServiceUser.getManagerName(),
                connectedAt: isNil(userIdentity.connectedAt)
                    ? new Date()
                    : userIdentity.connectedAt,
                deletedAt: null,
            });

            return userIdentity;
        } catch (err) {
            this.error(err, account, { hrisIdentityServiceUser, userIdentity });
            throw err;
        }
    }

    /**
     * Resolves manager names for HRIS integrations
     * Since some HRIS integrations only provide manager IDs, this method looks up manager names
     * from the user identities and updates the managerName field accordingly
     *
     * @param account - The account to process
     */
    async resolveManagerNamesForHrisV2(
        account: Account,
        hrisApi: IHrisIdentityServices,
    ): Promise<void> {
        this.log('Starting manager name resolution for HRIS integrations', account);

        if (!hrisApi.needsManagerNameResolution()) {
            this.log('Provider does not need manager name resolution', account);
            return;
        }

        // Get all HRIS user identities that have managerId but no managerName
        const hrisIdentitiesWithManagerId =
            await this.userIdentityRepository.getHrisUserIdentitiesWithManagerId(account);

        if (isEmpty(hrisIdentitiesWithManagerId)) {
            this.log('No HRIS identities found that need manager name resolution', account);
            return;
        }

        this.log(
            `Found ${hrisIdentitiesWithManagerId.length} HRIS identities needing manager name resolution`,
            account,
        );

        // Get all HRIS user identities to use as lookup for manager names
        const allHrisIdentities =
            await this.userIdentityRepository.getAllHrisUserIdentities(account);

        const identitiesWithManagerName = hrisApi.getMappedManagerName(
            hrisIdentitiesWithManagerId,
            allHrisIdentities,
        );

        if (identitiesWithManagerName.length > 0) {
            try {
                await this.userIdentityRepository.save(identitiesWithManagerName);
                this.log(
                    `Saved ${identitiesWithManagerName.length} identities with resolved manager names`,
                    account,
                );
            } catch (error) {
                this.error(error, account, {
                    method: 'resolveManagerNamesAtProviderLevel',
                    identityCount: identitiesWithManagerName.length,
                });
            }
        }

        this.log('Manager name resolution completed.', account);
    }

    /**
     * Verifies that an entry belongs to the correct account during identity synchronization.
     * This method prevents cross-tenant data conflicts by ensuring that user entries
     * are only processed within their correct organizational context.
     *
     * @param account - The account currently being processed
     * @param userIdentity - The user identity being synchronized
     * @param entry - The entry (user record) being checked
     * @throws ConflictException if the entry belongs to a different account
     */
    private async entryAccountCheckV2(
        account: Account,
        userIdentity: UserIdentity,
        entry: Entry,
    ): Promise<void> {
        const customerAccount = await this.authService.getCustomerAccountByEntry(entry);

        if (!isNil(customerAccount) && account.id !== customerAccount.id) {
            throw new ConflictException(
                `Entry found with id ${entry.id} using email ${userIdentity.email},
                during identity sync for account ${account.id} and domain ${account.domain},
                but entry belongs to another account with id ${customerAccount.id} and domain ${customerAccount.domain},
                halting sync process for this user...`,
                ErrorCode.CONFLICT_CROSS_TENANT_ENTRY,
            );
        }
    }

    /**
     * Creates an Entry for a given UserIdentity.
     * This method attempts to find an existing Entry by email, and if not found,
     * creates a new one. It also handles account checks and token clearing.
     *
     * @param account - The Account associated with the Entry
     * @param userIdentity - The UserIdentity for which to create or find an existing Entry
     * @returns A Promise resolving to the created or found Entry
     */
    private async createEntryV2(account: Account, userIdentity: UserIdentity): Promise<Entry> {
        this.log('Creating entry for user identity', account, userIdentity);

        let entry: Entry | null = null;
        let auditor: Auditor | null = null;

        try {
            // Attempt to find an existing entry by email
            entry = await this.entryCoreService.getEntryByEmailWithAccount(
                String(userIdentity.email),
                account.id,
            );
            // Check if the found entry belongs to the correct account
            await this.entryAccountCheckV2(account, userIdentity, entry);
            this.log(
                'Entry for user identity already exists, using that instead',
                account,
                userIdentity,
            );
        } catch (error) {
            // Handle potential conflicts
            if (error instanceof ConflictException && !isNil(entry)) {
                // If there's a conflict, try to get the auditor associated with the entry
                auditor = await this.auditorsCoreService.getAuditorByEntry(entry);
            }

            // Rethrow error if it's not a NotFoundException and no auditor was found
            if (!(error instanceof NotFoundException) && isNil(auditor)) {
                throw error;
            }
        }

        // Create a new entry if none was found
        if (isNil(entry)) {
            entry = new Entry();
            entry.email = String(userIdentity.email);
        } else {
            await this.clearEntryTokens(entry);
        }

        entry.setEntryAccount(account, true);
        this.log('Saving Entry for IdentityUser', account, entry);
        return this.entryCoreService.saveEntry(entry);
    }

    /**
     * Updates an existing Entry with information from a UserIdentity.
     * This method ensures that the Entry's email is synchronized with the UserIdentity's email,
     * but only performs the update if there's an actual change.
     *
     * @param account - The Account associated with the Entry
     * @param ours - The existing Entry object to be updated
     * @param userIdentity - The UserIdentity object containing potentially new information
     * @returns A Promise that resolves when the update is complete
     */
    private async updateEntryV2(
        account: Account,
        ours: Entry,
        userIdentity: UserIdentity,
    ): Promise<void> {
        const entry = cloneDeep(ours);

        // TODO: What would happen if multiple user identity emails exist for the same user?
        // Ranking system should be considered? How is this going to be handled for Multi-IdP?

        // Perform a sanity check on the userIdentity's email
        if (isNil(userIdentity.email) || isEmpty(userIdentity.email)) {
            this.warn('Email is required to update entry', account, entry);
            return;
        }

        if (entry.email === userIdentity.email) {
            this.log('No changes detected to entry', account, entry);
            return;
        }

        this.log('Saving changes to entry', account, entry);

        entry.email = userIdentity.email;

        await this.entryCoreService.saveEntry(entry);
    }

    /**
     * Creates a new user or retrieves an existing one based on the provided email.
     * This method is part of the user synchronization process, ensuring that each
     * identity email is associated with a user in the system.
     *
     * @param account - The account associated with the user
     * @param entry - The entry object related to the user
     * @param identityEmail - The email address of the user from the identity provider
     * @returns A Promise that resolves to a User object (either existing or newly created)
     */
    private async createUserV2(
        account: Account,
        entry: Entry,
        identityEmail: string,
        userAvatar?: string | null,
    ): Promise<User> {
        try {
            const existingUser = await this.usersCoreService.getUserByEmail(String(identityEmail));

            // If an existing user is found, return it immediately
            if (!isNil(existingUser)) {
                return existingUser;
            }
        } catch (e) {
            this.log(`No user found with email ${identityEmail}, creating new user`, account);
        }

        const newUser = new User();
        newUser.entryId = entry.id;
        newUser.email = String(identityEmail);
        newUser.drataTermsAgreedAt = null;
        newUser.language = account.language;
        if (!isNil(userAvatar)) {
            newUser.avatar = userAvatar;
        }
        return newUser;
    }

    /**
     * Creates a disabled EMPLOYEE role for a new user.
     *
     * This method sets up the initial role structure for a user, but keeps it in a disabled state.
     * The role will be activated later during the personnel processing phase, once the employment
     * status is verified.
     *
     * @param user - The user for whom the role is being created
     * @returns A Promise resolving to an array containing the newly created (disabled) UserRole
     */
    private async createDisabledUserRolesV2(user: User): Promise<UserRole[]> {
        const employeeRole = new UserRole();
        employeeRole.role = Role.EMPLOYEE;
        employeeRole.user = user;

        const role = Role[Role.EMPLOYEE];

        await this.usersCoreService.addUsersRoles(role, user, null);
        /**
         * Creates Employee Role in disabled state
         * NOTE: Role remains soft-deleted until the employment status is verified
         * during the personnel processing phase
         * DEPENDENCIES: Requires personnel phase to activate role based on verified employment status
         */
        employeeRole.deletedAt = new Date();
        await this.usersCoreService.deleteUsersRolesAndPermissions(role, user, null);
        await this.userRoleRepository.save(employeeRole);

        return [employeeRole];
    }

    /**
     * Processes personnel data for all users in an account.
     * This method synchronizes the personnel data based on the user identities found for a user and manages policy distributions.
     * It handles both new and existing personnel, taking into account the presence of an HRIS directory.
     *
     * @param account - The account for which to process personnel
     * @param hasHrisDirectory - Boolean indicating whether an HRIS directory is available
     * @returns A Promise that resolves when all personnel have been processed
     */
    async processPersonnelV2(account: Account, hasHrisDirectory: boolean): Promise<void> {
        const policyDistributionBatchSize = config.get('sync.policyDistributionBatchSize');
        const benchmark = new Benchmark();
        this.log('Process Personnel has started', account, null, benchmark);

        await forEachTokenPage(
            async (_, page = 0) => {
                const pageResult = await this.usersCoreService.getAllPersonnelUsers({ page });
                return {
                    data: {
                        page: pageResult?.data ?? [],
                        nextPageToken: isEmpty(pageResult?.data) ? null : true,
                    },
                };
            },
            async (data: User[]) => {
                if (isEmpty(data)) {
                    this.warn(
                        'No personnel found to be processed. Exiting the sync.',
                        account,
                        null,
                        benchmark,
                    );
                    return;
                }
                this.log(`Processing ${data.length} users for personnel sync`, account);

                for await (const user of data) {
                    this.log(`Processing user ${user.email}`, account);
                    try {
                        // Skip processing if user has no primary identity and no existing personnel record
                        if (
                            isNil(getPrimaryIdpUserIdentity(user.identities)) &&
                            isNil(user.personnel)
                        ) {
                            this.log(
                                `Skipping procesing personnel for user ${user.email}. No identity found`,
                                account,
                            );
                            continue;
                        }

                        const { personnel, personnelWasUpdated } =
                            await this.processUserPersonnelV2(account, user, hasHrisDirectory);

                        // Add to policy distribution list if personnel was updated and is currently employed
                        if (personnelWasUpdated && isInACurrentStatus(personnel.employmentStatus)) {
                            this.pushToPolicyDistributionPersonnelListFromMap(account, personnel);
                        }
                        if (
                            this.getPolicyDistributionPersonnelListFromMap?.length >=
                            policyDistributionBatchSize
                        ) {
                            this.log(
                                `Distributing policies to personnel with email ${user.email}`,
                                account,
                            );
                            await this.distributePoliciesToPersonnelV2(account);
                        }
                    } catch (err) {
                        this.error(err, account, {
                            user: {
                                id: user.id,
                                firstName: user.firstName,
                                lastName: user.lastName,
                                email: user.email,
                                jobTitle: user.jobTitle,
                            },
                            err,
                            method: 'processPersonnelV2',
                        });
                    }
                }
            },
        );

        // Distribute any remaining policies
        await this.distributePoliciesToPersonnelV2(account);
        benchmark.end();
        this.log('Process personnel has finished', account, null, benchmark);
    }

    /**
     * Processes and synchronizes personnel data for a single user.
     * This method handles the creation or update of personnel records based on user identity information.
     * It manages the synchronization between user data from identity providers (IDP) and HR systems (HRIS),
     * updates personnel status, and triggers relevant events and policy distributions.
     *
     * @param account - The account associated with the user
     * @param user - The user object to process
     * @param hasHrisDirectory - Boolean indicating whether an HRIS directory is available
     * @returns A Promise resolving to an object containing the updated personnel and a flag indicating if updates were made
     */
    async processUserPersonnelV2(
        account: Account,
        user: User,
        hasHrisDirectory: boolean,
    ): Promise<{ personnel: Personnel; personnelWasUpdated: boolean }> {
        // Retrieve primary identities from IDP and HRIS
        const idpUserIdentity = getPrimaryIdpUserIdentity(user.identities);
        const hrisUserIdentity = getPrimaryHrisUserIdentity(user.identities);
        const recentConnection = await this.connectionsCoreService.getIdentityProviderConnection();
        let personnel: Personnel;
        let currentPersonnel: Personnel;
        let personnelDelta: string[] = [];

        if (!isNil(user.personnel)) {
            // Update existing personnel record
            personnel = user.personnel;
            // At this point, user from personnel is not fully load so we assign for logging purposes
            personnel.user = user;
            if (isNil(idpUserIdentity) && isInAFormerStatus(personnel.employmentStatus)) {
                return { personnel, personnelWasUpdated: false };
            }
            currentPersonnel = cloneDeep(personnel);

            // Syncing existing personnel record
            personnelDelta = this.mapPersonnelValuesV2(
                account,
                user,
                personnel,
                hasHrisDirectory,
                idpUserIdentity,
                hrisUserIdentity,
            );

            if (personnelDelta.length > 0) {
                this._eventBus.publish(new AssetPersonnelUpdatedEvent(account, personnel));
            }
        } else {
            // Create new personnel record
            if (isNil(idpUserIdentity)) {
                throw new Error(
                    `Personnel record can't be create to user missing an IdP user identity: ${user.email}`,
                );
            }
            personnel = new Personnel();
            personnel.user = user;
            personnelDelta = this.mapPersonnelValuesV2(
                account,
                user,
                personnel,
                hasHrisDirectory,
                idpUserIdentity,
                hrisUserIdentity,
            );

            // Initialize and set up personnel data
            const personnelData = new PersonnelData();
            this.setPersonnelDataValues(personnelData);
            personnel.data = personnelData;

            // Save new personnel and create associated records
            await this.personnelCoreService.savePersonnel(personnel);
            await this.devicesCoreService.createUnknownDevice(personnel);
            const complianceChecks = await this.createComplianceChecksV2(
                account,
                personnel,
                idpUserIdentity,
            );
            personnel.complianceChecks = complianceChecks;
            personnelDelta.push('complianceChecks');

            // TODO: Unwind this code for non-production environment.
            // Consider separating the logic into proper seeders.
            if (!isProd() && !config.get('api.simulateProdIdentitySync')) {
                /**
                 * If the Personnel has a former status then we soft delete his/her roles
                 * this only is when we do a nukeAndPave for some Personnels what are
                 * created as former and have roles
                 */
                if (allStrictFormerStatuses.includes(personnel.employmentStatus)) {
                    await this.usersCoreService.deleteAllUserRolesPermissions(account, user);
                }
                // non-prod: re-align data with real connection/agent based data instead of seeding
                await this.completeComplianceDeviceChecks(account, personnel);
            }
            currentPersonnel = cloneDeep(personnel);

            this._eventBus.publish(
                new AssetPersonnelCreatedEvent(
                    account,
                    user,
                    personnel,
                    idpUserIdentity.connection,
                ),
            );
        }
        if (isNil(personnel)) {
            throw new Error(`Personnel record not found for user with email: ${user.email}`);
        }

        // Save personnel changes if any
        if (personnelDelta.length > 0) {
            await this.personnelCoreService.savePersonnel(personnel);
            this.log(
                `Update changes to personnel with email: ${user.email}: ${personnelDelta.join(
                    ', ',
                )}`,
                account,
            );
        }

        // Restore employee user role if necessary
        if (
            isInACurrentStatus(personnel.employmentStatus) &&
            !user.roles.some(role => role.deletedAt == null && role.role == Role.EMPLOYEE)
        ) {
            await this.usersCoreService.restoreUserEmployeeRole(user);
            await this.usersCoreService.restoreUserEmployeePermissions(user);
            this.log(`User with email: ${user.email} restored employee role`);
        }

        await this.recomputePersonnelComplianceChecksV2(
            account,
            currentPersonnel,
            personnel,
            idpUserIdentity,
        );

        this._eventBus.publish(
            new AutopilotUserUpdatedEvent(
                account,
                [user, personnel],
                hrisUserIdentity ?? idpUserIdentity,
                recentConnection,
            ),
        );

        // Check if personnel should be separated
        const shouldSeparatePersonnel = personnelToFormerStatus(
            currentPersonnel.employmentStatus,
            personnel.employmentStatus,
        );

        if (shouldSeparatePersonnel) {
            const separationComplete = await this.separateUserPersonnelV2(
                account,
                currentPersonnel,
                personnel,
            );
            if (!separationComplete) {
                await this.restorePersonnelEmploymentStatusV2(account, personnel, currentPersonnel);
            }
        }

        return { personnel, personnelWasUpdated: personnelDelta.length > 0 };
    }

    /**
     * Resolves and sets compliance check values for a given compliance check.
     *
     * This method determines and sets the appropriate values for a compliance check based on
     * the check type, user identity information, and whether the personnel is a future hire.
     * It handles special cases such as MFA compliance and sets default values for other checks.
     *
     * @param account - The account associated with the personnel
     * @param complianceCheck - The compliance check object to be updated
     * @param idpUserIdentity - The user identity information from the identity provider
     * @param isFutureHire - Optional flag indicating if the personnel is a future hire
     */
    private resolveComplianceCheckValuesV2(
        account: Account,
        complianceCheck: ComplianceCheck,
        idpUserIdentity: UserIdentity | null,
        isFutureHire?: boolean,
    ): void {
        const logIdentifier = `email: ${idpUserIdentity?.email},
            name: '${idpUserIdentity?.firstName} ${idpUserIdentity?.lastName}',
            userIdentityId: ${idpUserIdentity?.userId}`;
        // set the frequency
        complianceCheck.checkFrequency = complianceCheckFrequency(complianceCheck.type);

        this.log(
            `Resolving compliance check values for ${ComplianceCheckType[complianceCheck.type]}`,
            account,
        );
        // set the expiration date
        complianceCheck.expiresAt = new Date();
        // set the last checked at
        complianceCheck.lastCheckedAt = new Date();

        if (isFutureHire) {
            // We should set all the complice checks to excluded if is a future hire
            complianceCheck.status = ComplianceCheckStatus.EXCLUDED;
            return;
        }

        // set initial compliance
        if (complianceCheck.status !== ComplianceCheckStatus.EXCLUDED) {
            complianceCheck.status = ComplianceCheckStatus.FAIL;
        }

        // special check here for MFA
        if (complianceCheck.type === ComplianceCheckType.IDENTITY_MFA) {
            if (idpUserIdentity?.hasMfa === true) {
                this.log(`MFA compliant: true (based on IDP), ${logIdentifier}`, account);
                // set the value
                if (complianceCheck.status !== ComplianceCheckStatus.EXCLUDED) {
                    complianceCheck.status = ComplianceCheckStatus.PASS;
                }
                // set the next expiration date
                complianceCheck.expiresAt = checkFrequencyNextExpiration(
                    complianceCheck.checkFrequency,
                );
            } else {
                this.log(
                    `MFA compliant: false (keeping default values), ${logIdentifier}`,
                    account,
                );
            }
        }
    }

    /**
     * Maps personnel values based on the latest information from IDP and HRIS.
     *
     * This method is responsible for mapping the personnel data with the most up-to-date
     * information from the Identity Provider (IDP) and Human Resource Information System (HRIS).
     * It handles updates to employment status, start and separation dates, manager information,
     * and tracks all changes made to the personnel record.
     *
     * @param account - The account associated with the personnel
     * @param user - The user object linked to the personnel
     * @param personnel - The current personnel record to be updated
     * @param hasHrisDirectory - Boolean indicating if HRIS directory is available
     * @param idpUserIdentity - User identity information from the Identity Provider (optional)
     * @param hrisUserIdentity - User identity information from the HRIS (optional)
     * @returns An array of strings describing the changes made to the personnel record
     */
    private mapPersonnelValuesV2(
        account: Account,
        user: User,
        personnel: Personnel,
        hasHrisDirectory: boolean,
        idpUserIdentity: UserIdentity | null = null,
        hrisUserIdentity: UserIdentity | null = null,
    ): string[] {
        const personnelChanges: string[] = [];
        // Skip processing for out-of-scope personnel
        if (isOutOfScope(personnel.employmentStatus)) {
            this.log(`Skipping sync personnel marked as OOS for ${user.email}`);
            return [];
        }

        const hasHrisIdentity = !isNil(hrisUserIdentity);

        // Determine the latest employment status
        const latestEmploymentStatus = getLatestEmploymentStatus(
            personnel,
            hasHrisDirectory,
            idpUserIdentity,
            hrisUserIdentity,
        );
        const hasEmploymentStatusChanged = personnel.employmentStatus !== latestEmploymentStatus;

        // Get hire and termination dates
        const { startedAtDate, resolutionReason: startedAtDateResolutionReason } = getStartedAtDate(
            personnel,
            hasHrisDirectory,
            idpUserIdentity,
            hrisUserIdentity,
        );
        this.log(startedAtDateResolutionReason, account);
        const { separatedAtDate, resolutionReason: separatedAtDateResolutionReason } =
            getSeparatedAtDate(personnel, hasHrisDirectory, idpUserIdentity, hrisUserIdentity);
        this.log(separatedAtDateResolutionReason, account);

        // Handle employment status updates
        if (!isNil(personnel.statusUpdatedAt)) {
            this.log(
                `Manual override detected, setting employment status to ${
                    EmploymentStatus[personnel.employmentStatus]
                }`,
                account,
            );
            // When manual override is present, preserve existing start date (don't update from HRIS/IDP)
        } else {
            // Update start date when no manual override
            if (!isNil(startedAtDate) && personnel.startDate !== startedAtDate.toISO8601String()) {
                personnelChanges.push(
                    `startDate ${
                        personnel.startDate
                    } changed to ${startedAtDate.toISO8601String()}`,
                );
                personnel.startDate = startedAtDate.toISO8601String();
            }

            if (hasEmploymentStatusChanged) {
                // Update employment status and handle separation (separatedAt) date
                personnelChanges.push(
                    `employmentStatus changed (${EmploymentStatus[personnel.employmentStatus]} to ${
                        EmploymentStatus[latestEmploymentStatus]
                    })`,
                );
                personnel.employmentStatus = latestEmploymentStatus;
                personnel.notHumanReason = null;
            }

            switch (latestEmploymentStatus) {
                case EmploymentStatus.FUTURE_HIRE:
                    personnel.separatedAt = null;
                    personnel.notHumanReason = 'This personnel has a future hire date.';
                    break;
                case EmploymentStatus.FORMER_EMPLOYEE:
                case EmploymentStatus.FORMER_CONTRACTOR:
                case EmploymentStatus.SPECIAL_FORMER_CONTRACTOR:
                    this.log(
                        `Resolved former employment status: ${
                            EmploymentStatus[latestEmploymentStatus]
                        } and separation date: ${separatedAtDate?.toISO8601String()}`,
                        account,
                    );
                    // We need to set separationDate for backwards compatibility
                    if (
                        !isNil(separatedAtDate) &&
                        personnel.separationDate !== separatedAtDate.toISO8601String()
                    ) {
                        personnel.separationDate = separatedAtDate.toISO8601String();
                        personnel.statusUpdatedAt = null;
                    }
                    break;
                case EmploymentStatus.UNKNOWN:
                case EmploymentStatus.CURRENT_EMPLOYEE:
                case EmploymentStatus.CURRENT_CONTRACTOR:
                case EmploymentStatus.SPECIAL_FORMER_EMPLOYEE:
                case EmploymentStatus.SERVICE_ACCOUNT:
                case EmploymentStatus.OUT_OF_SCOPE:
                default:
                    personnel.separatedAt = null;
                    personnel.separationDate = null;
                    break;
            }
            this.log(
                `Changing employment status for ${user.email} to ${EmploymentStatus[latestEmploymentStatus]}`,
                account,
            );
        }

        // started at
        if (personnel.startedAt !== startedAtDate) {
            personnelChanges.push(
                `startedAt ${personnel.startedAt?.toISO8601String()} changed to ${startedAtDate?.toISO8601String()}`,
            );
            personnel.startedAt = startedAtDate;
        }
        // separated at
        if (personnel.separatedAt !== separatedAtDate) {
            personnelChanges.push(
                `separatedAt ${personnel.separatedAt?.toISO8601String()} changed to ${separatedAtDate?.toISO8601String()}`,
            );
            personnel.separatedAt = separatedAtDate;
        }
        // manager name
        const managerName = hasHrisIdentity ? hrisUserIdentity.managerName : null;
        if (personnel.managerName !== managerName) {
            personnelChanges.push(`managerName ${personnel.managerName} changed to ${managerName}`);
            personnel.managerName = managerName;
        }
        // manager id
        const managerId = hasHrisIdentity ? hrisUserIdentity.managerId : null;
        if (personnel.managerExternalId !== managerId) {
            personnelChanges.push(
                `managerExternalId ${personnel.managerExternalId} changed to ${managerId}`,
            );
            personnel.managerExternalId = managerId;
        }

        return personnelChanges;
    }

    /**
     * Creates compliance checks for a given personnel based on their identity provider information.
     *
     * This method generates a set of compliance checks for all compliance types defined in the
     * ComplianceCheckType enum. It takes into account whether the personnel is a future hire
     * and uses the identity provider's user identity information to resolve compliance check values.
     *
     * The method performs the following steps:
     * 1. Initializes an array to store compliance checks
     * 2. Determines if the personnel is a future hire
     * 3. Iterates through all compliance check types
     * 4. Creates and configures a compliance check for each type
     * 5. Resolves specific values for each compliance check
     * 6. Saves all created compliance checks to the database
     *
     * @param {Account} account - The account associated with the personnel
     * @param {Personnel} personnel - The personnel for whom compliance checks are being created
     * @param {UserIdentity} idpUserIdentity - The user identity information from the identity provider
     * @returns {Promise<ComplianceCheck[]>} A promise that resolves to an array of created compliance checks
     */
    createComplianceChecksV2(
        account: Account,
        personnel: Personnel,
        idpUserIdentity: UserIdentity,
    ): Promise<ComplianceCheck[]> {
        const complianceChecks: ComplianceCheck[] = [];
        const isFutureHire =
            !isNil(personnel.startedAt) && isFutureDate(personnel.startedAt ?? null);

        for (const complianceType of getNumericEnumValues(ComplianceCheckType)) {
            const complianceCheck = new ComplianceCheck();
            complianceCheck.personnel = personnel;
            complianceCheck.type = complianceType;
            this.resolveComplianceCheckValuesV2(
                account,
                complianceCheck,
                idpUserIdentity,
                isFutureHire,
            );
            complianceChecks.push(complianceCheck);
        }

        this.log(`Added ${complianceChecks.length} compliance checks for personnel`, account);

        return this.complianceChecksCoreService.saveComplianceChecks(complianceChecks);
    }

    /**
     * Recomputes compliance checks for personnel, ensuring all necessary checks are in place
     * and up-to-date, especially when transitioning from a special to a current status.
     *
     * This method handles the creation of missing compliance checks, updates the MFA compliance check,
     * and recalculates the overall personnel compliance status.
     *
     * @param account - The account associated with the personnel
     * @param currentPersonnel - The current state of the personnel record
     * @param personnel - The updated personnel record
     * @param idpUserIdentity - The identity provider's user identity information
     * @returns A Promise that resolves when all compliance checks have been recomputed
     */
    private async recomputePersonnelComplianceChecksV2(
        account: Account,
        currentPersonnel: Personnel,
        personnel: Personnel,
        idpUserIdentity: UserIdentity | null,
    ): Promise<void> {
        try {
            this.log(
                `Recomputing personnel with email ${personnel?.user?.email} compliance checks`,
                account,
            );
            if (
                personnelSpecialToCurrentStatus(
                    currentPersonnel.employmentStatus,
                    personnel.employmentStatus,
                )
            ) {
                this.log(
                    `Creating compliance checks and user policy versions for previous special former personnel with id ${personnel.id}`,
                    account,
                );

                const personnelComplianceChecksTypes = personnel.complianceChecks.map(
                    complianceCheck => complianceCheck.type,
                );

                const neededComplianceChecks = getNumericEnumValues(ComplianceCheckType).filter(
                    checkType => !personnelComplianceChecksTypes.includes(checkType),
                );

                // Added await that was missed when going from v1 to v2
                await this.complianceChecksCoreService.createComplianceChecks(
                    neededComplianceChecks,
                    personnel,
                );
            }

            await this.updateMFAComplianceCheckV2(account, personnel, idpUserIdentity);
            await this.recomputePersonnelCompliance(account, personnel);
        } catch (error) {
            this.error(error, account, { personnel: personnel, identity: idpUserIdentity });
        }
    }

    /**
     * Checks if a user has valid MFA evidence documents.
     *
     * This method retrieves all MFA evidence documents for a given user and checks
     * if any of them are still valid (not expired). It's used as a fallback method
     * to verify MFA compliance when the Identity Provider doesn't confirm MFA status directly.
     *
     * @param account - The account associated with the personnel
     * @param personnel - The personnel whose MFA documents are being checked
     * @returns A Promise that resolves to a boolean indicating if any valid MFA evidence document exists
     */
    private async hasMfaUserDocumentsV2(account: Account, personnel: Personnel): Promise<boolean> {
        if (isNil(personnel.user)) {
            return false;
        }

        // Retrieve all MFA evidence documents for the user
        const userDocuments = await this.usersCoreService.listAllDocumentsOfType(
            personnel.user.id,
            UserDocumentType.MFA_EVIDENCE,
        );

        // Check if any document is still valid (not expired)
        const isSomeDocumentValid = userDocuments.some(
            ({ renewalDate }) => !hasExpired(new Date(renewalDate)),
        );

        return isSomeDocumentValid;
    }

    /**
     * Updates the Multi-Factor Authentication (MFA) compliance check for a given personnel.
     * This method checks the MFA status from the Identity Provider (IDP) and user documents,
     * updates the compliance check status, and handles any necessary events or logging.
     *
     * @param account - The account associated with the personnel
     * @param personnel - The personnel whose MFA compliance is being checked
     * @param idpUserIdentity - The user identity information from the Identity Provider
     */
    private async updateMFAComplianceCheckV2(
        account: Account,
        personnel: Personnel,
        idpUserIdentity: UserIdentity | null,
    ) {
        if (isNil(personnel.user)) {
            throw new ValidationException(`No user found for personnel ID: ${personnel.id}`);
        }
        const logIdentifier = `email: ${personnel.user?.email}, name: '${
            personnel.user?.firstName
        } ${personnel.user?.lastName}', identityId: ${idpUserIdentity?.identityId ?? 'Not Found'}`;
        // Find the MFA compliance check for this personnel
        const mfaComplianceCheck = personnel.complianceChecks.find(
            complianceCheck => complianceCheck.type === ComplianceCheckType.IDENTITY_MFA,
        );

        // If no MFA compliance check is found, log a warning and exit
        if (isEmpty(mfaComplianceCheck) || isNil(mfaComplianceCheck)) {
            this.warn(`MFA compliance not found for ${logIdentifier}`, account);
            return;
        }

        // Initialize compliance check status as FAIL and set current date as expiration
        let complianceCheckStatus = ComplianceCheckStatus.FAIL;
        let expiresAt = new Date();

        // Check if the compliance check is excluded
        if (mfaComplianceCheck.status === ComplianceCheckStatus.EXCLUDED) {
            this.log(
                `MFA compliant: excluded (based on compliance check exclusion), ${logIdentifier}`,
                account,
            );
            complianceCheckStatus = ComplianceCheckStatus.EXCLUDED;
        } else {
            if (idpUserIdentity?.hasMfa === true) {
                this.log(`MFA compliant: true (based on IDP), ${logIdentifier}`, account);
                complianceCheckStatus = ComplianceCheckStatus.PASS;
                // Calculate next expiration date based on check frequency
                expiresAt = checkFrequencyNextExpiration(mfaComplianceCheck.checkFrequency);
            } else {
                // If IDP doesn't confirm MFA, check user documents
                const isSomeDocumentValid = await this.hasMfaUserDocumentsV2(account, personnel);
                if (isSomeDocumentValid) {
                    this.log(
                        `User is MFA compliant (based on document), ${logIdentifier}`,
                        account,
                    );
                    complianceCheckStatus = ComplianceCheckStatus.PASS;
                } else {
                    this.log(`MFA compliant: false, ${logIdentifier}`, account);
                }
            }
        }

        mfaComplianceCheck.lastCheckedAt = new Date();

        // If the compliance status has changed, update and publish an event
        if (complianceCheckStatus !== mfaComplianceCheck.status) {
            mfaComplianceCheck.status = complianceCheckStatus;
            mfaComplianceCheck.expiresAt = expiresAt;
            this._eventBus.publish(
                new AutopilotComplianceCheckUpdatedEvent(
                    account,
                    [personnel, mfaComplianceCheck],
                    idpUserIdentity,
                ),
            );
        }

        this.log(`Save compliance check for ${personnel?.user?.email}`, account, {
            complianceCheck: mfaComplianceCheck,
        });
        await this.complianceChecksCoreService.saveComplianceCheck(mfaComplianceCheck);

        // Update the full compliance status for the user
        await this.complianceChecksOrchestrationService.updateFullCompliance(personnel.user);
    }

    /**
     * Distributes policies to a batch of personnel for a given account.
     * This method handles the distribution of ALL-scoped policies to personnel
     * who have been queued for policy distribution.
     *
     * @param account - The account for which policies are being distributed
     * @returns A promise that resolves when the distribution is complete
     */
    private async distributePoliciesToPersonnelV2(account: Account): Promise<void> {
        const policyDistributionPersonnelList =
            this.getPolicyDistributionPersonnelListFromMap(account);
        // Check if there are any personnel queued for policy distribution
        if (!policyDistributionPersonnelList?.length) {
            this.log('No personnel to distribute policies to', account);
            return;
        }
        try {
            // Get the user policies service for the account and publish policies
            // to the queued personnel with ALL scope
            await this.userPoliciesCoreService.publishForPersonnelSet(
                account,
                policyDistributionPersonnelList,
                [PolicyScope.ALL],
            );
            // Clear the queue of personnel after successful distribution
            // This prevents duplicate distributions in subsequent calls
            this.clearPolicyDistributionPersonnelListFromMap(account);
        } catch (error) {
            this.log(`Failed to publish policies for personnel batch:`, error);
        }
    }

    /**
     * Separates a user's personnel record, handling the process based on feature flag status.
     * This method is responsible for updating the personnel status, removing roles and ownerships,
     * and notifying the system about the separation.
     *
     * @param account - The account associated with the personnel
     * @param currentPersonnel - The current state of the personnel record
     * @param personnel - The updated personnel record to be processed
     * @param idpUserIdentity - The identity provider's user identity information
     * @throws Error if the personnel record doesn't have an associated user
     */
    private async separateUserPersonnelV2(
        account: Account,
        currentPersonnel: Personnel,
        personnel: Personnel,
    ): Promise<boolean> {
        let separationComplete = false;
        try {
            // Ensure the personnel has an associated user
            if (isNil(personnel.user)) {
                throw new Error('Cannot process personnel without user.');
            }

            if (
                !(await isFlagEnabled(
                    account,
                    this.featureFlagService,
                    FeatureFlag.RELEASE_MULTI_IDP_SEPARATE_USER_PERSONNEL,
                ))
            ) {
                this.log(
                    `FF is disabled, skipping separation for personnel with email: ${personnel.user?.email}; restoring previous employment status`,
                    account,
                );
                return separationComplete;
            }

            const logDetails = {
                personnelId: currentPersonnel.id,
                userId: currentPersonnel.fkUserId,
                initialStatus: EmploymentStatus[currentPersonnel.employmentStatus],
                resolvedStatus: EmploymentStatus[personnel.employmentStatus],
            };
            this.log(`Separating personnel with id ${currentPersonnel.id}`, account, {
                ...logDetails,
                roles: currentPersonnel.user?.roles
                    .filter(ur => ur.deletedAt === null)
                    .map(ur => Role[ur.role]),
            });

            // Execute the separation logic (e.g., removing roles, ownerships)
            await this.usersCoreService.doSeparation(account, personnel.user, null, true);
            separationComplete = true;
            this.log(`Personnel separated with id ${currentPersonnel.id}`, account, logDetails);

            const recentConnection =
                await this.connectionsCoreService.getIdentityProviderConnection();

            // Notify the system about the personnel separation
            this._eventBus.publish(
                new AutopilotPersonnelSeperatedEvent(account, personnel, recentConnection),
            );
        } catch (error) {
            this.error(error, account, { personnel: personnel });
        }
        return separationComplete;
    }

    /**
     * Restores the employment status of a personnel to its previous state.
     *
     * This method is typically called when an attempt to separate a user's personnel record fails
     * or the idp sync v2 separation feature flag is OFF, and we need to revert the personnel changes.
     * It performs the following actions:
     * 1. Restores the employment status to the previous value.
     * 2. Restores the startedAt date to the previous value.
     * 3. Sets the separatedAt date to null.
     * 4. Logs the restoration action with details.
     * 5. Saves the updated personnel record.
     *
     * @param account - The account associated with the personnel
     * @param personnel - The current personnel record that needs to be restored
     * @param previousPersonnel - The previous state of the personnel record to restore from
     * @returns A Promise that resolves when the restoration is complete
     */
    private async restorePersonnelEmploymentStatusV2(
        account: Account,
        personnel: Personnel,
        previousPersonnel: Personnel,
    ): Promise<void> {
        personnel.employmentStatus = previousPersonnel.employmentStatus;
        personnel.startedAt = previousPersonnel.startedAt;
        personnel.separatedAt = null;
        this.log(
            `Restoring previous employment status (${
                EmploymentStatus[previousPersonnel.employmentStatus]
            }), startedAt (${
                previousPersonnel.startedAt?.toISO8601String
            }) and separatedAt (NULL) to personnel with email ${personnel.user?.email}`,
            account,
        );
        await this.personnelCoreService.savePersonnel(personnel);
    }

    private get userRoleRepository(): UserRoleRepository {
        return this.getCustomTenantRepository(UserRoleRepository);
    }

    /**
     * Cleans up compliance check exclusions for a given account.
     * This method archives expired exclusions, removes obsolete group exclusions,
     * and resets stale exclusion statuses.
     *
     * @param account - The account for which to cleanup compliance check exclusions
     * @returns A Promise that resolves when the cleanup is complete
     */
    private async cleanupComplianceCheckExclusions(account: Account): Promise<void> {
        try {
            this.log('Starting compliance check exclusions cleanup', account);

            await this.complianceChecksOrchestrationService.archiveExpiredOrObsoleteExclusions(
                account,
            );
            await this.complianceChecksOrchestrationService.cleanupStaleExclusionStatuses(account);

            this.log('Completed compliance check exclusions cleanup', account);
        } catch (error) {
            this.error(error, account, 'Failed to cleanup compliance check exclusions');
            // Don't throw - allow sync to continue
        }
    }

    /**
     * Checks if the given connection is the highest ranked connection
     * Used to determine if a connection is allowed to make data updates
     *
     * @param connectionId - The ID of the connection to check
     * @param highestRankedConnectionId - The ID of the highest ranked connection
     * @returns True if the connection is the highest ranked one or if no highest ranked connection is set
     */
    private async isHighestRankedConnection(
        account: Account,
        connectionId: number | null,
        highestRankedConnectionId: number | null,
    ): Promise<boolean> {
        if (isNil(highestRankedConnectionId)) {
            return true;
        }
        const isMultiIdpHighestRankedConnectionEnabled =
            await isMultiIdpEntitlementAndFeatureFlagsEnabled(account, this.featureFlagService);
        // If feature flag is not enabled return true so all connections can update
        if (!isMultiIdpHighestRankedConnectionEnabled) {
            return true;
        }

        // If no current connection, don't allow updates
        if (isNil(connectionId)) {
            return false;
        }

        // Check if the connection IDs match
        return connectionId === highestRankedConnectionId;
    }
}
