/* eslint-disable no-await-in-loop */
import {
    CheckResultStatus,
    CustomFieldsSection,
    EventCategory,
    EventType,
    SortDir,
    SortType,
    TestSource,
} from '@drata/enums';
import {
    BadRequestException,
    ForbiddenException,
    Injectable,
    InternalServerErrorException,
    NotFoundException,
    UnprocessableEntityException,
} from '@nestjs/common';
import { isEmpty } from '@nestjs/common/utils/shared.utils';
import { AiCoreService } from 'app/ai/services/ai-core.service';
import { TestFailureSummaryMetadata } from 'app/ai/types/test-failure-summary.type';
import { EventAnnotatedEvent } from 'app/analytics/observables/events/event-annotated.event';
import { ApprovalReview } from 'app/approvals/v1/entities/approval-review.entity';
import { Approval } from 'app/approvals/v1/entities/approval.entity';
import { AutopilotRecipeInstance } from 'app/autopilot/entities/autopilot-recipe-instance.entity';
import { AutopilotRecipeInstanceRepository } from 'app/autopilot/repositories/autopilot-recipe-instance.repository';
import { ConnectionsRepository } from 'app/companies/connections/repositories/connections.repository';
import { Product } from 'app/companies/products/entities/product.entity';
import { ProductRepository } from 'app/companies/products/repositories/product.repository';
import { CustomField } from 'app/custom-fields/entities/custom-field.entity';
import { CustomFormula } from 'app/custom-fields/entities/custom-formula.entity';
import { CustomFieldCreatedEvent } from 'app/custom-fields/observables/events/custom-field-created.event';
import { CustomFieldDeletedEvent } from 'app/custom-fields/observables/events/custom-field-deleted.event';
import { CustomFieldPlacedEvent } from 'app/custom-fields/observables/events/custom-field-placed.event';
import { CustomFieldSectionUpdatedEvent } from 'app/custom-fields/observables/events/custom-field-section-updated.event';
import { CustomFieldUnplacedEvent } from 'app/custom-fields/observables/events/custom-field-unplaced.event';
import { CustomFieldUpdatedEvent } from 'app/custom-fields/observables/events/custom-field-updated.event';
import { CustomFormulaCreatedEvent } from 'app/custom-fields/observables/events/custom-formula-created.event';
import { CustomFormulaDeletedEvent } from 'app/custom-fields/observables/events/custom-formula-deleted.event';
import { CustomFormulaUpdatedEvent } from 'app/custom-fields/observables/events/custom-formula-updated.event';
import { LibraryDocument } from 'app/document-library/entities/library-document.entity';
import { EvidenceLibraryVersionTestResultRepository } from 'app/document-library/evidence-library/repositories/evidence-library-version-test-result.repository';
import { EventDtoAdapter } from 'app/events/adapters/event-dto.adapter';
import { CurrentTestEventsMetadataRequestDto } from 'app/events/dtos/current-test-events-metadata-request.dto';
import { EventRequestDto } from 'app/events/dtos/event-request.dto';
import { EventsRequestDto } from 'app/events/dtos/events-request.dto';
import { NoteFileRequestDto } from 'app/events/dtos/note-file-request.dto';
import { NoteFilesRequestDto } from 'app/events/dtos/note-files-request.dto';
import { EventWorkspaceMap } from 'app/events/entities/event-workspace-map.entity';
import { Event } from 'app/events/entities/event.entity';
import { NoteFile } from 'app/events/entities/note-file.entity';
import { PdfTransmission } from 'app/events/entities/pdf-transmission.enum';
import { EventExpand } from 'app/events/enums/event-expand.enum';
import { WORKSPACE_EVENTS_CATEGORIES as workspaceCategories } from 'app/events/events.constants';
import { shouldFetchMostRecent } from 'app/events/helpers/events.helper';
import { ControlApprovalApprovedEvent } from 'app/events/observables/events/control-approval-approved.event';
import { ControlApprovalEditEvent } from 'app/events/observables/events/control-approval-edit.event';
import { ControlApprovalRemovedEvent } from 'app/events/observables/events/control-approval-removed.event';
import { ControlApprovalRequestChangesEvent } from 'app/events/observables/events/control-approval-request-changes.event';
import { ControlApprovalReviewAddedEvent } from 'app/events/observables/events/control-approval-review-added.event';
import { ControlApprovalReviewRemovedEvent } from 'app/events/observables/events/control-approval-review-removed.event';
import { ControlApprovalSentToApproversEvent } from 'app/events/observables/events/control-approval-sent-to-approvers.event';
import { ControlApprovalSetupEvent } from 'app/events/observables/events/control-approval-setup.event';
import { ControlNoteDeletedEvent } from 'app/events/observables/events/control-note-deleted.event';
import { ControlNoteEditedEvent } from 'app/events/observables/events/control-note-edited.event';
import { ControlTestInstanceNoteDeletedEvent } from 'app/events/observables/events/control-test-instance-note-deleted.event';
import { ControlTestInstanceNoteEditedEvent } from 'app/events/observables/events/control-test-instance-note-edited.event';
import { EventWorkspaceRepository } from 'app/events/repositories/event-workspace.repository';
import { EventRepository } from 'app/events/repositories/event.repository';
import { ControlTestInstanceEventSummaryCsvData } from 'app/events/types/control-test-instance-event-summary-csv-data.type';
import { CurrentTestEventsMetadataType } from 'app/events/types/current-event-metadata.type';
import { DownloaderEventPayloadType } from 'app/events/types/downloader-event-payload.type';
import { EventPDFNoteFilesType } from 'app/events/types/event-pdf-note-files.type';
import { EventPdfType } from 'app/events/types/event-pdf.type';
import { EventRequestType } from 'app/events/types/event-request.type';
import { EventTemplateMetadataType } from 'app/events/types/event-template-metadata.type';
import { EventWithPdfTransmission } from 'app/events/types/event-with-pdf-transmission.type';
import { EventsRequestType } from 'app/events/types/events-request.type';
import { Control } from 'app/grc/entities/control.entity';
import { UpcomingTaskDetails } from 'app/grc/types/upcoming-task-details.type';
import { MonitorInstanceExclusion } from 'app/monitors/entities/monitor-instance-exclusion.entity';
import { MonitorInstanceExclusionRepository } from 'app/monitors/repositories/monitor-instance-exclusion.repository';
import { MonitorInstanceRepository } from 'app/monitors/repositories/monitor-instance.repository';
import { NormalizeCount } from 'app/normalization/decorators/normalize-count.decorator';
import { Count } from 'app/normalization/entities/count.entity';
import { CountService } from 'app/normalization/services/count.service';
import { CreateEventNoteRequestDto } from 'app/notes/dtos/create-event-note-request.dto';
import { NoteRequestDto } from 'app/notes/dtos/note-request.dto';
import { NotesRequestDto } from 'app/notes/dtos/notes-request.dto';
import { Note } from 'app/notes/entities/note.entity';
import { NotesFileRepository } from 'app/notes/repositories/notes-file.repository';
import { NotesRepository } from 'app/notes/repositories/notes.repository';
import { NotesService } from 'app/notes/services/notes.service';
import { User } from 'app/users/entities/user.entity';
import { AuditorRepository } from 'auditors/repositories/auditor.repository';
import { Account } from 'auth/entities/account.entity';
import { EntryCoreService } from 'auth/services/entry-core.service';
import { PolloAdapter } from 'commons/adapters/pollo.adapter';
import { ClientType } from 'commons/enums/auth/client-type.enum';
import { ProviderType } from 'commons/enums/auth/provider-type.enum';
import { CustomFieldsEntityType } from 'commons/enums/custom-fields-entity-type.enum';
import { EventSource } from 'commons/enums/events/event-source.enum';
import { ProcessFeature } from 'commons/enums/process-feature.enum';
import { ProcessType } from 'commons/enums/process-type.enum';
import { UploadType } from 'commons/enums/upload-type.enum';
import { Role } from 'commons/enums/users/role.enum';
import { createZipBuffer, getStreamsFromFiles } from 'commons/helpers/buffer.helper';
import { fileNameDate } from 'commons/helpers/date.helper';
import {
    clientTypeEventServiceLabel,
    extractMetadataFromFileVersionOne,
    formatCustomTestMonitorDescriptions,
    formatEventFileName,
    generateFileDataVersionOne,
    generateSubfolder,
    getAutopilotTaskResponseStatus,
    getCheckResultStatus,
    getEventControlIdFromMetadata,
    getEventStatus,
    getRequestDescription,
} from 'commons/helpers/event.helper';
import { extractResourceArnFromFailItem } from 'commons/helpers/monitor.helper';
import { promiseAllSettledInBatches } from 'commons/helpers/promise.helper';
import { sleep } from 'commons/helpers/sleep.helper';
import {
    checkIsDocumentOrImage,
    getFileExtension,
    sanitizeFileName,
} from 'commons/helpers/upload.helper';
import { fullName, hasRole } from 'commons/helpers/user.helper';
import { AppService } from 'commons/services/app.service';
import { CursorPage } from 'commons/types/cursor-page.type';
import { PaginationType } from 'commons/types/pagination.type';
import { UploadedFileType } from 'commons/types/uploaded-file.type';
import config from 'config';
import { Downloader } from 'dependencies/downloader/downloader';
import { DownloadOptions } from 'dependencies/downloader/types/download-options.type';
import { DownloadStreamDetails } from 'dependencies/downloader/types/download-stream-details.type';
import { DownloaderPayloadType } from 'dependencies/downloader/types/downloader-payload.interface';
import { HtmlToPdfConverter } from 'dependencies/html-to-pdf-converter/html-to-pdf-converter';
import { Uploader } from 'dependencies/uploader/uploader';
import { FeatureFlagCategory } from 'feature-flags/enums/feature-flag-category.enum';
import { FeatureFlag } from 'feature-flags/feature-flag.enum';
import { FeatureFlagService } from 'feature-flags/feature-flag.service';
import fs from 'fs';
import hbs from 'hbs';
import { findIndex, first, get, isNil } from 'lodash';
import moment from 'moment';
import path from 'path';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { format } from 'util';

@Injectable()
export class EventsService extends AppService {
    private static TIME_FORMAT = 'MMM DD, YYYY @ h:mm:ss A UTC';
    private readonly batchEvents = 20;

    private readonly notesTemplate: HandlebarsTemplateDelegate<any>;
    private readonly exclusionsTemplate: HandlebarsTemplateDelegate<any>;

    constructor(
        private readonly uploader: Uploader,
        private readonly downloader: Downloader,
        private readonly htmlToPdfConverter: HtmlToPdfConverter,
        private readonly countService: CountService,
        private readonly auditorRepository: AuditorRepository,
        private readonly entryCoreService: EntryCoreService,
        private readonly notesService: NotesService,
        private readonly featureFlagService: FeatureFlagService,
        private readonly aiCoreService: AiCoreService,
    ) {
        super();

        this.notesTemplate = hbs.handlebars.compile(
            fs.readFileSync(
                path.join(__dirname, '..', '..', '..', 'views', 'partials', 'notes.hbs'),
                'utf8',
            ),
        );

        this.exclusionsTemplate = hbs.handlebars.compile(
            fs.readFileSync(
                path.join(__dirname, '..', '..', '..', 'views', 'partials', 'exclusions.hbs'),
                'utf8',
            ),
        );

        hbs.handlebars.registerPartial('notes-section', this.notesTemplate);
        hbs.handlebars.registerPartial('exclusions-section', this.exclusionsTemplate);

        // helper to check if date is valid
        hbs.handlebars.registerHelper('isValidDate', function (dateToCheck, options) {
            if (moment(dateToCheck).isValid()) {
                return options.fn(this);
            }
        });
        hbs.handlebars.registerHelper('formatDate', unformattedDate => {
            return moment.utc(unformattedDate).format('M/DD/YYYY - h:mm A');
        });
    }

    async getEventWithPdfTransmission(
        account: Account,
        id: string,
        relations: string[] = [],
        user: User,
        eventsMetadata?: CurrentTestEventsMetadataRequestDto,
    ): Promise<EventWithPdfTransmission> {
        const event = await this.getEvent(account, id, relations, user);
        const notes = await this.noteRepository.getAllNotesWithFiles(event.id);

        const currentTestEventsMetadata = await this.getCurrentTestEventsMetadata(
            account,
            event,
            eventsMetadata,
        );

        let recipe: AutopilotRecipeInstance;
        let evidence: LibraryDocument | null = null;
        if (!isNil(event.controlTestInstanceHistory?.controlTestInstance)) {
            /**
             * TODO: we need to be sure to get the recipe that was used at the moment of the assessment
             * instead of the current recipe, we need to link evidence to monitor history
             * */
            const controlTestInstanceId = get(
                event,
                'controlTestInstanceHistory.controlTestInstance.id',
            );
            recipe =
                await this.autopilotRecipeInstanceRepository.getRecipeByControlInstanceOrFail(
                    controlTestInstanceId,
                );

            // TODO: Remove feature flag validation with this ticket:https://drata.atlassian.net/browse/ENG-58940
            const testEvidenceFeatureFlag = await this.featureFlagService.evaluate(
                {
                    name: FeatureFlag.RELEASE_MERGE_TEST_EVIDENCE_INTO_EL,
                    category: FeatureFlagCategory.NONE,
                    defaultValue: false,
                },
                user,
                account,
            );

            const workspaceId = account?.getCurrentProduct()?.id;
            if (testEvidenceFeatureFlag && !isNil(workspaceId)) {
                const testResult =
                    await this.evidenceLibraryVersionTestResultRepository.getTestResultByControlTestInstance(
                        controlTestInstanceId,
                        workspaceId,
                    );

                evidence = get(testResult, 'libraryDocumentVersion.libraryDocument', null);
            }
        }

        const pdfTransmission = await this.getEventPdfTransmission(
            account,
            event,
            user,
            notes,
            recipe,
        );

        return {
            event,
            pdfTransmission,
            evidence,
            currentTestEventsMetadata,
        };
    }

    private async getCurrentTestEventsMetadata(
        account: Account,
        event: Event,
        metadata?: CurrentTestEventsMetadataRequestDto,
    ): Promise<CurrentTestEventsMetadataType | null> {
        try {
            const includeMetadata =
                !isNil(get(metadata, 'includeMetadata', null)) &&
                !isNil(get(event, 'controlTestInstanceHistory.controlTestInstance.testId', null))
                    ? metadata?.includeMetadata
                    : false;

            if (!includeMetadata) {
                return null;
            }

            const currentEvents = await this.listEvents(
                account,
                Object.assign(new EventsRequestDto(), {
                    page: 1,
                    limit: 100,
                    sort: SortType.CREATED, // this is weird but internally the repository uses this instead of CREATED_AT
                    sortDir: SortDir.DESC,
                    source: EventSource.AUTOPILOT,
                    mostRecent: true,
                    testId: event.controlTestInstanceHistory.controlTestInstance.testId,
                    workspaceId: account.getCurrentProduct().id,
                }),
            );

            if (isNil(currentEvents) || isEmpty(currentEvents.data)) {
                return null;
            }

            const currentEventCount = currentEvents.data.length;
            const currentPosition = findIndex(currentEvents.data, e => e.id === event.id);

            if (currentPosition === -1) {
                return null;
            }

            const nextEventId = get(currentEvents.data[currentPosition + 1], 'id', null);
            const previousEventId = get(currentEvents.data[currentPosition - 1], 'id', null);
            return {
                currentEventCount,
                currentPosition: currentPosition + 1,
                nextEventId,
                previousEventId,
            };
        } catch (e) {
            return null;
        }
    }

    /**
     *
     * @param id
     * @param relations
     * @returns
     */
    async getEvent(
        account: Account,
        id: string,
        relations: string[] = [],
        user?: User,
    ): Promise<Event> {
        const event: Event = await this.eventRepository.findOneOrFail({
            where: { id },
            relations,
        });

        if (!isNil(event.fileKey) && (await this.isReadingFromS3(account))) {
            let file: object | null = null;
            /*
             * We try/catch to fail gracefully.
             * If S3 download fails, then we still want to return the event to user
             */
            try {
                file = await this.downloadEventFile(account, event);
            } catch (error) {
                this.logger.error(
                    PolloAdapter.acct(
                        `Download event file failed for fileKey: ${event.fileKey}`,
                        account,
                        this.constructor.name,
                    )
                        .setIdentifier({ eventId: event.id })
                        .setError(error),
                    account.domain,
                );
            }
            if (!isNil(file)) {
                event.metadata = JSON.stringify(extractMetadataFromFileVersionOne(file));
                await this.featureFlaggedLog(
                    PolloAdapter.acct(
                        `File read from S3. fileKey: ${event.fileKey}`,
                        account,
                        this.constructor.name,
                    ).setIdentifier({ fileKey: event.fileKey, eventId: event.id }),
                    account,
                );
            } else {
                this.logger.error(
                    PolloAdapter.acct(
                        `Event with id ${event.id} has file_key and we are reading from S3, but file is null. Metadata should be returned from SQL`,
                        account,
                        this.constructor.name,
                    ).setIdentifier({ eventId: event.id, fileKey: event.fileKey }),
                    account.domain,
                );
            }
        }

        let isAuditorReadOnly = false;
        if (!isNil(user)) {
            const entry = await this.entryCoreService.getEntryWithoutRelationsByEmailOrFail(
                user.email,
            );
            const auditor = await this.auditorRepository.findOne({
                where: { entry: { id: entry.id } },
            });
            isAuditorReadOnly = hasRole(user, [Role.ACT_AS_READ_ONLY]) && !isNil(auditor);
        }

        if (isAuditorReadOnly && event.metadata) {
            const metadata = JSON.parse(event.metadata);
            delete metadata['note'];
            event.metadata = JSON.stringify(metadata);
        }

        return formatCustomTestMonitorDescriptions(event, account.companyName);
    }

    private async downloadEventFile(account: Account, event: Event): Promise<object> {
        return this.downloader.getPrivateFileWithBucket<object>(
            config.get('aws.s3.eventBucket'),
            event.fileKey,
        );
    }

    /**
     *
     * @param dto
     * @param account
     * @returns
     */
    async listEvents(account: Account, dto: EventsRequestDto): Promise<PaginationType<Event>> {
        const { workspaceId, eventErrorStatus } = dto;
        const product = await this.productRepository.findOneBy({
            id: workspaceId,
        });

        // sanity check for public api
        if (!isNil(workspaceId) && isNil(product)) {
            throw new NotFoundException(
                `No workspace found associated with workspace ID: ${workspaceId}`,
            );
        }

        const events = await this.eventRepository.listEvents(dto, account.companyName, product);

        if (events.total < 0) {
            /**
             * If the events total is less that zero - we did not query for the complete
             * count to avoid a full tablescan - use the cached value for count here ...
             */
            events.total = await this.getEventsCount(account);
        }

        // get only the events with an error status (applies only when filtering most recent events)
        if (shouldFetchMostRecent(dto) && eventErrorStatus) {
            events.data = events.data.filter(
                event => getEventStatus(event) === CheckResultStatus[CheckResultStatus.ERROR],
            );
        }

        // ResponseDtos rely on metadata even for list
        // Example: Monitoring 'haveFailedResources' and frontend AI Summary
        if (await this.isReadingFromS3(account)) {
            for (const event of events.data) {
                if (!isNil(event.fileKey)) {
                    await this.setEventMetadataFromRemote(account, event);
                }
            }
        }

        return events;
    }

    /**
     * API V2 compatible event listing.
     */
    async listEventsWithCursor(
        account: Account,
        eventsRequestType: EventsRequestType,
    ): Promise<CursorPage<Event>> {
        const events = await this.eventRepository.listEventsWithCursor(eventsRequestType);
        if (
            eventsRequestType?.expand?.includes(EventExpand.metadata) &&
            (await this.isReadingFromS3(account))
        ) {
            for (let event of events.data) {
                if (!isNil(event.fileKey)) {
                    event = await this.setEventMetadataFromRemote(account, event);
                }
            }
        }
        return events;
    }

    async getEventV2(
        account: Account,
        eventId: string,
        eventRequestType: EventRequestType,
    ): Promise<Event> {
        let event: Event = await this.eventRepository.getEventOrFail(
            account,
            eventId,
            eventRequestType,
        );
        if (
            eventRequestType?.expand?.includes(EventExpand.metadata) &&
            !isNil(event.fileKey) &&
            (await this.isReadingFromS3(account))
        ) {
            event = await this.setEventMetadataFromRemote(account, event);
        }

        return event;
    }

    /**
     * @deprecated Deprecating this as there is only instance of it being used and it can be swapped to the repository call
     *
     * @param {EventType} type
     * @param {string[]} dates
     * @param {number} limit
     * @param {number} page
     * @returns {Promise<PaginationType<Event>>}
     */
    async getEventsWithUsersConnectionsAndControlTests(
        type: EventType[],
        dates: string[],
        limit: number,
        page: number,
        controlTestInstanceIds: number[],
        account: Account,
    ): Promise<PaginationType<Event>> {
        const paginatedEvents =
            await this.eventRepository.getEventsWithUsersConnectionsAndControlTests(
                type,
                dates,
                limit,
                page,
                controlTestInstanceIds,
            );
        const result = await promiseAllSettledInBatches(
            paginatedEvents.data,
            this.batchEvents,
            async event => this.attachMonitorInstanceAndExclusionToEvent(event),
        );
        for (const errorReason of result.rejected.reasons) {
            this.logger.error(
                PolloAdapter.acct(`getEventsByTypesAndDates failed`, account)
                    .setError(errorReason)
                    .setSubContext(this.getEventsWithUsersConnectionsAndControlTests.name)
                    .setContext(this.constructor.name),
            );
        }

        if (result.rejected.count) {
            throw new InternalServerErrorException(
                'Failed to get events. getEventsByTypesAndDates',
            );
        }

        return paginatedEvents;
    }

    async attachMonitorInstanceAndExclusionToEvent(event: Event): Promise<Event> {
        const controlTestInstanceId = event?.controlTestInstanceHistory?.controlTestInstance?.id;
        if (!isNil(controlTestInstanceId)) {
            const controlTestInstanceReferrence =
                event.controlTestInstanceHistory.controlTestInstance;
            const [monitorInstanceExclusions, monitorInstances] = await Promise.all([
                this.monitorInstanceExclusionRepository.getExclusionsByControlTestInstanceId(
                    controlTestInstanceId,
                ),
                this.monitorInstanceRepository.getMonitorTestInstanceByControlTestInstanceId(
                    controlTestInstanceId,
                ),
            ]);
            controlTestInstanceReferrence['monitorInstanceExclusions'] = monitorInstanceExclusions;
            controlTestInstanceReferrence['monitorInstances'] = monitorInstances;
        }
        return event;
    }

    /**
     *
     * @param account
     * @returns
     */
    async getEventsCount(account: Account): Promise<number> {
        let counts = await this.countService.find(account);

        if (isNil(counts)) {
            counts = new Count();
            counts.events = await this.eventRepository.getEventsCount();
            counts = await this.countService.save(account, counts);
        }

        return counts.events;
    }

    async createNote(
        account: Account,
        user: User,
        createEventNoteRequestDto: CreateEventNoteRequestDto,
        eventId: string,
        noteFiles?: UploadedFileType[],
    ): Promise<Note> {
        const event: Event | null = await this.eventRepository.findOneBy({ id: eventId });
        if (event === null) {
            throw new NotFoundException();
        }

        const note = await this.notesService.createEventNote(
            account,
            user,
            createEventNoteRequestDto,
            event,
            noteFiles,
            createEventNoteRequestDto.fileMetadata,
        );

        this._eventBus.publish(new EventAnnotatedEvent(account, user, event));

        return note;
    }

    async deleteNote(account: Account, user: User, id: string): Promise<void> {
        const deletedNote = await this.notesService.deleteNote(user, account, id);

        if (!isNil(deletedNote.control)) {
            this._eventBus.publish(
                new ControlNoteDeletedEvent(account, user, deletedNote.control, deletedNote),
            );
        }
        if (!isNil(deletedNote.controlTestInstance)) {
            this._eventBus.publish(
                new ControlTestInstanceNoteDeletedEvent(account, user, deletedNote),
            );
        }
    }

    async updateNote(
        account: Account,
        user: User,
        requestDto: NoteRequestDto,
        id: string,
    ): Promise<Note> {
        const note = await this.notesService.updateNote(user, id, requestDto.comment);
        if (!isNil(note.control)) {
            this._eventBus.publish(new ControlNoteEditedEvent(account, user, note.control, note));
        }
        if (!isNil(note.controlTestInstance)) {
            this._eventBus.publish(new ControlTestInstanceNoteEditedEvent(account, user, note));
        }
        return note;
    }

    async getNotes(
        dto: NotesRequestDto,
        account: Account,
        eventId: string,
    ): Promise<PaginationType<Note>> {
        return this.noteRepository.getEventNotes(dto, eventId);
    }

    async getNoteFiles(id: string, dto: NoteFilesRequestDto): Promise<PaginationType<NoteFile>> {
        return this.noteFileRepository.getNoteFiles(id, dto);
    }
    /**
     *
     * @param {string} eventId
     * @param {Account} account
     * @param {User} user
     */
    async getEventPdfAndNoteFiles(
        eventId: string,
        account: Account,
        user: User | null,
        useAsyncPdfGeneration = false,
    ): Promise<EventPDFNoteFilesType> {
        const event = await this.getEvent(account, eventId, [
            'controlTestInstanceHistory',
            'controlTestInstanceHistory.controlTestInstance',
            'controlTestInstanceHistory.controlTestInstance.monitorInstanceExclusions',
            'controlTestInstanceHistory.controlTestInstance.monitorInstanceExclusions.connection',
            'controlTestInstanceHistory.controlTestInstance.monitorInstanceExclusions.exclusionDesignator',
        ]);
        return this.getEventPdfAndNoteFilesFromEvent(event, account, user, useAsyncPdfGeneration);
    }

    /**
     *
     * @param resolve
     * @param reject
     * @param statusId
     * @returns
     */
    async getData(
        resolve: any,
        reject: any,
        account: Account,
        statusId: string,
    ): Promise<{
        status: string;
        download_id: string;
        download_url: string;
        number_of_pages: number;
    }> {
        let response = null;
        const limit = 60; // hard limit on 10 minutes (based on current pdf converter vendor)
        const waitTime = 10000; // every 10 seconds poll
        let count = 0;
        do {
            this.log('Checking status of PDF conversion using status id', account, { statusId });
            response = await this.htmlToPdfConverter.getPdfStatus(statusId);
            this.log('Current PDF conversion status for status id', account, {
                statusId,
                status: response.status,
            });
            // check for key responses to react, otherwise continue to wait..
            switch (response?.status) {
                case 'completed':
                    return resolve(response);
                case 'failed':
                case undefined:
                    return reject(response);
            }
            this.log('PDF conversion still processing, continuing to wait..', account, {
                sleepTime: waitTime,
            });
            await sleep(waitTime);
        } while (count++ <= limit);
    }

    /**
     *
     * @param html
     * @returns
     */
    async getAsyncPdf(account: Account, html: string): Promise<EventPdfType> {
        const { status_id: statusId } = await this.htmlToPdfConverter.asyncConvertToPdfBuffer(html);

        return new Promise<EventPdfType>((r, j) => void this.getData(r, j, account, statusId));
    }

    async getEventPdfAndNoteFilesFromEvent(
        event: Event,
        account: Account,
        user: User | null,
        useAsyncCall = false,
    ): Promise<EventPDFNoteFilesType> {
        let noteFiles = [];
        const notes = await this.noteRepository.getAllNotesWithFiles(event.id);

        let recipe: AutopilotRecipeInstance;

        if (!isNil(event.controlTestInstanceHistory?.controlTestInstance)) {
            const eventRecipeId = event.getMetadata()?.response?.data?.recipeId ?? null; // All evidence for AP2 should include the Recipe Id if it was generated after February 26, 2024

            recipe = await (eventRecipeId
                ? this.autopilotRecipeInstanceRepository.getRecipesWithDeletedById(eventRecipeId)
                : this.autopilotRecipeInstanceRepository.getRecipeByControlInstanceOrFail(
                      event.controlTestInstanceHistory.controlTestInstance.id,
                  ));
        }

        noteFiles = isEmpty(notes) ? [] : await this.getFileNoteBuffer(notes);

        const html = await this.getEventHtml(account, event, user, notes, recipe);

        if (isNil(html)) {
            throw new InternalServerErrorException();
        }

        // generate PDF
        return this.generateEventPdfAndNoteFiles(useAsyncCall, account, html, event, noteFiles);
    }

    private async generateEventPdfAndNoteFiles(
        useAsyncCall: boolean,
        account: Account,
        html: string,
        event: Event,
        noteFiles: DownloadStreamDetails[],
    ): Promise<EventPDFNoteFilesType> {
        try {
            let data: Buffer;
            let mimetype: string;

            // Added this logic because when generating the control evidence package some
            // pdfs might take a long time to be generated, thus the sync call fails with a 504.
            if (useAsyncCall) {
                const { download_url: downloadUrl } = await this.getAsyncPdf(account, html);
                ({ data, mimetype } = await this.htmlToPdfConverter.getPdfDownload(downloadUrl));
            } else {
                ({ data, mimetype } = await this.htmlToPdfConverter.convertToPdfBuffer(
                    html,
                    account,
                ));
            }

            let fileName: string;

            const controlTestInstance = get(
                event,
                'controlTestInstanceHistory.controlTestInstance',
                null,
            );
            if (controlTestInstance && controlTestInstance.isCustom()) {
                fileName = sanitizeFileName(
                    `${formatEventFileName(EventType[event.type])}-${
                        event.controlTestInstanceHistory.controlTestInstance.testId
                    }.pdf`,
                );
            } else {
                fileName = sanitizeFileName(`${formatEventFileName(EventType[event.type])}.pdf`);
            }

            return {
                data,
                mimetype,
                fileName,
                noteFiles,
            };
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(`PDF generation Failed for event with id ${event.id}`, account)
                    .setContext(this.constructor.name)
                    .setSubContext(this.generateEventPdfAndNoteFiles.name)
                    .setError(error)
                    .setIdentifier({
                        event,
                        html,
                    }),
            );
            throw error;
        }
    }

    /**
     *
     * @param {Account} account
     * @param {User} user
     * @param {string} eventId
     */
    async getEventPdfDownloadUrl(
        account: Account,
        user: User,
        eventId: string,
        options?: DownloadOptions,
    ): Promise<DownloaderPayloadType> {
        try {
            const { data, mimetype, fileName, noteFiles } = await this.getEventPdfAndNoteFiles(
                eventId,
                account,
                user,
            );
            // upload the file from the buffer and return the path to it
            const uploadedFile = await this.uploader.uploadPrivateFileFromBuffer(
                account.id,
                UploadType.EVENT,
                data,
                fileName,
                mimetype,
            );
            let downloadUrl = uploadedFile.key;

            if (!isEmpty(noteFiles)) {
                const zip = await createZipBuffer(
                    noteFiles.concat(
                        await getStreamsFromFiles([{ file: uploadedFile.key }], this.downloader),
                    ),
                );

                const zipData = await this.uploader.uploadPrivateFileFromBuffer(
                    account.id,
                    UploadType.EVENT_NOTE_ALL,
                    zip,
                    `${fileNameDate()}-${UploadType.EVENT_NOTE}.zip`,
                    config.get('archive.contentType'),
                );
                downloadUrl = zipData.key;
            }
            return await this.downloader.getDownloadUrl(downloadUrl, options);
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(`getEventPdfDownloadUrl failed: ${error.message}`, account)
                    .setError(error)
                    .setIdentifier(error),
            );
            throw error;
        }
    }

    /**
     *
     * @param {Account} account
     * @param {User} user
     * @param {string} eventId
     */
    async getEventPdfDownloadUrlPreview(
        account: Account,
        user: User,
        eventId: string,
        evidenceId?: number,
        options?: DownloadOptions,
    ): Promise<DownloaderEventPayloadType> {
        try {
            this.logger.log(
                PolloAdapter.acct(
                    `Generating PDF for preview for Evidence ${evidenceId} based on Event ${eventId}`,
                    account,
                ),
            );
            const eventPdf = await this.getEventPdfAndNoteFiles(eventId, account, user);
            const data = get(eventPdf, 'data', null);
            const fileName = get(eventPdf, 'fileName', null);
            const mimetype = get(eventPdf, 'mimetype', null);

            if (isNil(data) || isNil(fileName) || isNil(mimetype)) {
                const error = new Error('Error creating evidence file based on Test Result');
                this.logger.error(
                    PolloAdapter.acct(
                        `${this.getEventPdfDownloadUrlPreview.name} failed: `,
                        account,
                    )
                        .setError(error)
                        .setIdentifier(error),
                );
                throw error;
            }

            const uploadedFile = await this.uploader.uploadPrivateFileFromBuffer(
                account.id,
                UploadType.EVENT,
                data,
                fileName,
                mimetype,
            );
            const downloadUrl = get(uploadedFile, 'key', null);
            if (isNil(uploadedFile) || isNil(downloadUrl)) {
                const error = new Error('Error uploading evidence file');
                this.logger.error(
                    PolloAdapter.acct(
                        `${this.getEventPdfDownloadUrlPreview.name} failed: `,
                        account,
                    )
                        .setError(error)
                        .setIdentifier(error),
                );
                throw error;
            }
            const file = await this.downloader.getDownloadUrl(downloadUrl, options);
            return {
                ...file,
                fileName,
                evidenceId,
            };
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(`getEventPdfDownloadUrlPreview failed: ${error.message}`, account)
                    .setError(error)
                    .setIdentifier(error),
            );
            throw error;
        }
    }

    async uploadNoteFiles(
        account: Account,
        user: User,
        requestDto: NoteFileRequestDto,
        id: string,
        file: UploadedFileType,
    ): Promise<NoteFile> {
        const extension = getFileExtension(file.originalname);
        const fileName =
            (isNil(requestDto.name)
                ? file.originalname.substring(
                      // truncate filename if too long for the entity
                      0,
                      config.get('validation.maxVarcharText') - extension.length,
                  )
                : requestDto.name) + extension;
        return this.uploadHelper(account, user, id, fileName, file, requestDto.creationDate);
    }

    async getNoteFileDownloadUrl(id: string): Promise<DownloaderPayloadType> {
        const noteFile = await this.noteFileRepository.findOneByOrFail({ id });
        return this.downloader.getDownloadUrl(noteFile.file);
    }

    async deleteNoteFile(account: Account, user: User, id: string): Promise<void> {
        const noteFile = await this.noteFileRepository.findOneOrFail({
            where: { id },
            relations: ['note'],
        });
        if (user.id !== noteFile.note.owner.id) {
            throw new ForbiddenException('Only the same user or an admin can delete a note');
        }
        await this.noteFileRepository.softDelete(id);
    }

    /**
     *
     * @param account
     * @param dto
     * @returns
     */
    @NormalizeCount<Event>(Event)
    async createEvent(account: Account, dto: EventRequestDto): Promise<Event> {
        const event = new Event();
        const updatedEvent = EventDtoAdapter.merge(event, dto);
        await updatedEvent.setId();
        updatedEvent.createdAt = new Date();
        updatedEvent.autopilotTaskResponseStatus = getAutopilotTaskResponseStatus(event);
        updatedEvent.checkResultStatus = getCheckResultStatus(
            updatedEvent.autopilotTaskResponseStatus,
        );
        updatedEvent.controlId = getEventControlIdFromMetadata(updatedEvent);
        if (await this.isWritingToS3(account)) {
            const fileKey = await this.uploadEvent(account, updatedEvent);
            if (!isNil(fileKey)) {
                updatedEvent.fileKey = fileKey;
                // If we successfully uploaded to S3 and SQL writing is disabled,
                // we can clear the metadata to save space
                if (!(await this.isWritingToSQL(account))) {
                    updatedEvent.metadata = '{}'; // Keep minimal JSON to maintain schema validity
                }
            } else {
                this.logger.error(
                    PolloAdapter.acct(
                        `Upload event to blob storage failed for event ${event.id}, it fallbacked to SQL storage`,
                        account,
                        this.constructor.name,
                    ).setIdentifier({ eventId: event.id }),
                    account.domain,
                );
            }
        }
        const createdEvent = await this.eventRepository._saveOne(updatedEvent, {
            reload: false,
        });
        await this.createWorkspaceEventMap(account.getCurrentProduct(), createdEvent, dto);
        this.logEventIfSupportUser([createdEvent], dto);
        return createdEvent;
    }

    private async uploadEvent(account: Account, event: Event): Promise<string | null> {
        /*
         * We try/catch to fail gracefully.
         * If S3 upload fails, then we still want to write the event to SQL
         */
        try {
            const subfolder = generateSubfolder(event);
            const fileData = generateFileDataVersionOne(event);
            const file = await this.uploader.uploadPrivateFileFromBuffer(
                account.id,
                UploadType.EVENT,
                Buffer.from(fileData),
                event.id,
                'application/json',
                subfolder,
                false,
                config.get('aws.s3.eventBucket'),
            );
            await this.featureFlaggedLog(
                PolloAdapter.acct(
                    `Wrote event to S3 for event: ${event.id}`,
                    account,
                    this.constructor.name,
                ).setIdentifier({ eventId: event.id }),
                account,
            );
            return file.key;
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(
                    `Upload event to blob storage failed for event ${event.id}`,
                    account,
                    this.constructor.name,
                )
                    .setIdentifier({ eventId: event.id })
                    .setError(error),
                account.domain,
            );
            return null;
        }
    }

    private isWritingToS3(account: Account): Promise<boolean> {
        const flag = {
            category: FeatureFlagCategory.NONE,
            name: FeatureFlag.MIGRATION_EVENTS_WRITE_TO_S3,
            defaultValue: true,
        };
        return this.featureFlagService.evaluateAs(flag, account);
    }

    public isReadingFromS3(account: Account): Promise<boolean> {
        const flag = {
            category: FeatureFlagCategory.NONE,
            name: FeatureFlag.MIGRATION_EVENTS_READ_FROM_S3,
            defaultValue: true,
        };
        return this.featureFlagService.evaluateAs(flag, account);
    }

    private isWritingToSQL(account: Account): Promise<boolean> {
        const flag = {
            category: FeatureFlagCategory.NONE,
            name: FeatureFlag.MIGRATION_EVENTS_WRITE_TO_SQL,
            defaultValue: true,
        };
        return this.featureFlagService.evaluateAs(flag, account);
    }

    /**
     * Custom log for use during the feature rollout to
     * confirm reading and writing in S3 is functioning
     * properly. If we like this pattern, will add an
     * enhanced version to logger
     */
    private async featureFlaggedLog(message: PolloMessage, account: Account) {
        const flag = {
            category: FeatureFlagCategory.NONE,
            name: FeatureFlag.MIGRATION_EVENTS_LOGGING_S3,
            defaultValue: false,
        };
        if (await this.featureFlagService.evaluateAs(flag, account)) {
            this.logger.log(message);
        }
    }

    /**
     *
     * @param account
     * @param dtos
     * @returns
     */
    async createEvents(account: Account, dtos: EventRequestDto[]): Promise<Event[]> {
        try {
            const events: Event[] = [];
            const isWritingToSql = await this.isWritingToSQL(account);
            for (const dto of dtos) {
                const event = new Event();
                await event.setId();
                event.createdAt = new Date();
                event.autopilotTaskResponseStatus = getAutopilotTaskResponseStatus(event);
                event.checkResultStatus = getCheckResultStatus(event.autopilotTaskResponseStatus);
                const mergedEvent = EventDtoAdapter.merge(event, dto);
                mergedEvent.controlId = getEventControlIdFromMetadata(mergedEvent);
                events.push(mergedEvent);
            }

            if (await this.isWritingToS3(account)) {
                const uploads = events.map(event => {
                    return this.uploadEvent(account, event);
                });
                const fileKeys = await Promise.allSettled(uploads);
                events.forEach((event, index) => {
                    const fileKey = fileKeys[index];
                    if (!isNil(fileKey) && fileKey.status === 'fulfilled' && fileKey.value) {
                        event.fileKey = fileKey.value;
                        // If we successfully uploaded to S3 and SQL writing is disabled,
                        // we can clear the metadata to save space
                        if (!isWritingToSql) {
                            event.metadata = '{}'; // Keep minimal JSON to maintain schema validity
                        }
                    }
                });
            }

            const savedEvents = await this.eventRepository._saveMany(events, {
                reload: false,
            });
            this.logEventIfSupportUser(savedEvents, dtos[0]);

            await this.countService.incrementEvents(account, events.length);
            const product = account.getCurrentProduct();
            let eventsWorkspaces = [];

            if (!isNil(product) && !isEmpty(savedEvents)) {
                eventsWorkspaces = savedEvents.map(event => {
                    if (workspaceCategories.includes(event.category)) {
                        const eventWorkspaceMap = new EventWorkspaceMap();
                        eventWorkspaceMap.event = event;
                        eventWorkspaceMap.product = product;

                        return eventWorkspaceMap;
                    }
                });
            }

            if (!isEmpty(eventsWorkspaces)) {
                await this.eventWorkspaceRepository.insert(eventsWorkspaces);
            }

            return savedEvents;
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(`Create events failed`, account)
                    .setContext(this.constructor.name)
                    .setSubContext(this.createEvents.name)
                    .setError(error),
            );
            return [];
        }
    }

    async generateEventSummaryCsv(eventId: string) {
        const [aiExecutionGroup] = await this.aiCoreService.getByFeatureIds([eventId], {
            processFeature: ProcessFeature.EVENT_TEST_FAILURE,
            processType: ProcessType.SUMMARY,
        });

        if (isNil(aiExecutionGroup)) {
            throw new UnprocessableEntityException('The event does not have an AI execution');
        }

        const csvData: ControlTestInstanceEventSummaryCsvData[] = [];
        const {
            connection,
            controlTestInstanceHistory: { controlTestInstance },
        } = await this.eventRepository.findOneOrFail({
            where: { id: eventId },
            relations: {
                controlTestInstanceHistory: {
                    controlTestInstance: true,
                },
            },
        });

        for (const execution of aiExecutionGroup.executions) {
            const summary: TestFailureSummaryMetadata = JSON.parse(execution.response.data);
            const { resource, resourceIds, cause } = summary;

            for (const resourceId of resourceIds) {
                const monitorInstance = controlTestInstance.monitorInstances[0];

                const resourceArn = extractResourceArnFromFailItem(resourceId, monitorInstance);

                csvData.push({
                    provider: isNil(connection) ? 'Autopilot' : ClientType[connection.clientType],
                    accountId: connection?.clientAlias ?? connection?.clientId ?? '-',
                    resourceId,
                    resourceName: resource,
                    summary: cause.explanation,
                    resourceArn,
                });
            }
        }

        return {
            data: csvData,
            filename: format(
                config.get('reports.controlTestInstanceEventSummaryFileName'),
                controlTestInstance.name,
            ),
        };
    }

    /**
     *
     * @param product
     * @param createdEvent
     * @param dto
     */
    private async createWorkspaceEventMap(
        product: Product,
        event: Event,
        dto: EventRequestDto,
    ): Promise<void> {
        try {
            if (!isNil(dto.metadata?.product)) {
                product = dto.metadata.product;
            }

            if (!isNil(product) && workspaceCategories.includes(dto.category)) {
                const eventWorkspaceMap = new EventWorkspaceMap();
                eventWorkspaceMap.event = event;
                eventWorkspaceMap.product = product;

                await this.eventWorkspaceRepository.insert(eventWorkspaceMap);
            }
        } catch (error) {
            this.logger.error(
                PolloMessage.msg(`Create createWorkspaceEventMap failed`).setError(error),
            );
        }
    }

    private async uploadHelper(
        account: Account,
        user: User,
        noteId: string,
        fileName: string,
        file: UploadedFileType,
        creationDate: Date,
    ): Promise<NoteFile> {
        const note = await this.noteRepository.findOneByOrFail({ id: noteId });
        if (note.owner.id !== user.id) {
            throw new ForbiddenException('Only the note owner can add a note file');
        }
        if (note.noteFiles.length + 1 > config.get('noteFile.maxNoteFilesPerNote')) {
            throw new BadRequestException(
                `Only ${config.get('noteFile.maxNoteFilesPerNote')} noteFiles per note allowed`,
            );
        }
        const noteFile = new NoteFile();
        noteFile.name = fileName;
        noteFile.note = note;
        noteFile.createdAt = creationDate;
        try {
            await checkIsDocumentOrImage(file);
            const uploadedFile = await this.uploader.uploadPrivateFile(
                file,
                UploadType.EVENT_NOTE,
                account.id,
            );
            noteFile.file = uploadedFile.key;
        } catch (error) {
            this.logger.logConditionalWarningOrError(
                PolloAdapter.acct(error, account).setError(error),
            );
            throw error;
        }
        return this.noteFileRepository.save(noteFile);
    }

    private getFileNoteBuffer(notes: Note[]): Promise<DownloadStreamDetails[]> {
        const tmpArr = [];
        for (const note of notes) {
            for (const noteFile of note.noteFiles) {
                tmpArr.push(noteFile);
            }
        }
        if (!isEmpty(tmpArr)) {
            return getStreamsFromFiles(tmpArr, this.downloader);
        }
    }

    private getEventTemplateObj(event: Event): EventTemplateMetadataType {
        let sourcePath = '';
        let exclusions: MonitorInstanceExclusion[] = [];
        if (!isNil(event.controlTestInstanceHistory)) {
            exclusions =
                event.controlTestInstanceHistory?.controlTestInstance?.monitorInstanceExclusions?.filter(
                    mie =>
                        (isNil(mie.deletedAt) || mie.deletedAt > event.createdAt) &&
                        mie.createdAt < event.createdAt &&
                        mie?.connection?.id === event?.connection?.id,
                );
            sourcePath = '../../../views/autopilot-event.hbs';
        } else {
            sourcePath = '../../../views/event.hbs';
        }
        return { sourcePath, exclusions };
    }

    /**
     *
     *
     * @param account
     * @param event
     * @param user
     * @param notes
     * @returns
     */
    private async getEventHtml(
        account: Account,
        event: Event,
        user: User | null,
        notes: Note[],
        recipe?: AutopilotRecipeInstance,
    ): Promise<string | null> {
        let html: string | null = null;
        this.logger.log(
            PolloAdapter.acct(
                `Setting template to create PDF for event with id ${event.id}...`,
                account,
            )
                .setContext(this.constructor.name)
                .setSubContext(this.getEventHtml.name),
        );

        try {
            const { sourcePath, exclusions } = this.getEventTemplateObj(event);
            const testResult = getEventStatus(event);
            const source = fs.readFileSync(path.join(__dirname, sourcePath), 'utf8');

            // push the content through handlebars
            const template = hbs.handlebars.compile(source);

            let username = '';

            if (!isNil(event.user)) {
                username = fullName(event.user);
            } else {
                switch (event.source) {
                    case EventSource.PUBLIC_API:
                        username = config.get('api.eventTrackingPublicApiUsername');
                        break;
                    case EventSource.VENDOR_QUESTIONNAIRE:
                        username = config.get('api.eventTrackingVendorQuestionnaireUsername');
                        break;
                    case EventSource.AUTOPILOT:
                        username = config.get('api.eventTrackingDefaultUsername');
                        break;
                    case EventSource.SCHEDULED:
                        username = config.get('api.eventTrackingScheduledUsername');
                        break;
                    case EventSource.DRATA_POLICY:
                        username = config.get('api.eventTrackingDrataPolicyUsername');
                        break;
                    default:
                        throw new InternalServerErrorException('Invalid event source');
                }
            }

            let testType: string | null = null;
            let testLogic: string | null = null;
            let testName: string | null = null;
            let testDescription: string | null = null;

            // TODO: update this for monitor with multiples recipes
            const isCustomTest = !isNil(recipe) && recipe.source === TestSource.CUSTOM;
            if (isCustomTest) {
                testType = TestSource[recipe.source];
                testLogic = JSON.stringify(recipe.recipe, null, 2);
                testName = recipe.name;
                testDescription = get(
                    event,
                    'controlTestInstanceHistory.controlTestInstance.monitorInstances[0].evidenceCollectionDescription',
                    null,
                );
            }

            const serviceLabel = isNil(event.connection)
                ? null
                : clientTypeEventServiceLabel(
                      event.connection.clientType,
                      event.connection.providerType,
                  );

            const accountIdLabel = isNil(event.connection) ? null : event.connection.clientId;

            const edrConnectionClients =
                await this.connectionRepository.getConnectionsByProviderTypesV2({
                    providerTypes: [ProviderType.EDR],
                });

            const edrClientType = first(edrConnectionClients)?.clientType;

            let requestDescription = getRequestDescription(event, edrClientType);
            if (isNil(requestDescription)) {
                requestDescription = config.get('handleBars.eventDefaultRequest');
            }

            // get the html from the handlebars template
            html = template({
                companyName: account.companyName,
                userName: username,
                timestamp: moment.utc(event.createdAt).format(EventsService.TIME_FORMAT),
                description: event.description,
                testType: testType,
                testLogic: testLogic,
                testName: testName,
                connectionAlias: event?.connection?.clientAlias ?? null,
                testDescription: testDescription,
                service: serviceLabel,
                accountId: accountIdLabel,
                request: requestDescription,
                results: testResult,
                createdAt: moment.utc().format(EventsService.TIME_FORMAT),
                json: JSON.stringify(event.getMetadata(), null, 2),
                notes: notes,
                exclusions: exclusions,
            });
        } catch (error) {
            this.error(error, account, { userId: user?.id });
        }
        return html;
    }

    private async getEventPdfTransmission(
        account: Account,
        event: Event,
        user: User,
        notes: Note[],
        recipe?: AutopilotRecipeInstance,
    ): Promise<PdfTransmission> {
        /**
         * If this proves to cumbersome to resolve, we can refactor
         * to approximate the length via faster calculations, i.e.:
         * event.metadata.length + notes.reduce((acc, curr) => acc + curr.comment.length)
         * + username.length + serviceLabel.length + ... + hbsTemplate.length
         */
        const html = await this.getEventHtml(account, event, user, notes, recipe);
        /**
         * validation.maxEventHtmlLength was resolved experimentally. This
         * value was determined by testing different event metadata sizes.
         */
        if (!isNil(html) && html.length > config.get('validation.maxEventHtmlLength')) {
            return PdfTransmission.EMAIL;
        }
        return PdfTransmission.DOWNLOAD;
    }

    removeControlApproval(
        account: Account,
        user: User,
        approval: Approval,
        control: Control,
    ): void {
        this._eventBus.publish(new ControlApprovalRemovedEvent(account, user, control, approval));
    }

    setUpControlApprovalEvent(
        account: Account,
        user: User,
        control: Control,
        approval: Approval,
    ): void {
        this._eventBus.publish(new ControlApprovalSetupEvent(account, user, control, approval));
    }

    addControlApprovalReviewEvent(
        account: Account,
        user: User,
        control: Control,
        approval: Approval,
        review: ApprovalReview,
    ): void {
        this._eventBus.publish(
            new ControlApprovalReviewAddedEvent(
                account,
                user,
                approval,
                review,
                control,
                review.user,
            ),
        );
    }
    removeControlApprovalReviewEvent(
        account: Account,
        user: User,
        control: Control,
        approval: Approval,
        review: ApprovalReview,
    ): void {
        this._eventBus.publish(
            new ControlApprovalReviewRemovedEvent(account, user, approval, review, control),
        );
    }

    editControlApprovalEvent(
        account: Account,
        user: User,
        skipNextDeadline: boolean,
        control: Control,
        newApproval: Approval,
        oldApproval: Approval,
    ): void {
        this._eventBus.publish(
            new ControlApprovalEditEvent(
                account,
                user,
                skipNextDeadline,
                control,
                newApproval,
                oldApproval,
            ),
        );
    }

    sentControlApprovalToApproversEvent(
        account: Account,
        user: User,
        skipNextDeadline: boolean,
        control: Control,
        newApproval: Approval,
        oldApproval: Approval,
        reviews: ApprovalReview[],
        task: UpcomingTaskDetails,
    ): void {
        this._eventBus.publish(
            new ControlApprovalSentToApproversEvent(
                account,
                user,
                skipNextDeadline,
                newApproval,
                oldApproval,
                reviews,
                control,
                task,
            ),
        );
    }

    approveControlApprovalEvent(
        account: Account,
        user: User,
        control: Control,
        approval: Approval,
        review: ApprovalReview,
        nextApproval: Approval,
    ): void {
        this._eventBus.publish(
            new ControlApprovalApprovedEvent(
                account,
                user,
                approval,
                review,
                control,
                nextApproval,
            ),
        );
    }
    requestChangesControlApprovalEvent(
        account: Account,
        user: User,
        control: Control,
        approval: Approval,
        review: ApprovalReview,
        note: Note,
        task: UpcomingTaskDetails,
    ): void {
        this._eventBus.publish(
            new ControlApprovalRequestChangesEvent(
                account,
                user,
                approval,
                review,
                control,
                note,
                task,
            ),
        );
    }

    // #region Custom Fields

    customFieldCreatedEvent(account: Account, user: User, customField: CustomField): void {
        this._eventBus.publish(new CustomFieldCreatedEvent(account, user, customField));
    }

    customFormulaCreatedEvent(
        account: Account,
        user: User,
        customField: CustomField,
        customFormula: CustomFormula,
    ): void {
        this._eventBus.publish(
            new CustomFormulaCreatedEvent(account, user, customField, customFormula),
        );
    }

    customFieldDeletedEvent(account: Account, user: User, customField: CustomField): void {
        this._eventBus.publish(new CustomFieldDeletedEvent(account, user, customField));
    }

    customFormulaDeletedEvent(account: Account, user: User, customField: CustomField): void {
        this._eventBus.publish(new CustomFormulaDeletedEvent(account, user, customField));
    }

    customFieldUpdatedEvent(account: Account, user: User, customField: CustomField): void {
        this._eventBus.publish(new CustomFieldUpdatedEvent(account, user, customField));
    }

    customFormulaUpdatedEvent(
        account: Account,
        user: User,
        customField: CustomField,
        customFormula: CustomFormula,
    ): void {
        this._eventBus.publish(
            new CustomFormulaUpdatedEvent(account, user, customField, customFormula),
        );
    }

    customFieldPlacedEvent(
        account: Account,
        user: User,
        customFieldLocationId: number,
        customFieldId: number,
        customFieldName: string,
        customFieldEntity: CustomFieldsEntityType,
        customFieldSection: CustomFieldsSection,
    ): void {
        this._eventBus.publish(
            new CustomFieldPlacedEvent(
                account,
                user,
                customFieldLocationId,
                customFieldId,
                customFieldName,
                customFieldEntity,
                customFieldSection,
            ),
        );
    }

    customFieldUnplacedEvent(
        account: Account,
        user: User,
        customFieldLocationId: number,
        customFieldId: number,
        customFieldName: string,
        customFieldEntity: CustomFieldsEntityType,
        customFieldSection: CustomFieldsSection,
    ): void {
        this._eventBus.publish(
            new CustomFieldUnplacedEvent(
                account,
                user,
                customFieldLocationId,
                customFieldId,
                customFieldName,
                customFieldEntity,
                customFieldSection,
            ),
        );
    }

    customFieldSectionUpdatedEvent(
        account: Account,
        user: User,
        customFieldLocationId: number,
        customFieldId: number,
        customFieldName: string,
        customFieldLocationEntityId: CustomFieldsEntityType,
        newSectionId: CustomFieldsSection,
        oldSectionId: CustomFieldsSection,
    ): void {
        this._eventBus.publish(
            new CustomFieldSectionUpdatedEvent(
                account,
                user,
                customFieldLocationId,
                customFieldId,
                customFieldName,
                customFieldLocationEntityId,
                newSectionId,
                oldSectionId,
            ),
        );
    }

    private logEventIfSupportUser(events: Event[], dto: EventRequestDto): void {
        const supportUserEmail = config.get('api.supportUser.email');
        const isSupportUser = dto?.user?.email === supportUserEmail;

        if (!isSupportUser) {
            return;
        }

        const [event] = events;
        const isSingleEvent = events.length === 1;

        const message = isSingleEvent
            ? `Support user took action: ${event?.description}`
            : 'Support user took bulk action: multiple events created';

        const eventIdentifier = {
            eventId: isSingleEvent ? event?.id : undefined,
            eventIds: isSingleEvent ? undefined : events.map(e => e.id),
            type: EventType[event?.type],
            category: EventCategory[event?.category],
        };

        this.logger.log(PolloMessage.msg(message).setIdentifier({ event: eventIdentifier }));
    }

    public async setEventMetadataFromRemote(account: Account, event: Event): Promise<Event> {
        try {
            const file = await this.downloadEventFile(account, event);
            if (!isNil(file)) {
                event.metadata = JSON.stringify(extractMetadataFromFileVersionOne(file));
                await this.featureFlaggedLog(
                    PolloAdapter.acct(
                        `File read from S3. fileKey: ${event.fileKey}`,
                        account,
                        this.constructor.name,
                    ).setIdentifier({ fileKey: event.fileKey, eventId: event.id }),
                    account,
                );
            }
        } catch (error) {
            this.logger.warn(
                PolloAdapter.acct(
                    `Failed to set event metadata from remote. fileKey: ${event.fileKey}`,
                    account,
                    this.constructor.name,
                ).setError(error),
            );
        }
        return event;
    }

    async getTestEventsWithoutConnection(
        account: Account,
        controlTestInstanceId: number,
        includeMetadata = true,
    ): Promise<Event[]> {
        const events =
            await this.eventRepository.getTestEventsWithoutConnection(controlTestInstanceId);

        if (includeMetadata && (await this.isReadingFromS3(account))) {
            for (const event of events) {
                if (!isNil(event.fileKey)) {
                    await this.setEventMetadataFromRemote(account, event);
                }
            }
        }

        return events;
    }

    async updateEvent(account: Account, event: Event): Promise<Event> {
        if (await this.isWritingToS3(account)) {
            const fileKey = await this.uploadEvent(account, event);
            if (!isNil(fileKey)) {
                event.fileKey = fileKey;
                if (!(await this.isWritingToSQL(account))) {
                    event.metadata = '{}';
                }
            }
        }
        return this.eventRepository._saveOne(event, { reload: false });
    }

    async updateEvents(account: Account, events: Event[]): Promise<Event[]> {
        const updatedEvents: Event[] = [];
        for (const event of events) {
            const updatedEvent = await this.updateEvent(account, event);
            updatedEvents.push(updatedEvent);
        }
        return updatedEvents;
    }

    //#endregion Custom Fields

    private get eventRepository(): EventRepository {
        return this.getCustomTenantRepository(EventRepository);
    }

    private get noteRepository(): NotesRepository {
        return this.getCustomTenantRepository(NotesRepository);
    }

    private get noteFileRepository(): NotesFileRepository {
        return this.getCustomTenantRepository(NotesFileRepository);
    }

    private get eventWorkspaceRepository(): EventWorkspaceRepository {
        return this.getCustomTenantRepository(EventWorkspaceRepository);
    }

    private get productRepository(): ProductRepository {
        return this.getCustomTenantRepository(ProductRepository);
    }

    private get autopilotRecipeInstanceRepository(): AutopilotRecipeInstanceRepository {
        return this.getCustomTenantRepository(AutopilotRecipeInstanceRepository);
    }

    private get connectionRepository(): ConnectionsRepository {
        return this.getCustomTenantRepository(ConnectionsRepository);
    }

    private get monitorInstanceExclusionRepository(): MonitorInstanceExclusionRepository {
        return this.getCustomTenantRepository(MonitorInstanceExclusionRepository);
    }

    private get monitorInstanceRepository(): MonitorInstanceRepository {
        return this.getCustomTenantRepository(MonitorInstanceRepository);
    }

    private get evidenceLibraryVersionTestResultRepository(): EvidenceLibraryVersionTestResultRepository {
        return this.getCustomTenantRepository(EvidenceLibraryVersionTestResultRepository);
    }
}
