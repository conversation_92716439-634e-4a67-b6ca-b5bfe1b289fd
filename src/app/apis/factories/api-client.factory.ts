import { HttpService } from '@nestjs/axios';
import { Injectable, InternalServerErrorException, Type } from '@nestjs/common';
import { EventBus } from '@nestjs/cqrs';
import { IApiServices } from 'app/apis/interfaces/api-services.interface';
import { AikidoApi } from 'app/apis/providers/aikido.api';
import { ApideckApi } from 'app/apis/providers/apideck.api';
import { ArnicaApi } from 'app/apis/providers/arnica.api';
import { AsanaApi } from 'app/apis/providers/asana.api';
import { AtlasApi } from 'app/apis/providers/atlas.api';
import { AtlassianApi } from 'app/apis/providers/atlassian.api';
import { Auth0Api } from 'app/apis/providers/auth0.api';
import { AwsCodeCommitApi } from 'app/apis/providers/aws-code-commit.api';
import { AwsGovCloudApi } from 'app/apis/providers/aws-gov-cloud.api';
import { AwsInspectorApi } from 'app/apis/providers/aws-inspector.api';
import { AwsOrgUnitsParallelizedApi } from 'app/apis/providers/aws-org-units/aws-org-units-parallelized.api';
import { AwsApi } from 'app/apis/providers/aws.api';
import { AzureDevopsBoardsApi } from 'app/apis/providers/azure-devops-boards.api';
import { AzureDevopsReposApi } from 'app/apis/providers/azure-devops-repos.api';
import { AzureDevopsApi } from 'app/apis/providers/azure-devops.api';
import { AzureGccHighApi } from 'app/apis/providers/azure-gcc-high.api';
import { AzureMgGccHighApi } from 'app/apis/providers/azure-mg-gcc-high.api';
import { AzureOrgUnitsApi } from 'app/apis/providers/azure-org-units.api';
import { AzureApi } from 'app/apis/providers/azure.api';
import { BambooHrNativeApi } from 'app/apis/providers/bamboo-hr-native.api';
import { BambooHRApi } from 'app/apis/providers/bamboo-hr.api';
import { BitbucketCodeApi } from 'app/apis/providers/bitbucket-code.api';
import { BitbucketApi } from 'app/apis/providers/bitbucket.api';
import { CertnApi } from 'app/apis/providers/certn.api';
import { CheckrApi } from 'app/apis/providers/checkr.api';
import { ClickupApi } from 'app/apis/providers/clickup.api';
import { CloudFlareApi } from 'app/apis/providers/cloudflare.api';
import { ConfluenceApi } from 'app/apis/providers/confluence.api';
import { CoverdashApi } from 'app/apis/providers/coverdash.api';
import { CrowdStrikeSpotlightApi } from 'app/apis/providers/crowdstrike-spotlight.api';
import { CrowdStrikeApi } from 'app/apis/providers/crowdstrike.api';
import { CurriculaApi } from 'app/apis/providers/curricula.api';
import { CustomDataApi } from 'app/apis/providers/custom-data.api';
import { ApromoreAwsApi } from 'app/apis/providers/custom/aws/apromore.aws.api';
import { AviatrixAwsApi } from 'app/apis/providers/custom/aws/aviatrix.aws.api';
import { AwsCustomApiConstants } from 'app/apis/providers/custom/aws/aws-custom.constants';
import { BillwerkAwsApi } from 'app/apis/providers/custom/aws/billwerk.aws.api';
import { BlueshiftAwsApi } from 'app/apis/providers/custom/aws/blueshift.aws.api';
import { FivetranAwsApi } from 'app/apis/providers/custom/aws/fivetran.aws.api';
import { GetvisibilityAwsApi } from 'app/apis/providers/custom/aws/getvisibility.aws.api';
import { JsrNahqAwsApi } from 'app/apis/providers/custom/aws/jsr-nahq.aws.api';
import { JustEatTakeawayAwsApi } from 'app/apis/providers/custom/aws/just-eat-takeaway.aws.api';
import { KituAwsApi } from 'app/apis/providers/custom/aws/kitu.aws.api';
import { LastpassAwsApi } from 'app/apis/providers/custom/aws/lastpass.aws.api';
import { ObsidianAwsApi } from 'app/apis/providers/custom/aws/obsidian.aws.api';
import { OrcaAwsApi } from 'app/apis/providers/custom/aws/orca.aws.api';
import { QuiltAwsApi } from 'app/apis/providers/custom/aws/quilt.aws.api';
import { SenseyeDocsAwsApi } from 'app/apis/providers/custom/aws/senseye.aws.api';
import { SensorTowerAwsApi } from 'app/apis/providers/custom/aws/sensor-tower.api';
import { SinchSmbAwsApi } from 'app/apis/providers/custom/aws/sinchsmb.aws.api';
import { SnapDocsAwsApi } from 'app/apis/providers/custom/aws/snapdocs.aws.api';
import { SofiAwsApi } from 'app/apis/providers/custom/aws/sofi.aws.api';
import { TravelwifiAwsApi } from 'app/apis/providers/custom/aws/travelwifi.aws.api';
import { VerifierAwsApi } from 'app/apis/providers/custom/aws/verifier.aws.api';
import { XpanseAwsApi } from 'app/apis/providers/custom/aws/xpanse.aws.api';
import { AzureCustomApiConstants } from 'app/apis/providers/custom/azure/azure-custom.constants';
import { KeyfactorAzureApi } from 'app/apis/providers/custom/azure/keyfactor.azure.api';
import { OpenWaterAzureApi } from 'app/apis/providers/custom/azure/openwater.azure.api';
import { ThreeSixtyInsightsAzureApi } from 'app/apis/providers/custom/azure/threesixtyinsights.azure.api';
import { CortoBitbucketApi } from 'app/apis/providers/custom/bitbucket/corto.bitbucket.api';
import { BitbucketCustomApiConstants } from 'app/apis/providers/custom/bitbucket/custom-bitbucket.constants';
import { DatadogCustomApiConstants } from 'app/apis/providers/custom/datadog/datadog-custom.constants';
import { LemonadeDatadogApi } from 'app/apis/providers/custom/datadog/lemonade.datadog.api';
import { ThreeSixtyInsightsDatadogApi } from 'app/apis/providers/custom/datadog/threesixtyinsights.datadog.api';
import { GcpCustomApiConstants } from 'app/apis/providers/custom/gcp/custom-gcp.constants';
import { RevolutGcpApi } from 'app/apis/providers/custom/gcp/revolut.gcp.api';
import { TymeshiftGcpApi } from 'app/apis/providers/custom/gcp/tymeshift.gcp.api';
import { WebFxGcpApi } from 'app/apis/providers/custom/gcp/web-fx.gcp.api';
import { DoubleVerifyGitlabApi } from 'app/apis/providers/custom/gitlab/doubleverify-gitlab.api';
import { GitlabCustomApiConstants } from 'app/apis/providers/custom/gitlab/gitlab-custom.constants';
import { AdhereIntuneApi } from 'app/apis/providers/custom/intune/adherehealth.intune.api';
import { AffindaIntuneApi } from 'app/apis/providers/custom/intune/affinda.intune.api';
import { CalAmpIntuneApi } from 'app/apis/providers/custom/intune/calamp.intune.api';
import { ChaConsultingIntuneApi } from 'app/apis/providers/custom/intune/chaconsulting.intune.api';
import { fengateIntuneApi } from 'app/apis/providers/custom/intune/fengate.intune.api';
import { IdqIntuneApi } from 'app/apis/providers/custom/intune/idq.intune.api';
import { IntuneCustomConstants } from 'app/apis/providers/custom/intune/intune-custom.constants';
import { KeyfactorIntuneApi } from 'app/apis/providers/custom/intune/keyfactor.intune.api';
import { NetspiIntuneApi } from 'app/apis/providers/custom/intune/netspi.intune.api';
import { PrimeTheraIntuneApi } from 'app/apis/providers/custom/intune/primetherapeutics.intune.api';
import { RecogniIntuneApi } from 'app/apis/providers/custom/intune/recogni.intune.api';
import { RemoveryIntuneApi } from 'app/apis/providers/custom/intune/removery.intune.api';
import { SanitasIntuneApi } from 'app/apis/providers/custom/intune/sanitas.intune.api';
import { SinchIntuneApi } from 'app/apis/providers/custom/intune/sinch.intune.api';
import { SymphonyAiIntuneApi } from 'app/apis/providers/custom/intune/symphonyai.intune.api';
import { TempWorksIntuneApi } from 'app/apis/providers/custom/intune/tempworks.intune.api';
import { ThreeSixtyInsightsIntuneApi } from 'app/apis/providers/custom/intune/threesixtyinsights.intune.api';
import { TlsCvIntuneApi } from 'app/apis/providers/custom/intune/tlscv.intune.api';
import { TrssIntuneApi } from 'app/apis/providers/custom/intune/trss.intune.api';
import { WestMonroeIntuneApi } from 'app/apis/providers/custom/intune/westmonroe.intune.api';
import { ZevoIntuneApi } from 'app/apis/providers/custom/intune/zevo.intune.api';
import { AAOGlobalMicrosoft365Api } from 'app/apis/providers/custom/microsoft-365/aao.global.microsoft-365.api';
import { AciMicrosoft365Api } from 'app/apis/providers/custom/microsoft-365/aci.microsoft-365.api';
import { AcrisureMicrosoft365Api } from 'app/apis/providers/custom/microsoft-365/acrisure.microsoft365.api';
import { AdhereMicrosoft365Api } from 'app/apis/providers/custom/microsoft-365/adherehealth.microsoft-365.api';
import { AffindaMicrosoft365Api } from 'app/apis/providers/custom/microsoft-365/affinda.microsoft-365.api';
import { AvcrmMicrosoft365Api } from 'app/apis/providers/custom/microsoft-365/avcrm.microsoft-365.api';
import { BBEMicrosoft365Api } from 'app/apis/providers/custom/microsoft-365/bbe.microsoft-365.api';
import { CalAmpMicrosoft365Api } from 'app/apis/providers/custom/microsoft-365/calamp.microsoft-365.api';
import { ChaConsultingMicrosoft365Api } from 'app/apis/providers/custom/microsoft-365/chaconsulting.microsoft-365.api';
import { ChicagoLighthouseMicrosoft365Api } from 'app/apis/providers/custom/microsoft-365/chicagolighthouse.microsoft-365.api';
import { CxOneNiceMicrosoft365Api } from 'app/apis/providers/custom/microsoft-365/cxone-nice.microsoft-365.api';
import { EasypayEverywhereMicrosoft365Api } from 'app/apis/providers/custom/microsoft-365/easypayeverywhere.microsoft-365.api';
import { EquitrustMicrosoft365Api } from 'app/apis/providers/custom/microsoft-365/equitrust.microsoft-365.api';
import { fengateMicrosoft365Api } from 'app/apis/providers/custom/microsoft-365/fengate.microsoft-365.api';
import { FinthriveMicrosoft365Api } from 'app/apis/providers/custom/microsoft-365/finthrive.microsoft-365.api';
import { FugroMicrosoft365Api } from 'app/apis/providers/custom/microsoft-365/fugro.microsoft-365.api';
import { HoneywellMicrosoft365Api } from 'app/apis/providers/custom/microsoft-365/honeywell.microsoft-365.api';
import { IdqMicrosoft365Api } from 'app/apis/providers/custom/microsoft-365/idq.microsoft-365.api';
import { IQVIAMicrosoft365Api } from 'app/apis/providers/custom/microsoft-365/iqvia.microsoft-365.api';
import { JndlaMicrosoft365Api } from 'app/apis/providers/custom/microsoft-365/jndla.microsoft-365.api';
import { KeyfactorMicrosoft365Api } from 'app/apis/providers/custom/microsoft-365/keyfactor.microsoft-365.api';
import { LesakatechConnectedMicrosoft365Api } from 'app/apis/providers/custom/microsoft-365/lesakaconnected.microsoft-365.api';
import { LesakatechMicrosoft365Api } from 'app/apis/providers/custom/microsoft-365/lesakatech.microsoft-365.api';
import { MadCapMicrosoft365Api } from 'app/apis/providers/custom/microsoft-365/madcap.microsoft-365.api';
import { MaronMarvelMicrosoft365Api } from 'app/apis/providers/custom/microsoft-365/maronmarvel.microsoft-365.api';
import { MbcMicrosoft365Api } from 'app/apis/providers/custom/microsoft-365/mbc.microsoft-365.api';
import { Microsoft365CustomApiConstants } from 'app/apis/providers/custom/microsoft-365/microsoft-365-custom.constants';
import { MilyliMicrosoft365Api } from 'app/apis/providers/custom/microsoft-365/milyli.microsoft-365.api';
import { MpsMonitor365Api } from 'app/apis/providers/custom/microsoft-365/mpsmonitor.microsoft-365.api';
import { NavvisMicrosoft365Api } from 'app/apis/providers/custom/microsoft-365/navvis.microsoft-365.api';
import { NetspiMicrosoft365Api } from 'app/apis/providers/custom/microsoft-365/netspi.microsoft-365.api';
import { OnetoOneHealthMicrosoft365Api } from 'app/apis/providers/custom/microsoft-365/onetoone.microsoft-365.api';
import { PaytechMicrosoft365Api } from 'app/apis/providers/custom/microsoft-365/paytech.microsoft-365.api';
import { PortimaMicrosoft365Api } from 'app/apis/providers/custom/microsoft-365/portima.microsoft-365.api';
import { PrimeTheraMicrosoft365Api } from 'app/apis/providers/custom/microsoft-365/primetherapeutics.microsoft-365.api';
import { QuarterhillMicrosoft365Api } from 'app/apis/providers/custom/microsoft-365/quarterhill.microsoft-365.api';
import { ReadySignalMicrosoft365Api } from 'app/apis/providers/custom/microsoft-365/readysignal.microsoft-365.api';
import { RemoveryMicrosoft365Api } from 'app/apis/providers/custom/microsoft-365/removery.microsoft-365.api';
import { RoundTripMicrosoft365Api } from 'app/apis/providers/custom/microsoft-365/roundtrip.microsoft-365.api';
import { SanitasMicrosoft365Api } from 'app/apis/providers/custom/microsoft-365/sanitas.microsoft-365.api';
import { SequenturMicrosoft365Api } from 'app/apis/providers/custom/microsoft-365/sequentur.microsoft-365.api';
import { SinchMicrosoft365Api } from 'app/apis/providers/custom/microsoft-365/sinch.microsoft-365.api';
import { SwissquoteLUMicrosoft365Api } from 'app/apis/providers/custom/microsoft-365/swissquotelu.microsoft-365.api';
import { SymphonyAiMicrosoft365Api } from 'app/apis/providers/custom/microsoft-365/symphonyai.microsoft-365.api';
import { TempWorksMicrosoft365Api } from 'app/apis/providers/custom/microsoft-365/tempworks.microsoft-365.api';
import { ThreeSixtyInsightsMicrosoft365Api } from 'app/apis/providers/custom/microsoft-365/threesixtyinsights.microsoft-365.api';
import { TLSMicrosoft365Api } from 'app/apis/providers/custom/microsoft-365/tls.microsoft-365.api';
import { TransmaxMicrosoft365Api } from 'app/apis/providers/custom/microsoft-365/transmax.microsoft-365.api';
import { TrssMicrosoft365Api } from 'app/apis/providers/custom/microsoft-365/trss.microsoft-365.api';
import { VolarisMicrosoft365Api } from 'app/apis/providers/custom/microsoft-365/volaris.microsoft-365.api';
import { WellHealthMicrosoft365Api } from 'app/apis/providers/custom/microsoft-365/well.health.microsoft-365.api';
import { WestMonroeMicrosoft365Api } from 'app/apis/providers/custom/microsoft-365/westmonroe.microsoft-365.api';
import { WiproMicrosoft365Api } from 'app/apis/providers/custom/microsoft-365/wipro.microsoft-365.api';
import { CyberArkApi } from 'app/apis/providers/cyberark.api';
import { DatadogApi } from 'app/apis/providers/datadog.api';
import { DigitalOceanApi } from 'app/apis/providers/digitalocean.api';
import { DocuSignApi } from 'app/apis/providers/docusign.api';
import { FiberyApi } from 'app/apis/providers/fibery.api';
import { GcpBaseApi } from 'app/apis/providers/gcp-base.api';
import { GcpApi } from 'app/apis/providers/gcp.api';
import { GitHubCodeApi } from 'app/apis/providers/github-code.api';
import { GitHubApi } from 'app/apis/providers/github.api';
import { GitlabBetaApi } from 'app/apis/providers/gitlab-beta.api';
import { ********************* } from 'app/apis/providers/gitlab-issues-on-prem.api';
import { GitlabIssuesApi } from 'app/apis/providers/gitlab-issues.api';
import { GitlabOnPremApi } from 'app/apis/providers/gitlab-on-prem.api';
import { GitlabApi } from 'app/apis/providers/gitlab.api';
import { GoogleOAuthApi } from 'app/apis/providers/google-oauth.api';
import { GoogleSdkApi } from 'app/apis/providers/google.sdk.api';
import { GustoApi } from 'app/apis/providers/gusto.api';
import { HerokuApi } from 'app/apis/providers/heroku.api';
import { HexnodeApi } from 'app/apis/providers/hexnode.api';
import { HireRightApi } from 'app/apis/providers/hireright.api';
import { HubspotApi } from 'app/apis/providers/hubspot.api';
import { IdpFileApi } from 'app/apis/providers/idp-file.api';
import { IntuneGccHighApi } from 'app/apis/providers/intune-gcc-high.api';
import { IntuneApi } from 'app/apis/providers/intune.api';
import { JamfApi } from 'app/apis/providers/jamf.api';
import { JiraDataCenterApi } from 'app/apis/providers/jira-data-center.api';
import { JiraApi } from 'app/apis/providers/jira.api';
import { JumpCloudIdentityApi } from 'app/apis/providers/jumpcloud-identity.api';
import { JumpCloudApi } from 'app/apis/providers/jumpcloud.api';
import { KandjiApi } from 'app/apis/providers/kandji.api';
import { KarmaCheckApi } from 'app/apis/providers/karmacheck.api';
import { KnowBe4Api } from 'app/apis/providers/knowbe4.api';
import { KolideApi } from 'app/apis/providers/kolide.api';
import { LinearApi } from 'app/apis/providers/linear.api';
import { Microsoft365GccHighApi } from 'app/apis/providers/microsoft-365-gcc-high.api';
import { Microsoft365Api } from 'app/apis/providers/microsoft-365.api';
import { MicrosoftDefenderApi } from 'app/apis/providers/microsoft-defender.api';
import { MicrosoftTeamsApi } from 'app/apis/providers/microsoft-teams.api';
import { MiroApi } from 'app/apis/providers/miro.api';
import { NewRelicApi } from 'app/apis/providers/new-relic.api';
import { NotionApi } from 'app/apis/providers/notion.api';
import { OktaIdentityApi } from 'app/apis/providers/okta-identity.api';
import { OneLoginApi } from 'app/apis/providers/one-login.api';
import { PagerDutyApi } from 'app/apis/providers/pager-duty.api';
import { PingOneApi } from 'app/apis/providers/pingone.api';
import { PivotalTrackerApi } from 'app/apis/providers/pivotaltracker.api';
import { QualysApi } from 'app/apis/providers/qualys.api';
import { Rapid7VmsApi } from 'app/apis/providers/rapid7-vms.api';
import { Rapid7Api } from 'app/apis/providers/rapid7.api';
import { RipplingMDMApi } from 'app/apis/providers/rippling-mdm.api';
import { RipplingApi } from 'app/apis/providers/rippling.api';
import { SalesforceApi } from 'app/apis/providers/salesforce.api';
import { SegmentApi } from 'app/apis/providers/segment.api';
import { SemgrepApi } from 'app/apis/providers/semgrep.api';
import { SentinelOneApi } from 'app/apis/providers/sentinel-one.api';
import { SentinelOneVMApi } from 'app/apis/providers/sentinelone-vm.api';
import { SentryApi } from 'app/apis/providers/sentry.api';
import { ServicenowApi } from 'app/apis/providers/servicenow.api';
import { ShortcutApi } from 'app/apis/providers/shortcut.api';
import { SlackApi } from 'app/apis/providers/slack.api';
import { SnowflakeApi } from 'app/apis/providers/snowflake.api';
import { SnykApi } from 'app/apis/providers/snyk.api';
import { StackOneSecurityTrainingApi } from 'app/apis/providers/stack-one/stack-one-security-training.api';
import { StackOneUarApi } from 'app/apis/providers/stack-one/stack-one-uar.api';
import { SterlingApi } from 'app/apis/providers/sterling.api';
import { TargetProcessApi } from 'app/apis/providers/targetprocess.api';
import { TenableApi } from 'app/apis/providers/tenable.api';
import { TrelloApi } from 'app/apis/providers/trello.api';
import { VercelApi } from 'app/apis/providers/vercel.api';
import { VettyApi } from 'app/apis/providers/vetty.api';
import { WizCloudApi } from 'app/apis/providers/wiz-cloud.api';
import { WizCodeApi } from 'app/apis/providers/wiz-code.api';
import { WizApi } from 'app/apis/providers/wiz.api';
import { WorkOsApi } from 'app/apis/providers/work-os.api';
import { WorkspaceONEApi } from 'app/apis/providers/workspace-one.api';
import { XeroApi } from 'app/apis/providers/xero.api';
import { ZohoApi } from 'app/apis/providers/zoho.api';
import { ZoomApi } from 'app/apis/providers/zoom.api';
import { CheckrHttpServiceRateLimit } from 'app/apis/services/checkr/checkr-http.service';
import { MergedevHrisApi } from 'app/apis/services/mergedev/apis/mergedev-hris.api';
import { MergedevTicketingCreationApi } from 'app/apis/services/mergedev/apis/mergedev-ticketing-creation.api';
import { ConnectionEntity } from 'app/companies/connections/entities/connection.entity';
import { Account } from 'auth/entities/account.entity';
import { StackOneSecurityTrainingClientTypes } from 'auth/helpers/stack-one/stack-one-provider-type.helper';
import { CacheService } from 'cache/cache.service';
import { InMemoryCacheService } from 'cache/in-memory-cache.service';
import { ClientType } from 'commons/enums/auth/client-type.enum';
import { ProviderType } from 'commons/enums/auth/provider-type.enum';
import { HttpServiceRateLimit } from 'commons/services/rate-limit.service';
import { Downloader } from 'dependencies/downloader/downloader';
import { FeatureFlagService } from 'feature-flags/feature-flag.service';
import { FileValidationService } from 'file-validation/services/file-validation.service';
import { isNil } from 'lodash';
import { CsvParser } from 'nest-csv-parser';
import { OAuth2Adapter } from 'oauth2/adapters/oauth2.adapter';
import { v4 as uuidv4 } from 'uuid';

// CLI_CLIENT_FACTORY_IMPORT_TAG_KEY

// INTEGRATION QUICK FIND: Client Type
// https://www.notion.so/drata/f295b355f9ac4b72ae7ab103c543f258

/**
 * When a clientAlias is supplied, we should include the tenant's domain. It serves as an additional
 * security measure to prevent potential unauthorized access through the same alias.
 */
type CustomApiProvider = {
    clientId: string[];
    clientType: ClientType;
    clientAlias?: string;
    domain?: string;
    type: Type<IApiServices>;
};

type CustomHttpService = {
    type: Type<IApiServices>;
    service: Type<HttpServiceRateLimit>;
};

@Injectable()
export class ApiClientFactory {
    private customApiProviders: CustomApiProvider[] = [
        {
            clientId: AwsCustomApiConstants.apromoreAwsClientIds,
            clientType: ClientType.AWS,
            type: ApromoreAwsApi,
        },
        {
            clientId: AwsCustomApiConstants.obsidianAwsClientIds,
            clientType: ClientType.AWS,
            type: ObsidianAwsApi,
        },
        {
            clientId: AwsCustomApiConstants.orcaAwsClientIds,
            clientType: ClientType.AWS,
            type: OrcaAwsApi,
        },
        {
            clientId: AwsCustomApiConstants.sensorTowerAwsClientIds,
            clientType: ClientType.AWS,
            type: SensorTowerAwsApi,
        },
        {
            clientId: AwsCustomApiConstants.sofiAwsClientIds,
            clientType: ClientType.AWS,
            type: SofiAwsApi,
        },
        {
            clientId: AwsCustomApiConstants.xpanseAwsClientIds,
            clientType: ClientType.AWS,
            type: XpanseAwsApi,
        },
        {
            clientId: AwsCustomApiConstants.aviatrixAwsClientIds,
            clientType: ClientType.AWS,
            type: AviatrixAwsApi,
        },
        {
            clientId: AzureCustomApiConstants.openWaterAzureClientIds,
            clientType: ClientType.AZURE,
            type: OpenWaterAzureApi,
        },
        {
            clientId: AzureCustomApiConstants.keyfactorAzureClientIds,
            clientType: ClientType.AZURE,
            type: KeyfactorAzureApi,
        },
        {
            clientId: DatadogCustomApiConstants.lemonadeDatadogClientIds,
            clientType: ClientType.DATADOG,
            type: LemonadeDatadogApi,
        },
        {
            clientId: DatadogCustomApiConstants.threeSixtyInsightsDatadogClientIds,
            clientType: ClientType.DATADOG,
            type: ThreeSixtyInsightsDatadogApi,
        },
        {
            clientId: GcpCustomApiConstants.revolutGcpClientIds,
            clientType: ClientType.GCP,
            type: RevolutGcpApi,
        },
        {
            clientId: GcpCustomApiConstants.tymeshiftGcpClientIds,
            clientType: ClientType.GCP,
            type: TymeshiftGcpApi,
        },
        {
            clientId: GcpCustomApiConstants.webFxGcpClientIds,
            clientType: ClientType.GCP,
            type: WebFxGcpApi,
        },
        {
            clientId: GitlabCustomApiConstants.doubleVerifyGitlabClientIds,
            clientType: ClientType.GITLAB,
            clientAlias: GitlabCustomApiConstants.doubleVerifyClientAlias,
            domain: GitlabCustomApiConstants.doubleVerifyDomain,
            type: DoubleVerifyGitlabApi,
        },
        {
            clientId: AwsCustomApiConstants.snapDocsAwsClientIds,
            clientType: ClientType.AWS,
            type: SnapDocsAwsApi,
        },
        {
            clientId: AwsCustomApiConstants.senseyeAwsClientIds,
            clientType: ClientType.AWS,
            type: SenseyeDocsAwsApi,
        },
        {
            clientId: AwsCustomApiConstants.sinchSmbAwsClientIds,
            clientType: ClientType.AWS,
            type: SinchSmbAwsApi,
        },
        {
            clientId: Microsoft365CustomApiConstants.sinchMicrosoft365ClientIds,
            clientType: ClientType.MICROSOFT_365,
            type: SinchMicrosoft365Api,
        },
        {
            clientId: Microsoft365CustomApiConstants.chicagoLighthouse365ClientIds,
            clientType: ClientType.MICROSOFT_365,
            type: ChicagoLighthouseMicrosoft365Api,
        },
        {
            clientId: Microsoft365CustomApiConstants.tlsMicrosoft365ClientIds,
            clientType: ClientType.MICROSOFT_365,
            type: TLSMicrosoft365Api,
        },
        {
            clientId: Microsoft365CustomApiConstants.quarterhillMicrosoft365ClientIds,
            clientType: ClientType.MICROSOFT_365,
            type: QuarterhillMicrosoft365Api,
        },
        {
            clientId: Microsoft365CustomApiConstants.acrisureMicrosoft365ClientIds,
            clientType: ClientType.MICROSOFT_365,
            type: AcrisureMicrosoft365Api,
        },
        {
            clientId: Microsoft365CustomApiConstants.paytechMicrosoft365ClientIds,
            clientType: ClientType.MICROSOFT_365,
            type: PaytechMicrosoft365Api,
        },
        {
            clientId: Microsoft365CustomApiConstants.wellHealthMicrosoft365ClientIds,
            clientType: ClientType.MICROSOFT_365,
            type: WellHealthMicrosoft365Api,
        },
        {
            clientId: AwsCustomApiConstants.blueshiftAwsClientIds,
            clientType: ClientType.AWS,
            type: BlueshiftAwsApi,
        },
        {
            clientId: AwsCustomApiConstants.quiltAwsClientIds,
            clientType: ClientType.AWS,
            type: QuiltAwsApi,
        },
        {
            clientId: IntuneCustomConstants.sinchIntuneClientIds,
            clientType: ClientType.INTUNE,
            type: SinchIntuneApi,
        },
        {
            clientId: IntuneCustomConstants.tlsCvIntuneClientIds,
            clientType: ClientType.INTUNE,
            type: TlsCvIntuneApi,
        },
        {
            clientId: IntuneCustomConstants.tlsCvIntuneClientIds,
            clientType: ClientType.INTUNE,
            type: RecogniIntuneApi,
        },
        {
            clientId: IntuneCustomConstants.chaConsultingIntuneClientIds,
            clientType: ClientType.INTUNE,
            type: ChaConsultingIntuneApi,
        },
        {
            clientId: IntuneCustomConstants.affindaIntuneClientIds,
            clientType: ClientType.INTUNE,
            type: AffindaIntuneApi,
        },
        {
            clientId: IntuneCustomConstants.tempWorksIntuneClientIds,
            clientType: ClientType.INTUNE,
            type: TempWorksIntuneApi,
        },
        {
            clientId: AwsCustomApiConstants.lastpassClientIds,
            clientType: ClientType.AWS,
            type: LastpassAwsApi,
        },
        {
            clientId: AwsCustomApiConstants.fivetranClientIds,
            clientType: ClientType.AWS,
            type: FivetranAwsApi,
        },
        {
            clientId: AwsCustomApiConstants.verifierClientIds,
            clientType: ClientType.AWS,
            type: VerifierAwsApi,
        },
        {
            clientId: AwsCustomApiConstants.jsrNahqClientIds,
            clientType: ClientType.AWS,
            type: JsrNahqAwsApi,
        },
        {
            clientId: AwsCustomApiConstants.getvisibilityClientIds,
            clientType: ClientType.AWS,
            type: GetvisibilityAwsApi,
        },
        {
            clientId: Microsoft365CustomApiConstants.volarisMicrosoft365ClientIds,
            clientType: ClientType.MICROSOFT_365,
            type: VolarisMicrosoft365Api,
        },
        {
            clientId: Microsoft365CustomApiConstants.iqviaMicrosoft365ClientIds,
            clientType: ClientType.MICROSOFT_365,
            type: IQVIAMicrosoft365Api,
        },
        {
            clientId: Microsoft365CustomApiConstants.aaoGlobalMicrosoft365ClientIds,
            clientType: ClientType.MICROSOFT_365,
            type: AAOGlobalMicrosoft365Api,
        },
        {
            clientId: AwsCustomApiConstants.kituClientIds,
            clientType: ClientType.AWS,
            type: KituAwsApi,
        },
        {
            clientId: Microsoft365CustomApiConstants.fugroMicrosoft365ClientIds,
            clientType: ClientType.MICROSOFT_365,
            type: FugroMicrosoft365Api,
        },
        {
            clientId: Microsoft365CustomApiConstants.mpsMonitorMicrosoft365ClientIds,
            clientType: ClientType.MICROSOFT_365,
            type: MpsMonitor365Api,
        },
        {
            clientId: Microsoft365CustomApiConstants.honeywellMicrosoft365ClientIds,
            clientType: ClientType.MICROSOFT_365,
            type: HoneywellMicrosoft365Api,
        },
        {
            clientId: AwsCustomApiConstants.travelwifiClientIds,
            clientType: ClientType.AWS,
            type: TravelwifiAwsApi,
        },
        {
            clientId: AwsCustomApiConstants.billwerkClientIds,
            clientType: ClientType.AWS,
            type: BillwerkAwsApi,
        },
        {
            clientId: Microsoft365CustomApiConstants.equitrustMicrosoft365ClientIds,
            clientType: ClientType.MICROSOFT_365,
            type: EquitrustMicrosoft365Api,
        },
        {
            clientId: Microsoft365CustomApiConstants.netspiMicrosoft365ClientIds,
            clientType: ClientType.MICROSOFT_365,
            type: NetspiMicrosoft365Api,
        },
        {
            clientId: IntuneCustomConstants.netspiIntuneClientIds,
            clientType: ClientType.INTUNE,
            type: NetspiIntuneApi,
        },
        {
            clientId: Microsoft365CustomApiConstants.sanitasMicrosoft365ClientIds,
            clientType: ClientType.MICROSOFT_365,
            type: SanitasMicrosoft365Api,
        },
        {
            clientId: IntuneCustomConstants.sanitasIntuneClientIds,
            clientType: ClientType.INTUNE,
            type: SanitasIntuneApi,
        },
        {
            clientId: Microsoft365CustomApiConstants.adhereMicrosoft365ClientIds,
            clientType: ClientType.MICROSOFT_365,
            type: AdhereMicrosoft365Api,
        },
        {
            clientId: IntuneCustomConstants.adhereIntuneClientIds,
            clientType: ClientType.INTUNE,
            type: AdhereIntuneApi,
        },
        {
            clientId: Microsoft365CustomApiConstants.idqMicrosoft365ClientIds,
            clientType: ClientType.MICROSOFT_365,
            type: IdqMicrosoft365Api,
        },
        {
            clientId: IntuneCustomConstants.idqIntuneClientIds,
            clientType: ClientType.INTUNE,
            type: IdqIntuneApi,
        },
        {
            clientId: Microsoft365CustomApiConstants.primetheraMicrosoft365ClientIds,
            clientType: ClientType.MICROSOFT_365,
            type: PrimeTheraMicrosoft365Api,
        },
        {
            clientId: IntuneCustomConstants.primetheraIntuneClientIds,
            clientType: ClientType.INTUNE,
            type: PrimeTheraIntuneApi,
        },
        {
            clientId: Microsoft365CustomApiConstants.idqMicrosoft365ClientIds,
            clientType: ClientType.MICROSOFT_365,
            type: fengateMicrosoft365Api,
        },
        {
            clientId: IntuneCustomConstants.idqIntuneClientIds,
            clientType: ClientType.INTUNE,
            type: fengateIntuneApi,
        },
        {
            clientId: Microsoft365CustomApiConstants.madcapMicrosoft365ClientIds,
            clientType: ClientType.MICROSOFT_365,
            type: MadCapMicrosoft365Api,
        },
        {
            clientId: Microsoft365CustomApiConstants.sequenturMicrosoft365ClientIds,
            clientType: ClientType.MICROSOFT_365,
            type: SequenturMicrosoft365Api,
        },
        {
            clientId: Microsoft365CustomApiConstants.tempworksMicrosoft365ClientIds,
            clientType: ClientType.MICROSOFT_365,
            type: TempWorksMicrosoft365Api,
        },
        {
            clientId: Microsoft365CustomApiConstants.aciMicrosoft365ClientIds,
            clientType: ClientType.MICROSOFT_365,
            type: AciMicrosoft365Api,
        },
        {
            clientId: Microsoft365CustomApiConstants.removeryMicrosoft365ClientIds,
            clientType: ClientType.MICROSOFT_365,
            type: RemoveryMicrosoft365Api,
        },
        {
            clientId: IntuneCustomConstants.removeryIntuneClientIds,
            clientType: ClientType.INTUNE,
            type: RemoveryIntuneApi,
        },
        {
            clientId: Microsoft365CustomApiConstants.chaconsultingMicrosoft365ClientIds,
            clientType: ClientType.MICROSOFT_365,
            type: ChaConsultingMicrosoft365Api,
        },
        {
            clientId: Microsoft365CustomApiConstants.affindaMicrosoft365ClientIds,
            clientType: ClientType.MICROSOFT_365,
            type: AffindaMicrosoft365Api,
        },
        {
            clientId: AwsCustomApiConstants.justEatTakeawayClientIds,
            clientType: ClientType.AWS,
            type: JustEatTakeawayAwsApi,
        },
        {
            clientId: Microsoft365CustomApiConstants.BBEMicrosoft365ClientIds,
            clientType: ClientType.MICROSOFT_365,
            type: BBEMicrosoft365Api,
        },
        {
            clientId: Microsoft365CustomApiConstants.finthriveMicrosoft365ClientIds,
            clientType: ClientType.MICROSOFT_365,
            type: FinthriveMicrosoft365Api,
        },
        {
            clientId: Microsoft365CustomApiConstants.cxOneNiceMicrosoft365ClientIds,
            clientType: ClientType.MICROSOFT_365,
            type: CxOneNiceMicrosoft365Api,
        },
        {
            clientId: Microsoft365CustomApiConstants.wiproMicrosoft365ClientIds,
            clientType: ClientType.MICROSOFT_365,
            type: WiproMicrosoft365Api,
        },
        {
            clientId: Microsoft365CustomApiConstants.lesakaConnectedMicrosoft365ClientIds,
            clientType: ClientType.MICROSOFT_365,
            type: LesakatechConnectedMicrosoft365Api,
        },
        {
            clientId: Microsoft365CustomApiConstants.oneToOneMicrosoft365ClientIds,
            clientType: ClientType.MICROSOFT_365,
            type: OnetoOneHealthMicrosoft365Api,
        },
        {
            clientId: Microsoft365CustomApiConstants.avcrmMicrosoft365ClientIds,
            clientType: ClientType.MICROSOFT_365,
            type: AvcrmMicrosoft365Api,
        },
        {
            clientId: Microsoft365CustomApiConstants.maronmarvelMicrosoft365ClientIds,
            clientType: ClientType.MICROSOFT_365,
            type: MaronMarvelMicrosoft365Api,
        },
        {
            clientId: Microsoft365CustomApiConstants.westMonroeMicrosoft365ClientIds,
            clientType: ClientType.MICROSOFT_365,
            type: WestMonroeMicrosoft365Api,
        },
        {
            clientId: IntuneCustomConstants.westMonroeIntuneClientIds,
            clientType: ClientType.INTUNE,
            type: WestMonroeIntuneApi,
        },
        {
            clientId: IntuneCustomConstants.zevoIntuneClientIds,
            clientType: ClientType.INTUNE,
            type: ZevoIntuneApi,
        },
        {
            clientId: Microsoft365CustomApiConstants.navvisMicrosoft365ClientIds,
            clientType: ClientType.MICROSOFT_365,
            type: NavvisMicrosoft365Api,
        },
        {
            clientId: Microsoft365CustomApiConstants.keyfactorMicrosoft365ClientIds,
            clientType: ClientType.MICROSOFT_365,
            type: KeyfactorMicrosoft365Api,
        },
        {
            clientId: Microsoft365CustomApiConstants.keyfactorMicrosoft365ClientIds,
            clientType: ClientType.MICROSOFT_365,
            type: KeyfactorMicrosoft365Api,
        },
        {
            clientId: Microsoft365CustomApiConstants.lesakatechMicrosoft365ClientIds,
            clientType: ClientType.MICROSOFT_365,
            type: LesakatechMicrosoft365Api,
        },
        {
            clientId: Microsoft365CustomApiConstants.roundtripMicrosoft365ClientIds,
            clientType: ClientType.MICROSOFT_365,
            type: RoundTripMicrosoft365Api,
        },
        {
            clientId: Microsoft365CustomApiConstants.threeSixtyInsightsMicrosoft365ClientIds,
            clientType: ClientType.MICROSOFT_365,
            type: ThreeSixtyInsightsMicrosoft365Api,
        },
        {
            clientId: IntuneCustomConstants.threeSixtyInsightsIntuneClientIds,
            clientType: ClientType.INTUNE,
            type: ThreeSixtyInsightsIntuneApi,
        },
        {
            clientId: IntuneCustomConstants.keyfactorIntuneClientIds,
            clientType: ClientType.INTUNE,
            type: KeyfactorIntuneApi,
        },
        {
            clientId: IntuneCustomConstants.trssIntuneClientIds,
            clientType: ClientType.INTUNE,
            type: TrssIntuneApi,
        },
        {
            clientId: Microsoft365CustomApiConstants.trssMicrosoft365ClientIds,
            clientType: ClientType.MICROSOFT_365,
            type: TrssMicrosoft365Api,
        },
        {
            clientId: IntuneCustomConstants.calampIntuneClientIds,
            clientType: ClientType.INTUNE,
            type: CalAmpIntuneApi,
        },
        {
            clientId: Microsoft365CustomApiConstants.calampMicrosoft365ClientIds,
            clientType: ClientType.MICROSOFT_365,
            type: CalAmpMicrosoft365Api,
        },
        {
            clientId: AzureCustomApiConstants.threeSixtyInsightsAzureClientIds,
            clientType: ClientType.AZURE,
            type: ThreeSixtyInsightsAzureApi,
        },
        {
            clientId: Microsoft365CustomApiConstants.SwissquoteLUMicrosoft365ClientIds,
            clientType: ClientType.MICROSOFT_365,
            type: SwissquoteLUMicrosoft365Api,
        },
        {
            clientId: Microsoft365CustomApiConstants.easypayEverywhereMicrosoft365ClientIds,
            clientType: ClientType.MICROSOFT_365,
            type: EasypayEverywhereMicrosoft365Api,
        },
        {
            clientId: BitbucketCustomApiConstants.cortoBitbucketClientIds,
            clientType: ClientType.BITBUCKET,
            type: CortoBitbucketApi,
        },
        {
            clientId: Microsoft365CustomApiConstants.transmaxMicrosoft365ClientIds,
            clientType: ClientType.MICROSOFT_365,
            type: TransmaxMicrosoft365Api,
        },
        {
            clientId: Microsoft365CustomApiConstants.readySignalMicrosoft365ClientIds,
            clientType: ClientType.MICROSOFT_365,
            type: ReadySignalMicrosoft365Api,
        },
        {
            clientId: Microsoft365CustomApiConstants.symphonyAiMicrosoft365ClientIds,
            clientType: ClientType.MICROSOFT_365,
            type: SymphonyAiMicrosoft365Api,
        },
        {
            clientId: IntuneCustomConstants.symphonyAiIntuneClientIds,
            clientType: ClientType.INTUNE,
            type: SymphonyAiIntuneApi,
        },
        {
            clientId: Microsoft365CustomApiConstants.mbcMicrosoft365ClientIds,
            clientType: ClientType.MICROSOFT_365,
            type: MbcMicrosoft365Api,
        },
        {
            clientId: Microsoft365CustomApiConstants.portimaMicrosoft365ClientIds,
            clientType: ClientType.MICROSOFT_365,
            type: PortimaMicrosoft365Api,
        },
        {
            clientId: Microsoft365CustomApiConstants.jndlaMicrosoft365ClientIds,
            clientType: ClientType.MICROSOFT_365,
            type: JndlaMicrosoft365Api,
        },
        {
            clientId: Microsoft365CustomApiConstants.milyliMicrosoft365ClientIds,
            clientType: ClientType.MICROSOFT_365,
            type: MilyliMicrosoft365Api,
        },
    ];

    /**
     *
     */
    private customHttpServices: CustomHttpService[] = [
        {
            type: CheckrApi,
            service: CheckrHttpServiceRateLimit,
        },
    ];

    constructor(
        private readonly httpService: HttpService,
        private readonly cacheService: CacheService,
        private readonly oauth2: OAuth2Adapter,
        private readonly eventBus: EventBus,
        private readonly featureFlagService: FeatureFlagService,
        private readonly downloader: Downloader,
        private readonly csvParser: CsvParser,
        private readonly fileValidationService: FileValidationService,
        private readonly inMemoryCacheService: InMemoryCacheService,
    ) {}

    async api(connection: ConnectionEntity, account: Account): Promise<IApiServices> {
        if (isNil(connection)) {
            throw new InternalServerErrorException('Connection can not be null');
        }

        if (isNil(account)) {
            throw new InternalServerErrorException('Account can not be null');
        }

        const loggingContext = {
            domain: account.domain,
            companyName: account.companyName,
            traceId: uuidv4(),
            account,
        };

        /**
         * We do not need to have a list of 50 cases for all StackOne security training providers.
         */
        if (StackOneSecurityTrainingClientTypes.includes(connection.clientType)) {
            return this.build(StackOneSecurityTrainingApi, connection, account);
        }

        switch (connection.clientType) {
            case ClientType.APIDECK:
                return this.build(ApideckApi, connection, account);

            case ClientType.ASANA:
                return this.buildOAuth2(AsanaApi, connection, account);

            case ClientType.MONGO_DB_ATLAS:
                return this.build(AtlasApi, connection, account);

            case ClientType.AWS:
                return this.build(AwsApi, connection, account);

            case ClientType.AWS_GOV_CLOUD:
                return this.build(AwsGovCloudApi, connection, account);

            case ClientType.AWS_ORG_UNITS:
                const awsOrgUnitsApi = new AwsOrgUnitsParallelizedApi(
                    connection,
                    this.httpService,
                    this.cacheService,
                    loggingContext,
                    account,
                    this,
                    this.featureFlagService,
                );

                /**
                 * initializing the SubApisManager requires the sdk to exist,
                 * which is instantiated on initialize().
                 */
                await awsOrgUnitsApi.initialize();
                return awsOrgUnitsApi;

            case ClientType.AWS_INSPECTOR:
                return this.build(AwsInspectorApi, connection, account);

            case ClientType.AZURE:
                return this.buildOAuth2(AzureApi, connection, account);

            case ClientType.AZURE_GCC_HIGH:
                return this.buildOAuth2(AzureGccHighApi, connection, account);

            case ClientType.AZURE_MG_GCC_HIGH:
                return this.buildOAuth2(AzureMgGccHighApi, connection, account);

            case ClientType.AZURE_ORG_UNITS:
                return this.buildOAuth2(AzureOrgUnitsApi, connection, account);

            case ClientType.AZURE_BOARDS:
                return this.buildOAuth2(AzureDevopsBoardsApi, connection, account);

            case ClientType.AZURE_REPOS:
                return this.buildOAuth2(AzureDevopsReposApi, connection, account);

            case ClientType.AZURE_DEVOPS:
                return this.buildOAuth2(AzureDevopsApi, connection, account);

            case ClientType.BITBUCKET:
                return this.buildOAuth2(BitbucketApi, connection, account);

            case ClientType.BITBUCKET_CODE:
                return this.buildOAuth2(BitbucketCodeApi, connection, account);

            case ClientType.CERTN:
                return this.build(CertnApi, connection, account);

            case ClientType.CHECKR:
                return this.buildOAuth2(CheckrApi, connection, account);

            case ClientType.DATADOG:
                return this.build(DatadogApi, connection, account);

            case ClientType.CLICKUP:
                return this.buildOAuth2(ClickupApi, connection, account);

            case ClientType.CLOUDFLARE:
                return new CloudFlareApi(
                    connection,
                    this.httpService,
                    this.cacheService,
                    loggingContext,
                );

            case ClientType.SHORTCUT:
                return this.build(ShortcutApi, connection, account);

            case ClientType.DIGITAL_OCEAN:
                return this.buildOAuth2(DigitalOceanApi, connection, account);

            case ClientType.FIBERY:
                return this.build(FiberyApi, connection, account);

            case ClientType.GCP:
                const api = (await this.build(GcpApi, connection, account)) as GcpBaseApi;
                api.setInMemoryCacheService(this.inMemoryCacheService);

                return api;

            case ClientType.GITHUB:
            case ClientType.GITHUB_ENTERPRISE:
            case ClientType.GITHUB_ISSUES:
            case ClientType.GITHUB_ISSUES_ENTERPRISE:
                return this.build(GitHubApi, connection, account);

            case ClientType.GITHUB_CODE:
                return this.build(GitHubCodeApi, connection, account);

            case ClientType.GITLAB:
                /**
                 * The providers override appears to have been changed and does not support
                 * simple overrides by domain. The GITLAB clientId is not a good candidate
                 * to determine overrides as it is updated frequently and is cumbersome - use
                 * this if check to return the beta github api for our release candidates
                 */
                if (GitlabCustomApiConstants.gitlabBetaApiDomains.includes(account.domain)) {
                    return this.buildOAuth2(GitlabBetaApi, connection, account);
                }

                return this.buildOAuth2(GitlabApi, connection, account);

            case ClientType.GITLAB_ON_PREM:
                return this.buildOAuth2(GitlabOnPremApi, connection, account);

            case ClientType.GITLAB_ISSUES:
                return this.buildOAuth2(GitlabIssuesApi, connection, account);

            case ClientType.GITLAB_ISSUES_ON_PREM:
                return this.buildOAuth2(*********************, connection, account);

            case ClientType.GOOGLE_ADMIN_CONSOLE:
            case ClientType.GOOGLE:
                return this.build(GoogleSdkApi, connection, account);

            case ClientType.GOOGLE_OAUTH:
            case ClientType.GOOGLE_ADMIN_CONSOLE_OAUTH:
                return this.buildOAuth2(GoogleOAuthApi, connection, account);

            case ClientType.GUSTO:
                return this.buildOAuth2(GustoApi, connection, account);

            case ClientType.HEROKU:
                return this.buildOAuth2(HerokuApi, connection, account);

            case ClientType.INTUNE:
                return this.buildOAuth2(IntuneApi, connection, account);

            case ClientType.INTUNE_GCC_HIGH:
                return this.buildOAuth2(IntuneGccHighApi, connection, account);

            case ClientType.JAMF:
                return this.buildOAuth2(JamfApi, connection, account);

            case ClientType.JIRA:
                return this.buildOAuth2(JiraApi, connection, account);

            case ClientType.JUMPCLOUD:
                return this.build(JumpCloudApi, connection, account);

            case ClientType.KANDJI:
                return this.build(KandjiApi, connection, account);

            case ClientType.HEXNODE_UEM:
                return this.build(HexnodeApi, connection, account);

            case ClientType.KARMACHECK:
                return this.build(KarmaCheckApi, connection, account);

            case ClientType.LINEAR:
                return this.buildOAuth2(LinearApi, connection, account);

            case ClientType.MERGEDEV_ADP_WORKFORCE_NOW:
            case ClientType.MERGEDEV_HR_PARTNER:
            case ClientType.MERGEDEV_HUMAANS:
            case ClientType.MERGEDEV_PERSONIO:
            case ClientType.MERGEDEV_SAP_SUCCESSFACTORS:
            case ClientType.MERGEDEV_SAGE:
            case ClientType.MERGEDEV_KALLIDUS:
            case ClientType.MERGEDEV_TRINET:
            case ClientType.MERGEDEV_TRINET_HR:
            case ClientType.MERGEDEV_FRESHTEAM:
            case ClientType.MERGEDEV_HI_BOB:
            case ClientType.MERGEDEV_JUSTWORKS:
            case ClientType.MERGEDEV_PAYLOCITY:
            case ClientType.MERGEDEV_WORKDAY:
            case ClientType.MERGEDEV_HR_CLOUD:
            case ClientType.MERGEDEV_UKG_PRO:
            case ClientType.MERGEDEV_UKG_READY:
            case ClientType.MERGEDEV_PAYCOR:
            case ClientType.MERGEDEV_NAMELY:
            case ClientType.MERGEDEV_INSPERITY_PREMIER:
            case ClientType.MERGEDEV_DAYFORCE:
            case ClientType.MERGEDEV_ALEXISHR:
            case ClientType.MERGEDEV_BREATHE:
            case ClientType.MERGEDEV_CHARLIE:
            case ClientType.MERGEDEV_CHARTHOP:
            case ClientType.MERGEDEV_DEEL:
            case ClientType.MERGEDEV_FACTORIAL:
            case ClientType.MERGEDEV_INTELLIHR:
            case ClientType.MERGEDEV_KEKA:
            case ClientType.MERGEDEV_LUCCA:
            case ClientType.MERGEDEV_OFFICIENT:
            case ClientType.MERGEDEV_PAYCHEX:
            case ClientType.MERGEDEV_PEOPLE_HR:
            case ClientType.MERGEDEV_OYSTERHR:
            case ClientType.MERGEDEV_EMPLOYMENT_HERO:
            case ClientType.MERGEDEV_ZOHO_PEOPLE:
            case ClientType.MERGEDEV_LATTICE_HRIS:
            case ClientType.MERGEDEV_DARWINBOX:
            case ClientType.MERGEDEV_LEAPSOME:
                return this.build(MergedevHrisApi, connection, account);

            case ClientType.MERGEDEV_BAMBOO_HR:
                return this.build(BambooHRApi, connection, account);

            case ClientType.MERGEDEV_SERVICENOW:
                return this.build(ServicenowApi, connection, account);

            case ClientType.MERGEDEV_JIRA_DATA_CENTER:
                return this.build(JiraDataCenterApi, connection, account);

            case ClientType.MERGEDEV_AHA:
            case ClientType.MERGEDEV_BASECAMP:
            case ClientType.MERGEDEV_BITBUCKET:
            case ClientType.MERGEDEV_TEAMWORK:
            case ClientType.MERGEDEV_HIVE:
            case ClientType.MERGEDEV_WRIKE:
            case ClientType.MERGEDEV_FRESHDESK:
            case ClientType.MERGEDEV_ZENDESK:
            case ClientType.MERGEDEV_FRESHSERVICE:
            case ClientType.MERGEDEV_HEIGHT:
            case ClientType.MERGEDEV_FRONT:
            case ClientType.MERGEDEV_ZOHO_DESK:
                return this.build(MergedevTicketingCreationApi, connection, account);

            case ClientType.MERGEDEV_ONELOGIN:
                return this.build(OneLoginApi, connection, account);

            case ClientType.MERGEDEV_JUMPCLOUD:
                return this.build(JumpCloudIdentityApi, connection, account);

            case ClientType.MERGEDEV_CYBERARK:
                return this.build(CyberArkApi, connection, account);

            case ClientType.MERGEDEV_PINGONE:
                return this.build(PingOneApi, connection, account);

            case ClientType.MICROSOFT_365:
                return this.buildOAuth2(Microsoft365Api, connection, account);

            case ClientType.MICROSOFT_365_GCC_HIGH:
                return this.buildOAuth2(Microsoft365GccHighApi, connection, account);

            case ClientType.NEW_RELIC:
                return this.build(NewRelicApi, connection, account);

            case ClientType.PIVOTAL_TRACKER:
                return this.build(PivotalTrackerApi, connection, account);

            case ClientType.RIPPLING:
                const hasMdm = connection.connectionProviderTypes.some(
                    ({ providerType, enabledAt }) =>
                        providerType === ProviderType.MDM && !isNil(enabledAt),
                );
                const hasUar = connection.connectionProviderTypes.some(
                    ({ providerType, enabledAt }) =>
                        providerType === ProviderType.USER_ACCESS_REVIEW && !isNil(enabledAt),
                );

                let apiToBuild = RipplingApi;
                if (hasMdm || hasUar) {
                    apiToBuild = RipplingMDMApi;
                }
                return this.buildOAuth2(apiToBuild, connection, account);

            case ClientType.TARGET_PROCESS:
                return this.build(TargetProcessApi, connection, account);

            case ClientType.TRELLO:
                return this.build(TrelloApi, connection, account);
            // Remove WorkOS connection test
            case ClientType.WORK_OS:
                return this.build(WorkOsApi, connection, account);

            case ClientType.ZOHO:
                return this.buildOAuth2(ZohoApi, connection, account);

            case ClientType.OKTA_IDENTITY:
                return this.build(OktaIdentityApi, connection, account);

            case ClientType.AWS_CODECOMMIT:
                return this.build(AwsCodeCommitApi, connection, account);

            case ClientType.CURRICULA:
                return this.buildOAuth2(CurriculaApi, connection, account);

            case ClientType.KNOWBE4:
                return this.build(KnowBe4Api, connection, account);

            case ClientType.RIPPLING_MDM:
                return this.buildOAuth2(RipplingMDMApi, connection, account);

            case ClientType.DOCUSIGN:
                return this.buildOAuth2(DocuSignApi, connection, account);

            case ClientType.SLACK:
                return this.buildOAuth2(SlackApi, connection, account);

            case ClientType.MICROSOFT_TEAMS:
                return this.buildOAuth2(MicrosoftTeamsApi, connection, account);

            case ClientType.CONFLUENCE:
                return this.buildOAuth2(ConfluenceApi, connection, account);

            case ClientType.WORKSPACE_ONE:
                return this.buildOAuth2(WorkspaceONEApi, connection, account);

            case ClientType.RAPID7:
                return this.build(Rapid7Api, connection, account);
            case ClientType.CSV_IDP:
                return new IdpFileApi(
                    connection,
                    this.httpService,
                    this.cacheService,
                    loggingContext,
                    account,
                    this.downloader,
                    this.eventBus,
                    this.csvParser,
                );
            case ClientType.SENTINEL_ONE:
                return this.build(SentinelOneApi, connection, account);
            case ClientType.HUBSPOT:
                return this.buildOAuth2(HubspotApi, connection, account);
            case ClientType.SNOWFLAKE:
                return this.buildOAuth2(SnowflakeApi, connection, account);
            case ClientType.MIRO:
                return this.buildOAuth2(MiroApi, connection, account);
            case ClientType.ZOOM:
                return this.buildOAuth2(ZoomApi, connection, account);
            case ClientType.AUTH0:
                return this.buildOAuth2(Auth0Api, connection, account);
            case ClientType.SEGMENT:
                return this.build(SegmentApi, connection, account);
            case ClientType.SENTRY:
                return this.build(SentryApi, connection, account);
            case ClientType.CROWDSTRIKE:
                return this.buildOAuth2(CrowdStrikeApi, connection, account);
            case ClientType.XERO:
                return this.buildOAuth2(XeroApi, connection, account);
            case ClientType.PAGER_DUTY:
                return this.build(PagerDutyApi, connection, account);
            case ClientType.ATLASSIAN:
                return this.buildOAuth2(AtlassianApi, connection, account);
            case ClientType.WIZ:
                return this.build(WizApi, connection, account);
            case ClientType.VERCEL:
                return this.build(VercelApi, connection, account);
            case ClientType.STACKONE_SMARTRECRUITERS:
            case ClientType.STACKONE_TEAMTAILOR:
            case ClientType.STACKONE_ASHBY:
            case ClientType.STACKONE_ATTIO:
            case ClientType.STACKONE_CONTENTFUL:
            case ClientType.STACKONE_KLAVIYO:
            case ClientType.STACKONE_LASTPASS:
            case ClientType.STACKONE_LEAPSOME:
            case ClientType.STACKONE_LEVER:
            case ClientType.STACKONE_ORACLEHCM:
            case ClientType.STACKONE_PINPOINT:
            case ClientType.STACKONE_PIPEDRIVE:
            case ClientType.STACKONE_RECRUITEE:
            case ClientType.STACKONE_WEBEX:
            case ClientType.STACKONE_WORKABLE:
            case ClientType.STACKONE_ZELT:
            case ClientType.STACKONE_BITWARDEN:
            case ClientType.STACKONE_SALESLOFT:
            case ClientType.STACKONE_DIXA:
            case ClientType.STACKONE_FRESHSALES:
            case ClientType.STACKONE_CANVA:
            case ClientType.STACKONE_GREENHOUSE:
            case ClientType.STACKONE_AUTODESK:
            case ClientType.STACKONE_ELASTIC:
            case ClientType.STACKONE_RENDER:
            case ClientType.STACKONE_TERRAFORM:
            case ClientType.STACKONE_DOMO:
            case ClientType.STACKONE_ENVOY:
            case ClientType.STACKONE_SCALEWAY:
            case ClientType.STACKONE_JETBRAINS:
            case ClientType.STACKONE_FIVETRAN:
            case ClientType.STACKONE_INTERCOM:
            case ClientType.STACKONE_1PASSWORD:
            case ClientType.STACKONE_MIXPANEL:
            case ClientType.STACKONE_OPTIMIZELY:
            case ClientType.STACKONE_TWILIO:
            case ClientType.STACKONE_ANSIBLE:
            case ClientType.STACKONE_LATTICE:
            case ClientType.STACKONE_WEBFLOW:
            case ClientType.STACKONE_BOX:
            case ClientType.STACKONE_DROPBOX:
            case ClientType.STACKONE_DROPBOX_SIGN:
            case ClientType.STACKONE_HARVEST:
            case ClientType.STACKONE_KAMELEOON:
            case ClientType.STACKONE_MAKE:
            case ClientType.STACKONE_RETOOL:
            case ClientType.STACKONE_TOGGL:
            case ClientType.STACKONE_ANTHROPIC:
            case ClientType.STACKONE_LACEWORK:
            case ClientType.STACKONE_DATABRICKS:
            case ClientType.STACKONE_IFS:
            case ClientType.STACKONE_TRAVISCI:
            case ClientType.STACKONE_MATILLIONETL:
            case ClientType.STACKONE_SONARCLOUD:
            case ClientType.STACKONE_AIRCALL:
            case ClientType.STACKONE_15FIVE:
            case ClientType.STACKONE_ROLLBAR:
            case ClientType.STACKONE_EGNYTE:
            case ClientType.STACKONE_QLIK:
            case ClientType.STACKONE_BULLHORN:
            case ClientType.STACKONE_OPENVPN:
            case ClientType.STACKONE_SOPHOS:
            case ClientType.STACKONE_MEISTERTASK:
            case ClientType.STACKONE_TALENTLMS:
            case ClientType.STACKONE_ONEFLOW:
            case ClientType.STACKONE_RING_CENTRAL:
            case ClientType.STACKONE_ARTICULATE:
            case ClientType.STACKONE_DIALPAD:
            case ClientType.STACKONE_TABLEAU:
            case ClientType.STACKONE_DUO:
            case ClientType.STACKONE_GONG:
            case ClientType.STACKONE_IRONCLAD:
            case ClientType.STACKONE_SCORO:
            case ClientType.STACKONE_TEAMVIEWER_REMOTE:
            case ClientType.STACKONE_SPOTDRAFT:
            case ClientType.STACKONE_SPENDESK:
            case ClientType.STACKONE_SENDGRID:
            case ClientType.STACKONE_SMARTSHEET:
            case ClientType.STACKONE_CHECKMK:
            case ClientType.STACKONE_OPENAI:
            case ClientType.STACKONE_NETLIFY:
                return this.build(StackOneUarApi, connection, account);
            case ClientType.NOTION:
                return this.buildOAuth2(NotionApi, connection, account);
            case ClientType.COVERDASH:
                return this.build(CoverdashApi, connection, account);
            case ClientType.SALESFORCE:
            case ClientType.SALESFORCE_UAR:
                return this.buildOAuth2(SalesforceApi, connection, account);
            case ClientType.STERLING:
                // not a http-based API, instantiate here
                return new SterlingApi(
                    connection,
                    this.httpService,
                    this.cacheService,
                    {
                        domain: account.domain,
                        companyName: account.companyName,
                        traceId: uuidv4(),
                    },
                    this.downloader,
                    account,
                    this.eventBus,
                    this.fileValidationService,
                );
            case ClientType.HIRERIGHT:
                // not a http-based API, instantiate here
                return new HireRightApi(
                    connection,
                    this.httpService,
                    this.cacheService,
                    {
                        domain: account.domain,
                        companyName: account.companyName,
                        traceId: uuidv4(),
                    },
                    this.downloader,
                    account,
                    this.eventBus,
                    this.fileValidationService,
                );
            case ClientType.VETTY:
                return this.build(VettyApi, connection, account);
            case ClientType.LEEN_TENABLE:
                return this.build(TenableApi, connection, account);
            case ClientType.LEEN_QUALYS:
                return this.build(QualysApi, connection, account);
            case ClientType.LEEN_SEMGREP:
                return this.build(SemgrepApi, connection, account);
            case ClientType.LEEN_SNYK:
                return this.build(SnykApi, connection, account);
            case ClientType.LEEN_ARNICA:
                return this.build(ArnicaApi, connection, account);
            case ClientType.LEEN_SENTINELONE_VMS:
                return this.build(SentinelOneVMApi, connection, account);
            case ClientType.LEEN_CROWDSTRIKE_VMS:
                return this.build(CrowdStrikeSpotlightApi, connection, account);
            case ClientType.LEEN_MS_DEFENDER_VMS:
                return this.build(MicrosoftDefenderApi, connection, account);
            case ClientType.LEEN_RAPID7_VMS:
                return this.build(Rapid7VmsApi, connection, account);
            case ClientType.LEEN_AIKIDO:
                return this.build(AikidoApi, connection, account);
            case ClientType.LEEN_WIZ_VMS:
                return this.build(WizCloudApi, connection, account);
            case ClientType.LEEN_WIZ_CODE:
                return this.build(WizCodeApi, connection, account);
            case ClientType.BAMBOO_HR:
                return this.buildOAuth2(BambooHrNativeApi, connection, account);
            case ClientType.CUSTOM:
                return this.build(CustomDataApi, connection, account);
            case ClientType.KOLIDE:
                return this.build(KolideApi, connection, account);
            // CLI_CLIENT_FACTORY_TAG_KEY
            default:
                throw new InternalServerErrorException(
                    `ClientType ${ClientType[connection.clientType]} is not setup yet`,
                );
        }
    }

    /**
     *
     * @param target
     * @param connection
     * @returns
     */
    private async build(
        target: Type<IApiServices>,
        connection: ConnectionEntity,
        account: Account,
    ): Promise<IApiServices> {
        const customApiProvider = this.getCustomProvider(target, connection, account);

        if (!isNil(customApiProvider)) {
            target = customApiProvider.type;
        }

        return new target(
            connection,
            this.httpService,
            this.cacheService,
            {
                domain: account.domain,
                companyName: account.companyName,
                traceId: uuidv4(),
            },
            account,
            this.featureFlagService,
            this.eventBus,
        );
    }

    private getCustomHttpService(target: Type<IApiServices>): CustomHttpService {
        return this.customHttpServices.find((provider: CustomHttpService) => {
            return provider.type === target;
        });
    }

    /**
     *
     * @param target
     * @param connection
     * @param account
     * @returns
     */
    private async buildOAuth2(
        target: Type<IApiServices>,
        connection: ConnectionEntity,
        account: Account,
    ): Promise<IApiServices> {
        const customApiProvider = this.getCustomProvider(target, connection, account);
        const customHttpService = this.getCustomHttpService(target);
        let httpService = this.httpService;

        if (!isNil(customApiProvider)) {
            target = customApiProvider.type;
        }

        if (!isNil(customHttpService)) {
            const service = customHttpService.service;
            httpService = new service().getHttpService();
        }

        return new target(
            this.oauth2,
            connection,
            httpService,
            this.cacheService,
            {
                domain: account.domain,
                companyName: account.companyName,
                traceId: uuidv4(),
                account,
            },
            this.eventBus,
            account,
            this.featureFlagService,
        );
    }

    /**
     *
     * @param target
     * @param connection
     * @returns
     */
    private getCustomProvider(
        target: Type<IApiServices>,
        connection: ConnectionEntity,
        account: Account,
    ): CustomApiProvider {
        return this.customApiProviders.find((provider: CustomApiProvider) => {
            return (
                provider.clientType === connection.clientType &&
                ((provider.clientAlias === connection.clientAlias &&
                    provider.domain === account.domain) ||
                    provider.clientId.includes(connection.clientId))
            );
        });
    }
}
