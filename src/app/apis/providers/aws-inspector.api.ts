import { VulnerabilitySeverity } from '@drata/enums';
import { AwsSetupType } from 'app/apis/enums/aws-setup-type.enum';
import { IVulnerabilityFindingsServices } from 'app/apis/interfaces/vulnerability-finding-services.interface';
import { Api } from 'app/apis/providers/api';
import { AwsInspectorMonitoringData } from 'app/apis/services/aws-inspector/aws-inspector-monitoring-data';
import { AwsInspectorSdk } from 'app/apis/services/aws-inspector/aws-inspector.sdk';
import { ServicePageableType as Paginated } from 'app/apis/types/service/service-pageable.type';
import { ConnectionEntity } from 'app/companies/connections/entities/connection.entity';
import { VulnerabilityFindingEntity } from 'app/vulnerability/entities/monitoring/vulnerability-finding.entity';
import { VulnerabilityInfrastructureInfoType } from 'app/vulnerability/types/monitoring/vulnerability-infrastructure-info.type';
import { VulnerabilitySlaConfig } from 'app/vulnerability/types/monitoring/vulnerability-sla-config.type';
import { Account } from 'auth/entities/account.entity';
import { PolloAdapter } from 'commons/adapters/pollo.adapter';
import { ApiDataSource } from 'commons/enums/api-data-source.enum';
import { VulnerabilityServiceModeType } from 'commons/enums/vulnerability-service-mode.enum';
import { getAccountAlias, isAwsGovCloudAccount, resolveClientId } from 'commons/helpers/aws.helper';
import config from 'config';
import { AwsClientBuilder } from 'dependencies/aws/aws-client.builder';
import { isNil } from 'lodash';

export class AwsInspectorApi extends Api implements IVulnerabilityFindingsServices {
    private awsClientBuilder: AwsClientBuilder;

    private parentOrgClientBuilder: AwsClientBuilder | null;

    private sdk: AwsInspectorSdk;

    private accountAlias: string;

    async initialize(): Promise<void> {
        if (isAwsGovCloudAccount(this.metadata.key)) {
            await this.initializeGovCloudArn();
        } else {
            await this.initializeAwsArn();
        }

        this.accountAlias = await getAccountAlias(this.awsClientBuilder);
    }

    ping(): Promise<boolean> {
        const { vulnerability } = this.connection.getMetadata();
        const regions = vulnerability?.regions ?? [];
        return this.getSdk().ping(regions);
    }

    async getClientAlias(): Promise<string> {
        return this.accountAlias;
    }

    getClientId(): Promise<string> {
        return Promise.resolve(resolveClientId(this.connection) ?? '');
    }

    getDataSource(): ApiDataSource {
        return ApiDataSource.AWS_INSPECTOR;
    }

    getSource(): ApiDataSource {
        return ApiDataSource.AWS_INSPECTOR;
    }

    getNextPageToken(region: string): string {
        return `${AwsInspectorSdk.REGION_TOKEN_PREFIX}${region}`;
    }

    getServiceMode(): VulnerabilityServiceModeType {
        return VulnerabilityServiceModeType.FINDINGS;
    }

    getInfrastructureInfo(key: string): Promise<VulnerabilityInfrastructureInfoType> {
        return this.getSdk().getInfrastructureInfo(key);
    }

    getFindingIdsToUpdate(findings: VulnerabilityFindingEntity[]): string[] {
        return findings.map(finding => finding.findingId).filter(finding => finding);
    }

    listFindings(
        regions: string[],
        severity: VulnerabilitySeverity[],
        firstFound: Date,
        slaMatrix: VulnerabilitySlaConfig[],
        nextPageToken: string,
        maxResults: number,
    ): Promise<Paginated<AwsInspectorMonitoringData>> {
        return this.getSdk().listFindings(
            regions,
            severity,
            firstFound,
            slaMatrix,
            nextPageToken,
            maxResults,
        );
    }

    listFindingsById(
        ids: string[],
        slaMatrix: VulnerabilitySlaConfig[],
        region: string,
    ): Promise<AwsInspectorMonitoringData[]> {
        return this.getSdk().listFindingsById(ids, slaMatrix, region);
    }

    hasCoverage(regions: string[]): Promise<boolean> {
        return this.getSdk().hasCoverage(regions);
    }

    getMaxResults(): number {
        return config.get('pagination.token.max');
    }

    getMaxResultsUpdate(): number {
        return config.get('aws.inspector.maxUpdateRecords');
    }

    getMaxParallelizedResultsToUpdate(): number {
        return config.get('aws.inspector.maxParallelizedResultsToUpdate');
    }

    getMetadataForDisplay(): Map<string, any> {
        const metadata = new Map();
        metadata['key'] = this.metadata['key'];
        metadata['accountId'] = this.connection.accountId;
        metadata['showAggregateView'] = this.metadata.showAggregateView;
        return metadata;
    }

    private getSdk(): AwsInspectorSdk {
        const client = this.parentOrgClientBuilder ?? this.awsClientBuilder;
        if (isNil(this.sdk)) {
            this.sdk = new AwsInspectorSdk(client);
        }

        return this.sdk;
    }

    cleanConnection(account: Account, connection: ConnectionEntity) {
        this.logger.log(
            PolloAdapter.cxt('No cleanup implemented for this provider', this.loggingContext)
                .setContext(this.constructor.name)
                .setSubContext(this.cleanConnection.name)
                .setMetadata({
                    connectionId: connection.id,
                }),
        );
    }

    private async initializeAwsArn() {
        const params = {
            RoleArn: this.metadata.key,
            ExternalId: this.metadata.externalId,
            RoleSessionName: config.get('aws.inspector.roleSessionName'),
        };

        this.awsClientBuilder = new AwsClientBuilder(
            params,
            AwsSetupType.STANDARD,
            this.loggingContext,
        );
        await this.awsClientBuilder.buildConfiguration();
        this.awsClientBuilder.setDefaultRegion(config.get('aws.region.default'));

        if (!isNil(this.metadata.organizationRole)) {
            const paramsForParentOrg = {
                RoleArn: this.metadata.organizationRole,
                ExternalId: this.metadata.externalId,
                RoleSessionName: config.get('aws.inspector.roleSessionName'),
            };

            this.parentOrgClientBuilder = new AwsClientBuilder(
                paramsForParentOrg,
                AwsSetupType.STANDARD,
                this.loggingContext,
            );
            await this.parentOrgClientBuilder.buildConfiguration();
            this.parentOrgClientBuilder.setDefaultRegion(config.get('aws.region.default'));
        } else {
            this.parentOrgClientBuilder = null;
        }
    }

    private async initializeGovCloudArn() {
        const params = {
            RoleArn: this.metadata.key,
            ExternalId: this.metadata.externalId,
            RoleSessionName: config.get(`aws.govcloud.role.name`),
        };

        this.awsClientBuilder = new AwsClientBuilder(
            params,
            AwsSetupType.GOV_CLOUD,
            this.loggingContext,
        );
        await this.awsClientBuilder.buildConfiguration();
        this.awsClientBuilder.setDefaultRegion(config.get('aws.govcloud.region.default'));
    }
}
