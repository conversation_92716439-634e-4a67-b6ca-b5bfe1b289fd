import { HttpService } from '@nestjs/axios';
import { HttpStatus } from '@nestjs/common';
import { EventBus } from '@nestjs/cqrs';
import { MobileDeviceManagementData } from 'app/apis/classes/api-data/mobile-device-management-data.class';
import { ApiResponse } from 'app/apis/classes/api/api-response.class';
import { CompliantStatus } from 'app/apis/enums/intune/intune-compliant-status.enum';
import { PrefetchedDeviceDataType } from 'app/apis/enums/intune/prefetched-device-data-type.enum';
import { IMobileDeviceManagementServices } from 'app/apis/interfaces/mobile-device-management-services.interface';
import { IServiceRunner } from 'app/apis/interfaces/service-runner.interface';
import { OAuth2Api } from 'app/apis/providers/oauth2.api';
import { IntuneComputerInventoryService } from 'app/apis/services/intune/intune-computer-inventory.service';
import { IntuneMapper } from 'app/apis/services/intune/intune.mapper';
import { PingService } from 'app/apis/services/intune/ping.service';
import {
    CompanyConfiguration,
    CompleteDeviceData,
    CompleteDeviceDataPage,
    ManyDataTypes,
} from 'app/apis/types/intune/intune.types';
import { ConnectionMetadata } from 'app/companies/connections/classes/connection-metadata.class';
import { ConnectionEntity } from 'app/companies/connections/entities/connection.entity';
import { DeviceFailedEvent } from 'app/devices/observables/events/device-failed.event';
import { Account } from 'auth/entities/account.entity';
import { CacheService } from 'cache/cache.service';
import { PolloAdapter } from 'commons/adapters/pollo.adapter';
import { LoggingContext } from 'commons/adapters/types/logging-context.type';
import { ApiDataSource } from 'commons/enums/api-data-source.enum';
import { ClientType } from 'commons/enums/auth/client-type.enum';
import { MobileDeviceManagementSourceType } from 'commons/enums/mdm-source-type.enum';
import {
    getStatusFromResponseError,
    isNotUnauthorizedRequestError,
    isRequestInvalidAuthenticationError,
} from 'commons/helpers/http.helper';
import { PaginationResponse } from 'commons/helpers/pagination/pagination.response';
import { TokenPagination } from 'commons/helpers/pagination/token.pagination';
import { TokenPaginationResponse } from 'commons/helpers/pagination/token.pagination.response';
import config from 'config';
import { FeatureFlagCategory } from 'feature-flags/enums/feature-flag-category.enum';
import { FeatureFlag } from 'feature-flags/feature-flag.enum';
import { FeatureFlagService } from 'feature-flags/feature-flag.service';
import { cloneDeep, get, isEmpty, isNil } from 'lodash';
import { OAuth2Adapter } from 'oauth2/adapters/oauth2.adapter';

export class IntuneApi extends OAuth2Api implements IMobileDeviceManagementServices {
    /**
     *
     */
    protected clientDeviceIds = new Set<string>();
    /**
     *
     */
    protected hasErrorsPullingDevices = false;

    /**
     *
     */
    protected intuneComputerInventoryService: IntuneComputerInventoryService;

    /**
     *
     */
    protected companyConfiguration: CompanyConfiguration;

    /**
     *
     * @param connection
     * @param httpService
     * @param cacheService
     * @param loggingContext
     */
    constructor(
        oauth2: OAuth2Adapter,
        connection: ConnectionEntity,
        httpService: HttpService,
        cacheService: CacheService,
        loggingContext: LoggingContext,
        eventBus: EventBus,
        account: Account,
        private readonly featureFlagService: FeatureFlagService,
    ) {
        super(oauth2, connection, httpService, cacheService, loggingContext, eventBus);
    }

    /**
     *
     */
    getPingService(): IServiceRunner {
        const metadata = this.connection.getMetadata();
        return PingService.get(metadata, this.getClientType());
    }

    /**
     *
     */
    getDataSource(): ApiDataSource {
        return ApiDataSource.INTUNE;
    }

    /**
     *
     */
    getClientType(): ClientType {
        return ClientType.INTUNE;
    }

    /**
     *
     */
    getMdmSourceType(): MobileDeviceManagementSourceType {
        return MobileDeviceManagementSourceType.INTUNE;
    }

    /**
     *
     */
    async getClientId(): Promise<string> {
        // tenant id will act as client id
        const { refreshToken } = this.connection.getMetadata();
        return `{${refreshToken}}`;
    }

    /**
     *
     * @returns
     */
    tokenObserverImplemented(): boolean {
        return true;
    }

    /**
     *
     * @returns
     */
    async getClientAlias(): Promise<string> {
        return null;
    }

    /**
     * @TODO REMOVE/clean this method after https://drata.atlassian.net/browse/ENG-16006 gets deployed
     * @param metadata
     */
    async getClient(metadata: ConnectionMetadata): Promise<OAuth2Api> {
        // force new access token every time we init client
        const accessToken = await this.oauth2.getAccessToken(
            this.getClientType(),
            metadata.refreshToken ?? metadata.key,
            metadata,
        );

        metadata.key = accessToken.accessToken;
        metadata.refreshToken = accessToken.refreshToken;
        metadata.expiresAt = accessToken.expiresAt;
        metadata.scope = accessToken.scope;
        this.metadata = metadata;

        const cloneMeta = cloneDeep(this.metadata);
        this.connection.setMetadata(cloneMeta);

        return this;
    }

    /**
     *
     * @param pagination
     * @param account
     * @returns
     */
    async getAssets(
        pagination: TokenPagination,
        account: Account,
    ): Promise<ApiResponse<TokenPaginationResponse<MobileDeviceManagementData>>> {
        if (isEmpty(this.companyConfiguration)) {
            await this.setCompanyConfiguration(account);
        }

        this.logger.log(
            PolloAdapter.cxt('Getting Intune assets', this.loggingContext)
                .setContext(this.constructor.name)
                .setSubContext(this.getAssets.name)
                .setIdentifier({
                    pagination,
                }),
        );

        let response: CompleteDeviceDataPage;

        try {
            response = await this.getPaginatedAssets(pagination, account);
        } catch (e) {
            const isUnauthorized = get(e, 'statusCode', null) === HttpStatus.UNAUTHORIZED;

            if (isUnauthorized && isRequestInvalidAuthenticationError(e)) {
                this.logger.warn(
                    PolloAdapter.cxt(
                        'InvalidTokenAuthentication error, refreshing token',
                        this.loggingContext,
                    )
                        .setContext(this.constructor.name)
                        .setSubContext(this.getAssets.name)
                        .setError(e),
                );

                await this.updateMetadata();

                // TODO: need error handling
                response = await this.getPaginatedAssets(pagination, account);
            } else {
                this.logger.error(
                    PolloAdapter.cxt(e.message, this.loggingContext)
                        .setContext(this.constructor.name)
                        .setSubContext(this.getAssets.name)
                        .setError(e),
                );

                throw e;
            }
        }

        pagination.setNewNextPageToken(response?.nextLink ?? null);

        this.logger.log(
            PolloAdapter.cxt('Intune assets', this.loggingContext)
                .setIdentifier({
                    pagination,
                })
                .setContext(this.constructor.name)
                .setSubContext(this.getAssets.name),
        );

        this.showDevicesWithUnknownPolicyState(account, response.data);

        return this.tokenPaginatedResponse(
            IntuneMapper.inventory(
                response.data,
                pagination.getNextPageToken(),
                account,
                this.getMdmSourceType(),
            ),
        );
    }

    /**
     *
     * @param account
     */
    async setCompanyConfiguration(account: Account): Promise<void> {
        const companyCacheKey = `${account.id}-intune-company-configuration`;

        let companyConfig = await this.getFromCache<CompanyConfiguration>(companyCacheKey);

        if (!isNil(companyConfig)) {
            this.logger.log(
                PolloAdapter.acct(`Company configuration fetched from Cache`, account)
                    .setContext(this.constructor.name)
                    .setSubContext(this.setCompanyConfiguration.name),
            );

            this.companyConfiguration = companyConfig;
        }

        const customPoliciesValues =
            await this.getIntuneComputerInventoryService().getCustomPoliciesForWindows();

        const { antivirusScriptId, customProfilesNamesForMac, fileVaultsValues } =
            await this.getIntuneComputerInventoryService().getAssetScriptsAndProfiles(account);

        const intuneAccountId =
            await this.getIntuneComputerInventoryService().getIntuneAccountId(account);

        companyConfig = {
            customPoliciesValues,
            antivirusScriptId,
            customProfilesNamesForMac,
            fileVaultsValues,
            intuneAccountId,
        };

        /**
         * Cache the projects for 3 hours
         */
        await this.setCache(companyCacheKey, companyConfig, 60 * 60 * 3);

        /**
         * set company-level configuration
         */
        this.companyConfiguration = companyConfig;
    }

    /**
     *
     * @param pagination
     * @param account
     * @returns
     */
    protected async getPaginatedAssets(
        pagination: TokenPagination,
        account: Account,
    ): Promise<CompleteDeviceDataPage> {
        const { clientDevices, nextLink } =
            await this.getIntuneComputerInventoryService().getAllClientDevices(pagination, account);

        const response: CompleteDeviceDataPage = {
            data: [],
            nextLink,
        };

        const {
            customPoliciesValues,
            antivirusScriptId,
            customProfilesNamesForMac,
            fileVaultsValues,
            intuneAccountId,
        } = this.companyConfiguration;

        try {
            await this.getIntuneComputerInventoryService().setCompliancePoliciesToCache(
                account,
                this.getMaxResults(),
            );
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(`Could not get paginated data`, account)
                    .setContext(this.constructor.name)
                    .setSubContext(this.getPaginatedAssets.name)
                    .setError(error),
            );
        }

        const intuneBatchCallsEnabled = await this.featureFlagService.evaluateAs(
            {
                category: FeatureFlagCategory.NONE,
                defaultValue: false,
                name: FeatureFlag.RELEASE_MDM_INTUNE_API_BATCH_CALLS,
            },
            account,
        );
        this.logger.log(
            PolloAdapter.acct(`Feature flag RELEASE_MDM_INTUNE_API_BATCH_CALLS state`, account)
                .setContext(this.constructor.name)
                .setSubContext(this.getPaginatedAssets.name)
                .setIdentifier({
                    state: intuneBatchCallsEnabled,
                }),
        );

        let prefetchedDevicesData = new Map<PrefetchedDeviceDataType, Map<string, ManyDataTypes>>();
        if (intuneBatchCallsEnabled) {
            prefetchedDevicesData =
                await this.getIntuneComputerInventoryService().prefetchDevicesData(
                    account,
                    clientDevices,
                    antivirusScriptId,
                    customPoliciesValues,
                );
        }

        for (const clientDevice of clientDevices) {
            try {
                const assetData =
                    // eslint-disable-next-line no-await-in-loop
                    await this.getIntuneComputerInventoryService().getAssetData({
                        clientDevice,
                        account,
                        antivirusScriptId,
                        customPoliciesValues,
                        fileVaultsValues,
                        customProfilesNamesForMac,
                        intuneAccountId,
                        intuneBatchCallsEnabled,
                        prefetchedDevicesData,
                    });

                if (!isNil(assetData)) {
                    this.clientDeviceIds.add(assetData.id);
                    response.data.push(assetData);
                }
            } catch (e) {
                this.hasErrorsPullingDevices = true;
                this.eventBus.publish(
                    new DeviceFailedEvent(
                        account,
                        clientDevice.id,
                        clientDevice.userPrincipalName,
                        this.connection,
                    ),
                );

                this.logger.error(
                    PolloAdapter.cxt(
                        `Skipping ${clientDevice.id} device because of error: ${e.message}`,
                        this.loggingContext,
                    )
                        .setContext(this.constructor.name)
                        .setSubContext(this.getPaginatedAssets.name)
                        .setError(e),
                );

                if (
                    isRequestInvalidAuthenticationError(e) ||
                    isNotUnauthorizedRequestError(e) ||
                    getStatusFromResponseError(e) === HttpStatus.INTERNAL_SERVER_ERROR
                ) {
                    // eslint-disable-next-line no-await-in-loop
                    await this.updateMetadata();
                    continue;
                }
            }
        }

        return response;
    }

    /**
     *
     * @returns
     */
    protected getIntuneComputerInventoryService() {
        if (isNil(this.intuneComputerInventoryService)) {
            this.intuneComputerInventoryService = new IntuneComputerInventoryService(
                this.connection.getMetadata(),
                this.getClientType(),
                this.featureFlagService,
            );
        } else {
            this.intuneComputerInventoryService.refreshMetadata(this.connection.getMetadata());
        }

        return this.intuneComputerInventoryService;
    }

    /**
     *
     * @returns
     */
    getMaxResults(): number {
        return config.get('sync.maxResults');
    }

    /**
     *
     * @returns
     */
    getMetadataForDisplay(): Map<string, any> {
        const metadata = new Map();
        metadata['key'] = '*';
        metadata['clientKey'] = '*';
        metadata['clientSecret'] = '*';

        return metadata;
    }

    /**
     *
     * @returns {Promise<string[]>}
     */
    async getRemoteExternalIds(): Promise<string[]> {
        const deviceExternalIds = Array.from(this.clientDeviceIds);

        this.logger.log(
            PolloAdapter.cxt('Got list of externalIds for active devices', this.loggingContext)
                .setContext(this.constructor.name)
                .setSubContext(this.getRemoteExternalIds.name)
                .setIdentifier({
                    count: deviceExternalIds.length,
                }),
        );

        return deviceExternalIds;
    }

    async getDevicesToDelete(): Promise<
        ApiResponse<PaginationResponse<MobileDeviceManagementData>>
    > {
        this.logger.warn(
            PolloAdapter.cxt(
                'Intune found some errors while pulling devices, skipping device deletion',
                this.loggingContext,
            )
                .setContext(this.constructor.name)
                .setSubContext(this.getDevicesToDelete.name),
        );
        return Promise.resolve({
            data: { page: [] },
            nextPageToken: null,
        } as unknown as ApiResponse<PaginationResponse<MobileDeviceManagementData>>);
    }

    deleteByDifference(): Promise<boolean> {
        return Promise.resolve(!this.hasErrorsPullingDevices);
    }

    showDevicesWithUnknownPolicyState(account: Account, devices: CompleteDeviceData[]): void {
        const policiesKeys = [
            'firewall',
            'diskEncrypted',
            'gatekeeper',
            'screenlock',
            'passwordRequired',
            'antivirusServices',
        ];

        /**
         * extract policies from devices
         */
        try {
            devices.forEach(device => {
                const policies: any = [];

                /**
                 * find policies keys in device data and store policies
                 */
                policiesKeys.forEach(policyKey => {
                    if (device.hasOwnProperty(policyKey)) {
                        if (Array.isArray(device[policyKey])) {
                            policies.push(...device[policyKey]);
                        } else {
                            policies.push(device[policyKey]);
                        }
                    }
                });

                /**
                 * select policies with unknown status/state
                 */
                const policiesWithUnknownStatus = policies.filter(policy => {
                    const state = get(policy, 'state', get(policy, 'status', null));
                    return state === CompliantStatus.UNKNOWN;
                });

                if (!isEmpty(policiesWithUnknownStatus)) {
                    this.logger.warn(
                        PolloAdapter.acct(
                            `There are policies with unknown state in device.`,
                            account,
                        )
                            .setContext(this.constructor.name)
                            .setSubContext(this.showDevicesWithUnknownPolicyState.name)
                            .setIdentifier({
                                user: device.primaryUser?.userPrincipalName,
                                deviceName: device.deviceName,
                                policies: policiesWithUnknownStatus,
                            }),
                    );
                }
            });
        } catch (error) {
            this.logger.warn(
                PolloAdapter.cxt(
                    `Something went wrong while getting device policies with unknown status.`,
                    this.loggingContext,
                )
                    .setContext(this.constructor.name)
                    .setSubContext(this.showDevicesWithUnknownPolicyState.name)
                    .setError(error),
            );
        }
    }
}
