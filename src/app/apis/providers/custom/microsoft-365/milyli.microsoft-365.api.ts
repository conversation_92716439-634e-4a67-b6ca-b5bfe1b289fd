/* eslint-disable no-await-in-loop */
import { MicrosoftIdentityUser } from 'app/apis/classes/microsoft-identity/microsoft-identity-user.class';
import { Microsoft365Api } from 'app/apis/providers/microsoft-365.api';
import { ServicePageableType as Pageable } from 'app/apis/types/service/service-pageable.type';
import { ConnectionMetadata } from 'app/companies/connections/classes/connection-metadata.class';
import { resolveNames } from 'commons/helpers/name-resolver.helper';
import { iEndsWith } from 'commons/helpers/string.helper';
import { filter, get, has, isEmpty, isNil, replace } from 'lodash';

export class MilyliMicrosoft365Api extends Microsoft365Api {
    /**
     *
     * @returns
     */
    shouldRunGroupSync(): boolean {
        return true;
    }

    /**
     *
     * @param metadata
     * @param domain
     * @param pageSize
     * @param nextPageToken
     * @param isMultiDomain
     * @param policyApplicationTypes
     * @returns
     */
    async users(
        metadata: ConnectionMetadata,
        domain: string,
        pageSize: number,
        nextPageToken: string,
        isMultiDomain: boolean,
        policyApplicationTypes: string[],
    ): Promise<Pageable<MicrosoftIdentityUser>> {
        const { securityDefaultSettingEnabled, policies } =
            await this.getSecurityDefaultAndPolicies(policyApplicationTypes);

        const { users, userGroupMap } = await this.getMsftUsers(metadata.groupIdList, pageSize);
        const msftUsers = MilyliMicrosoft365Api.users(users, domain, isMultiDomain);

        await this.fetchAndSetMfa(
            msftUsers,
            pageSize,
            policies,
            securityDefaultSettingEnabled,
            userGroupMap,
        );

        await this.fetchMembershipsAndRoles(msftUsers, pageSize);

        return {
            token: null,
            data: msftUsers,
        };
    }

    /**
     *
     * @param users
     * @param domain
     * @param isMultiDomain
     * @returns
     */
    private static users(
        users: any,
        domain: string,
        isMultiDomain: boolean,
        mfaEnforced = false,
    ): MicrosoftIdentityUser[] {
        let data: MicrosoftIdentityUser[] = [];

        if (isEmpty(users)) {
            return data;
        }

        data = users
            .filter(
                (user: { accountEnabled: boolean }) =>
                    has(user, 'accountEnabled') && user.accountEnabled,
            )
            .map((user: any) => {
                const createdAt = user.employeeHireDate ?? user.createdDateTime;

                const { firstName, lastName } = resolveNames(
                    user.givenName,
                    user.surname,
                    user.displayName,
                );

                return new MicrosoftIdentityUser({
                    id: user.id,
                    firstName,
                    lastName,
                    fullName: user.displayName,
                    jobTitle: user.jobTitle,
                    primaryEmail: MilyliMicrosoft365Api.getPrimaryEmail(user, domain),
                    emails: user.otherMails,
                    avatarUrl: null,
                    externalIds: null,
                    hasMfa: mfaEnforced,
                    createdAt: createdAt,
                });
            });

        if (!isMultiDomain) {
            data = filter(data, (user: MicrosoftIdentityUser) => {
                return iEndsWith(user.getPrimaryEmail(), domain);
            });
        }

        return data;
    }

    /**
     *
     * @param user
     * @param domain
     * @returns
     */
    static getPrimaryEmail(user: any, domain: string): string {
        let primaryEmail = get(user, 'mail', null);

        if (isNil(primaryEmail)) {
            primaryEmail = get(user, 'userPrincipalName', null);

            if (isNil(primaryEmail) && !isNil(domain)) {
                const displayName = get(user, 'displayName', null);

                if (!isNil(displayName)) {
                    primaryEmail = `${replace(displayName, ' ', '')}@${domain}`;
                } else {
                    primaryEmail = `${user.id}@${domain}`;
                }
            }
        }

        return primaryEmail;
    }
}
