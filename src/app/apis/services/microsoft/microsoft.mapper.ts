import { MicrosoftIdentityGroupUser } from 'app/apis/classes/microsoft-identity/microsoft-identity-group-user.class';
import { MicrosoftIdentityGroup } from 'app/apis/classes/microsoft-identity/microsoft-identity-group.class';
import { MicrosoftIdentityUser } from 'app/apis/classes/microsoft-identity/microsoft-identity-user.class';
import { MicrosoftThirdPartyAppUser } from 'app/apis/classes/microsoft-identity/microsoft-third-party-app-user.class';
import { MicrosoftThirdPartyApp } from 'app/apis/classes/microsoft-identity/microsoft-third-party-app.class';
import { IdentityGroup } from 'app/apis/interfaces/identity-group.interface';
import { TData } from 'app/apis/types/data';
import { AvatarInfoM365 } from 'app/apis/types/m365/avatar-info-m365';
import { PhotoDataM365 } from 'app/apis/types/m365/photo-data-m365';
import {
    ConditionalAccessPolicy,
    MappedConditionalAccessPolicy,
} from 'app/apis/types/m365/polices.types';
import { ServicePrincipalAppRole } from 'app/apis/types/m365/service-principal-application-role.type';
import { ServicePrincipalApplication } from 'app/apis/types/m365/service-principal-application.type';
import { M365User } from 'app/apis/types/m365/user-info-m365.type';
import { UserRegistrationDetails } from 'app/apis/types/m365/user.types';
import { ServicePageableType as Pageable } from 'app/apis/types/service/service-pageable.type';
import { IdentitySource } from 'app/users/personnel/entities/identity-source.enum';
import { GroupType } from 'commons/enums/users/group-type.enum';
import { partition } from 'commons/helpers/array.helper';
import { resolveNames } from 'commons/helpers/name-resolver.helper';
import { iEndsWith, iIncludes } from 'commons/helpers/string.helper';
import { filter, get, has, isEmpty, isNil, pick } from 'lodash';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { PolloLogger } from 'pollo-logger/pollo.logger';

export class MicrosoftMapper {
    private static logger = PolloLogger.logger(this.constructor.name);

    /**
     *
     * @param response
     * @param domain
     * @param filterDomain
     * @returns
     */
    static users(
        users: Array<M365User>,
        domain: string,
        isMultiDomain: boolean,
    ): MicrosoftIdentityUser[] {
        // Sanity check
        if (isEmpty(users)) {
            return [];
        }

        const [activeUsers, inactiveUsers] = partition(users, user => user?.accountEnabled);

        if (!isEmpty(inactiveUsers)) {
            this.logger.log(
                PolloMessage.msg('Microsoft sync skipping disabled users')
                    .setDomain(domain)
                    .setIdentifier({
                        inactiveUsers: inactiveUsers.length,
                        inactiveUsersInfo: inactiveUsers.map(user =>
                            pick(user, [
                                'id',
                                'email',
                                'mail',
                                'userPrincipalName',
                                'displayName',
                                'otherMails',
                            ]),
                        ),
                    }),
            );
        }

        // Filter users by valid domain (in case isMultiDomain is off)
        const [validUsers, invalidUsers] = !isMultiDomain
            ? partition(activeUsers, user => {
                  const primaryEmail = MicrosoftMapper.getPrimaryEmail(user, domain);
                  return iEndsWith(primaryEmail, domain);
              })
            : [activeUsers, []];

        if (!isEmpty(invalidUsers)) {
            this.logger.log(
                PolloMessage.msg('Microsoft sync skipping invalid users')
                    .setDomain(domain)
                    .setIdentifier({
                        isMultiDomain,
                        invalidUsers: invalidUsers.length,
                        invalidUsersInfo: invalidUsers.map(user =>
                            pick(user, [
                                'id',
                                'email',
                                'mail',
                                'userPrincipalName',
                                'displayName',
                                'otherMails',
                            ]),
                        ),
                    }),
            );
        }

        return validUsers.map((user: any) => {
            const createdAt = user.employeeHireDate ?? user.createdDateTime;
            const { firstName, lastName } = resolveNames(
                user.givenName,
                user.surname,
                user.displayName,
            );
            return new MicrosoftIdentityUser({
                id: user.id,
                firstName,
                lastName,
                fullName: user.displayName,
                jobTitle: user.jobTitle,
                primaryEmail: MicrosoftMapper.getPrimaryEmail(user, domain, true),
                emails: user.otherMails,
                avatarUrl: `/users/${user.id}/photos/120x120/$value`,
                externalIds: null,
                hasMfa: false,
                createdAt: createdAt,
            });
        });
    }

    /**
     *
     * @param response
     */
    static userPhoto(response: AvatarInfoM365): TData<PhotoDataM365> {
        let res = null;

        if (!isNil(response)) {
            const { mimeType, buffer, eTag } = response;
            const photoData = buffer.toString('base64');

            res = {
                data: {
                    photoData,
                    mimeType,
                    eTag,
                },
            };
        }

        return res;
    }

    /**
     * Returns a set of user IDs that have Multi-Factor Authentication (MFA) enabled.
     *
     * @param {UserRegistrationDetails[]} mfas - An array of user registration details.
     * @returns {Set<string>} - A set of user IDs with MFA enabled.
     */
    static identityMfaEnabledSet(mfas: UserRegistrationDetails[]): {
        idsSet: Set<string>;
        principalNamesSet: Set<string>;
    } {
        if (!Array.isArray(mfas) || mfas.length === 0) {
            return { idsSet: new Set(), principalNamesSet: new Set() };
        }

        const usersWithMfaEnabled = mfas.filter(mfa => mfa.isMfaRegistered);
        const userIdsWithMfaEnabled = usersWithMfaEnabled.map(user => user.id);

        const userPrincipalNamesWithMfaEnabled = usersWithMfaEnabled.map(
            user => user.userPrincipalName,
        );

        return {
            idsSet: new Set(userIdsWithMfaEnabled),
            principalNamesSet: new Set(userPrincipalNamesWithMfaEnabled),
        };
    }

    /**
     *
     * @param response
     */
    static identityCapMfa(
        // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
        users: MicrosoftIdentityUser[],
        capUserList: any,
    ): any {
        const { includedUserIds } = capUserList;

        // set mfa true for users who are included in CAP
        for (const user of users) {
            if (iIncludes(includedUserIds, user.getId())) {
                user.setHasMfa(true);
            }
        }
    }

    /**
     * Try and get in this order
     * 1. userPrincipalName
     * 2. mail
     * 3. displayName@domain
     * 4. default: id@domain
     *
     * @param user
     * @param domain
     * @returns
     */

    private static CUSTOMER_DOMAINS = [
        'chicagolighthouse.org',
        'paytech-commercial.com',
        'equitrust.com',
        'quarterhill.com',
        'tlscv.com',
        'sequentur.com',
        'tempworks.com',
        'visit-aci.com',
        'chasolutions.com',
        'acitpa.com',
        'bbe.ca',
        'ai360platform.onmicrosoft.com',
        'wipro.com',
        'finthrive.com',
        'avcrm.com',
        'maronmarvel.com',
        'navvis.com',
        '121.health',
        'removery.com',
        'westmonroe.com',
        'lesakatech.com',
        'roundtriphealth.com',
        '360insights.com',
        'swissquote.lu',
        'easypayeverywhere.co.za',
        'connected.co.za',
        'madcapsoftware.com',
        'transmax.com.au',
        'calamp.com',
        'idq.com',
        'fengate.com',
        'acrisure.com',
        'symphonyai.com',
        'able.tech',
        'keyfactor.com',
        'netspi.com',
        'mbc.net',
        'adherehealth.com',
        'portima.com',
        'keraltyus.com',
        'primetherapeutics.com',
        'jndla.com',
        'milyli.com',
    ];

    static getPrimaryEmail(
        user: any,
        domain: string | null,
        fallbackOnDomain = false,
    ): string | null {
        if (!user) {
            return null;
        }

        if (domain && MicrosoftMapper.CUSTOMER_DOMAINS.includes(domain) && user.mail) {
            this.logger.log(
                PolloMessage.msg(
                    'Domain is included in customer domains array, searching user using email',
                )
                    .setDomain(domain)
                    .setIdentifier(user.mail),
            );
            return user.mail;
        }

        if (has(user, 'userPrincipalName')) {
            return user.userPrincipalName;
        }

        if (has(user, 'mail')) {
            return user.mail;
        }

        if (fallbackOnDomain) {
            return has(user, 'displayName')
                ? `${user.displayName}@${domain}`
                : `${user.id}@${domain}`;
        }

        return null;
    }

    /**
     * @param policies
     * @param applicationType
     */
    static mappedMfaPolicies(policies: ConditionalAccessPolicy[]): MappedConditionalAccessPolicy[] {
        return policies.map((policy: ConditionalAccessPolicy) => {
            return {
                id: policy.id,
                displayName: policy?.displayName,
                includedUsers: policy.conditions?.users?.includeUsers,
                excludedUsers: policy.conditions?.users?.excludeUsers,
                includedGroups: policy.conditions?.users?.includeGroups,
                excludedGroups: policy.conditions?.users?.excludeGroups,
            };
        });
    }

    static groups(
        response: any,
        domain: string,
        isMultiDomain: boolean,
        connectionId: number,
    ): Pageable<MicrosoftIdentityGroup> {
        let data: MicrosoftIdentityGroup[] = [];
        let token = null;

        if (!isNil(response)) {
            const groups = get(response, 'value', []);

            data = groups.map((group: any) => {
                const { mail, mailEnabled } = group;
                const microsoftDomain = mailEnabled
                    ? mail.substring(mail.lastIndexOf('@') + 1)
                    : domain;
                return new MicrosoftIdentityGroup({
                    externalId: group.id,
                    name: group.displayName,
                    domain: microsoftDomain,
                    description: group.description ?? '',
                    email: mail,
                    source: IdentitySource.MICROSOFT_365,
                    orgUnitPath: '',
                    type: GroupType.GROUP,
                    connectionId,
                });
            });

            token = response.nextPageToken;
        }

        if (!isMultiDomain) {
            data = filter(data, (group: MicrosoftIdentityGroup) => {
                return iEndsWith(group.domain, domain);
            });
        }

        return { data, token } as Pageable<MicrosoftIdentityGroup>;
    }

    static groupUsers(
        // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
        response: any,
        domain: string,
        isMultiDomain: boolean,
    ): Pageable<MicrosoftIdentityGroupUser> {
        let data: MicrosoftIdentityGroupUser[] = [];
        let token = null;

        if (!isNil(response)) {
            const users = get(response, 'value', []);
            const filteredUsers = filter(users, (member: any) => {
                return member['@odata.type'] === '#microsoft.graph.user';
            });

            data = filteredUsers.map((user: any) => {
                const { id } = user;
                const mail = MicrosoftMapper.getPrimaryEmail(user, domain);
                const userDomain = mail ? mail.substring(mail.lastIndexOf('@') + 1) : '';

                const identity = new MicrosoftIdentityGroupUser({
                    email: mail,
                    id,
                });

                identity['domain'] = userDomain;

                return identity;
            });

            token = response.nextPageToken;
        }

        if (!isMultiDomain) {
            data = filter(data, (user: MicrosoftIdentityGroupUser) => {
                return iEndsWith(user['domain'], domain);
            });
        }

        return { data, token } as Pageable<MicrosoftIdentityGroupUser>;
    }

    static thirdPartyApp(response: any): Pageable<MicrosoftThirdPartyApp> {
        const applications: ServicePrincipalApplication[] = get(response, 'value', []);

        return {
            data: applications.map(app => new MicrosoftThirdPartyApp(app)),
            token: response?.nextPageToken ?? null,
        };
    }

    static thirdPartyAppUser(data: Array<any>): Pageable<MicrosoftThirdPartyAppUser> {
        return {
            data: data.map(u => new MicrosoftThirdPartyAppUser(u)),
            token: null,
        };
    }
    static thirdPartyAppGroupUser(
        response: any,
        group: IdentityGroup,
        appRoles: ServicePrincipalAppRole[],
    ): Pageable<MicrosoftThirdPartyAppUser> {
        return {
            data: response.data.map(result => {
                const user = result.data;

                const parsedUser = {
                    id: user.id,
                    email: user.primaryEmail,
                    groups: [group.name],
                    roles: appRoles.map(role => ({
                        id: role.id,
                        name: role?.displayName ?? 'Default Access',
                        description: role?.description ?? group.name,
                    })),
                };

                return new MicrosoftThirdPartyAppUser(parsedUser);
            }),
            token: null,
        };
    }

    static thirdPartyAppGroups(
        data: Array<IdentityGroup>,
        connectionId: number,
    ): Pageable<MicrosoftIdentityGroup> {
        const groups = data.map(group => {
            return new MicrosoftIdentityGroup({
                externalId: group.externalId ?? '',
                name: group.name ?? '',
                domain: '',
                description: group.description ?? '',
                email: '',
                source: IdentitySource.MICROSOFT_365,
                orgUnitPath: '',
                type: GroupType.GROUP,
                connectionId,
            });
        });

        return {
            data: groups,
            token: null,
        };
    }

    private static policyEnabled(policy: any): boolean {
        return policy.state === 'enabled';
    }

    private static checkApplicationType(policy: any, applicationType: string[]): boolean {
        const application = get(policy, 'conditions.applications.includeApplications', []);

        const includesAllApplications = this.validateAllApplications(policy);

        if (includesAllApplications) {
            return true;
        }

        return applicationType.some(appType => iIncludes(application, appType));
    }

    private static validateAllApplications(policy: any): boolean {
        const applications = get(policy, 'conditions.applications.includeApplications', []);

        return iIncludes(applications, 'All');
    }
}
