/* eslint-disable no-await-in-loop */
import {
    BatchRequestContent,
    BatchRequestStep,
    BatchResponseContent,
    Client,
    PageCollection,
    PageIterator,
    PageIteratorCallback,
} from '@microsoft/microsoft-graph-client';
import { ApiResponse } from 'app/apis/classes/api/api-response.class';
import { mapComplianceStatusToString } from 'app/apis/enums/intune/intune-compliant-status.enum';
import { PrefetchedDeviceDataType } from 'app/apis/enums/intune/prefetched-device-data-type.enum';
import { IntuneCustomConstants } from 'app/apis/providers/custom/intune/intune-custom.constants';
import { ClientDeviceIdPaginationManager } from 'app/apis/services/intune/client-device-id-pagination-manager.class';
import { DeviceDataPagination } from 'app/apis/services/intune/device-data-pagination.class';
import { DeviceScriptsPaginationManager } from 'app/apis/services/intune/device-scripts-pagination-manager.class';
import { isRetired } from 'app/apis/services/intune/intune-utils';
import { IntuneService } from 'app/apis/services/intune/intune.service';
import { MacCustomProfilesPagination } from 'app/apis/services/intune/mac-custom-profiles-pagination.class';
import { ManagedAppsForDevicePaginationManager } from 'app/apis/services/intune/managed-apps-for-device-pagination-manager.class';
import { PrimaryUserPagination } from 'app/apis/services/intune/primary-user-pagination.class';
import { WindowsCustomPolicyPagination } from 'app/apis/services/intune/windows-custom-policy-pagination.class';
import {
    AntivirusData,
    ClientDevice,
    CompleteDeviceData,
    CompliancePolicy,
    CustomPoliciesValues,
    CustomProfilesForMac,
    DevicePolicy,
    FileVaults,
    IntuneClientParameters,
    MacProfile,
    MacProfilesData,
    ManyDataTypes,
    MobileAppIntentAndStates,
    PrimaryUserData,
    ProfileFields,
    WindowsPolicyStatusData,
} from 'app/apis/types/intune/intune.types';
import { ConnectionMetadata } from 'app/companies/connections/classes/connection-metadata.class';
import { Account } from 'auth/entities/account.entity';
import { PolloAdapter } from 'commons/adapters/pollo.adapter';
import { Benchmark } from 'commons/benchmark/benchmark';
import { Retry } from 'commons/decorators/retry.decorator';
import { ClientType } from 'commons/enums/auth/client-type.enum';
import { isRequestInvalidAuthenticationError } from 'commons/helpers/http.helper';
import { mapSchemaToValues } from 'commons/helpers/intune.helper';
import { forEachTokenPage } from 'commons/helpers/pagination/pagination.helper';
import { TokenPagination } from 'commons/helpers/pagination/token.pagination';
import config from 'config';
import { FeatureFlagCategory } from 'feature-flags/enums/feature-flag-category.enum';
import { FeatureFlag } from 'feature-flags/feature-flag.enum';
import { FeatureFlagService } from 'feature-flags/feature-flag.service';
import {
    clone,
    concat,
    flatten,
    get,
    includes,
    isEmpty,
    isEqual,
    isNil,
    pick,
    values as vals,
} from 'lodash';
import { PolloLogger } from 'pollo-logger/pollo.logger';
import querystring from 'querystring';

export class IntuneComputerInventoryService extends IntuneService {
    protected logger = PolloLogger.logger(this.constructor.name);

    private readonly customSyncExcludedDomains = ['weareeverise.com'];

    // Keep 'Drata -' prefix on these default windows policies
    private readonly DEFAULT_WINDOWS_POLICIES_PREFIX = 'Drata -';
    private readonly DEFAULT_WINDOWS_CUSTOM_POLICIES: CustomPoliciesValues = {
        SCREENLOCK: ['Drata - Screen Lock'],
        UPDATES: ['Drata - Windows Updates'],
    };

    private SCRIPT_NAMES = {
        ANTIVIRUS: 'Drata - Antivirus',
        HARDWARE: 'Drata - Hardware',
    };

    private DEFAULT_MAC_CUSTOM_PROFILES = {
        SCREENSAVER: {
            values: ['Drata - Screen Saver'],
            key: 'screensaver',
        },
        UPDATES: {
            values: ['Drata - Software Updates'],
            key: 'updates',
        },
    };

    private COMPLIANCE_WINDOWS_POLICIES = [
        {
            compliancePolicyName: 'Windows10CompliancePolicy.ActiveFirewallRequired',
            key: 'firewall',
        },
        {
            compliancePolicyName: 'Windows10CompliancePolicy.AntivirusRequired',
            key: 'antivirusServices',
        },
    ];

    private COMPLIANCE_MAC_POLICIES = [
        {
            compliancePolicyName: 'MacOSCompliancePolicy.FirewallEnabled',
            key: 'firewall',
        },
        {
            compliancePolicyName: 'MacOSCompliancePolicy.StorageRequireEncryption',
            key: 'diskEncrypted',
        },
        {
            compliancePolicyName: 'MacOSCompliancePolicy.GatekeeperAllowedAppSource',
            key: 'gatekeeper',
        },
        {
            compliancePolicyName: 'MacOSCompliancePolicy.PasswordMinutesOfInactivityBeforeLock',
            key: 'screenlock',
        },
        {
            compliancePolicyName: 'MacOSCompliancePolicy.PasswordRequired',
            key: 'passwordRequired',
        },
    ];

    private CachedCompliancePolicies: {
        COMPLIANCE_WINDOWS_POLICIES: CompliancePolicy;
        COMPLIANCE_MAC_POLICIES: CompliancePolicy;
    } = {
        COMPLIANCE_WINDOWS_POLICIES: {},
        COMPLIANCE_MAC_POLICIES: {},
    };

    private readonly INTUNE_REPORT_PAGE_SIZE = config.get('intune.device.pageSize');

    constructor(
        metadata: ConnectionMetadata,
        clientType: ClientType,
        private readonly featureFlagService: FeatureFlagService,
    ) {
        super(metadata, clientType);
    }

    async getAllClientDevices(
        pagination: TokenPagination,
        account: Account,
    ): Promise<{ clientDevices: ClientDevice[]; nextLink: string | null }> {
        try {
            const maxResults = pagination.getMaxResults();
            const nextPageToken = pagination.getNextPageToken();
            let counter = 0;

            const clientDevices: ClientDevice[] = [];

            const response: PageCollection = await this.getClientsIds(
                await this.getThrottledGraphClient(),
                maxResults,
                nextPageToken,
                account,
            );

            const nextLink = response['@odata.nextLink'] ?? null;

            const perItemCallback: PageIteratorCallback = dataItem => {
                counter++;
                clientDevices.push(dataItem);
                return counter < maxResults;
            };

            const pageIterator = new PageIterator(
                await this.getThrottledGraphClient(),
                response,
                perItemCallback,
            );

            await pageIterator.iterate();

            return { clientDevices, nextLink };
        } catch (error) {
            if (isRequestInvalidAuthenticationError(error)) {
                throw error;
            } else {
                this.logger.error(
                    PolloAdapter.acct(error.message, account)
                        .setError(error)
                        .setContext(this.constructor.name)
                        .setSubContext(this.getAllClientDevices.name),
                );
                return { clientDevices: [], nextLink: null };
            }
        }
    }

    async getAssetScriptsAndProfiles(account: Account): Promise<{
        antivirusScriptId: string;
        customProfilesNamesForMac: CustomProfilesForMac;
        fileVaultsValues: FileVaults[];
    }> {
        const { antivirusScriptId } = await this.getScriptIdsByName(account);

        const customProfilesNamesForMac = this.getCustomProfilesNames();
        const fileVaultsValues: FileVaults[] = await this.getFileVaults(account);

        return {
            antivirusScriptId,
            customProfilesNamesForMac,
            fileVaultsValues,
        };
    }

    async getAssetData(
        dto: IntuneClientParameters,
    ): Promise<CompleteDeviceData | null | undefined> {
        const {
            clientDevice,
            account,
            antivirusScriptId,
            customPoliciesValues: customPoliciesForWindows,
            fileVaultsValues,
            customProfilesNamesForMac,
            intuneBatchCallsEnabled,
            prefetchedDevicesData,
        } = dto;

        const clientDeviceId = get(clientDevice, 'id');

        let benchmark;
        let deviceData: CompleteDeviceData | undefined = undefined;
        if (intuneBatchCallsEnabled) {
            deviceData = (
                prefetchedDevicesData?.get(PrefetchedDeviceDataType.COMPLETE_DEVICE) as Map<
                    string,
                    CompleteDeviceData
                >
            )?.get(clientDeviceId);
            if (deviceData) {
                this.logger.debug(
                    PolloAdapter.acct('Extracted deviceData from batch', account).setIdentifier({
                        clientDeviceId,
                        deviceData,
                    }),
                );
            } else {
                this.logger.warn(
                    PolloAdapter.acct(
                        'Unable to find a device data response in batch for client device id',
                        account,
                    ).setIdentifier({
                        clientDeviceId,
                    }),
                );
            }
        } else {
            benchmark = new Benchmark();
            /**
             * If feature flag is off or the returned response did not work, fetch from api.
             */
            deviceData = await this.getClientData(clientDeviceId, account);
            this.logger.log(
                PolloAdapter.acct('Ran getClientData', account).setExecutionTime(
                    benchmark.endTime(),
                ),
            );
        }

        if (isRetired(get(deviceData, 'managementState', null))) {
            return deviceData;
        }

        if (!isNil(deviceData)) {
            // TODO clean up benchmark
            benchmark = new Benchmark();
            await this.setPrimaryUser(
                deviceData,
                account,
                intuneBatchCallsEnabled,
                prefetchedDevicesData,
            );
            this.logger.log(
                PolloAdapter.acct('Ran setPrimaryUser', account).setExecutionTime(
                    benchmark.endTime(),
                ),
            );

            if (deviceData.isMissingPrimaryUser) {
                return deviceData;
            }

            // TODO clean up benchmark
            benchmark = new Benchmark();
            await this.getManagedApps(
                deviceData,
                account,
                intuneBatchCallsEnabled,
                prefetchedDevicesData,
            );
            this.logger.log(
                PolloAdapter.acct('Ran getManagedApps', account).setExecutionTime(
                    benchmark.endTime(),
                ),
            );
            // TODO clean up benchmark
            benchmark = new Benchmark();
            await this.setCompliancePolicies(deviceData, account);
            this.logger.log(
                PolloAdapter.acct('Ran setCompliancePolicies', account).setExecutionTime(
                    benchmark.endTime(),
                ),
            );
            // TODO clean up benchmark
            benchmark = new Benchmark();
            await this.setAntivirus(
                antivirusScriptId,
                clientDeviceId,
                deviceData,
                account,
                intuneBatchCallsEnabled,
                prefetchedDevicesData,
            );
            this.logger.log(
                PolloAdapter.acct('Ran setAntivirus', account).setExecutionTime(
                    benchmark.endTime(),
                ),
            );

            const operatingSystem: string = get(deviceData, 'operatingSystem', '');
            if (
                Object.entries(customPoliciesForWindows).length > 0 &&
                includes(operatingSystem.toLowerCase(), 'windows')
            ) {
                // TODO: Remove when rewritten or reassess after https://drata.atlassian.net/browse/ENG-57180
                if (this.customSyncExcludedDomains.includes(account.domain)) {
                    this.logger.log(
                        PolloAdapter.acct('Skipping sync of custom policies', account)
                            .setContext(this.constructor.name)
                            .setSubContext(this.setWindowsCustomPoliciesByDevice.name),
                    );
                } else {
                    // TODO clean up benchmark
                    benchmark = new Benchmark();
                    await this.setWindowsCustomPoliciesByDevice(
                        customPoliciesForWindows,
                        deviceData,
                        account,
                        intuneBatchCallsEnabled,
                        prefetchedDevicesData,
                    );
                    this.logger.log(
                        PolloAdapter.acct(
                            'Ran setWindowsCustomPoliciesByDevice',
                            account,
                        ).setExecutionTime(benchmark.endTime()),
                    );
                }
            }
            if (includes(operatingSystem.toLowerCase(), 'macos')) {
                this.setFileVaultByDevice(fileVaultsValues, deviceData);

                // TODO clean up benchmark
                benchmark = new Benchmark();
                await this.getCustomProfilesForMac(
                    customProfilesNamesForMac,
                    deviceData,
                    account,
                    intuneBatchCallsEnabled,
                    prefetchedDevicesData,
                );
                this.logger.log(
                    PolloAdapter.acct('Ran getCustomProfilesForMac', account).setExecutionTime(
                        benchmark.endTime(),
                    ),
                );
            }

            const { id, userId, deviceName, serialNumber } = deviceData;

            this.logger.log(
                PolloAdapter.acct(
                    `Intune Device call - ${deviceData.serialNumber}: Device completeData`,
                    account,
                )
                    .setIdentifier({
                        deviceData: {
                            id,
                            userId,
                            deviceName,
                            serialNumber,
                            /**
                             * props that are not part of CompleteDeviceData type
                             */
                            ...pick(deviceData, [
                                'antivirus',
                                'isEncrypted',
                                'deviceType',
                                'complianceState',
                                'diskEncrypted',
                            ]),
                        },
                    })
                    .setSubContext(this.getAssetData.name)
                    .setContext(this.constructor.name),
            );
            return deviceData;
        }
        this.logger.warn(
            PolloAdapter.acct(
                `Intune Device call - could not retrieve device complete data for ${clientDevice.userPrincipalName}`,
                account,
            ).setResult({
                clientDevice: clientDevice,
            }),
        );
        return null;
    }

    async getIntuneAccountId(account: Account): Promise<string | null> {
        const graphClient = await this.getThrottledGraphClient();
        const response = await graphClient.api(`/deviceManagement`).get();

        if (isNil(response)) {
            this.logger.log(
                PolloAdapter.acct(`Invalid intuneAccountId response`, account)
                    .setContext(this.constructor.name)
                    .setSubContext(this.getIntuneAccountId.name),
            );
            return null;
        }

        const intuneAccountId = get(response, 'intuneAccountId', null);

        if (isNil(intuneAccountId)) {
            this.logger.log(
                PolloAdapter.acct('Invalid intuneAccountId response', account)
                    .setContext(this.constructor.name)
                    .setIdentifier({ response })
                    .setSubContext(this.getIntuneAccountId.name),
            );
            return null;
        }

        return intuneAccountId;
    }

    private async setAntivirus(
        antivirusScript: string,
        clientDeviceId: string,
        device: CompleteDeviceData,
        account: Account,
        intuneBatchCallsEnabled?: boolean,
        prefetchedDevicesData?: Map<PrefetchedDeviceDataType, Map<string, ManyDataTypes>>,
    ) {
        const antivirus = await this.getScriptDevice(
            antivirusScript,
            clientDeviceId,
            account,
            intuneBatchCallsEnabled,
            prefetchedDevicesData,
        );

        const result = get(antivirus, 'resultMessage', null);
        if (!isNil(antivirus)) {
            this.logger.log(
                PolloAdapter.acct(
                    `Intune Device call - ${device.serialNumber}:  antivirus results`,
                    account,
                ).setIdentifier({
                    device: this.getDeviceSummary(device),
                    raw: antivirus,
                }),
            );
        } else {
            this.logger.log(
                PolloAdapter.acct(
                    `Intune Device call - ${device.serialNumber}:  no antivirus found using script ${antivirusScript}`,
                    account,
                ).setIdentifier({
                    device: this.getDeviceSummary(device),
                }),
            );
        }

        device.antivirus = !isNil(antivirus) && !isEmpty(result) ? JSON.parse(result) : {};
    }

    private async getClientData(
        id: string,
        account: Account,
    ): Promise<CompleteDeviceData | undefined> {
        try {
            if (!isNil(id)) {
                const graphClient = await this.getThrottledGraphClient();
                return await graphClient
                    .api(`/deviceManagement/managedDevices/${id}`)
                    .version('beta')
                    .expand('detectedApps')
                    .get();
            }
            return undefined;
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(
                    `Intune device call - could not get device data for device with id ${id}`,
                    account,
                )
                    .setError(error)
                    .setIdentifier({ error }),
            );
            throw error;
        }
    }

    private async setPrimaryUser(
        device: CompleteDeviceData,
        account: Account,
        intuneBatchCallsEnabled?: boolean,
        prefetchedDevicesData?: Map<PrefetchedDeviceDataType, Map<string, ManyDataTypes>>,
    ): Promise<void> {
        try {
            const id = get(device, 'id', null);
            if (!isNil(id)) {
                let response: PrimaryUserData | undefined = undefined;

                if (intuneBatchCallsEnabled) {
                    response = (
                        prefetchedDevicesData?.get(PrefetchedDeviceDataType.PRIMARY_USER) as Map<
                            string,
                            PrimaryUserData
                        >
                    )?.get(id);
                    if (response) {
                        this.logger.debug(
                            PolloAdapter.acct(
                                'Extracted primary user data from batch',
                                account,
                            ).setIdentifier({
                                deviceId: id,
                                primaryUserData: response,
                            }),
                        );
                    } else {
                        this.logger.warn(
                            PolloAdapter.acct(
                                'Unable to find a primary user response in batch for client device id',
                                account,
                            ).setIdentifier({
                                deviceId: id,
                            }),
                        );
                    }
                } else {
                    const graphClient = await this.getThrottledGraphClient();
                    response = await graphClient
                        .api(`/deviceManagement/managedDevices/${id}/users`)
                        .version('beta')
                        .select(['userPrincipalName', 'id'])
                        .get();
                }
                if (!isNil(response) && !isEmpty(response.value)) {
                    const primaryUser = get(response, 'value[0]');
                    device.primaryUser = primaryUser;
                    this.logger.log(
                        PolloAdapter.acct(
                            `Intune Device call - ${device.serialNumber}: set primary user`,
                            account,
                        ).setResult({
                            device: this.getDeviceSummary(device),
                            raw: response,
                        }),
                    );
                } else {
                    device.isMissingPrimaryUser = true;
                    this.logger.log(
                        PolloAdapter.acct(
                            `Intune Device call - ${device.serialNumber}: missing primary user`,
                            account,
                        ).setIdentifier({
                            device: this.getDeviceSummary(device),
                        }),
                    );
                }
            }
        } catch (error) {
            this.logger.log(
                PolloAdapter.acct(
                    `Intune Device call - ${
                        device.serialNumber
                    }: missing permission to be able to get the primary user with device name ${get(
                        device,
                        'deviceName',
                    )}`,
                    account,
                ).setIdentifier({ error }),
            );

            // special use case where we delete if no primary user found
            if (get(error, 'statusCode', null) === 404) {
                device.primaryUser = null;
                return;
            }

            throw error;
        }
    }

    private async getManagedApps(
        device: CompleteDeviceData,
        account: Account,
        intuneBatchCallsEnabled?: boolean,
        prefetchedDevicesData?: Map<PrefetchedDeviceDataType, Map<string, ManyDataTypes>>,
    ): Promise<void> {
        try {
            let response: MobileAppIntentAndStates | undefined = undefined;
            if (intuneBatchCallsEnabled) {
                response = (
                    prefetchedDevicesData?.get(PrefetchedDeviceDataType.MANAGED_APPS) as Map<
                        string,
                        MobileAppIntentAndStates
                    >
                )?.get(device.id);
                if (response) {
                    this.logger.debug(
                        PolloAdapter.acct(
                            'Extracted managed apps from batch',
                            account,
                        ).setIdentifier({
                            deviceId: device.id,
                            managedApps: response,
                        }),
                    );
                } else {
                    this.logger.warn(
                        PolloAdapter.acct(
                            'Unable to find a managed apps response in batch for client device id',
                            account,
                        ).setIdentifier({
                            deviceId: device.id,
                        }),
                    );
                }
            } else {
                const graphClient = await this.getThrottledGraphClient();
                response = await graphClient
                    .api(`users/${device.primaryUser?.id}/mobileAppIntentAndStates/${device.id}`)
                    .select('mobileAppList')
                    .version('beta')
                    .get();
            }

            if (response?.mobileAppList) {
                const installedManagedApps = response.mobileAppList?.filter(
                    ({ installState }) => installState === 'installed',
                );

                this.logger.log(
                    PolloAdapter.acct(
                        `Intune Managed Apps response for user: ${device.primaryUser?.userPrincipalName}`,
                        account,
                    )
                        .setIdentifier({
                            installedManagedApps,
                            device: this.getDeviceSummary(device),
                        })
                        .setContext(this.constructor.name)
                        .setSubContext(this.getManagedApps.name),
                );

                for (const managedApp of installedManagedApps) {
                    const { ...detectedApp } = managedApp;

                    device.detectedApps = [...device.detectedApps, detectedApp];
                }
            } else {
                this.logger.log(
                    PolloAdapter.acct(
                        `No managed apps returned for user: ${device.primaryUser?.userPrincipalName}`,
                        account,
                    )
                        .setIdentifier({
                            device: this.getDeviceSummary(device),
                        })
                        .setContext(this.constructor.name)
                        .setSubContext(this.getManagedApps.name),
                );
            }
        } catch (error) {
            this.logger.warn(
                PolloAdapter.acct(
                    `Error occurred attempting to get Managed Apps for user: ${device.primaryUser?.userPrincipalName}`,
                    account,
                ).setIdentifier({
                    device: this.getDeviceSummary(device),
                    error,
                }),
            );
        }
    }

    private async getClientsIds(
        client: Client,
        maxResults: number,
        link: string | null,
        account: Account,
    ): Promise<PageCollection> {
        if (link === '1' || link == null) {
            /**
             * TODO: remove hardcode domain once Personal-Devices-Toggle
             * https://drata.atlassian.net/browse/ENG-12360
             */
            if (account.domain === 'cialdnb.com') {
                return client
                    .api('/deviceManagement/managedDevices')
                    .select('id')
                    .filter(
                        `managedDeviceOwnerType eq 'company' or managedDeviceOwnerType eq 'unknown'`,
                    )
                    .top(maxResults)
                    .get();
            }
            return client
                .api('/deviceManagement/managedDevices')
                .select(['id', 'deviceName', 'userPrincipalName'])
                .filter(`operatingSystem eq 'windows' or operatingSystem eq 'macos'`)
                .top(maxResults)
                .get();
        }

        return client.api(link.toString()).get();
    }

    private async setWindowsCustomPoliciesByDevice(
        customPolicyValues: CustomPoliciesValues,
        deviceData: CompleteDeviceData,
        account: Account,
        intuneBatchCallsEnabled?: boolean,
        prefetchedDevicesData?: Map<PrefetchedDeviceDataType, Map<string, ManyDataTypes>>,
    ) {
        let policyStatuses: WindowsPolicyStatusData | undefined = [];
        try {
            if (intuneBatchCallsEnabled) {
                policyStatuses = (
                    prefetchedDevicesData?.get(
                        PrefetchedDeviceDataType.WINDOWS_CUSTOM_POLICY,
                    ) as Map<string, WindowsPolicyStatusData>
                )?.get(deviceData.id);
                if (policyStatuses) {
                    this.logger.debug(
                        PolloAdapter.acct(
                            'Extracted policyStatuses from batch',
                            account,
                        ).setIdentifier({
                            clientDeviceId: deviceData.id,
                            policyStatuses,
                        }),
                    );
                } else {
                    // reset
                    policyStatuses = [];
                    this.logger.warn(
                        PolloAdapter.acct(
                            'Unable to find a policy statuses entry in batch for client device id',
                            account,
                        ).setIdentifier({
                            clientDeviceId: deviceData.id,
                        }),
                    );
                }
            } else {
                const customPolicies: string[] = concat(
                    customPolicyValues.UPDATES,
                    customPolicyValues.SCREENLOCK,
                );
                const defaultPolicies: string[] = concat(
                    this.DEFAULT_WINDOWS_CUSTOM_POLICIES.UPDATES,
                    this.DEFAULT_WINDOWS_CUSTOM_POLICIES.SCREENLOCK,
                );
                let response: any = null,
                    page = 0;

                const isEmptyDeviceReportSearchAllowed =
                    await this.featureFlagService.evaluateAsDomain(
                        {
                            category: FeatureFlagCategory.NONE,
                            defaultValue: false,
                            name: FeatureFlag.RELEASE_EMPTY_WINDOWS_DEVICE_REPORT_SEARCH_PARAM_ALLOWED,
                        },
                        account.domain,
                    );
                do {
                    /**
                     * Sometimes the default policy names are put in the custom policy connection metadata.
                     * For anyone without custom policies, it's more often efficient to search by policy prefix.
                     * Unfortunately cannot get this API's search query to use an OR operator.
                     *
                     * Device Id doesn't work as search param. Email/UPN works but only for devices that have policies
                     * applied to the user, so is better to avoid the search param when custom policies are used.
                     */
                    let search = isEmptyDeviceReportSearchAllowed
                        ? ''
                        : (deviceData.primaryUser?.userPrincipalName ?? '');
                    if (isEqual(customPolicies, defaultPolicies)) {
                        search = this.DEFAULT_WINDOWS_POLICIES_PREFIX;
                    }

                    response = JSON.parse(
                        String(await this.getDeviceStatus(deviceData, page++, search)),
                    );

                    /**
                     * We cannot trust that MSFT won't reorder the schema on every page.
                     * Based on what we've seen with these APIs, using extra caution.
                     *
                     * Limit collected data to the policies and user needed.
                     * This should happen during search but filtering wasn't supported for the fields.
                     */
                    policyStatuses = concat(
                        policyStatuses,
                        response?.Values.map((row: any[]) =>
                            response?.Schema.reduce((policy: any, col: any, prop: number) => {
                                policy[col.Column] = row[prop];
                                return policy;
                            }, {}),
                        ).filter(
                            r =>
                                ((customPolicyValues.UPDATES ?? []).includes(r.PolicyName) ||
                                    !deviceData.primaryUser?.userPrincipalName ||
                                    r.UPN == deviceData.primaryUser.userPrincipalName) &&
                                customPolicies.includes(r.PolicyName),
                        ),
                    );

                    // TEMP: should be small, first few pages (some customers maxing out just under 200 records)
                    this.logger.log(
                        PolloAdapter.acct(
                            `Intune Custom Policies response for device ${deviceData.deviceName}`,
                            account,
                        )
                            .setIdentifier({
                                deviceId: deviceData.id,
                            })
                            .setMetadata({
                                customPolicies,
                                policyStatuses,
                                page,
                                response,
                                userPrincipleName: deviceData.primaryUser?.userPrincipalName,
                            })
                            .setContext(this.constructor.name)
                            .setSubContext(this.setWindowsCustomPoliciesByDevice.name),
                    );
                    /**
                     * The API above is undocumented and doens't return the odata paging documentation.
                     * The MSFT APIs tend to have blank pages with invalid row counts.
                     * So, will copmare both to stop paging efficiently.
                     *
                     * This should be monitored if there are issues with empty pages.
                     */
                } while (
                    response?.TotalRowCount > this.INTUNE_REPORT_PAGE_SIZE * page &&
                    response?.Values?.length > 0
                );
            }

            deviceData.updates = policyStatuses
                .filter(i => customPolicyValues.UPDATES.includes(i.PolicyName))
                .map(p => ({
                    id: `${p.UserId}_${p.PolicyId}_${p.IntuneDeviceId}`,
                    status: mapComplianceStatusToString(p.PolicyStatus),
                    userPrincipalName: p.UPN,
                }));

            deviceData.screenlock = policyStatuses
                .filter(i => customPolicyValues.SCREENLOCK.includes(i.PolicyName))
                .map(p => ({
                    id: `${p.UserId}_${p.PolicyId}_${p.IntuneDeviceId}`,
                    status: mapComplianceStatusToString(p.PolicyStatus),
                    userPrincipalName: p.UPN,
                }));

            this.logger.log(
                PolloAdapter.acct(`Set policies for device: ${deviceData.deviceName}`, account)
                    .setIdentifier({
                        deviceId: deviceData.id,
                        updates: deviceData.updates,
                        screenlock: deviceData.screenlock,
                        policyStatuses,
                    })
                    .setContext(this.constructor.name)
                    .setSubContext(this.setWindowsCustomPoliciesByDevice.name),
            );
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(
                    `Failed to process Intune Custom Policies response for device ${deviceData.deviceName}`,
                    account,
                )
                    .setIdentifier({
                        deviceId: deviceData.id,
                        userId: deviceData.userId,
                        userPrincipalName: deviceData.userPrincipalName,
                        customPolicyValues,
                    })
                    // TEMP: should be small, count should only be on first page
                    .setMetadata({
                        error,
                    })
                    .setContext(this.constructor.name)
                    .setSubContext(this.setWindowsCustomPoliciesByDevice.name)
                    .setError(error),
            );
        }
    }

    private async setCompliancePolicies(item: CompleteDeviceData, account: Account): Promise<void> {
        const operatingSystem = get(item, 'operatingSystem', '');
        const compliancePoliciesList = includes(operatingSystem.toLowerCase(), 'macos')
            ? this.COMPLIANCE_MAC_POLICIES
            : this.COMPLIANCE_WINDOWS_POLICIES;

        const compliancePolicyType = includes(operatingSystem.toLowerCase(), 'macos')
            ? 'COMPLIANCE_MAC_POLICIES'
            : 'COMPLIANCE_WINDOWS_POLICIES';

        for (const { compliancePolicyName, key } of compliancePoliciesList) {
            try {
                const data = await this.getPoliciesFromCache(
                    item.id,
                    compliancePolicyType,
                    key,
                    compliancePolicyName,
                );
                if (!isNil(data)) {
                    item[key] = data;
                    this.logger.log(
                        PolloAdapter.acct(
                            `Intune Device call - ${item.serialNumber}: Compliance Policy ${compliancePolicyName}`,
                            account,
                        ).setResult({
                            raw: data,
                            device: this.getDeviceSummary(item),
                        }),
                    );
                }
            } catch (error) {
                this.logger.error(
                    PolloAdapter.acct(
                        `Intune Device call - ${item.serialNumber}: Missing Compliance Policy ${compliancePolicyName}`,
                        account,
                    )
                        .setError(error)
                        .setContext(this.constructor.name)
                        .setSubContext(this.setCompliancePolicies.name),
                );

                throw error;
            }
        }
    }

    private async getPolicyByDeviceId(deviceId: string, policy: string): Promise<any> {
        const graphClient = await this.getThrottledGraphClient();
        return graphClient
            .api(
                `/deviceManagement/deviceCompliancePolicySettingStateSummaries/${policy}/deviceComplianceSettingStates`,
            )
            .filter(`deviceId eq '${deviceId}'`)
            .get();
    }

    private async getPoliciesFromCache(
        deviceId: string,
        compliancePolicyType: string,
        policyKey: string,
        policyName: string,
    ): Promise<DevicePolicy> {
        const policies = get(
            this.CachedCompliancePolicies,
            `${compliancePolicyType}.${policyKey}`,
            null,
        );

        if (isNil(policies) || !policies.isDonePaging) {
            const response = await this.getPolicyByDeviceId(deviceId, policyName);

            return get(response, 'value[0]', null);
        }
        return get(policies, `policies.${deviceId}`, null);
    }

    async setCompliancePoliciesToCache(account: Account, maxPageResults: number): Promise<any> {
        const compliancePoliciesLists = {
            COMPLIANCE_MAC_POLICIES: this.COMPLIANCE_MAC_POLICIES,
            COMPLIANCE_WINDOWS_POLICIES: this.COMPLIANCE_WINDOWS_POLICIES,
        };

        for (const [compliancePolicyType, compliancePoliciesList] of Object.entries(
            compliancePoliciesLists,
        )) {
            for (const { key, compliancePolicyName } of compliancePoliciesList) {
                await forEachTokenPage(
                    (nextPageToken: string) => {
                        return this.getCompliancePoliciesPaginated(
                            TokenPagination.paged(nextPageToken, maxPageResults),
                            account,
                            compliancePolicyName,
                            `${compliancePolicyType}.${key}`,
                        );
                    },
                    async (policyResults: any[], response: ApiResponse<any>) => {
                        const policiesToKeep = this.CachedCompliancePolicies[compliancePolicyType];
                        if (isNil(policiesToKeep[key])) {
                            this.CachedCompliancePolicies[compliancePolicyType][key] = {
                                policies: {},
                                lastToken: get(response, 'data.nextPageToken'),
                                isDonePaging: false,
                            };
                        }

                        policiesToKeep[key].lastToken = get(response, 'data.nextPageToken', null);

                        policiesToKeep[key].isDonePaging = isNil(policiesToKeep[key].lastToken);

                        if (isEmpty(policyResults)) {
                            return;
                        }

                        policyResults.forEach(p => {
                            const deviceId = p.deviceId;
                            this.CachedCompliancePolicies[compliancePolicyType][key].policies[
                                deviceId
                            ] = p;
                        });
                    },
                );
            }
        }
    }

    private async getCompliancePoliciesPaginated(
        pagination: TokenPagination,
        account: Account,
        compliancePolicyName: string,
        cacheLocation: string,
    ): Promise<{
        data: { page: DevicePolicy[]; nextPageToken: string | null };
    }> {
        try {
            const skipPaging = get(
                this.CachedCompliancePolicies,
                `${cacheLocation}.isDonePaging`,
                false,
            );

            if (skipPaging) {
                // we don't need to re page everything
                return {
                    data: {
                        page: [],
                        nextPageToken: null,
                    },
                };
            }

            const maxResults = pagination.getMaxResults();
            const nextPageToken =
                get(this.CachedCompliancePolicies, `${cacheLocation}.lastToken`, null) ||
                pagination.getNextPageToken();
            let counter = 0;
            const page: any[] = [];

            const response: PageCollection = await this.getPolicies(
                maxResults,
                nextPageToken,
                compliancePolicyName,
            );

            if (!isNil(response) && !isEmpty(response.value)) {
                this.logger.log(
                    PolloAdapter.acct(
                        `Intune Compliance Policy paginated call: ${compliancePolicyName}`,
                        account,
                    ).setResult({
                        raw: response.value,
                    }),
                );
            }

            const nextLink = response['@odata.nextLink'] ?? null;

            const perItemCallback: PageIteratorCallback = dataItem => {
                counter++;
                page.push(dataItem);
                return counter < maxResults;
            };

            const pageIterator = new PageIterator(
                await this.getThrottledGraphClient(),
                response,
                perItemCallback,
            );

            await pageIterator.iterate();

            return {
                data: {
                    page,
                    nextPageToken: nextLink,
                },
            };
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(`Intune ERROR on getCompliancePoliciesPaginated`, account)
                    .setContext(this.constructor.name)
                    .setError(error),
            );
            throw error;
        }
    }

    private async getPolicies(
        maxResults: number,
        nextPageLink: string,
        policy: string,
    ): Promise<PageCollection> {
        const graphClient = await this.getThrottledGraphClient();
        if (nextPageLink === '1') {
            return graphClient
                .api(
                    `/deviceManagement/deviceCompliancePolicySettingStateSummaries/${policy}/deviceComplianceSettingStates`,
                )
                .top(maxResults)
                .get();
        }

        return graphClient.api(nextPageLink.toString()).get();
    }

    private async getFileVaults(account: Account): Promise<any> {
        try {
            const graphClient = await this.getThrottledGraphClient();
            const response = await graphClient
                .api('/deviceManagement/managedDeviceEncryptionStates')
                .version('beta')
                .get();

            if (!isNil(response) && !isEmpty(response.value)) {
                const filteredEncryptionStates = response.value.filter(device => {
                    // deviceType can be windowsRT or macMDM, we only need the macMDMs type
                    const deviceType = get(device, 'deviceType', '');
                    return includes(deviceType.toLowerCase(), 'mac');
                });

                this.logger.log(
                    PolloAdapter.acct('Intune File Vault (MacOS) response', account)
                        .setIdentifier({
                            filteredEncryptionStates,
                        })
                        .setContext(this.constructor.name)
                        .setSubContext(this.getFileVaults.name),
                );

                return filteredEncryptionStates;
            }
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(
                    `Intune (MacOS) Failed trying to get File Vault information for tenant ${account.domain}`,
                    account,
                )
                    .setError(error)
                    .setContext(this.constructor.name)
                    .setSubContext(this.getFileVaults.name),
            );
        }

        return [];
    }

    private setFileVaultByDevice(
        fileVaultsValues: FileVaults[],
        completeData: CompleteDeviceData,
    ): void {
        const deviceId = get(completeData, 'id', null);
        if (!isNil(deviceId)) {
            completeData.fileVault = fileVaultsValues.find(
                (fileVaultDeviceInfo: FileVaults) => fileVaultDeviceInfo.id === deviceId,
            );
        }
    }

    private async getCustomProfilesForMac(
        customProfiles: any,
        completeData: CompleteDeviceData,
        account: Account,
        intuneBatchCallsEnabled?: boolean,
        prefetchedDevicesData?: Map<PrefetchedDeviceDataType, Map<string, ManyDataTypes>>,
    ): Promise<any> {
        try {
            const customProfilesNames = vals(customProfiles).map(({ values: items }) => items);

            let profiles: MacProfile[] | undefined = [];

            if (intuneBatchCallsEnabled) {
                profiles = (
                    prefetchedDevicesData?.get(PrefetchedDeviceDataType.MAC_PROFILES) as Map<
                        string,
                        MacProfilesData
                    >
                )?.get(completeData.id);
                if (profiles) {
                    this.logger.debug(
                        PolloAdapter.acct(
                            'Extracted mac profile from batch',
                            account,
                        ).setIdentifier({
                            clientDeviceId: completeData.id,
                            profiles,
                        }),
                    );
                } else {
                    profiles = [];
                    this.logger.warn(
                        PolloAdapter.acct(
                            'Unable to find a mac profiles entry in batch for client device id',
                            account,
                        ).setIdentifier({
                            clientDeviceId: completeData.id,
                        }),
                    );
                }
            } else {
                /**
                 * TODO: We shouldn't need to filter after due to length limitations in the query parameters and customers with long policy names.
                 * We should change these requirements so we can download only what we need. Update the help article that only x policy and n length is supported instead.
                 * OR use batch processing
                 * displayName filter is hit or miss but id filter seems to be consistent
                 * https://learn.microsoft.com/en-us/graph/filter-query-parameter?view=graph-rest-beta&tabs=http
                 */
                const requestBody = {
                    // eslint-disable-next-line max-len
                    filter: `((PolicyBaseTypeName eq 'Microsoft.Management.Services.Api.DeviceConfiguration') or (PolicyBaseTypeName eq 'DeviceManagementConfigurationPolicy') or (PolicyBaseTypeName eq 'DeviceConfigurationAdmxPolicy') or (PolicyBaseTypeName eq 'Microsoft.Management.Services.Api.DeviceManagementIntent')) and (IntuneDeviceId eq '${completeData.id}')`,
                    orderBy: ['PolicyName'],
                };
                const take = 25; // TODO: Replace with intune report page size config later
                let page = 0;
                let profileFields: ProfileFields;
                profiles = [];
                do {
                    const graphClient = await this.getThrottledGraphClient();
                    const response = await graphClient
                        .api('deviceManagement/reports/getConfigurationPoliciesReportForDevice')
                        .version('beta')
                        .post(
                            JSON.stringify({
                                ...requestBody,
                                skip: page * take,
                                take,
                            }),
                        );

                    const { parsed, values } = mapSchemaToValues(response);
                    profiles = profiles.concat(values);
                    profileFields ||= parsed;
                    page++;

                    if (account.domain === 'mews.com') {
                        this.logger.log(
                            PolloAdapter.acct(
                                `Intune custom profiles for mac call: ${completeData.id}`,
                                account,
                            ).setResult({
                                raw: response,
                            }),
                        );
                    }
                } while (profileFields.TotalRowCount > profiles.length);
            }

            if (!isEmpty(profiles)) {
                const filteredCustomProfiles = profiles.filter((deviceConfig: any) =>
                    flatten(customProfilesNames).includes(deviceConfig.PolicyName),
                );

                this.logger.log(
                    PolloAdapter.acct(
                        `Intune Custom Profiles (MacOS) found for device with id: ${completeData.id}`,
                        account,
                    )
                        .setIdentifier({
                            profiles: profiles.map(profile => profile.PolicyName),
                            customProfilesNames: flatten(customProfilesNames),
                            deviceEmail: completeData.email,
                            filteredCustomProfiles: filteredCustomProfiles.map(
                                profile => profile.PolicyName,
                            ),
                        })
                        .setContext(this.constructor.name)
                        .setSubContext(this.getCustomProfilesForMac.name),
                );

                for (const customProfile of vals(customProfiles)) {
                    const profilesValue = filteredCustomProfiles.find(customProfileValue =>
                        customProfile.values.includes(customProfileValue.PolicyName),
                    );
                    if (!isNil(profilesValue)) {
                        completeData[customProfile.key] = isEmpty(completeData[customProfile.key])
                            ? [profilesValue]
                            : [...completeData[customProfile.key], profilesValue];
                    }
                }
            } else {
                this.logger.log(
                    PolloAdapter.acct(
                        `Intune Custom Profiles (MacOS) not found for device with id: ${completeData.id}.}`,
                        account,
                    )
                        .setIdentifier({
                            deviceId: completeData.id,
                            deviceEmail: completeData.email,
                        })
                        .setContext(this.constructor.name)
                        .setSubContext(this.getCustomProfilesForMac.name),
                );
            }
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(
                    `Intune (MacOS) Failed trying to get Custom Profiles for asset with id/email: ${completeData.id}/${completeData.email}`,
                    account,
                )
                    .setIdentifier({ error })
                    .setError(error)
                    .setContext(this.constructor.name)
                    .setSubContext(this.getCustomProfilesForMac.name),
            );
            throw error;
        }
    }

    private async getScriptDevice(
        id: string,
        deviceId: string,
        account: Account,
        intuneBatchCallsEnabled?: boolean,
        prefetchedDevicesData?: Map<PrefetchedDeviceDataType, Map<string, ManyDataTypes>>,
    ): Promise<any> {
        if (isNil(id) || isNil(deviceId)) {
            return null;
        }
        try {
            let response: AntivirusData | undefined = undefined;
            if (intuneBatchCallsEnabled) {
                response = (
                    prefetchedDevicesData?.get(PrefetchedDeviceDataType.ANTIVIRUS) as Map<
                        string,
                        AntivirusData
                    >
                )?.get(deviceId);
                if (response) {
                    this.logger.debug(
                        PolloAdapter.acct(
                            'Extracted antivirus data response from batch',
                            account,
                        ).setIdentifier({
                            deviceId,
                            antivirusData: response,
                        }),
                    );
                } else {
                    this.logger.warn(
                        PolloAdapter.acct(
                            'Unable to find a antivirus data response in batch for client device id',
                            account,
                        ).setIdentifier({
                            deviceId,
                        }),
                    );
                }
            } else {
                const graphClient = await this.getThrottledGraphClient();
                response = await graphClient
                    .api(`deviceManagement/deviceManagementScripts/${id}/deviceRunStates`)
                    .filter(`endsWith(id, '${deviceId}')`)
                    .version('beta')
                    .get();
                this.logger.debug(
                    PolloAdapter.acct('antivirus response from api', account).setIdentifier({
                        deviceId,
                        antivirusData: response,
                    }),
                );
                if (!isNil(response) && !isEmpty(response.value)) {
                    return get(response, 'value[0]');
                }
            }

            return null;
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(
                    `Intune getScriptDevice failed for device with id: ${deviceId}`,
                    account,
                )
                    .setIdentifier({ error })
                    .setError(error)
                    .setContext(this.constructor.name)
                    .setSubContext(this.getScriptDevice.name),
            );
            throw error;
        }
    }

    private async getScriptIdsByName(account: Account): Promise<{
        antivirusScriptId: string;
    }> {
        const graphClient = await this.getThrottledGraphClient();
        const response = await graphClient
            .api('deviceManagement/deviceManagementScripts')
            .filter(`displayName eq '${querystring.escape(this.SCRIPT_NAMES.ANTIVIRUS)}'`)
            .version('beta')
            .get();

        const ids = {};
        if (!isNil(response) && !isEmpty(response.value)) {
            (response?.value ?? []).forEach(script => (ids[script.displayName] = script.id));
        }
        const scripts = {
            antivirusScriptId: ids[this.SCRIPT_NAMES.ANTIVIRUS] ?? null,
        };

        this.logger.log(
            PolloAdapter.acct('AntiVirus ScriptIds response', account).setIdentifier({
                raw: response.value,
                scripts,
            }),
        );

        return scripts;
    }

    @Retry(
        {
            maxRetries: 3,
            retryWait: 10000,
            exponential: true,
        },
        /**
         * Account ONLY for non-rate errors
         * GraphClient RetryHandler already handles exponential backoff in microsoft.service.ts
         * TBD: Monitor for overflow of fitler query
         */
        ...IntuneCustomConstants.INTUNE_RANDOM_ERRORS,
    )
    private async getDeviceStatus(
        deviceData: CompleteDeviceData,
        page: number,
        search: string,
    ): Promise<any> {
        const graphClient = await this.getThrottledGraphClient();
        /**
         * This is the only filter I could get to work PolicyBaseTypeName + IntuneDeviceId.
         * Does pull in Settings Catalog from UnifiedPolicyType
         * Used search to isolate user being evaluated
         *
         * This is the worst API and documentation I've worked with in 30 years
         */
        return graphClient
            .api(`/deviceManagement/reports/getConfigurationPoliciesReportForDevice`)
            .version('beta')
            .header('ConsistencyLevel', 'eventual') // more MSFT stuff to get search
            .post({
                // Had to force into post body as SDK doesn't do it for you for some reason
                top: this.INTUNE_REPORT_PAGE_SIZE, // force to get what we can in first page, we don't expect a lot (aka pageSize)
                skip: page * this.INTUNE_REPORT_PAGE_SIZE,
                count: true, // make sure count comes through even though it defaults
                select: [
                    'IntuneDeviceId',
                    'PolicyBaseTypeName',
                    'PolicyId',
                    'PolicyStatus',
                    'UPN',
                    'UserId',
                    'PolicyName',
                ],
                /**
                 * 'Microsoft.Management.Services.Api.DeviceConfiguration seems to be the
                 * main filter we need here, but reverse engineering the MSFT UX
                 * for this list uses all the configurations on same view.
                 *
                 * Also, the API gets buggy when using different combinations and crashes.
                 * It is unclear if this will allow usage of 'Settings Catalog' items.
                 **/
                filter:
                    `((PolicyBaseTypeName eq 'Microsoft.Management.Services.Api.DeviceConfiguration')` +
                    ` or (PolicyBaseTypeName eq 'DeviceManagementConfigurationPolicy')` +
                    ` or (PolicyBaseTypeName eq 'DeviceConfigurationAdmxPolicy') ` +
                    ` or (PolicyBaseTypeName eq 'Microsoft.Management.Services.Api.DeviceManagementIntent'))` +
                    ` and IntuneDeviceId eq '${deviceData.id}'`,
                search,
            });
    }

    private getCustomProfilesNames(): CustomProfilesForMac {
        const { customProfileScreensaverNameForMac, customProfileUpdateNameForMac } = this.metadata;
        const customProfiles = clone(this.DEFAULT_MAC_CUSTOM_PROFILES);
        if (!isNil(customProfileUpdateNameForMac)) {
            customProfiles.UPDATES.values = customProfileUpdateNameForMac;
        }
        if (!isNil(customProfileScreensaverNameForMac)) {
            customProfiles.SCREENSAVER.values = customProfileScreensaverNameForMac;
        }
        return customProfiles;
    }

    public getCustomPoliciesForWindows() {
        const { customPolicyUpdateName, customPolicyScreenLockName } = this.metadata;
        const customPolicies = clone(this.DEFAULT_WINDOWS_CUSTOM_POLICIES);
        if (!isNil(customPolicyUpdateName)) {
            customPolicies.UPDATES = customPolicyUpdateName;
        }
        if (!isNil(customPolicyScreenLockName)) {
            customPolicies.SCREENLOCK = customPolicyScreenLockName;
        }
        return customPolicies;
    }

    public refreshMetadata(metadata: ConnectionMetadata): void {
        const { key } = this.metadata;
        this.metadata = metadata;

        // validate if accessToken has changed
        if (key !== metadata.key) {
            this.setGraphClient();
        }
    }

    private getDeviceSummary(device: CompleteDeviceData): Partial<CompleteDeviceData> {
        const { id, deviceName, userId, email, serialNumber, primaryUser } = device;
        return {
            id,
            deviceName,
            userId,
            email,
            serialNumber,
            primaryUser,
        };
    }

    async prefetchDevicesData(
        account: Account,
        clientDevices: ClientDevice[],
        antivirusScriptId: string,
        customPoliciesForWindows: CustomPoliciesValues,
    ): Promise<Map<PrefetchedDeviceDataType, Map<string, ManyDataTypes>>> {
        const prefetchedDevicesData = new Map<
            PrefetchedDeviceDataType,
            Map<string, ManyDataTypes>
        >();

        let deviceDataResponses = new Map<string, CompleteDeviceData | null>();
        let benchmark;
        try {
            benchmark = new Benchmark();
            deviceDataResponses = await this.getDeviceDatas(account, clientDevices);
            this.logger.log(
                PolloAdapter.acct('Benchmark for getDeviceDatas()', account)
                    .setExecutionTime(benchmark.endTime())
                    .setContext(this.constructor.name)
                    .setSubContext(this.prefetchDevicesData.name),
            );
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(
                    `Failed to get deviceDataResponses from getDeviceDatas()`,
                    account,
                )
                    .setContext(this.constructor.name)
                    .setSubContext(this.prefetchDevicesData.name)
                    .setError(error),
            );
            deviceDataResponses = new Map<string, CompleteDeviceData | null>();
        }
        prefetchedDevicesData.set(PrefetchedDeviceDataType.COMPLETE_DEVICE, deviceDataResponses);

        let primaryUserResponses = new Map<string, PrimaryUserData | null>();
        try {
            benchmark = new Benchmark();
            primaryUserResponses = await this.getPrimaryUsers(account, clientDevices);
            this.logger.log(
                PolloAdapter.acct('Benchmark for getPrimaryUsers()', account)
                    .setExecutionTime(benchmark.endTime())
                    .setContext(this.constructor.name)
                    .setSubContext(this.prefetchDevicesData.name),
            );
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(
                    `Failed to get primaryUserResponses from getPrimaryUsers()`,
                    account,
                )
                    .setContext(this.constructor.name)
                    .setSubContext(this.prefetchDevicesData.name)
                    .setError(error),
            );
            primaryUserResponses = new Map<string, PrimaryUserData | null>();
        }
        prefetchedDevicesData.set(PrefetchedDeviceDataType.PRIMARY_USER, primaryUserResponses);

        let managedAppsResponses = new Map<string, MobileAppIntentAndStates | null>();
        try {
            benchmark = new Benchmark();
            managedAppsResponses = await this.getManagedAppsForDevices(
                account,
                clientDevices,
                primaryUserResponses,
            );
            this.logger.log(
                PolloAdapter.acct('Benchmark for getManagedAppsForDevices()', account)
                    .setExecutionTime(benchmark.endTime())
                    .setContext(this.constructor.name)
                    .setSubContext(this.prefetchDevicesData.name),
            );
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(
                    `Failed to get managedAppsResponses from getManagedAppsForDevices()`,
                    account,
                )
                    .setContext(this.constructor.name)
                    .setSubContext(this.prefetchDevicesData.name)
                    .setError(error),
            );
            managedAppsResponses = new Map<string, MobileAppIntentAndStates | null>();
        }
        prefetchedDevicesData.set(PrefetchedDeviceDataType.MANAGED_APPS, managedAppsResponses);

        let deviceScriptResponses = new Map<string, AntivirusData | null>();
        try {
            benchmark = new Benchmark();
            deviceScriptResponses = await this.getDeviceScripts(
                account,
                clientDevices,
                antivirusScriptId,
            );
            this.logger.log(
                PolloAdapter.acct('Benchmark for getDeviceScripts()', account)
                    .setExecutionTime(benchmark.endTime())
                    .setContext(this.constructor.name)
                    .setSubContext(this.prefetchDevicesData.name),
            );
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(
                    `Failed to get deviceScriptResponses from getDeviceScripts()`,
                    account,
                )
                    .setContext(this.constructor.name)
                    .setSubContext(this.prefetchDevicesData.name)
                    .setError(error),
            );
            deviceScriptResponses = new Map<string, AntivirusData | null>();
        }
        prefetchedDevicesData.set(PrefetchedDeviceDataType.ANTIVIRUS, deviceScriptResponses);

        let windowsCustomPoliciesResponses = new Map<string, WindowsPolicyStatusData>();
        try {
            benchmark = new Benchmark();
            windowsCustomPoliciesResponses = await this.getWindowsCustomPolicies(
                account,
                clientDevices,
                deviceDataResponses,
                customPoliciesForWindows,
            );
            this.logger.log(
                PolloAdapter.acct('Benchmark for getWindowsCustomPolicies()', account)
                    .setExecutionTime(benchmark.endTime())
                    .setContext(this.constructor.name)
                    .setSubContext(this.prefetchDevicesData.name),
            );
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(
                    `Failed to get windowsCustomPoliciesResponses from getWindowsCustomPolicies()`,
                    account,
                )
                    .setContext(this.constructor.name)
                    .setSubContext(this.prefetchDevicesData.name)
                    .setError(error),
            );
        }
        prefetchedDevicesData.set(
            PrefetchedDeviceDataType.WINDOWS_CUSTOM_POLICY,
            windowsCustomPoliciesResponses,
        );

        let macProfilesResponses = new Map<string, MacProfilesData>();
        try {
            benchmark = new Benchmark();
            macProfilesResponses = await this.getMacProfiles(
                account,
                clientDevices,
                deviceDataResponses,
            );
            this.logger.log(
                PolloAdapter.acct('Benchmark for getMacProfiles()', account)
                    .setExecutionTime(benchmark.endTime())
                    .setContext(this.constructor.name)
                    .setSubContext(this.prefetchDevicesData.name),
            );
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(
                    `Failed to get macProfilesResponses from getMacProfiles()`,
                    account,
                )
                    .setContext(this.constructor.name)
                    .setSubContext(this.prefetchDevicesData.name)
                    .setError(error),
            );
        }
        prefetchedDevicesData.set(PrefetchedDeviceDataType.MAC_PROFILES, macProfilesResponses);

        return prefetchedDevicesData;
    }

    /**
     *
     * @param account
     * @param clientDevices
     * @returns
     */
    private async getDeviceDatas(
        account: Account,
        clientDevices: ClientDevice[],
    ): Promise<Map<string, CompleteDeviceData | null>> {
        const clientDeviceIds = clientDevices.map(clientDevice => clientDevice.id);

        const client = await this.getThrottledGraphClient();

        const devicesDataManager = new ClientDeviceIdPaginationManager<
            CompleteDeviceData,
            DeviceDataPagination
        >(account, client, DeviceDataPagination, clientDeviceIds, this.getDeviceDatas.name);
        await devicesDataManager.manage();

        return devicesDataManager.getResponses();
    }

    private async getPrimaryUsers(
        account: Account,
        clientDevices: ClientDevice[],
    ): Promise<Map<string, PrimaryUserData | null>> {
        const clientDeviceIds = clientDevices.map(clientDevice => clientDevice.id);

        const client = await this.getThrottledGraphClient();

        const primaryUsersManager = new ClientDeviceIdPaginationManager<
            PrimaryUserData,
            PrimaryUserPagination
        >(account, client, PrimaryUserPagination, clientDeviceIds, this.getPrimaryUsers.name);

        await primaryUsersManager.manage();

        return primaryUsersManager.getResponses();
    }

    private async getManagedAppsForDevices(
        account: Account,
        clientDevices: ClientDevice[],
        primaryUserResponses: Map<string, PrimaryUserData | null>,
    ): Promise<Map<string, MobileAppIntentAndStates | null>> {
        const clientDeviceIds = clientDevices.map(clientDevice => clientDevice.id);

        const client = await this.getThrottledGraphClient();

        const managedAppsForDevicesPaginationManager = new ManagedAppsForDevicePaginationManager(
            account,
            client,
            clientDeviceIds,
            primaryUserResponses,
            this.getManagedAppsForDevices.name,
        );

        await managedAppsForDevicesPaginationManager.manage();

        return managedAppsForDevicesPaginationManager.getResponses();
    }

    private async getDeviceScripts(
        account: Account,
        clientDevices: ClientDevice[],
        scriptId: string,
    ): Promise<Map<string, AntivirusData | null>> {
        const clientDeviceIds = clientDevices.map(clientDevice => clientDevice.id);

        const client = await this.getThrottledGraphClient();

        const primaryUsersManager = new DeviceScriptsPaginationManager(
            account,
            client,
            clientDeviceIds,
            scriptId,
            this.getPrimaryUsers.name,
        );

        await primaryUsersManager.manage();

        return primaryUsersManager.getResponses();
    }

    async getWindowsCustomPolicies(
        account: Account,
        clientDevices: ClientDevice[],
        deviceDataResponses: Map<string, CompleteDeviceData | null>,
        customPoliciesForWindows: CustomPoliciesValues,
    ): Promise<Map<string, WindowsPolicyStatusData>> {
        const responses = new Map<string, WindowsPolicyStatusData>();

        if (this.customSyncExcludedDomains.includes(account.domain)) {
            // this customer is excluded from custom policies stuff
            return responses;
        }

        if (!Object.entries(customPoliciesForWindows).length) {
            // no custom policies set, no need to continue further
            return responses;
        }

        // find the relevant devices
        const windowsDevices: ClientDevice[] = [];
        for (const device of clientDevices) {
            // find the data for this device
            const completeDeviceData = deviceDataResponses.get(device.id);
            if (!completeDeviceData?.operatingSystem?.toLowerCase().includes('windows')) {
                /**
                 * device data info not available OR not a windows device, continue
                 */
                continue;
            }
            windowsDevices.push(device);
        }

        if (!windowsDevices.length) {
            // no windows devices found, no need to continue further
            return responses;
        }

        const customPolicies: string[] = concat(
            customPoliciesForWindows.UPDATES,
            customPoliciesForWindows.SCREENLOCK,
        );
        const defaultPolicies: string[] = concat(
            this.DEFAULT_WINDOWS_CUSTOM_POLICIES.UPDATES,
            this.DEFAULT_WINDOWS_CUSTOM_POLICIES.SCREENLOCK,
        );

        const batchLimit = config.get('intune.device.batchLimit');
        const paginations = new Map<string, WindowsCustomPolicyPagination>();

        const isEmptyDeviceReportSearchAllowed = await this.featureFlagService.evaluateAsDomain(
            {
                category: FeatureFlagCategory.NONE,
                defaultValue: false,
                name: FeatureFlag.RELEASE_EMPTY_WINDOWS_DEVICE_REPORT_SEARCH_PARAM_ALLOWED,
            },
            account.domain,
        );

        while (windowsDevices.length || paginations.size) {
            while (windowsDevices.length && paginations.size < batchLimit) {
                /**
                 * Continue to drain windowsDevices until empty.
                 * we can have at most batchLimit paginations happening
                 * at the same time.
                 */
                const device = windowsDevices.shift();
                if (!device) {
                    // no more items
                    break;
                }

                // find the data for this device
                const completeDeviceData = deviceDataResponses.get(device.id);
                if (!completeDeviceData) {
                    /**
                     * device data info not available OR not a windows device, continue
                     */
                    continue;
                }

                /**
                 * Sometimes the default policy names are put in the custom policy connection metadata.
                 * For anyone without custom policies, it's more often efficient to search by policy prefix.
                 * Unfortunately cannot get this API's search query to use an OR operator.
                 *
                 * The exception to the above is when a device has many users assigned.
                 *
                 * Device Id doesn't work as search param. Email/UPN works but only for devices that have policies
                 * applied to the user, so is better to avoid the search param when custom policies are used.
                 */
                let search = isEmptyDeviceReportSearchAllowed
                    ? ''
                    : (completeDeviceData.primaryUser?.userPrincipalName ?? '');
                if (isEqual(customPolicies, defaultPolicies)) {
                    search = this.DEFAULT_WINDOWS_POLICIES_PREFIX;
                }

                this.logger.debug(
                    PolloAdapter.acct(
                        `Adding device for windows custom policies pagination`,
                        account,
                    ).setIdentifier({
                        device,
                    }),
                );

                /**
                 * Start paginating for this device.
                 */
                paginations.set(
                    device.id,
                    new WindowsCustomPolicyPagination(
                        account,
                        device,
                        completeDeviceData,
                        search,
                        customPoliciesForWindows,
                    ),
                );
            }

            /**
             * Build requests for the current active set of paginations.
             */
            const requests: BatchRequestStep[] = [];
            for (const [, pagination] of paginations) {
                requests.push(pagination.buildBatchRequestStep());
            }

            /**
             * Make the batch call
             */
            const batchResponseContent = await this.makeBatchCall(account, requests);

            /**
             * Process the responses
             */
            for (const [deviceId, pagination] of paginations) {
                /**
                 * Get the response for this device
                 */
                const response = batchResponseContent.getResponseById(deviceId);
                const status = response?.ok;
                if (!status) {
                    if (!pagination.retriesExhausted()) {
                        this.logger.warn(
                            PolloAdapter.acct(
                                `Windows custom policies pagination response not ok, retrying`,
                                account,
                            ).setIdentifier({
                                deviceId,
                                status,
                            }),
                        );

                        pagination.retry();
                    } else {
                        /**
                         * stop pagination, could not get any data
                         */
                        this.logger.warn(
                            PolloAdapter.acct(
                                `Windows custom policies pagination exhausted retries`,
                                account,
                            ).setIdentifier({
                                deviceId,
                                status,
                            }),
                        );
                        pagination.setForceDone();
                    }
                } else {
                    /**
                     * Extract the payload from the response and process
                     */
                    pagination.processResponse(await response.text());
                }

                if (pagination.done()) {
                    /**
                     * This was the last page, get the final payload from the pagination object
                     * and set them on the main responses map.
                     * Then, delete the pagination from the active set.
                     */
                    const policyStatuses = pagination.getPolicyStatuses();
                    this.logger.debug(
                        PolloAdapter.acct(
                            `Windows custom policies pagination done for device`,
                            account,
                        ).setIdentifier({
                            deviceId,
                            policyStatuses,
                        }),
                    );
                    responses.set(deviceId, policyStatuses);
                    paginations.delete(deviceId);
                }
            }
        }

        return responses;
    }

    async getMacProfiles(
        account: Account,
        clientDevices: ClientDevice[],
        deviceDataResponses: Map<string, CompleteDeviceData | null>,
    ): Promise<Map<string, MacProfilesData>> {
        const responses = new Map<string, MacProfilesData>();

        const macDevices: ClientDevice[] = [];
        for (const device of clientDevices) {
            // find the data for this device
            const completeDeviceData = deviceDataResponses.get(device.id);
            if (!completeDeviceData?.operatingSystem?.toLowerCase().includes('macos')) {
                /**
                 * device data info not available OR not a windows device, continue
                 */
                continue;
            }
            macDevices.push(device);
        }

        if (!macDevices.length) {
            // no macos devices found, no need to continue further
            return responses;
        }

        const batchLimit = config.get('intune.device.batchLimit');
        const paginations = new Map<string, MacCustomProfilesPagination>();
        while (macDevices.length || paginations.size) {
            /**
             * See if any new devices can be added into the paginations list
             */
            while (macDevices.length && paginations.size < batchLimit) {
                /**
                 * Continue to drain macDevices until empty.
                 * we can have at most batchLimit paginations happening
                 * at the same time.
                 */
                const device = macDevices.shift();
                if (!device) {
                    // no more items
                    break;
                }

                // find the data for this device
                const completeDeviceData = deviceDataResponses.get(device.id);
                if (!completeDeviceData) {
                    /**
                     * device data info not available OR not a windows device, continue
                     */
                    continue;
                }

                this.logger.debug(
                    PolloAdapter.acct(
                        `Adding device for mac profiles pagination`,
                        account,
                    ).setIdentifier({
                        device,
                    }),
                );

                /**
                 * Start paginating for this device.
                 */
                paginations.set(
                    device.id,
                    new MacCustomProfilesPagination(account, device, completeDeviceData),
                );
            }

            /**
             * Build requests for the current active set of paginations.
             */
            const requests: BatchRequestStep[] = [];
            for (const [, pagination] of paginations) {
                requests.push(pagination.buildBatchRequestStep());
            }

            /**
             * Make the batch call
             */
            const batchResponseContent = await this.makeBatchCall(account, requests);

            /**
             * Process the responses
             */
            for (const [deviceId, pagination] of paginations) {
                /**
                 * Get the response for this device
                 */
                const response = batchResponseContent.getResponseById(deviceId);
                const status = response?.ok;
                if (!status) {
                    if (!pagination.retriesExhausted()) {
                        this.logger.debug(
                            PolloAdapter.acct(
                                `Mac profiles pagination response not ok, retrying`,
                                account,
                            ).setIdentifier({
                                deviceId,
                                status,
                            }),
                        );
                        pagination.retry();
                    } else {
                        /**
                         * stop pagination, could not get any data
                         */
                        this.logger.warn(
                            PolloAdapter.acct(
                                `Mac profiles pagination exhausted retries`,
                                account,
                            ).setIdentifier({
                                deviceId,
                                status,
                            }),
                        );

                        pagination.setForceDone();
                    }
                } else {
                    /**
                     * Extract the payload from the response and process
                     */
                    pagination.processResponse(await response.text());
                }

                if (pagination.done()) {
                    /**
                     * This was the last page, get the final payload from the pagination object
                     * and set them on the main responses map.
                     * Then, delete the pagination from the active set.
                     */
                    const profiles = pagination.getProfiles();
                    this.logger.debug(
                        PolloAdapter.acct(
                            `Mac profiles pagination done for device`,
                            account,
                        ).setIdentifier({
                            deviceId,
                            profiles,
                        }),
                    );
                    responses.set(deviceId, profiles);
                    paginations.delete(deviceId);
                }
            }
        }

        return responses;
    }

    private async makeBatchCall(
        account,
        requests: BatchRequestStep[],
    ): Promise<BatchResponseContent> {
        const batchRequestContent = new BatchRequestContent(requests);
        const content = await batchRequestContent.getContent();
        this.logger.debug(
            PolloAdapter.acct(`batchRequestContent content`, account).setIdentifier({
                content,
            }),
        );

        const graphClient = await this.getThrottledGraphClient();
        const batchResponse = await graphClient.api('/$batch').version('beta').post(content);

        return new BatchResponseContent(batchResponse);
    }
}
