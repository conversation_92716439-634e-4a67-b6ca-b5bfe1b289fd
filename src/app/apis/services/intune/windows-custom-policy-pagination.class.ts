import { BatchRequestStep } from '@microsoft/microsoft-graph-client';
import { BatchPagination } from 'app/apis/services/intune/batch-pagination.class';
import {
    ClientDevice,
    CompleteDeviceData,
    CustomPoliciesValues,
    WindowsPolicyStatusData,
} from 'app/apis/types/intune/intune.types';
import { Account } from 'auth/entities/account.entity';
import { PolloAdapter } from 'commons/adapters/pollo.adapter';
import config from 'config';
import { concat, isEmpty } from 'lodash';
import { PolloLogger } from 'pollo-logger/pollo.logger';

/**
 *
 */
export class WindowsCustomPolicyPagination extends BatchPagination {
    private readonly INTUNE_REPORT_PAGE_SIZE = config.get('intune.device.pageSize');

    protected logger = PolloLogger.logger(this.constructor.name);

    private policyStatuses: WindowsPolicyStatusData;

    private response: any;

    private customPolicies: string[];

    private customUpdatesPolicies: string[];

    constructor(
        private readonly account: Account,
        private readonly clientDevice: ClientDevice,
        private readonly deviceData: CompleteDeviceData,
        private readonly search: string,
        customPolicies: CustomPoliciesValues,
    ) {
        super();
        this.policyStatuses = [];
        this.customPolicies = concat(customPolicies.UPDATES, customPolicies.SCREENLOCK);
        this.customUpdatesPolicies = customPolicies.UPDATES ?? []; // Fallback to empty array if no updates policies configured
    }

    /**
     * Notes from legacy code:
     * This is the only filter I could get to work PolicyBaseTypeName + IntuneDeviceId.
     * Does pull in Settings Catalog from UnifiedPolicyType
     * Used search to isolate user being evaluated
     *
     * This is the worst API and documentation I've worked with in 30 years
     */
    buildBatchRequestStep(): BatchRequestStep {
        const url = `https://graph.microsoft.com/deviceManagement/reports/getConfigurationPoliciesReportForDevice`;

        const method = 'POST';

        const headers = {
            ConsistencyLevel: 'eventual',
            'Content-Type': 'application/json',
        };

        const body = JSON.stringify({
            // Had to force into post body as SDK doesn't do it for you for some reason
            top: this.INTUNE_REPORT_PAGE_SIZE, // force to get what we can in first page, we don't expect a lot (aka pageSize)
            skip: this.page * this.INTUNE_REPORT_PAGE_SIZE,
            count: true, // make sure count comes through even though it defaults
            select: [
                'IntuneDeviceId',
                'PolicyBaseTypeName',
                'PolicyId',
                'PolicyStatus',
                'UPN',
                'UserId',
                'PolicyName',
            ],
            /**
             * 'Microsoft.Management.Services.Api.DeviceConfiguration seems to be the
             * main filter we need here, but reverse engineering the MSFT UX
             * for this list uses all the configurations on same view.
             *
             * Also, the API gets buggy when using different combinations and crashes.
             * It is unclear if this will allow usage of 'Settings Catalog' items.
             **/
            filter: [
                `((PolicyBaseTypeName eq 'Microsoft.Management.Services.Api.DeviceConfiguration')`,
                `or (PolicyBaseTypeName eq 'DeviceManagementConfigurationPolicy')`,
                `or (PolicyBaseTypeName eq 'DeviceConfigurationAdmxPolicy')`,
                `or (PolicyBaseTypeName eq 'Microsoft.Management.Services.Api.DeviceManagementIntent'))`,
                `and IntuneDeviceId eq '${this.clientDevice.id}'`,
            ].join(' '),
            search: this.search,
        });

        this.page++;

        return {
            id: this.clientDevice.id,
            request: new Request(url, {
                method,
                body,
                headers,
            }),
        };
    }

    processResponse(result: string): void {
        // decode
        const decoded = this.decodeBase64(result);
        this.response = JSON.parse(String(decoded));

        /**
         * We cannot trust that MSFT won't reorder the schema on every page.
         * Based on what we've seen with these APIs, using extra caution.
         *
         * Limit collected data to the policies and user needed.
         * This should happen during search but filtering wasn't supported for the fields.
         */
        if (isEmpty(this.response?.Values) || this.response?.TotalRowCount === 0) {
            this.logger.warn(
                PolloAdapter.acct(
                    `Prefetched Intune Custom Policies response returned empty response ${this.deviceData.deviceName}`,
                    this.account,
                )
                    .setIdentifier({
                        deviceId: this.deviceData.id,
                    })
                    .setMetadata({
                        customPolicies: this.customPolicies,
                        policyStatuses: this.policyStatuses,
                        page: this.page,
                        response: this.response,
                        userPrincipleName: this.deviceData.primaryUser?.userPrincipalName,
                    })
                    .setContext(this.constructor.name)
                    .setSubContext(this.processResponse.name),
            );
            this.setForceDone();
            return;
        }

        this.policyStatuses = concat(
            this.policyStatuses,
            this.response?.Values?.map((row: any[]) =>
                this.response?.Schema.reduce((policy: any, col: any, prop: number) => {
                    policy[col.Column] = row[prop];
                    return policy;
                }, {}),
            ).filter(
                r =>
                    (this.customUpdatesPolicies.includes(r.PolicyName) ||
                        !this.deviceData.primaryUser?.userPrincipalName ||
                        r.UPN == this.deviceData.primaryUser.userPrincipalName) &&
                    this.customPolicies.includes(r.PolicyName),
            ),
        );

        // TEMP: should be small, first few pages (some customers maxing out just under 200 records)
        this.logger.log(
            PolloAdapter.acct(
                `Prefetched Intune Custom Policies response for device ${this.deviceData.deviceName}`,
                this.account,
            )
                .setIdentifier({
                    deviceId: this.deviceData.id,
                })
                .setMetadata({
                    customPolicies: this.customPolicies,
                    policyStatuses: this.policyStatuses,
                    page: this.page,
                    response: this.response,
                    userPrincipleName: this.deviceData.primaryUser?.userPrincipalName,
                })
                .setContext(this.constructor.name)
                .setSubContext(this.processResponse.name),
        );
    }

    getPolicyStatuses(): WindowsPolicyStatusData {
        return this.policyStatuses;
    }

    done(): boolean {
        return (
            !(
                this.response?.TotalRowCount > this.INTUNE_REPORT_PAGE_SIZE * this.page &&
                this.response?.Values?.length > 0
            ) || this.forceDone
        );
    }

    decodeBase64(encodedString) {
        const decodedBuffer = Buffer.from(encodedString, 'base64');
        // or 'ascii' depending on the original encoding
        return decodedBuffer.toString('utf-8');
    }
}
