import { IGitlabQueryBuilder } from 'app/apis/services/gitlab/graphql/gitlab-query-builder.interface';
import { GitLabQueryBuilder } from 'app/apis/services/gitlab/graphql/gitlab-query.builder';
import { GitLabQueryBuilder as GitLabQueryBuilder_16_11_10_ee } from 'app/apis/services/gitlab/graphql/versions/16.11.10-ee-gitlab-query.builder';
import { isNil, startsWith } from 'lodash';

export class GitlabQueryBuilderFactory {
    private static instance: GitlabQueryBuilderFactory;

    static getInstance(): GitlabQueryBuilderFactory {
        if (isNil(GitlabQueryBuilderFactory.instance)) {
            GitlabQueryBuilderFactory.instance = new GitlabQueryBuilderFactory();
        }

        return GitlabQueryBuilderFactory.instance;
    }

    getBuilder(version: string | null): IGitlabQueryBuilder {
        if (isNil(version)) {
            return GitLabQueryBuilder.getInstance();
        }

        /**
         * https://drata.atlassian.net/browse/ENG-76412
         */
        if (startsWith(version, '16')) {
            /**
             * If the version is 16.* return GitLabQueryBuilder_16_11_10_ee - this
             * will remove the request for the 'active' field which is not supported
             *
             * GitLabQueryBuilder_16_11_10_ee does not support this field so use this
             * class for all 16.* versions - it is a subset of the unsupported versions.
             */
            return GitLabQueryBuilder_16_11_10_ee.getInstance();
        }

        switch (version) {
            case GitLabQueryBuilder_16_11_10_ee.version.toString(): {
                return GitLabQueryBuilder_16_11_10_ee.getInstance();
            }
            default: {
                return GitLabQueryBuilder.getInstance();
            }
        }
    }
}
