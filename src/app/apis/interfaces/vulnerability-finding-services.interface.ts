import { VulnerabilitySeverity } from '@drata/enums';
import { IVulnerabilityServices } from 'app/apis/interfaces/vulnerability-services.interface';
import { ServicePageableType as Paginated } from 'app/apis/types/service/service-pageable.type';
import { VulnerabilityFindingEntity } from 'app/vulnerability/entities/monitoring/vulnerability-finding.entity';
import { IVulnerabilityData } from 'app/vulnerability/interfaces/monitoring/vulnerability-data.interface';
import { VulnerabilityInfrastructureInfoType } from 'app/vulnerability/types/monitoring/vulnerability-infrastructure-info.type';
import { VulnerabilitySlaConfig } from 'app/vulnerability/types/monitoring/vulnerability-sla-config.type';

export interface IVulnerabilityFindingsServices extends IVulnerabilityServices {
    listFindings(
        regions: string[],
        severity: VulnerabilitySeverity[],
        firstFound: Date | null,
        slaMatrix: VulnerabilitySlaConfig[],
        nextPageToken: string | number | null,
        maxResults: number,
    ): Promise<Paginated<IVulnerabilityData>>;

    listFindingsById(
        ids: string[],
        slaMatrix: VulnerabilitySlaConfig[],
        region?: string | null,
    ): Promise<IVulnerabilityData[]>;

    getFindingIdsToUpdate(findings: VulnerabilityFindingEntity[]): string[];

    getInfrastructureInfo(key: string): Promise<VulnerabilityInfrastructureInfoType>;

    getMaxResults(): number;

    getMaxResultsUpdate(): number;

    getMaxParallelizedResultsToUpdate(): number;

    getNextPageToken(region: string): string | null;
}
