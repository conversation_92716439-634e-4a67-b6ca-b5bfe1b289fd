import { InternalServerErrorException, NotFoundException } from '@nestjs/common';
import { User } from 'app/users/entities/user.entity';
import { VendorTrustCenterAccessRequest } from 'app/users/vendors/entities/vendor-trust-center-access-request.entity';
import { Vendor } from 'app/users/vendors/entities/vendor.entity';
import { VendorRepository } from 'app/users/vendors/repositories/vendor.repository';
import { VendorTrustCenterAccessRequestRequirements } from 'app/users/vendors/types/vendor-trust-center-access-requirements.type';
import { VendorsTrustCenterService } from 'app/users/vendors/vendors-trust-center.service';
import { VendorTrustCenterAccessRequestRepository } from 'app/vendor-access-requests/repositories/vendor-trust-center-access-request.repository';
import { Account } from 'auth/entities/account.entity';
import { NotContentException } from 'commons/exceptions/not-content.exception';
import { createAppTestingModule } from 'commons/helpers/app-testing-module.helper';
import { SBAccessRequestFieldType } from 'dependencies/safebase/access-request-field-type.enum';
import { SBCategoryType } from 'dependencies/safebase/category-type.enum';
import { SBItemType } from 'dependencies/safebase/item-type.enum';
import { SBOrganizationDocumentAccessType } from 'dependencies/safebase/organization-document-access-type.enum';
import { SBPartnerRequestAccessStatusCode } from 'dependencies/safebase/partner-request-access-status-code.enum';
import { SafebaseVendorTrustCenterMaturity } from 'dependencies/safebase/safebase-vendor-trust-center-maturity.enum';
import { SafeBaseVendorTrustCenterSdk } from 'dependencies/safebase/safebase-vendor-trust-center.sdk';
import {
    SBAccessRequestRequirements,
    SBBaseItem,
    SBTrustCenterDocument,
    SBTrustCenterDocumentDownload,
    SBTrustCenterInfo,
    SBTrustCenterLegalItem,
    SBTrustCenterOverviewItem,
} from 'dependencies/safebase/safebase-vendor-trust-center.types';
import { VendorDirectoryItemV2 } from 'dependencies/vendor-directory/types/vendor-directory-item.type';
import { VendorDirectory } from 'dependencies/vendor-directory/vendor-directory';
import { VendorDirectoryFactory } from 'dependencies/vendor-directory/vendor-directory-factory';
import { MockProxy, mock } from 'jest-mock-extended';
import { factory, useSeeding } from 'typeorm-seeding';

describe('VendorsTrustCenterService', () => {
    let service: VendorsTrustCenterService;
    let mockSafeBaseSdk: MockProxy<SafeBaseVendorTrustCenterSdk>;
    let mockVendorDirectoryFactory: MockProxy<VendorDirectoryFactory>;
    let mockVendorDirectory: MockProxy<VendorDirectory>;
    let mockVendorRepository: MockProxy<VendorRepository>;
    let mockVendorTrustCenterAccessRequestRepository: MockProxy<VendorTrustCenterAccessRequestRepository>;
    let mockAccount: Account;

    const mockUser = {
        id: 1,
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
    } as User;

    const mockVendor = {
        id: 1,
        name: 'Test Vendor',
        url: 'https://example.com',
    } as Vendor;

    const mockVendorDirectoryItem = {
        id: 'SB:org123:product456',
        name: 'Test Vendor',
        description: 'Test vendor description',
    } as VendorDirectoryItemV2;

    const mockTrustCenterInfo = {
        name: 'Test Trust Center',
        organizationId: 'org123',
        trustCenterUrl: 'https://trust.example.com',
        products: {
            product1: { name: 'Product 1', slug: 'product1' },
            product2: { name: 'Product 2', slug: 'product2' },
        },
    } as SBTrustCenterInfo;

    beforeAll(async () => {
        await useSeeding({ configName: './src/tests/ormconfig-unit-tests' });
        mockAccount = await factory(Account)().make();
    });

    beforeEach(async () => {
        mockSafeBaseSdk = mock<SafeBaseVendorTrustCenterSdk>();
        mockVendorDirectory = mock<VendorDirectory>();
        mockVendorDirectoryFactory = mock<VendorDirectoryFactory>({
            create: jest.fn().mockReturnValue(mockVendorDirectory),
        });
        mockVendorRepository = mock<VendorRepository>();
        mockVendorTrustCenterAccessRequestRepository =
            mock<VendorTrustCenterAccessRequestRepository>();

        const module = await createAppTestingModule({
            providers: [
                VendorsTrustCenterService,
                { provide: SafeBaseVendorTrustCenterSdk, useValue: mockSafeBaseSdk },
                { provide: VendorDirectoryFactory, useValue: mockVendorDirectoryFactory },
            ],
        }).compile();

        service = module.get<VendorsTrustCenterService>(VendorsTrustCenterService);

        // Mock the parent class methods
        jest.spyOn(service, 'getCustomTenantRepository').mockImplementation(repository => {
            if (repository === VendorRepository) {
                return mockVendorRepository;
            }
            if (repository === VendorTrustCenterAccessRequestRepository) {
                return mockVendorTrustCenterAccessRequestRepository;
            }
            return mock();
        });

        jest.clearAllMocks();
    });

    describe('getVendorTrustCenter', () => {
        it('should return vendor trust center information successfully', async () => {
            // Arrange
            mockVendorRepository.findOne.mockResolvedValue(mockVendor);
            mockVendorDirectory.searchVendor.mockResolvedValue(mockVendorDirectoryItem);
            mockSafeBaseSdk.getTrustCenterInfo.mockResolvedValue(mockTrustCenterInfo);
            mockSafeBaseSdk.getProductsName.mockReturnValue(['Product 1', 'Product 2']);

            // Act
            const result = await service.getVendorTrustCenter(mockAccount, 1);

            // Assert
            expect(result).toEqual({
                name: 'Test Trust Center',
                organizationId: 'org123',
                trustCenterUrl: 'https://trust.example.com',
                products: ['Product 1', 'Product 2'],
            });
            expect(mockVendorRepository.findOne).toHaveBeenCalledWith({
                where: { id: 1 },
                select: ['id', 'url'],
            });
            expect(mockVendorDirectory.searchVendor).toHaveBeenCalledWith('example.com');
            expect(mockSafeBaseSdk.getTrustCenterInfo).toHaveBeenCalledWith('org123');
        });

        it('should throw NotContentException when vendor is not found', async () => {
            // Arrange
            mockVendorRepository.findOne.mockResolvedValue(null);

            // Act & Assert
            await expect(service.getVendorTrustCenter(mockAccount, 1)).rejects.toThrow(
                NotContentException,
            );
            expect(mockVendorRepository.findOne).toHaveBeenCalledWith({
                where: { id: 1 },
                select: ['id', 'url'],
            });
        });

        it('should throw NotContentException when vendor URL is null', async () => {
            // Arrange
            const vendorWithoutUrl = { ...mockVendor, url: null } as Vendor;
            mockVendorRepository.findOne.mockResolvedValue(vendorWithoutUrl);

            // Act & Assert
            await expect(service.getVendorTrustCenter(mockAccount, 1)).rejects.toThrow(
                NotContentException,
            );
        });

        it('should throw NotContentException when vendor directory item is not found', async () => {
            // Arrange
            mockVendorRepository.findOne.mockResolvedValue(mockVendor);
            mockVendorDirectory.searchVendor.mockResolvedValue(null);

            // Act & Assert
            await expect(service.getVendorTrustCenter(mockAccount, 1)).rejects.toThrow(
                NotContentException,
            );
        });

        it('should throw NotContentException when SafeBase SDK fails', async () => {
            // Arrange
            mockVendorRepository.findOne.mockResolvedValue(mockVendor);
            mockVendorDirectory.searchVendor.mockResolvedValue(mockVendorDirectoryItem);
            mockSafeBaseSdk.getTrustCenterInfo.mockRejectedValue(new Error('SafeBase API error'));

            // Act & Assert
            await expect(service.getVendorTrustCenter(mockAccount, 1)).rejects.toThrow(
                NotContentException,
            );
        });
    });

    describe('getVendorTrustCenterOverview', () => {
        it('should return vendor trust center overview successfully', async () => {
            // Arrange
            const mockOverviewItems = [
                {
                    id: 'overview1',
                    title: 'Overview',
                    itemType: SBItemType.LEGAL_SUB_PROCESSORS,
                    categoryType: SBCategoryType.OVERVIEW,
                    categoryTitle: 'Overview',
                    explanation: 'This is the trust center overview explanation',
                },
            ] as SBTrustCenterOverviewItem[];
            mockVendorRepository.findOne.mockResolvedValue(mockVendor);
            mockVendorDirectory.searchVendor.mockResolvedValue(mockVendorDirectoryItem);
            mockSafeBaseSdk.getTrustCenterOverviewItems.mockResolvedValue(mockOverviewItems);

            // Act
            const result = await service.getVendorTrustCenterOverview(mockAccount, 1);

            // Assert
            expect(result).toEqual({
                organizationId: 'org123',
                overview: 'This is the trust center overview explanation',
            });
            expect(mockSafeBaseSdk.getTrustCenterOverviewItems).toHaveBeenCalledWith('org123', [
                'product456',
            ]);
        });

        it('should throw NotContentException when SafeBase SDK fails', async () => {
            // Arrange
            mockVendorRepository.findOne.mockResolvedValue(mockVendor);
            mockVendorDirectory.searchVendor.mockResolvedValue(mockVendorDirectoryItem);
            mockSafeBaseSdk.getTrustCenterOverviewItems.mockRejectedValue(
                new Error('SafeBase API error'),
            );

            // Act & Assert
            await expect(service.getVendorTrustCenterOverview(mockAccount, 1)).rejects.toThrow(
                NotContentException,
            );
        });
    });

    describe('getVendorTrustCenterCertifications', () => {
        it('should return vendor trust center certifications successfully', async () => {
            // Arrange
            const mockComplianceItems = [
                {
                    id: 'cert1',
                    title: 'SOC 2 Type II',
                    itemType: SBItemType.LEGAL_SUB_PROCESSORS,
                    categoryType: SBCategoryType.COMPLIANCE,
                    categoryTitle: 'Compliance',
                    logo: 'https://example.com/logo.png',
                },
                {
                    id: 'cert2',
                    title: 'ISO 27001',
                    itemType: SBItemType.LEGAL_SUB_PROCESSORS,
                    categoryType: SBCategoryType.COMPLIANCE,
                    categoryTitle: 'Compliance',
                    logo: null,
                },
            ] as SBBaseItem[];

            // Mock valid access token
            mockVendorTrustCenterAccessRequestRepository.findAccessRequestByVendorIdAndRequestedId.mockResolvedValue(
                {
                    token: 'valid-token',
                    expiresAt: new Date(Date.now() + 60 * 60 * 1000),
                    status: 'granted',
                },
            );

            mockVendorRepository.findOne.mockResolvedValue(mockVendor);
            mockVendorDirectory.searchVendor.mockResolvedValue(mockVendorDirectoryItem);
            mockSafeBaseSdk.getTrustCenterComplianceItems.mockResolvedValue(mockComplianceItems);

            // Act
            const result = await service.getVendorTrustCenterCertifications(
                mockAccount,
                1,
                mockUser,
            );

            // Assert
            expect(result).toEqual([
                {
                    id: 'cert1',
                    name: 'SOC 2 Type II',
                    type: SBItemType.LEGAL_SUB_PROCESSORS,
                    maturityLevel: "na",
                    imgUrl: 'https://example.com/logo.png',
                    maturityLevel: SafebaseVendorTrustCenterMaturity.NA,
                },
                {
                    id: 'cert2',
                    name: 'ISO 27001',
                    maturityLevel: "na",
                    type: SBItemType.LEGAL_SUB_PROCESSORS,
                    imgUrl: '',
                    maturityLevel: SafebaseVendorTrustCenterMaturity.NA,
                },
            ]);
            expect(mockSafeBaseSdk.getTrustCenterComplianceItems).toHaveBeenCalledWith(
                'org123',
                ['product456'],
                'valid-token',
            );
        });

        it('should throw NotContentException when SafeBase SDK fails', async () => {
            // Arrange
            mockVendorRepository.findOne.mockResolvedValue(mockVendor);
            mockVendorDirectory.searchVendor.mockResolvedValue(mockVendorDirectoryItem);
            mockSafeBaseSdk.getTrustCenterComplianceItems.mockRejectedValue(
                new Error('SafeBase API error'),
            );

            // Act & Assert
            await expect(
                service.getVendorTrustCenterCertifications(mockAccount, 1, mockUser),
            ).rejects.toThrow(NotContentException);
        });
    });

    describe('getVendorTrustCenterSubProcessors', () => {
        it('should return vendor trust center sub-processors successfully', async () => {
            // Arrange
            const mockLegalItems = [
                {
                    id: 'legal1',
                    title: 'Sub Processors',
                    itemType: SBItemType.LEGAL_SUB_PROCESSORS,
                    categoryType: SBCategoryType.LEGAL,
                    categoryTitle: 'Legal',
                    listEntries: [
                        {
                            company: { name: 'Sub Processor 1' },
                            location: 'US',
                            additionalDetails: 'Data processing',
                        },
                        {
                            company: { name: 'Sub Processor 2' },
                            location: 'EU',
                            additionalDetails: 'Analytics',
                        },
                    ],
                },
                {
                    id: 'legal2',
                    title: 'Privacy Policy',
                    itemType: SBItemType.LEGAL_SUB_PROCESSORS,
                    categoryType: SBCategoryType.LEGAL,
                    categoryTitle: 'Legal',
                    listEntries: [],
                },
            ] as SBTrustCenterLegalItem[];

            // Mock valid access token
            mockVendorTrustCenterAccessRequestRepository.findAccessRequestByVendorIdAndRequestedId.mockResolvedValue(
                {
                    token: 'valid-token',
                    expiresAt: new Date(Date.now() + 60 * 60 * 1000),
                    status: 'granted',
                },
            );

            mockVendorRepository.findOne.mockResolvedValue(mockVendor);
            mockVendorDirectory.searchVendor.mockResolvedValue(mockVendorDirectoryItem);
            mockSafeBaseSdk.getTrustCenterLegalItems.mockResolvedValue(mockLegalItems);

            // Act
            const result = await service.getVendorTrustCenterSubProcessors(
                mockAccount,
                1,
                mockUser,
            );

            // Assert
            expect(result).toEqual([
                {
                    company: { name: 'Sub Processor 1' },
                    location: 'US',
                    additionalDetails: 'Data processing',
                },
                {
                    company: { name: 'Sub Processor 2' },
                    location: 'EU',
                    additionalDetails: 'Analytics',
                },
            ]);
            expect(mockSafeBaseSdk.getTrustCenterLegalItems).toHaveBeenCalledWith(
                'org123',
                ['product456'],
                'valid-token',
            );
        });

        it('should return empty array when no sub-processors found', async () => {
            // Arrange
            const mockLegalItems = [
                {
                    id: 'legal1',
                    title: 'Privacy Policy',
                    itemType: SBItemType.LEGAL_SUB_PROCESSORS,
                    categoryType: SBCategoryType.LEGAL,
                    categoryTitle: 'Legal',
                    listEntries: [],
                },
            ] as SBTrustCenterLegalItem[];
            mockVendorRepository.findOne.mockResolvedValue(mockVendor);
            mockVendorDirectory.searchVendor.mockResolvedValue(mockVendorDirectoryItem);
            mockSafeBaseSdk.getTrustCenterLegalItems.mockResolvedValue(mockLegalItems);

            // Act
            const result = await service.getVendorTrustCenterSubProcessors(
                mockAccount,
                1,
                mockUser,
            );

            // Assert
            expect(result).toEqual([]);
        });

        it('should handle sub-processors with null listEntries', async () => {
            // Arrange
            const mockLegalItems = [
                {
                    id: 'legal1',
                    title: 'Sub Processors',
                    itemType: SBItemType.LEGAL_SUB_PROCESSORS,
                    categoryType: SBCategoryType.LEGAL,
                    categoryTitle: 'Legal',
                    listEntries: undefined,
                },
            ] as SBTrustCenterLegalItem[];
            mockVendorRepository.findOne.mockResolvedValue(mockVendor);
            mockVendorDirectory.searchVendor.mockResolvedValue(mockVendorDirectoryItem);
            mockSafeBaseSdk.getTrustCenterLegalItems.mockResolvedValue(mockLegalItems);

            // Act
            const result = await service.getVendorTrustCenterSubProcessors(
                mockAccount,
                1,
                mockUser,
            );

            // Assert
            expect(result).toEqual([]);
        });

        it('should throw NotContentException when SafeBase SDK fails', async () => {
            // Arrange
            mockVendorRepository.findOne.mockResolvedValue(mockVendor);
            mockVendorDirectory.searchVendor.mockResolvedValue(mockVendorDirectoryItem);
            mockSafeBaseSdk.getTrustCenterLegalItems.mockRejectedValue(
                new Error('SafeBase API error'),
            );

            // Act & Assert
            await expect(
                service.getVendorTrustCenterSubProcessors(mockAccount, 1, mockUser),
            ).rejects.toThrow(NotContentException);
        });
    });

    describe('getVendorTrustCenterDocuments', () => {
        it('should return vendor trust center documents successfully', async () => {
            // Arrange
            const mockDocuments = [
                {
                    id: 'doc1',
                    name: 'SOC 2 Report',
                    publicAccess: true,
                    documentAccessType: SBOrganizationDocumentAccessType.ViewAndDownload,
                },
                {
                    id: 'doc2',
                    name: 'Privacy Policy',
                    publicAccess: false,
                    documentAccessType: SBOrganizationDocumentAccessType.ViewOnly,
                },
            ] as SBTrustCenterDocument[];

            // Mock valid access token
            mockVendorTrustCenterAccessRequestRepository.findAccessRequestByVendorIdAndRequestedId.mockResolvedValue(
                {
                    token: 'valid-token',
                    expiresAt: new Date(Date.now() + 60 * 60 * 1000),
                    status: 'granted',
                },
            );

            mockVendorRepository.findOne.mockResolvedValue(mockVendor);
            mockVendorDirectory.searchVendor.mockResolvedValue(mockVendorDirectoryItem);
            mockSafeBaseSdk.getTrustCenterDocuments.mockResolvedValue(mockDocuments);

            // Act
            const result = await service.getVendorTrustCenterDocuments(mockAccount, 1, mockUser);

            // Assert
            expect(result).toEqual([
                {
                    id: 'doc1',
                    name: 'SOC 2 Report',
                    isViewOnly: false,
                    isAccessRequired: false,
                },
                {
                    id: 'doc2',
                    name: 'Privacy Policy',
                    isViewOnly: true,
                    isAccessRequired: true,
                },
            ]);
            expect(mockSafeBaseSdk.getTrustCenterDocuments).toHaveBeenCalledWith(
                'org123',
                ['product456'],
                'valid-token',
            );
        });

        it('should handle duplicate documents and prioritize public access', async () => {
            // Arrange
            const mockDocuments = [
                {
                    id: 'doc1',
                    name: 'SOC 2 Report',
                    publicAccess: false,
                    documentAccessType: SBOrganizationDocumentAccessType.ViewAndDownload,
                },
                {
                    id: 'doc1',
                    name: 'SOC 2 Report',
                    publicAccess: true,
                    documentAccessType: SBOrganizationDocumentAccessType.ViewAndDownload,
                },
            ] as SBTrustCenterDocument[];
            mockVendorRepository.findOne.mockResolvedValue(mockVendor);
            mockVendorDirectory.searchVendor.mockResolvedValue(mockVendorDirectoryItem);
            mockSafeBaseSdk.getTrustCenterDocuments.mockResolvedValue(mockDocuments);

            // Act
            const result = await service.getVendorTrustCenterDocuments(mockAccount, 1, mockUser);

            // Assert
            expect(result).toHaveLength(1);
            expect(result[0]).toEqual({
                id: 'doc1',
                name: 'SOC 2 Report',
                isViewOnly: false,
                isAccessRequired: false,
            });
        });

        it('should throw NotContentException when SafeBase SDK fails', async () => {
            // Arrange
            mockVendorRepository.findOne.mockResolvedValue(mockVendor);
            mockVendorDirectory.searchVendor.mockResolvedValue(mockVendorDirectoryItem);
            mockSafeBaseSdk.getTrustCenterDocuments.mockRejectedValue(
                new Error('SafeBase API error'),
            );

            // Act & Assert
            await expect(
                service.getVendorTrustCenterDocuments(mockAccount, 1, mockUser),
            ).rejects.toThrow(NotContentException);
        });
    });

    describe('getVendorTrustCenterDocumentsById', () => {
        it('should return vendor trust center document download data successfully', async () => {
            // Arrange
            const mockDocumentDownload = [
                {
                    url: 'https://example.com/document.pdf',
                    fileName: 'SOC2_Report.pdf',
                },
            ] as SBTrustCenterDocumentDownload[];

            // Mock valid access token
            mockVendorTrustCenterAccessRequestRepository.findAccessRequestByVendorIdAndRequestedId.mockResolvedValue(
                {
                    token: 'valid-token',
                    expiresAt: new Date(Date.now() + 60 * 60 * 1000),
                    status: 'granted',
                },
            );

            mockVendorRepository.findOne.mockResolvedValue(mockVendor);
            mockVendorDirectory.searchVendor.mockResolvedValue(mockVendorDirectoryItem);
            mockSafeBaseSdk.getTrustCenterDocumentsById.mockResolvedValue(mockDocumentDownload);

            // Act
            const result = await service.getVendorTrustCenterDocumentsById(
                mockAccount,
                1,
                'doc1',
                mockUser,
            );

            // Assert
            expect(result).toEqual({
                url: 'https://example.com/document.pdf',
                fileName: 'SOC2_Report.pdf',
            });
            expect(mockSafeBaseSdk.getTrustCenterDocumentsById).toHaveBeenCalledWith(
                'org123',
                'doc1',
                ['product456'],
                'valid-token',
            );
        });

        it('should handle missing fileName', async () => {
            // Arrange
            const mockDocumentDownload = [
                {
                    url: 'https://example.com/document.pdf',
                    fileName: undefined,
                },
            ] as SBTrustCenterDocumentDownload[];
            mockVendorRepository.findOne.mockResolvedValue(mockVendor);
            mockVendorDirectory.searchVendor.mockResolvedValue(mockVendorDirectoryItem);
            mockSafeBaseSdk.getTrustCenterDocumentsById.mockResolvedValue(mockDocumentDownload);

            // Act
            const result = await service.getVendorTrustCenterDocumentsById(
                mockAccount,
                1,
                'doc1',
                mockUser,
            );

            // Assert
            expect(result).toEqual({
                url: 'https://example.com/document.pdf',
                fileName: '',
            });
        });

        it('should throw NotFoundException when document is not found', async () => {
            // Arrange
            mockVendorRepository.findOne.mockResolvedValue(mockVendor);
            mockVendorDirectory.searchVendor.mockResolvedValue(mockVendorDirectoryItem);
            mockSafeBaseSdk.getTrustCenterDocumentsById.mockResolvedValue([]);

            // Act & Assert
            await expect(
                service.getVendorTrustCenterDocumentsById(mockAccount, 1, 'doc1', mockUser),
            ).rejects.toThrow(NotContentException);
        });

        it('should throw NotContentException when document URL is missing', async () => {
            // Arrange
            const mockDocumentDownload = [
                {
                    url: '',
                    fileName: 'SOC2_Report.pdf',
                },
            ] as SBTrustCenterDocumentDownload[];
            mockVendorRepository.findOne.mockResolvedValue(mockVendor);
            mockVendorDirectory.searchVendor.mockResolvedValue(mockVendorDirectoryItem);
            mockSafeBaseSdk.getTrustCenterDocumentsById.mockResolvedValue(mockDocumentDownload);

            // Act & Assert
            await expect(
                service.getVendorTrustCenterDocumentsById(mockAccount, 1, 'doc1', mockUser),
            ).rejects.toThrow(NotContentException);
        });

        it('should throw NotContentException when SafeBase SDK fails', async () => {
            // Arrange
            mockVendorRepository.findOne.mockResolvedValue(mockVendor);
            mockVendorDirectory.searchVendor.mockResolvedValue(mockVendorDirectoryItem);
            mockSafeBaseSdk.getTrustCenterDocumentsById.mockRejectedValue(
                new Error('SafeBase API error'),
            );

            // Act & Assert
            await expect(
                service.getVendorTrustCenterDocumentsById(mockAccount, 1, 'doc1', mockUser),
            ).rejects.toThrow(NotContentException);
        });
    });

    describe('getVendorTrustCenterItemsByCategory', () => {
        it('should return vendor trust center items by category successfully', async () => {
            // Arrange
            const mockItems = [
                {
                    id: 'item1',
                    title: 'Security Framework',
                    itemType: 'SECURITY_FRAMEWORK',
                    categoryType: SBCategoryType.SECURITY_GRADES,
                    categoryTitle: 'Security',
                    maturity: SafebaseVendorTrustCenterMaturity.FULL,
                    question: 'How do you handle security?',
                    explanation: 'We follow industry standards',
                    additionalField: 'extra data',
                },
                {
                    id: 'item2',
                    title: 'Privacy Policy',
                    itemType: 'LEGAL_PRIVACY_POLICY',
                    categoryType: SBCategoryType.LEGAL,
                    categoryTitle: 'Legal',
                    maturity: SafebaseVendorTrustCenterMaturity.FULL,
                    question: undefined,
                    explanation: 'Our privacy policy',
                },
                {
                    id: 'item3',
                    title: 'Sub Processors',
                    itemType: SBItemType.LEGAL_SUB_PROCESSORS,
                    categoryType: SBCategoryType.LEGAL,
                    categoryTitle: 'Legal',
                    maturity: SafebaseVendorTrustCenterMaturity.FULL,
                    question: 'Sub processor question',
                    explanation: 'Sub processor explanation',
                },
            ];

            // Mock valid access token
            mockVendorTrustCenterAccessRequestRepository.findAccessRequestByVendorIdAndRequestedId.mockResolvedValue(
                {
                    token: 'valid-token',
                    expiresAt: new Date(Date.now() + 60 * 60 * 1000),
                    status: 'granted',
                },
            );

            mockVendorRepository.findOne.mockResolvedValue(mockVendor);
            mockVendorDirectory.searchVendor.mockResolvedValue(mockVendorDirectoryItem);
            mockSafeBaseSdk.getTrustCenterItems.mockResolvedValue(mockItems);

            // Act
            const result = await service.getVendorTrustCenterItemsByCategory(
                mockAccount,
                1,
                mockUser,
            );

            // Assert
            expect(result).toHaveLength(2); // Sub processors should be filtered out
            expect(result).toEqual([
                {
                    type: SBCategoryType.SECURITY_GRADES,
                    title: 'Security',
                    items: [
                        {
                            id: 'item1',
                            name: 'Security Framework',
                            type: 'SECURITY_FRAMEWORK',
                            categoryType: SBCategoryType.SECURITY_GRADES,
                            maturity: SafebaseVendorTrustCenterMaturity.FULL,
                            description: 'How do you handle security?',
                            additionalFields: {
                                additionalField: 'extra data',
                            },
                        },
                    ],
                },
                {
                    type: SBCategoryType.LEGAL,
                    title: 'Legal',
                    items: [
                        {
                            id: 'item2',
                            name: 'Privacy Policy',
                            type: 'LEGAL_PRIVACY_POLICY',
                            categoryType: SBCategoryType.LEGAL,
                            maturity: SafebaseVendorTrustCenterMaturity.FULL,
                            description: 'Our privacy policy',
                            additionalFields: {},
                        },
                    ],
                },
            ]);
            expect(mockSafeBaseSdk.getTrustCenterItems).toHaveBeenCalledWith(
                'org123',
                ['product456'],
                'valid-token',
            );
        });

        it('should throw NotContentException when SafeBase SDK fails', async () => {
            // Arrange
            mockVendorRepository.findOne.mockResolvedValue(mockVendor);
            mockVendorDirectory.searchVendor.mockResolvedValue(mockVendorDirectoryItem);
            mockSafeBaseSdk.getTrustCenterItems.mockRejectedValue(new Error('SafeBase API error'));

            // Act & Assert
            await expect(
                service.getVendorTrustCenterItemsByCategory(mockAccount, 1, mockUser),
            ).rejects.toThrow(NotContentException);
        });
    });

    describe('getVendorTrustCenterAccessRequestRequirements', () => {
        it('should return vendor trust center access request requirements successfully', async () => {
            // Arrange
            const mockAccessRequestRequirements: SBAccessRequestRequirements = {
                memberLoginStatusCode: SBPartnerRequestAccessStatusCode.REQUEST_MEMBER_NOT_FOUND,
                fields: [
                    {
                        name: 'firstName',
                        type: SBAccessRequestFieldType.TEXT,
                        label: 'First name',
                        required: true,
                        minLength: 1,
                        maxLength: 24,
                    },
                    {
                        name: 'workEmail',
                        type: SBAccessRequestFieldType.EMAIL,
                        label: 'Work Email',
                        required: true,
                    },
                    {
                        name: 'agreeToTerms',
                        type: SBAccessRequestFieldType.CHECKBOX,
                        label: "I have read and agree to SafeBase's Terms of Service and Privacy Notice.",
                        required: true,
                        urls: [{ label: 'Terms', url: 'https://safebase.io/terms'}, { label: 'Privacy', url: 'https://safebase.io/privacy'}],
                    },
                ],
            };

            // Mock valid access token
            mockVendorTrustCenterAccessRequestRepository.findAccessRequestByVendorIdAndRequestedId.mockResolvedValue(
                {
                    token: 'valid-token',
                    expiresAt: new Date(Date.now() + 60 * 60 * 1000),
                    status: 'granted',
                },
            );

            mockVendorRepository.findOne.mockResolvedValue(mockVendor);
            mockVendorDirectory.searchVendor.mockResolvedValue(mockVendorDirectoryItem);
            mockSafeBaseSdk.getAccessRequestRequirements.mockResolvedValue(
                mockAccessRequestRequirements,
            );

            // Act
            const result = await service.getVendorTrustCenterAccessRequestRequirements(
                mockAccount,
                mockUser,
                1,
            );

            // Assert
            const expectedResult: VendorTrustCenterAccessRequestRequirements = {
                memberLoginStatusCode: SBPartnerRequestAccessStatusCode.REQUEST_MEMBER_NOT_FOUND,
                fields: [
                    {
                        name: 'firstName',
                        type: SBAccessRequestFieldType.TEXT,
                        label: 'First name',
                        required: true,
                        minLength: 1,
                        maxLength: 24,
                    },
                    {
                        name: 'workEmail',
                        type: SBAccessRequestFieldType.EMAIL,
                        label: 'Work Email',
                        required: true,
                    },
                    {
                        name: 'agreeToTerms',
                        type: SBAccessRequestFieldType.CHECKBOX,
                        label: "I have read and agree to SafeBase's Terms of Service and Privacy Notice.",
                        required: true,
                        urls: [
                            { mask: "Terms", href: 'https://safebase.io/terms' },
                            { mask: "Privacy", href: 'https://safebase.io/privacy' },
                        ],
                    },
                ],
            };
            expect(result).toEqual(expectedResult);
            expect(mockVendorRepository.findOne).toHaveBeenCalledWith({
                where: { id: 1 },
                select: ['id', 'url'],
            });
            expect(mockVendorDirectory.searchVendor).toHaveBeenCalledWith('example.com');
            expect(mockSafeBaseSdk.getAccessRequestRequirements).toHaveBeenCalledWith(
                'org123',
                '<EMAIL>',
                'product456',
                'valid-token',
            );
        });

        it('should throw NotFoundException when vendor is not found', async () => {
            // Arrange
            mockVendorRepository.findOne.mockResolvedValue(null);

            // Act & Assert
            await expect(
                service.getVendorTrustCenterAccessRequestRequirements(mockAccount, mockUser, 1),
            ).rejects.toThrow(NotFoundException);
            expect(mockVendorRepository.findOne).toHaveBeenCalledWith({
                where: { id: 1 },
                select: ['id', 'url'],
            });
        });

        it('should process markdown in field labels and extract links with mask text', async () => {
            // Arrange
            const mockAccessRequestRequirements: SBAccessRequestRequirements = {
                memberLoginStatusCode: SBPartnerRequestAccessStatusCode.REQUEST_MEMBER_NOT_FOUND,
                fields: [
                    {
                        name: 'agreeToTerms',
                        type: SBAccessRequestFieldType.CHECKBOX,
                        label: 'I agree to the [Terms of Service](https://safebase.io/terms) and [Privacy Policy](https://safebase.io/privacy).',
                        required: true,
                        urls: [{ url: 'https://safebase.io/existing', label: 'Existing Link' }],
                    },
                ],
            };

            mockVendorRepository.findOne.mockResolvedValue(mockVendor);
            mockVendorDirectory.searchVendor.mockResolvedValue(mockVendorDirectoryItem);
            mockSafeBaseSdk.getAccessRequestRequirements.mockResolvedValue(
                mockAccessRequestRequirements,
            );

            // Act
            const result = await service.getVendorTrustCenterAccessRequestRequirements(
                mockAccount,
                mockUser,
                1,
            );

            // Assert
            const expectedResult: VendorTrustCenterAccessRequestRequirements = {
                memberLoginStatusCode: SBPartnerRequestAccessStatusCode.REQUEST_MEMBER_NOT_FOUND,
                fields: [
                    {
                        name: 'agreeToTerms',
                        type: SBAccessRequestFieldType.CHECKBOX,
                        label: 'I agree to the Terms of Service and Privacy Policy.',
                        required: true,
                        urls: [
                            { href: 'https://safebase.io/existing', mask: 'Existing Link' },
                            { href: 'https://safebase.io/terms', mask: 'Terms of Service' },
                            { href: 'https://safebase.io/privacy', mask: 'Privacy Policy' },
                        ],
                    },
                ],
            };
            expect(result).toEqual(expectedResult);
        });

        it('should throw InternalServerErrorException when SafeBase SDK fails', async () => {
            // Arrange
            mockVendorRepository.findOne.mockResolvedValue(mockVendor);
            mockVendorDirectory.searchVendor.mockResolvedValue(mockVendorDirectoryItem);
            mockSafeBaseSdk.getAccessRequestRequirements.mockRejectedValue(
                new Error('SafeBase API error'),
            );

            // Act & Assert
            await expect(
                service.getVendorTrustCenterAccessRequestRequirements(mockAccount, mockUser, 1),
            ).rejects.toThrow(InternalServerErrorException);
        });
    });

    describe('getVendorTrustCenterAccessRequest', () => {
        it('should return vendor trust center access request successfully', async () => {
            // Arrange
            const mockAccessRequest: VendorTrustCenterAccessRequest = {
                id: 1,
                requestedBy: '<EMAIL>',
                vendor: mockVendor,
                createdAt: new Date(),
            } as VendorTrustCenterAccessRequest;

            mockVendorRepository.findOne.mockResolvedValue(mockVendor);
            mockVendorTrustCenterAccessRequestRepository.findOne.mockResolvedValue(
                mockAccessRequest,
            );

            // Act
            const result = await service.getVendorTrustCenterAccessRequest(
                mockAccount,
                mockUser,
                1,
            );

            // Assert
            expect(result).toEqual(mockAccessRequest);
            expect(mockVendorRepository.findOne).toHaveBeenCalledWith({
                where: { id: 1 },
                select: ['id'],
            });
            expect(mockVendorTrustCenterAccessRequestRepository.findOne).toHaveBeenCalledWith({
                where: { vendor: { id: 1 }, requestedBy: '<EMAIL>' },
                relations: ['vendor'],
                order: { createdAt: 'DESC' },
            });
        });

        it('should throw NotFoundException when vendor is not found', async () => {
            // Arrange
            mockVendorRepository.findOne.mockResolvedValue(null);

            // Act & Assert
            await expect(
                service.getVendorTrustCenterAccessRequest(mockAccount, mockUser, 1),
            ).rejects.toThrow(NotContentException);
            expect(mockVendorRepository.findOne).toHaveBeenCalledWith({
                where: { id: 1 },
                select: ['id'],
            });
        });

        it('should throw NotFoundException when access request is not found', async () => {
            // Arrange
            mockVendorRepository.findOne.mockResolvedValue(mockVendor);
            mockVendorTrustCenterAccessRequestRepository.findOne.mockResolvedValue(null);

            // Act & Assert
            await expect(
                service.getVendorTrustCenterAccessRequest(mockAccount, mockUser, 1),
            ).rejects.toThrow(NotContentException);
        });

        it('should throw NotContentException when repository fails', async () => {
            // Arrange
            mockVendorRepository.findOne.mockRejectedValue(new Error('Database error'));

            // Act & Assert
            await expect(
                service.getVendorTrustCenterAccessRequest(mockAccount, mockUser, 1),
            ).rejects.toThrow(NotContentException);
        });
    });

    describe('processAccessTokenValidation', () => {
        beforeEach(() => {
            // Reset the mock before each test in this describe block
            mockVendorTrustCenterAccessRequestRepository.findAccessRequestByVendorIdAndRequestedId.mockReset();
            mockVendorTrustCenterAccessRequestRepository.softDeleteAccessRequestByVendorAndEmail.mockReset();
        });

        it('should return valid token when access request exists and is not expired', async () => {
            // Arrange
            const validToken = 'valid-safebase-token';
            const futureDate = new Date(Date.now() + 60 * 60 * 1000); // 1 hour from now
            mockVendorTrustCenterAccessRequestRepository.findAccessRequestByVendorIdAndRequestedId.mockResolvedValue(
                {
                    token: validToken,
                    expiresAt: futureDate,
                    status: 'granted',
                },
            );

            // Act
            const result = await service.processAccessTokenValidation(mockUser, 1);

            // Assert
            expect(result).toBe(validToken);
            expect(
                mockVendorTrustCenterAccessRequestRepository.findAccessRequestByVendorIdAndRequestedId,
            ).toHaveBeenCalledWith('<EMAIL>', 1);
        });

        it('should return empty string when access request does not exist', async () => {
            // Arrange
            mockVendorTrustCenterAccessRequestRepository.findAccessRequestByVendorIdAndRequestedId.mockResolvedValue(
                null,
            );

            // Act
            const result = await service.processAccessTokenValidation(mockUser, 1);

            // Assert
            expect(result).toBe('');
        });

        it('should return empty string when token does not exist', async () => {
            // Arrange
            const futureDate = new Date(Date.now() + 60 * 60 * 1000);
            mockVendorTrustCenterAccessRequestRepository.findAccessRequestByVendorIdAndRequestedId.mockResolvedValue(
                {
                    token: null,
                    expiresAt: futureDate,
                    status: 'granted',
                },
            );

            // Act
            const result = await service.processAccessTokenValidation(mockUser, 1);

            // Assert
            expect(result).toBe('');
        });

        it('should return empty string and cleanup when token is expired by status', async () => {
            // Arrange
            const futureDate = new Date(Date.now() + 60 * 60 * 1000);
            mockVendorTrustCenterAccessRequestRepository.findAccessRequestByVendorIdAndRequestedId.mockResolvedValue(
                {
                    token: 'expired-token',
                    expiresAt: futureDate,
                    status: 'expired',
                },
            );

            // Act
            const result = await service.processAccessTokenValidation(mockUser, 1);

            // Assert
            expect(result).toBe('');
            expect(
                mockVendorTrustCenterAccessRequestRepository.softDeleteAccessRequestByVendorAndEmail,
            ).toHaveBeenCalledWith('<EMAIL>', 1);
        });

        it('should return empty string and cleanup when token is expired by date', async () => {
            // Arrange
            const pastDate = new Date(Date.now() - 60 * 60 * 1000); // 1 hour ago
            mockVendorTrustCenterAccessRequestRepository.findAccessRequestByVendorIdAndRequestedId.mockResolvedValue(
                {
                    token: 'expired-token',
                    expiresAt: pastDate,
                    status: 'granted',
                },
            );

            // Act
            const result = await service.processAccessTokenValidation(mockUser, 1);

            // Assert
            expect(result).toBe('');
            expect(
                mockVendorTrustCenterAccessRequestRepository.softDeleteAccessRequestByVendorAndEmail,
            ).toHaveBeenCalledWith('<EMAIL>', 1);
        });

        it('should return empty string and cleanup when expiresAt is invalid', async () => {
            // Arrange
            mockVendorTrustCenterAccessRequestRepository.findAccessRequestByVendorIdAndRequestedId.mockResolvedValue(
                {
                    token: 'token-with-invalid-date',
                    expiresAt: null, // Type assertion to handle null for testing
                    status: 'granted',
                },
            );

            // Act
            const result = await service.processAccessTokenValidation(mockUser, 1);

            // Assert
            expect(result).toBe('');
            expect(
                mockVendorTrustCenterAccessRequestRepository.softDeleteAccessRequestByVendorAndEmail,
            ).toHaveBeenCalledWith('<EMAIL>', 1);
        });
    });

    describe('handleExpiredToken', () => {
        it('should soft delete access request and log expiration', async () => {
            // Arrange
            const expiresAt = new Date(Date.now() - 60 * 60 * 1000);
            const email = '<EMAIL>';
            const vendorId = 1;

            // Act
            await service.handleExpiredToken(email, vendorId, expiresAt);

            // Assert
            expect(
                mockVendorTrustCenterAccessRequestRepository.softDeleteAccessRequestByVendorAndEmail,
            ).toHaveBeenCalledWith(email, vendorId);
        });
    });

    describe('private method error scenarios', () => {
        it('should throw error when vendor directory item ID format is invalid', async () => {
            // Arrange
            const invalidVendorDirectoryItem = {
                ...mockVendorDirectoryItem,
                id: 'invalid-format',
            };
            mockVendorRepository.findOne.mockResolvedValue(mockVendor);
            mockVendorDirectory.searchVendor.mockResolvedValue(invalidVendorDirectoryItem);

            // Act & Assert
            await expect(service.getVendorTrustCenterOverview(mockAccount, 1)).rejects.toThrow(
                NotContentException,
            );
        });

        it('should throw error when vendor directory item ID does not start with SB:', async () => {
            // Arrange
            const invalidVendorDirectoryItem = {
                ...mockVendorDirectoryItem,
                id: 'INVALID:org123:product456',
            };
            mockVendorRepository.findOne.mockResolvedValue(mockVendor);
            mockVendorDirectory.searchVendor.mockResolvedValue(invalidVendorDirectoryItem);

            // Act & Assert
            await expect(service.getVendorTrustCenterOverview(mockAccount, 1)).rejects.toThrow(
                NotContentException,
            );
        });

        it('should throw error when vendor directory item ID has wrong number of parts', async () => {
            // Arrange
            const invalidVendorDirectoryItem = {
                ...mockVendorDirectoryItem,
                id: 'SB:org123',
            };
            mockVendorRepository.findOne.mockResolvedValue(mockVendor);
            mockVendorDirectory.searchVendor.mockResolvedValue(invalidVendorDirectoryItem);

            // Act & Assert
            await expect(service.getVendorTrustCenterOverview(mockAccount, 1)).rejects.toThrow(
                NotContentException,
            );
        });
    });
});
