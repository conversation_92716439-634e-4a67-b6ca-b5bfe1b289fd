export enum VendorTrustCenterAccessRequestStatus {
    PENDING = 'pending',
    GRANTED = 'granted',
    DECLINED = 'declined',
    EXPIRED = 'expired',
    REVOKED = 'revoked',
    NOT_FOUND = 'not_found',
}

export enum SBVendorTrustCenterAccessRequestStatus {
    PENDING = 'has_pending_request',
    GRANTED = 'has_approved_request',
    DECLINED = 'has_declined_request',
    NOT_FOUND = 'email_not_recognized',
    BLOCKED_DOMAIN = 'request_declined_blocked_domain',
    NOT_WORK_EMAIL = 'request_declined_not_work_email',
    ACCOUNT_EXISTS = 'has_existing_account',
    EXPIRED = 'has_expired_account',
    REVOKED = 'has_revoked_account',
}

export const VendorTrustCenterAccessRequestStatusMap: Map<SBVendorTrustCenterAccessRequestStatus, VendorTrustCenterAccessRequestStatus> = new Map([
    [SBVendorTrustCenterAccessRequestStatus.NOT_FOUND, VendorTrustCenterAccessRequestStatus.NOT_FOUND],
    [SBVendorTrustCenterAccessRequestStatus.PENDING, VendorTrustCenterAccessRequestStatus.PENDING],
    [SBVendorTrustCenterAccessRequestStatus.GRANTED, VendorTrustCenterAccessRequestStatus.GRANTED],
    [SBVendorTrustCenterAccessRequestStatus.ACCOUNT_EXISTS, VendorTrustCenterAccessRequestStatus.GRANTED],
    [SBVendorTrustCenterAccessRequestStatus.DECLINED, VendorTrustCenterAccessRequestStatus.DECLINED],
    [SBVendorTrustCenterAccessRequestStatus.BLOCKED_DOMAIN, VendorTrustCenterAccessRequestStatus.DECLINED],
    [SBVendorTrustCenterAccessRequestStatus.NOT_WORK_EMAIL, VendorTrustCenterAccessRequestStatus.DECLINED],
    [SBVendorTrustCenterAccessRequestStatus.EXPIRED, VendorTrustCenterAccessRequestStatus.EXPIRED],
    [SBVendorTrustCenterAccessRequestStatus.REVOKED, VendorTrustCenterAccessRequestStatus.REVOKED],
]);
