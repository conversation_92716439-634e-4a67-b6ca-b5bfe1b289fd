import { ApiProperty } from '@nestjs/swagger';
import { VendorTrustCenterAccessRequestFieldResponseDto } from 'app/users/vendors/dtos/vendor-trust-center-access-request-field-response.dto';
import { VendorTrustCenterAccessRequestRequirements } from 'app/users/vendors/types/vendor-trust-center-access-requirements.type';
import { ResponseDto } from 'commons/dtos/response.dto';
import { SBPartnerRequestAccessStatusCode } from 'dependencies/safebase/partner-request-access-status-code.enum';

export class VendorTrustCenterAccessRequestRequirementsResponseDto extends ResponseDto {
    @ApiProperty({
        type: 'string',
        description: 'Status code indicating the member login status',
        enum: SBPartnerRequestAccessStatusCode,
        example: SBPartnerRequestAccessStatusCode.REQUEST_MEMBER_NOT_FOUND,
        enumName: 'SBPartnerRequestAccessStatusCodeEnum',
    })
    memberLoginStatusCode: SBPartnerRequestAccessStatusCode;

    @ApiProperty({
        type: VendorTrustCenterAccessRequestFieldResponseDto,
        isArray: true,
        description: 'Array of required fields for access request',
    })
    fields: VendorTrustCenterAccessRequestFieldResponseDto[];

    build(
        data: VendorTrustCenterAccessRequestRequirements,
    ): VendorTrustCenterAccessRequestRequirementsResponseDto {
        this.memberLoginStatusCode = data.memberLoginStatusCode;
        this.fields = this.list(data.fields, VendorTrustCenterAccessRequestFieldResponseDto);

        return this.send() as VendorTrustCenterAccessRequestRequirementsResponseDto;
    }
}
