import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { VendorTrustCenterAccessRequestFieldOptionResponseDto } from 'app/users/vendors/dtos/vendor-trust-center-access-request-field-option-response.dto';
import { VendorTrustCenterAccessRequestFieldUrlResponseDto } from 'app/users/vendors/dtos/vendor-trust-center-access-request-field-url-response.dto';
import { VendorTrustCenterAccessRequirementsField } from 'app/users/vendors/types/vendor-trust-center-access-requirements.type';
import { ResponseDto } from 'commons/dtos/response.dto';
import { SBAccessRequestFieldType } from 'dependencies/safebase/access-request-field-type.enum';
import { isNil } from 'lodash';

export class VendorTrustCenterAccessRequestFieldResponseDto extends ResponseDto {
    @ApiProperty({
        type: 'string',
        example: 'firstName',
        description: 'Field name',
    })
    name: string;

    @ApiProperty({
        type: 'string',
        example: SBAccessRequestFieldType.TEXT,
        description: 'Field type',
        enum: SBAccessRequestFieldType,
        enumName: 'SBAccessRequestFieldTypeEnum',
    })
    type: SBAccessRequestFieldType;

    @ApiProperty({
        type: 'string',
        example: 'First name',
        description: 'Field label',
    })
    label: string;

    @ApiProperty({
        type: 'boolean',
        example: true,
        description: 'Whether the field is required',
    })
    required: boolean;

    @ApiPropertyOptional({
        type: 'number',
        example: 1,
        description: 'Minimum length for text fields',
    })
    minLength?: number;

    @ApiPropertyOptional({
        type: 'number',
        example: 24,
        description: 'Maximum length for text fields',
    })
    maxLength?: number;

    @ApiPropertyOptional({
        type: VendorTrustCenterAccessRequestFieldUrlResponseDto,
        isArray: true,
        description: 'URLs for checkbox fields with links',
    })
    urls?: VendorTrustCenterAccessRequestFieldUrlResponseDto[];

    @ApiPropertyOptional({
        type: VendorTrustCenterAccessRequestFieldOptionResponseDto,
        isArray: true,
        description: 'Options for select fields',
    })
    options?: VendorTrustCenterAccessRequestFieldOptionResponseDto[];

    build(
        field: VendorTrustCenterAccessRequirementsField,
    ): VendorTrustCenterAccessRequestFieldResponseDto {
        this.name = field.name;
        this.type = field.type;
        this.label = field.label;
        this.required = field.required;

        if (!isNil(field.minLength)) {
            this.minLength = field.minLength;
        }

        if (!isNil(field.maxLength)) {
            this.maxLength = field.maxLength;
        }

        if (!isNil(field.urls)) {
            this.urls = this.list(field.urls, VendorTrustCenterAccessRequestFieldUrlResponseDto);
        }

        if (!isNil(field.options)) {
            this.options = this.list(
                field.options,
                VendorTrustCenterAccessRequestFieldOptionResponseDto,
            );
        }

        return this.send() as VendorTrustCenterAccessRequestFieldResponseDto;
    }
}
