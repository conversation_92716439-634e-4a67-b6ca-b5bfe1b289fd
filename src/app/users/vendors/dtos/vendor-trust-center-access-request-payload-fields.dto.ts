import { ApiProperty } from '@nestjs/swagger';
import { RequestDto } from 'commons/dtos/request.dto';

export class VendorTrustCenterAccessRequestPayloadFieldsDto extends RequestDto {
    @ApiProperty({
        type: 'string',
        example: 'default',
        description: 'Product ID',
        required: false,
    })
    productId?: string;

    @ApiProperty({
        type: 'string',
        example: 'John',
        description: 'First name',
    })
    firstName: string;

    @ApiProperty({
        type: 'string',
        example: 'Doe',
        description: 'Last name',
    })
    lastName: string;

    @ApiProperty({
        type: 'string',
        example: '<EMAIL>',
        description: 'Email',
    })
    workEmail: string;

    @ApiProperty({
        type: 'string',
        example: 'Acme Corporation',
        description: 'Company name',
    })
    companyName: string;

    @ApiProperty({
        type: 'boolean',
        example: true,
        description: 'Agree to terms',
    })
    agreeToTerms: boolean;

    @ApiProperty({
        type: 'boolean',
        required: false,
        example: false,
        description: 'Custom checkbox',
    })
    customCheckbox?: boolean;

    @ApiProperty({
        type: 'string',
        required: false,
        example: 'US',
        description: 'Countries dropdown',
    })
    countriesDropdown?: string;

    @ApiProperty({
        type: 'string',
        required: false,
        example: 'Job Title',
        description: 'Job title text field',
    })
    jobTitleTextField?: string;

    @ApiProperty({
        type: 'string',
        required: false,
        example: '<EMAIL>',
        description: 'Point of contact field',
    })
    pointOfContactField?: string;

    @ApiProperty({
        type: 'string',
        required: false,
        example: 'customer',
        description: 'Relationship dropdown',
    })
    relationshipDropdown?: string;

    @ApiProperty({
        type: 'string',
        isArray: true,
        required: false,
        example: '[default]',
        description: 'Account member request products',
    })
    accountMemberRequestProducts?: string[];

}
