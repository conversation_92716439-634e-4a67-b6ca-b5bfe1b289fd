import { ApiProperty } from '@nestjs/swagger';
import { ResponseDto } from 'commons/dtos/response.dto';

export class VendorTrustCenterAccessResultDto extends ResponseDto {
    @ApiProperty({
        type: 'string',
        example: '123',
        description: 'Access Request ID',
    })
    id: string;

    @ApiProperty({
        type: 'string',
        example: 'has_pending_request',
        description: 'Access Request Status Code',
    })
    code: string;

    build(data: { id: string; code: string }): VendorTrustCenterAccessResultDto {
        this.id = data.id;
        this.code = data.code;
        return this.send();
    }
    
}
