import { ApiProperty } from '@nestjs/swagger';
import { VendorTrustCenterAccessRequirementsFieldUrl } from 'app/users/vendors/types/vendor-trust-center-access-requirements.type';
import { ResponseDto } from 'commons/dtos/response.dto';

export class VendorTrustCenterAccessRequestFieldUrlResponseDto extends ResponseDto {
    @ApiProperty({
        type: 'string',
        description: 'The URL href',
        example: 'https://example.com/security-policy',
    })
    href: string;

    @ApiProperty({
        type: 'string',
        description: 'Display text for the URL',
        example: 'Security Policy',
        required: false,
    })
    mask?: string;

    build(
        url: VendorTrustCenterAccessRequirementsFieldUrl,
    ): VendorTrustCenterAccessRequestFieldUrlResponseDto {
        this.href = url.href;
        if (url.mask) {
            this.mask = url.mask;
        }

        return this.send() as VendorTrustCenterAccessRequestFieldUrlResponseDto;
    }
}
