import { ApiProperty } from '@nestjs/swagger';
import { ResponseDto } from 'commons/dtos/response.dto';
import { SBSelectFieldOption } from 'dependencies/safebase/safebase-vendor-trust-center.types';

export class VendorTrustCenterAccessRequestFieldOptionResponseDto extends ResponseDto {
    @ApiProperty({
        type: 'string',
        example: 'customer',
        description: 'Option value',
    })
    value: string;

    @ApiProperty({
        type: 'string',
        example: 'Customer',
        description: 'Option label',
    })
    label: string;

    build(option: SBSelectFieldOption): VendorTrustCenterAccessRequestFieldOptionResponseDto {
        this.value = option.value;
        this.label = option.label;

        return this.send() as VendorTrustCenterAccessRequestFieldOptionResponseDto;
    }
}
