import { ApiProperty } from '@nestjs/swagger';
import { VendorTrustCenterAccessResultDto } from 'app/users/vendors/dtos/vendor-trust-center-access-request-result.dto';
import { ResponseDto } from 'commons/dtos/response.dto';

interface VendorTrustCenterAccessResponse {
    ok: boolean;
    result: VendorTrustCenterAccessResultDto[];
}

export class VendorTrustCenterAccessRequestSaveResponseDto extends ResponseDto {
    @ApiProperty({
        type: 'boolean',
        required: false,
        example: true,
        description: 'Access Request OK',
    })
    ok?: boolean;

    @ApiProperty({
        type: VendorTrustCenterAccessResultDto,
        isArray: true,
        required: false,
        example: [{
            id: '123',
            status: 'pending',
        }],
        description: 'Access Request Result',
    })
    result: VendorTrustCenterAccessResultDto[];

    build(data: VendorTrustCenterAccessResponse): VendorTrustCenterAccessRequestSaveResponseDto {
        this.ok = data.ok? data.ok: false;
        this.result = data.result;
        
        return this.send() as VendorTrustCenterAccessRequestSaveResponseDto;
    }
}
