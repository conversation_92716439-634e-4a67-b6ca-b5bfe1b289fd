import { ApiProperty } from '@nestjs/swagger';
import { VendorTrustCenterAccessRequestPayloadFieldsDto } from 'app/users/vendors/dtos/vendor-trust-center-access-request-payload-fields.dto';
import { RequestDto } from 'commons/dtos/request.dto';

export class VendorTrustCenterAccessRequestDto extends RequestDto {
    @ApiProperty({
        type: VendorTrustCenterAccessRequestPayloadFieldsDto,
        example: {
            productId: 'default',
            firstName: 'John',
            lastName: 'Doe',
            email: '<EMAIL>',
            companyName: 'Acme Corporation',
            agree: '123',
        },
        description: 'Fields for the access request',
    })
    fields: VendorTrustCenterAccessRequestPayloadFieldsDto;

    @ApiProperty({
        type: 'string',
        example: 'https://example.com/callback',
        description: 'Callback URL',
    })
    callbackUrl: string;

    @ApiProperty({
        type: 'string',
        example: 'https://example.com/redirect',
        description: 'Redirect URL',
    })
    redirectLocationUrl: string;
    
}
