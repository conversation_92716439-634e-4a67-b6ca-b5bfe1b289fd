import { User } from 'app/users/entities/user.entity';
import { VendorSecurityReviewDocuments } from 'app/users/vendors/entities/vendor-security-review-documents.entity';
import { Vendor } from 'app/users/vendors/entities/vendor.entity';
import { VendorSecurityReviewDecision } from 'app/users/vendors/enums/vendor-security-review-decision.enum';
import { Type } from 'class-transformer';
import { IsDate, IsIn, IsNotEmpty, IsOptional, MaxLength } from 'class-validator';
import { BaseEntity } from 'commons/entities/base.entity';
import { VendorSecurityReviewStatus } from 'commons/enums/vendor/vendor-security-review-status.enum';
import { VendorSecurityReviewType } from 'commons/enums/vendor/vendor-security-review-type.enum';
import { getValues } from 'commons/helpers/enum.helper';
import config from 'config';
import {
    Column,
    CreateDateColumn,
    DeleteDateColumn,
    Entity,
    Index,
    JoinColumn,
    ManyToOne,
    OneToMany,
    PrimaryGeneratedColumn,
    Relation,
    UpdateDateColumn,
} from 'typeorm';

@Entity()
export class VendorSecurityReview extends BaseEntity {
    @PrimaryGeneratedColumn({
        type: BaseEntity.integer(),
        unsigned: BaseEntity.unsigned(),
    })
    id: number;

    @IsOptional()
    @MaxLength(config.get('validation.maxVarcharText'))
    @Column({
        type: BaseEntity.varchar(),
        length: config.get('db.varcharLength'),
        nullable: true,
    })
    title?: string | null;

    @IsDate()
    @IsNotEmpty()
    @Type(() => Date)
    @Column({
        type: BaseEntity.timestamp(),
        name: 'requested_at',
        precision: config.get('db.dateTimePrecision'),
        nullable: false,
    })
    requestedAt: Date;

    @IsDate()
    @IsNotEmpty()
    @Type(() => Date)
    @Column({
        type: BaseEntity.timestamp(),
        name: 'review_deadline_at',
        precision: config.get('db.dateTimePrecision'),
        nullable: false,
    })
    reviewDeadlineAt: Date;

    @IsOptional()
    @IsIn(getValues(VendorSecurityReviewDecision))
    @Column({
        type: BaseEntity.smallInt(),
        nullable: true,
        default: VendorSecurityReviewDecision.PENDING,
    })
    decision?: VendorSecurityReviewDecision;

    @IsOptional()
    @MaxLength(config.get('validation.maxVendorPotentialReviewNoteText'))
    @Column({
        nullable: true,
        length: config.get('validation.maxVendorPotentialReviewNoteText'),
    })
    note?: string;

    @IsNotEmpty()
    @IsIn(getValues(VendorSecurityReviewStatus))
    @Column({
        type: BaseEntity.smallInt(),
        nullable: false,
        default: VendorSecurityReviewStatus.NOT_YET_STARTED,
    })
    status: VendorSecurityReviewStatus;

    @IsNotEmpty()
    @IsIn(getValues(VendorSecurityReviewType))
    @Column({
        type: BaseEntity.smallInt(),
        nullable: false,
    })
    type: VendorSecurityReviewType;

    @CreateDateColumn({ type: BaseEntity.timestamp(), name: 'created_at' })
    createdAt: Date;

    @UpdateDateColumn({ type: BaseEntity.timestamp(), name: 'updated_at' })
    updatedAt: Date;

    @DeleteDateColumn({ type: BaseEntity.timestamp(), name: 'deleted_at' })
    deletedAt: Date | null;

    @Index('FK_8b9ee6217915dc3032d69ba070c')
    @Column({
        type: BaseEntity.integer(),
        name: 'fk_requester_user_id',
        nullable: true,
        unsigned: true,
    })
    requesterUserId: number | null;

    @ManyToOne(() => User, requesterUser => requesterUser.id, {
        nullable: true,
        createForeignKeyConstraints: false,
    })
    @JoinColumn({ name: 'fk_requester_user_id' })
    requesterUser: Relation<User> | null;

    @ManyToOne(() => Vendor, vendor => vendor.id, {
        eager: false,
        nullable: false,
    })
    @JoinColumn({ name: 'fk_vendor_id' })
    vendor: Relation<Vendor>;

    @Index('FK_9e208cceb836ecfc2bcc0ffc02d')
    @Column({
        type: BaseEntity.integer(),
        name: 'fk_user_id',
        nullable: true,
        unsigned: true,
    })
    userId: number | null;

    @ManyToOne(() => User, user => user.id, {
        nullable: true,
        createForeignKeyConstraints: false,
    })
    @JoinColumn({ name: 'fk_user_id' })
    user: Relation<User> | null;

    @OneToMany(
        () => VendorSecurityReviewDocuments,
        securityReviewDocument => securityReviewDocument.vendorSecurityReview,
        { eager: false, nullable: false },
    )
    securityReviewDocuments: VendorSecurityReviewDocuments[];
}
