import { Vendor } from 'app/users/vendors/entities/vendor.entity';
import { VendorTrustCenterAccessRequestStatus } from 'app/users/vendors/enums/vendor-trust-center-access-request-status.enum';
import { Type } from 'class-transformer';
import { IsDate, IsIn, IsNotEmpty, IsOptional, IsUUID, MaxLength } from 'class-validator';
import { BaseEntity } from 'commons/entities/base.entity';
import { getValues } from 'commons/helpers/enum.helper';
import { decrypt, encrypt } from 'commons/helpers/security.helper';
import config from 'config';
import { isNil } from 'lodash';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { PolloLogger } from 'pollo-logger/pollo.logger';
import {
    AfterLoad,
    BeforeInsert,
    BeforeUpdate,
    Column,
    CreateDateColumn,
    DeleteDateColumn,
    Entity,
    Index,
    JoinColumn,
    ManyToOne,
    PrimaryGeneratedColumn,
    Relation,
    UpdateDateColumn,
} from 'typeorm';
@Entity()
export class VendorTrustCenterAccessRequest extends BaseEntity {
    @PrimaryGeneratedColumn({
        type: BaseEntity.integer(),
        unsigned: BaseEntity.unsigned(),
    })
    id: number;

    @IsOptional()
    @MaxLength(config.get('validation.maxLongText'))
    @Column({
        type: 'text',
        nullable: true,
    })
    token?: string | null;

    @IsNotEmpty()
    @IsIn(getValues(VendorTrustCenterAccessRequestStatus))
    @Column({
        type: BaseEntity.varchar(),
        length: config.get('db.varcharLength'),
        default: VendorTrustCenterAccessRequestStatus.PENDING,
    })
    status: VendorTrustCenterAccessRequestStatus;

    @Index()
    @IsNotEmpty()
    @MaxLength(config.get('validation.maxVarcharText'))
    @Column({
        name: 'requested_by',
        type: BaseEntity.varchar(),
        length: config.get('db.varcharLength'),
    })
    requestedBy: string;

    @IsNotEmpty()
    @IsDate()
    @Type(() => Date)
    @Column({
        name: 'expires_at',
        type: BaseEntity.timestamp(),
        precision: config.get('db.dateTimePrecision'),
        default: '1970-01-01 00:00:00.000000',
    })
    expiresAt: Date;

    @IsNotEmpty()
    @IsUUID()
    @Column({
        name: 'sb_access_request_id',
        type: BaseEntity.varchar(),
        length: config.get('db.uuidLength'),
        unique: true,
    })
    sbAccessRequestId: string;

    @ManyToOne(() => Vendor, {
        eager: false,
        nullable: false,
    })
    @JoinColumn({ name: 'fk_vendor_id' })
    vendor: Relation<Vendor>;

    @CreateDateColumn({ type: BaseEntity.timestamp(), name: 'created_at' })
    createdAt: Date;

    @UpdateDateColumn({ type: BaseEntity.timestamp(), name: 'updated_at' })
    updatedAt: Date;

    @Index()
    @DeleteDateColumn({ type: BaseEntity.timestamp(), name: 'deleted_at' })
    deletedAt: Date | null;

    @BeforeInsert()
    @BeforeUpdate()
    beforeInsertOrUpdate() {
        if (!isNil(this.token)) {
            try {
                // Check if token is already encrypted by trying to decrypt it
                decrypt(this.token);

                PolloLogger.logger(this.constructor.name).log(
                    PolloMessage.msg('Token already encrypted for access request').setIdentifier({
                        id: this.id,
                        sbAccessRequestId: this.sbAccessRequestId,
                    }),
                );
            } catch (error) {
                // If decrypt fails, token is plain text and needs encryption
                this.token = encrypt(this.token);
                PolloLogger.logger(this.constructor.name).log(
                    PolloMessage.msg('Token encrypted for access request').setIdentifier({
                        id: this.id,
                        sbAccessRequestId: this.sbAccessRequestId,
                    }),
                );
            }
        }
    }

    @AfterLoad()
    public afterLoad(): void {
        if (!isNil(this.token)) {
            this.token = decrypt(this.token);
            PolloLogger.logger(this.constructor.name).warn(
                PolloMessage.msg('Token decrypted successfully for access request').setIdentifier({
                    id: this.id,
                    sbAccessRequestId: this.sbAccessRequestId,
                }),
            );
        }
    }
}
