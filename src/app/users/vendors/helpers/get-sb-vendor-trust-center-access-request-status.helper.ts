import {
    VendorTrustCenterAccessRequestStatus,
    VendorTrustCenterAccessRequestStatusMap,
} from 'app/users/vendors/enums/vendor-trust-center-access-request-status.enum';
import { SBTrustCenterAccessRequestResult } from 'dependencies/safebase/safebase-vendor-trust-center.types';

export function getSBVendorTrustCenterAccessRequestStatus(result: SBTrustCenterAccessRequestResult): VendorTrustCenterAccessRequestStatus {
    return VendorTrustCenterAccessRequestStatusMap.get(
        result.code,
    ) ?? result.code as unknown as VendorTrustCenterAccessRequestStatus;
}
