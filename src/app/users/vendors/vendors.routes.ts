/* eslint-disable @typescript-eslint/no-duplicate-enum-values */
export enum VendorsRoute {
    // ========================================================================
    // CORE VENDOR ROUTES (vendors.controller.ts)
    // ========================================================================

    // Basic CRUD Operations
    GET_VENDORS = '/vendors',
    GET_VENDOR = '/vendors/:id',
    POST_ADD = '/vendors',
    PUT_UPDATE = '/vendors/:id',
    DELETE_VENDOR = '/vendors/:id',
    PUT_UPDATE_VENDOR_STATUS = '/vendors/:id/status',
    GET_VENDORS_STATS = '/vendors/stats',

    // Reports
    GET_REPORT = '/vendors/report/csv',
    GET_PROSPECTIVE_REPORT = '/prospective/report/csv',
    GET_VENDOR_REPORT_REVIEW = '/vendors/report/review/:id',
    POST_VENDOR_REVIEW = '/vendors/:id/review',
    GET_VENDOR_REVIEW = '/vendors/:id/review',

    // Bulk Operations
    GET_VENDORS_CSV_TEMPLATE = '/vendors/csv/template',
    GET_VENDORS_UPDATE_CSV_TEMPLATE = '/vendors/update/csv/template',
    POST_VENDORS_BULK_UPDATE_CSV_VALIDATE = '/vendors/bulk-update-csv/validate',
    POST_VENDORS_BULK = '/vendors/bulk',

    // Filters (V2 API)
    GET_FILTERS = '/vendors/v2/filters',
    GET_VENDORS_LIST = '/vendors/v2/list',

    // Feature Dismissals
    GET_VENDORS_FEATURE_DISMISSAL = '/vendors/:id/feature-dismissal',
    POST_VENDORS_FEATURE_DISMISSAL = '/vendors/:id/feature-dismissal',

    // ========================================================================
    // VENDOR DOCUMENTS ROUTES (vendors-documents.controller.ts)
    // ========================================================================
    GET_VENDOR_DOCUMENTS = '/vendors/:id/documents',
    GET_DOC = '/vendors/:id/documents/:docId/download',
    GET_PDF_DOC = '/vendors/:id/pdf-document/:docId/download',
    POST_DOC = '/vendors/:id/documents',
    DELETE_DOC = '/vendors/:id/documents/:docId',
    GET_SOC_REPORT_REVIEW = '/vendors/:id/documents/report-review/:docId/download',

    // ========================================================================
    // VENDOR EVENTS ROUTES (vendors-events.controller.ts)
    // ========================================================================
    GET_VENDOR_EVENTS = '/vendors/:id/events',
    GET_VENDOR_TOTAL_EVENTS = '/vendors/events/total',
    POST_VENDORS_EVENTS_VIEWED = '/vendors/events/viewed',
    GET_NEW_OPT_IN_VENDORS = '/vendors/new/opt-in',

    // ========================================================================
    // VENDOR SETTINGS ROUTES (vendors-settings.controller.ts)
    // ========================================================================
    GET_VENDORS_SETTINGS = '/vendors/settings/all',
    PUT_VENDORS_SETTINGS = '/vendors/settings/sso',
    PUT_VENDORS_REVIEW_PERIOD = '/vendors/settings/review-period',
    PUT_VENDORS_SETTINGS_EMAIL_CONTENT = '/vendors/settings/email-content',

    // ========================================================================
    // VENDOR QUESTIONNAIRES ROUTES (vendors-questionnaires.controller.ts)
    // ========================================================================
    GET_VENDORS_QUESTIONNAIRES = '/vendors/:id/questionnaires',
    POST_VENDOR_QUESTIONNAIRE_SEND = '/vendors/:id/questionnaire/send',
    POST_MANUAL_UPLOAD = '/vendors/:id/questionnaires/manual',

    // ========================================================================
    // VENDOR DISCOVERY ROUTES (vendors-discovery.controller.ts)
    // ========================================================================
    GET_VENDORS_DISCOVERED = '/vendor-discovered',
    DELETE_VENDOR_DISCOVERED = '/vendor-discovered/:id',
    POST_VENDOR_DISCOVERED_CONVERT = '/vendor-discovered/:id/convert',
    POST_VENDORS_DISCOVERED_BULK_ACTIONS = '/vendors-discovered/bulk-actions/convert',
    DELETE_VENDORS_DISCOVERED_BULK_ACTIONS = '/vendors-discovered/bulk-actions',
    GET_VENDORS_DISCOVERY = '/vendors-discovery',

    // ========================================================================
    // VENDOR RISK MANAGEMENT ROUTES (vendors-risk-management.controller.ts)
    // ========================================================================

    // Risk Management Core
    GET_VENDORS_RISK_MANAGEMENT = '/vendors/risk-management/list',
    GET_VENDORS_RISK_MANAGEMENT_DASHBOARD = '/vendors/risk-management/dashboard',
    GET_VENDORS_RISK_MANAGEMENT_STATISTICS = '/vendors/risk-management/statistics',
    GET_VENDORS_RISK_MANAGEMENT_FILTERS = '/vendors/risk-management/filters',
    GET_VENDORS_RISK_MANAGEMENT_V2 = '/vendors/v2/risk-management/list',
    GET_VENDORS_RISK_REPORT = '/vendors/risks/report/csv',
    POST_RISK_MANAGEMENT = '/vendors/risk',
    PUT_VENDOR_RISK = '/vendors/risk-management/:risk_id',
    GET_VENDORS_RISK = '/vendors/risk/:risk_id',
    DELETE_CUSTOM_RISKS = '/vendor/risks',
    GET_VENDOR_RISK_SETTINGS = '/vendor/risk/settings',
    GET_VENDORS_RISKS_STATUS_STATS = '/vendors/stats/vendor-status',

    // Risk Categories
    GET_RISK_CATEGORIES = '/vendors/risk/categories',
    POST_VENDORS_RISK_CATEGORY = '/vendors/risk/categories',
    DELETE_VENDORS_RISK_CATEGORIES = '/vendors/risk/categories',

    // Risk Notes
    POST_NOTES = '/vendors/risk-management/:risk_id/notes',
    PUT_NOTES = '/vendors/risk-management/:risk_id/notes/:note_id',
    DELETE_NOTES = '/vendors/risk-management/:risk_id/notes/:note_id',

    // ========================================================================
    // VENDOR SECURITY REVIEWS ROUTES (vendors-security-reviews.controller.ts)
    // ========================================================================

    // Security Reviews Core
    POST_VENDORS_SECURITY_REVIEW = '/vendors/:vendorId/security-reviews',
    GET_VENDORS_SECURITY_REVIEWS = '/vendors/:vendorId/security-reviews',
    GET_VENDORS_SECURITY_REVIEW = '/vendors/security-review/:id',
    PUT_VENDOR_SECURITY_REVIEW = '/vendors/security-review/:id',
    PUT_VENDOR_SECURITY_REVIEW_TITLE = '/vendors/security-review/:id/title',
    PUT_VENDOR_SECURITY_REVIEW_STATUS = '/vendors/security-review/:id/status',
    DELETE_VENDOR_SECURITY_REVIEW = '/vendors/security-review/:id',
    GET_VENDOR_SECURITY_REVIEW_SUMMARY = '/vendors/security-review/:id/summary',

    // Security Review Observations
    GET_VENDOR_SECURITY_REVIEW_OBSERVATION = '/vendors/security-review/:id/observations',
    POST_VENDOR_SECURITY_REVIEW_OBSERVATION = '/vendors/security-review/:id/observation',
    PUT_VENDOR_SECURITY_REVIEW_OBSERVATION = '/vendors/security-review/observation/:id',
    DELETE_VENDOR_SECURITY_REVIEW_OBSERVATION = '/vendors/security-review/observation/:id',

    // Security Review Documents
    POST_VENDOR_SECURITY_REVIEW_DOCUMENT = '/vendors/security-review/:id/document',
    DELETE_VENDOR_SECURITY_REVIEW_DOCUMENT = '/vendors/security-review/:id/document',
    GET_VENDOR_SECURITY_REVIEW_DOCUMENTS = '/vendors/security-review/:id/documents',
    GET_VENDOR_SECURITY_REVIEW_DOCUMENT = '/vendors/security-review/:id/document/:documentId',

    // Security Review Assessments
    GET_VENDOR_ASSESSMENTS = '/vendors/security-review/:securityReviewId/assessments',
    PUT_VENDOR_ASSESSMENT_STATUS = '/vendors/security-review/:id/assessments/status',
    GET_VENDOR_ASSESSMENTS_STATS = '/vendors/security-review/:id/assessments/stats',

    // ========================================================================
    // VENDOR CUSTOM FIELDS ROUTES (vendors-custom-fields.controller.ts)
    // ========================================================================
    POST_VENDORS_CUSTOM_FIELDS_SUBMISSIONS = '/vendors/:vendorId/custom-fields/submissions',
    GET_VENDORS_CUSTOM_FIELDS_SUBMISSIONS = '/vendors/:vendorId/custom-fields/submissions',
    GET_VENDORS_CUSTOM_FIELDS_LIST = '/vendors/custom-fields/list',

    // ========================================================================
    // VENDOR TRUST CENTER ROUTES (vendor-trust-center.controller.ts)
    // ========================================================================
    GET_VENDOR_TRUST_CENTER = '/vendors/:id/trust',
    GET_VENDOR_TRUST_CENTER_CERTIFICATIONS = '/vendors/:id/trust/certifications',
    GET_VENDOR_TRUST_CENTER_OVERVIEW = '/vendors/:id/trust/overview',
    GET_VENDOR_TRUST_CENTER_SUB_PROCESSORS = '/vendors/:id/trust/sub-processors',
    GET_VENDOR_TRUST_CENTER_DOCUMENTS = '/vendors/:id/trust/documents',
    GET_VENDOR_TRUST_CENTER_DOCUMENT_DOWNLOAD = '/vendors/:id/trust/documents/:documentId/download',
    GET_VENDOR_TRUST_CENTER_ITEMS = '/vendors/:id/trust/items',
    POST_VENDOR_TRUST_CENTER_ACCESS_REQUEST = '/vendors/:id/trust/access-request',
    GET_VENDOR_TRUST_CENTER_ACCESS_REQUEST = '/vendors/:id/trust/access-request',
    GET_VENDOR_TRUST_CENTER_ACCESS_REQUEST_REQUIREMENTS = '/vendors/:id/trust/request-access/requirements',
}
