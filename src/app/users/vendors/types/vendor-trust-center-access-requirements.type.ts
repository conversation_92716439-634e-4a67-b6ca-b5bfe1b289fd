import {
    SBAccessRequestRequirements,
    SBAccessRequestRequirementsField,
} from 'dependencies/safebase/safebase-vendor-trust-center.types';

export interface VendorTrustCenterAccessRequirementsFieldUrl {
    href: string;
    mask?: string;
}

export interface VendorTrustCenterAccessRequirementsField
    extends Omit<SBAccessRequestRequirementsField, 'urls'> {
    urls?: VendorTrustCenterAccessRequirementsFieldUrl[];
}

export interface VendorTrustCenterAccessRequestRequirements
    extends Omit<SBAccessRequestRequirements, 'fields'> {
    fields: VendorTrustCenterAccessRequirementsField[];
}
