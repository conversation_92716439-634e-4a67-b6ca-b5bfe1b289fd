import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsBoolean, IsOptional } from 'class-validator';
import { IsPositiveInt } from 'commons/decorators/is-positive-int.decorator';
import { IsValidEnum } from 'commons/decorators/is-valid-enum.decorator';
import { RequestDto } from 'commons/dtos/request.dto';
import { FeatureType } from 'commons/enums/feature-toggling/feature.enum';

export class UserConfigurableSettingUpdateRequestDto extends RequestDto {
    @ApiProperty({
        type: 'boolean',
        example: true,
        description: 'Indicates if user can run a feature',
    })
    @IsBoolean()
    isEnabled: boolean;

    @ApiProperty({
        oneOf: [
            { type: 'string', example: 'any' },
            { type: 'number', example: 1 },
            { type: 'boolean', example: true },
        ],
        required: false,
        nullable: true,
        example: '1',
        description: 'New value of setting',
    })
    @IsOptional()
    value?: string | number | boolean | null;

    @ApiProperty({
        required: false,
        type: 'number',
        example: 1,
        description: 'Feature id to change settings of',
    })
    @Type(() => Number)
    @IsOptional()
    @IsPositiveInt()
    featureId?: number;

    @ApiProperty({
        required: false,
        example: FeatureType[FeatureType.READINESS_BY_CONTROL],
        description: 'Get settings by group',
        enum: FeatureType,
    })
    @IsValidEnum(FeatureType)
    @IsOptional()
    featureType?: FeatureType;
}
