import { MongoAbility } from '@casl/ability';
import { ErrorCode } from '@drata/enums';
import {
    BadRequestException,
    Body,
    Controller,
    Get,
    NotFoundException,
    Param,
    ParseIntPipe,
    Post,
    Put,
    Query,
    StreamableFile,
    UploadedFile,
    UseInterceptors,
} from '@nestjs/common';
import { EventBus } from '@nestjs/cqrs';
import { FileInterceptor } from '@nestjs/platform-express';
import {
    ApiBadRequestResponse,
    ApiBody,
    ApiConsumes,
    ApiCreatedResponse,
    ApiForbiddenResponse,
    ApiInternalServerErrorResponse,
    ApiNotFoundResponse,
    ApiOkResponse,
    ApiOperation,
    ApiPreconditionFailedResponse,
    ApiProduces,
    ApiTags,
    ApiUnprocessableEntityResponse,
} from '@nestjs/swagger';
import { NotFoundError } from 'app/apis/exceptions/app.error';
import { AppController } from 'app/app.controller';
import { ReviewStatus } from 'app/approvals/v2/approval-review-groups-core/types/review-status.enum';
import { NotificationsOrchestrationService } from 'app/notifications/services/notifications-orchestration.service';
import { PolicyApprovalsCommerceService } from 'app/policy-approvals/policy-approvals-commerce/services/policy-approvals-commerce.service';
import { User } from 'app/users/entities/user.entity';
import { PolicyDraftVersionRequestDto } from 'app/users/policies/dtos/policy-draft-version-request.dto';
import { PolicyVersionAppendixResponseDto } from 'app/users/policies/dtos/policy-version-appendix-response.dto';
import { PolicyVersionApprovalRequestDto } from 'app/users/policies/dtos/policy-version-approval-request.dto';
import { PolicyVersionAssignVersionAndStatusRequestDto } from 'app/users/policies/dtos/policy-version-assign-version-and-status-request.dto';
import { PolicyVersionExplanationOfChangesRequestDto } from 'app/users/policies/dtos/policy-version-explanation-of-changes-request.dto';
import { PolicyVersionExternalFileRequestDto } from 'app/users/policies/dtos/policy-version-external-file.dto';
import { PolicyVersionHistoryRequestDto } from 'app/users/policies/dtos/policy-version-history-request.dto';
import { PolicyVersionHistoryResponseDto } from 'app/users/policies/dtos/policy-version-history-response.dto';
import { PolicyVersionHtmlRequestDto } from 'app/users/policies/dtos/policy-version-html-request.dto';
import { PolicyVersionHtmlResponseDto } from 'app/users/policies/dtos/policy-version-html-response.dto';
import { PolicyVersionPdfFromHtmlRequestDto } from 'app/users/policies/dtos/policy-version-pdf-from-html-request.dto';
import { PolicyVersionResponseDto } from 'app/users/policies/dtos/policy-version-response.dto';
import { UpdatePolicyRenewalRequestDto } from 'app/users/policies/dtos/update-policy-renewal-date-request.dto';
import { PolicyVersion } from 'app/users/policies/entities/policy-version.entity';
import { Policy } from 'app/users/policies/entities/policy.entity';
import { PolicyVersionAppendixResult } from 'app/users/policies/enums/policy-version-appendix-result.enum';
import { PoliciesRoute } from 'app/users/policies/policies.routes';
import { PoliciesService } from 'app/users/policies/services/policies.service';
import { PolicyEmailsService } from 'app/users/policies/services/policy-emails.service';
import { PolicyStatusService } from 'app/users/policies/services/policy-status.service';
import { PolicyVersionService } from 'app/users/policies/services/policy-version.service';
import { PolicyAppendixResponse } from 'app/users/policies/types/policy-appendix-response.type';
import { PolicyVersionDraftType } from 'app/users/policies/types/policy-version-draft.type';
import { PolicyVersionHtml } from 'app/users/policies/types/policy-version-html.type';
import { UsersCoreService } from 'app/users/services/users-core.service';
import { Account } from 'auth/entities/account.entity';
import { CheckAbilities } from 'casl/ability.handlers';
import { Action } from 'casl/actions';
import { uploadPrivateFileMulterOptions } from 'commons/configs/upload.config';
import { Dto } from 'commons/decorators/dto.decorator';
import { GetAccount } from 'commons/decorators/get-account.decorator';
import { GetUser } from 'commons/decorators/get-user.decorator';
import { Area, ProductArea } from 'commons/decorators/product-area.decorator';
import { Roles } from 'commons/decorators/roles.decorator';
import { ExceptionResponseDto } from 'commons/dtos/exception-response.dto';
import { ApiResponse } from 'commons/enums/api-response.enum';
import { ClientType } from 'commons/enums/auth/client-type.enum';
import { PolicyType } from 'commons/enums/users/policies/policy-type.enum';
import { PolicyVersionStatus } from 'commons/enums/users/policies/policy-version-status.enum';
import { Role } from 'commons/enums/users/role.enum';
import { Subject } from 'commons/enums/users/subject.enum';
import { getKeys } from 'commons/helpers/enum.helper';
import { IsPositiveIntPipe } from 'commons/pipes/is-positive-int-pipe';
import { PaginationType } from 'commons/types/pagination.type';
import { UploadedFileType } from 'commons/types/uploaded-file.type';
import { FeatureFlagCategory } from 'feature-flags/enums/feature-flag-category.enum';
import { FeatureFlag } from 'feature-flags/feature-flag.enum';
import { FeatureFlagService } from 'feature-flags/feature-flag.service';
import { isEmpty } from 'lodash';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { PolloLogger } from 'pollo-logger/pollo.logger';

@ApiTags('Policy-Version')
@Controller()
@ProductArea(Area.POLICY_CENTER)
export class PolicyVersionController extends AppController {
    protected logger = PolloLogger.logger(this.constructor.name);
    constructor(
        private readonly policyVersionService: PolicyVersionService,
        private readonly policiesService: PoliciesService,
        private readonly policyStatusService: PolicyStatusService,
        private readonly eventBus: EventBus,
        private readonly notificationsOrchestrationService: NotificationsOrchestrationService,
        private readonly policyApprovalsCommerceService: PolicyApprovalsCommerceService,
        private readonly featureFlagService: FeatureFlagService,
        private readonly usersCoreService: UsersCoreService,
        private readonly policyEmailsService: PolicyEmailsService,
    ) {
        super();
    }

    @ApiOperation({
        description: `Get policy version content by policy version id
        <br><br>
        Allowed Roles: [
            ${Role[Role.TECHGOV]},
            ${Role[Role.ADMIN]},
            ${Role[Role.ACT_AS_READ_ONLY]},
            ${Role[Role.POLICY_MANAGER]}
            ${Role[Role.WORKSPACE_ADMINISTRATOR]}
        ]`,
    })
    @ApiOkResponse({
        type: PolicyVersionResponseDto,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponseDto,
    })
    @Roles(
        Role.TECHGOV,
        Role.ADMIN,
        Role.ACT_AS_READ_ONLY,
        Role.POLICY_MANAGER,
        Role.WORKSPACE_ADMINISTRATOR,
    )
    @CheckAbilities((ability: MongoAbility) => ability.can(Action.Read, Subject[Subject.Policy]))
    @Dto(PolicyVersionResponseDto)
    @Get(PoliciesRoute.GET_POLICY_VERSION)
    async getPolicyVersion(
        @GetAccount() account: Account,
        @Param('policyVersionId', IsPositiveIntPipe) policyVersionId: number,
        @Param('policyId', IsPositiveIntPipe) policyId: number,
    ): Promise<PolicyVersion> {
        return this.policyVersionService.getPolicyVersion(account, policyId, policyVersionId);
    }

    @ApiOperation({
        description: `Get policy version history
        <br><br>
        Allowed Roles: [
            ${Role[Role.TECHGOV]},
            ${Role[Role.ADMIN]},
            ${Role[Role.ACT_AS_READ_ONLY]},
            ${Role[Role.POLICY_MANAGER]}
            ${Role[Role.WORKSPACE_ADMINISTRATOR]}
        ]`,
    })
    @ApiOkResponse({
        type: PolicyVersionHistoryResponseDto,
    })
    @ApiInternalServerErrorResponse({
        description: ApiResponse.INTERNAL_SERVER_ERROR,
        type: ExceptionResponseDto,
    })
    @Roles(
        Role.TECHGOV,
        Role.ADMIN,
        Role.ACT_AS_READ_ONLY,
        Role.POLICY_MANAGER,
        Role.WORKSPACE_ADMINISTRATOR,
    )
    @CheckAbilities((ability: MongoAbility) => ability.can(Action.Read, Subject[Subject.Policy]))
    @Dto(PolicyVersionHistoryResponseDto)
    @Get(PoliciesRoute.GET_POLICY_VERSIONS)
    getPolicyVersionHistoryByPolicyId(
        @GetAccount() account: Account,
        @Param('policyId', ParseIntPipe) policyId: number,
        @Query() requestDto: PolicyVersionHistoryRequestDto,
    ): Promise<PaginationType<PolicyVersion>> {
        return this.policiesService.getPolicyVersionHistoryByPolicyId(
            account,
            policyId,
            requestDto,
        );
    }

    @ApiOperation({
        description: `Count the personnel linked to a policy
        <br><br>
        Allowed Roles: [
            ${Role[Role.TECHGOV]},
            ${Role[Role.ADMIN]},
            ${Role[Role.ACT_AS_READ_ONLY]},
            ${Role[Role.POLICY_MANAGER]}
            ${Role[Role.WORKSPACE_ADMINISTRATOR]}
        ]`,
    })
    @ApiOkResponse({
        type: Number,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponseDto,
    })
    @Roles(
        Role.TECHGOV,
        Role.ADMIN,
        Role.ACT_AS_READ_ONLY,
        Role.POLICY_MANAGER,
        Role.WORKSPACE_ADMINISTRATOR,
    )
    @CheckAbilities((ability: MongoAbility) => ability.can(Action.Read, Subject[Subject.Policy]))
    @Get(PoliciesRoute.GET_POLICY_USER_LINK_COUNT)
    async getPersonnelCountForPolicyScope(
        @GetAccount() account: Account,
        @Param('policyId', IsPositiveIntPipe) policyId: number,
    ): Promise<number> {
        return this.policyVersionService.getPersonnelCountForPolicyScope(account, policyId);
    }

    @ApiOperation({
        description: `Get policy version template content
        <br><br>
        Allowed Roles: [
            ${Role[Role.TECHGOV]},
            ${Role[Role.ADMIN]},
            ${Role[Role.ACT_AS_READ_ONLY]},
            ${Role[Role.POLICY_MANAGER]}
        ]`,
    })
    @ApiOkResponse({
        type: PolicyVersionHtmlResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponseDto,
    })
    @ApiUnprocessableEntityResponse({
        description: ApiResponse.CONFLICT,
        type: ExceptionResponseDto,
    })
    @Roles(Role.TECHGOV, Role.ADMIN, Role.ACT_AS_READ_ONLY, Role.POLICY_MANAGER)
    @CheckAbilities((ability: MongoAbility) => ability.can(Action.Read, Subject[Subject.Policy]))
    @Dto(PolicyVersionHtmlResponseDto)
    @Get(PoliciesRoute.GET_POLICY_VERSION_HTML_BY_POLICY_ID)
    async getPolicyTemplateHtml(
        @GetAccount() account: Account,
        @Param('id', IsPositiveIntPipe) id: number,
    ): Promise<PolicyVersionHtml> {
        return this.policyVersionService.getPolicyVersionTemplateHtmlByPolicyId(account, id);
    }

    @ApiOperation({
        description: `Update a policy version html content
        <br><br>
        Allowed Roles: [
            ${Role[Role.TECHGOV]},
            ${Role[Role.ADMIN]},
            ${Role[Role.POLICY_MANAGER]}
        ]`,
    })
    @ApiOkResponse({
        type: PolicyVersionHtmlResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponseDto,
    })
    @Roles(Role.TECHGOV, Role.ADMIN, Role.POLICY_MANAGER)
    @CheckAbilities((ability: MongoAbility) => ability.can(Action.Manage, Subject[Subject.Policy]))
    @Dto(PolicyVersionHtmlResponseDto)
    @Put(PoliciesRoute.PUT_POLICY_VERSION_HTML_BY_POLICY_ID)
    updatePolicyHtml(
        @GetAccount() account: Account,
        @GetUser() user: User,
        @Param('id', IsPositiveIntPipe) id: number,
        @Body() requestDto: PolicyVersionHtmlRequestDto,
    ): Promise<PolicyVersionHtml> {
        return this.policyVersionService.updatePolicyVersionHtmlByPolicyId(
            id,
            account,
            user,
            requestDto.html,
        );
    }

    @ApiOperation({
        description: `Update a upload policy version to authored
        <br><br>
        Allowed Roles: [
            ${Role[Role.TECHGOV]},
            ${Role[Role.ADMIN]},
            ${Role[Role.POLICY_MANAGER]}
        ]`,
    })
    @ApiOkResponse({
        type: PolicyVersionResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponseDto,
    })
    @Roles(Role.TECHGOV, Role.ADMIN, Role.POLICY_MANAGER)
    @CheckAbilities((ability: MongoAbility) => ability.can(Action.Manage, Subject[Subject.Policy]))
    @Dto(PolicyVersionResponseDto)
    @Put(PoliciesRoute.PUT_POLICY_VERSION_TO_AUTHORED)
    updatePolicyVersionToAuthored(
        @GetAccount() account: Account,
        @GetUser() user: User,
        @Param('id', IsPositiveIntPipe) id: number,
    ): Promise<PolicyVersion> {
        return this.policyVersionService.updatePolicyVersionToAuthored(id, user, account);
    }

    @ApiOperation({
        description: `Update policy version partially to change explanation of changes
        <br><br>
        Allowed Roles: [
            ${Role[Role.TECHGOV]},
            ${Role[Role.ADMIN]},
            ${Role[Role.POLICY_MANAGER]}
            ${Role[Role.WORKSPACE_ADMINISTRATOR]}
        ]`,
    })
    @ApiOkResponse({
        type: PolicyVersionResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponseDto,
    })
    @ApiForbiddenResponse({
        description: ApiResponse.FORBIDDEN,
        type: ExceptionResponseDto,
    })
    @Roles(Role.TECHGOV, Role.ADMIN, Role.POLICY_MANAGER, Role.WORKSPACE_ADMINISTRATOR)
    @CheckAbilities((ability: MongoAbility) => ability.can(Action.Manage, Subject[Subject.Policy]))
    @Dto(PolicyVersionResponseDto)
    @Put(PoliciesRoute.PUT_POLICY_VERSION_EXPLANATION_OF_CHANGES)
    async putPolicyVersionExplanationOfChanges(
        @GetAccount() account: Account,
        @GetUser() user: User,
        @Param('policyVersionId', IsPositiveIntPipe) policyVersionId: number,
        @Param('policyId', IsPositiveIntPipe) policyId: number,
        @Body() dto: PolicyVersionExplanationOfChangesRequestDto,
    ): Promise<PolicyVersion> {
        return this.policyVersionService.updatePolicyVersionExplanationOfChangesByPolicyId(
            account,
            policyVersionId,
            policyId,
            user,
            dto.policyVersionExplanationOfChanges ?? '',
        );
    }

    @ApiOperation({
        description: `Update policy version partially to modify status
        <br><br>
        Allowed Roles: [
            ${Role[Role.TECHGOV]},
            ${Role[Role.ADMIN]},
            ${Role[Role.POLICY_MANAGER]},
            ${Role[Role.WORKSPACE_ADMINISTRATOR]}
        ]`,
    })
    @ApiOkResponse({
        type: PolicyVersionResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponseDto,
    })
    @ApiForbiddenResponse({
        description: ApiResponse.FORBIDDEN,
        type: ExceptionResponseDto,
    })
    @Roles(Role.TECHGOV, Role.ADMIN, Role.POLICY_MANAGER, Role.WORKSPACE_ADMINISTRATOR)
    @CheckAbilities((ability: MongoAbility) => ability.can(Action.Manage, Subject[Subject.Policy]))
    @Dto(PolicyVersionAppendixResponseDto)
    @Put(PoliciesRoute.PUT_POLICY_VERSION_APPROVAL)
    async putPolicyVersionStatus(
        @GetAccount() account: Account,
        @GetUser() user: User,
        @Param('policyVersionId', IsPositiveIntPipe) policyVersionId: number,
        @Param('policyId', IsPositiveIntPipe) policyId: number,
        @Body() dto: PolicyVersionApprovalRequestDto,
    ): Promise<PolicyAppendixResponse> {
        const policy = await this.policiesService.getPolicy(policyId);
        const policyVersion = await this.policyVersionService.getPolicyVersion(
            account,
            policyId,
            policyVersionId,
        );

        const validatedPolicyVersion =
            await this.policyVersionService.validatePolicyVersionOwner(policyVersion);

        if (dto.policyVersionStatus === PolicyVersionStatus.APPROVED) {
            if (!dto.reviewId) {
                throw new BadRequestException(
                    'Review Id is required when approving a policy version',
                );
            }

            await this.policyApprovalsCommerceService.submitPolicyApprovalReview(
                account,
                user,
                policyId,
                policyVersionId,
                dto.reviewId,
                ReviewStatus.APPROVED,
            );

            return {
                ...validatedPolicyVersion,
                appendixUploaded: PolicyVersionAppendixResult.NOT_UPLOADED,
            } as PolicyAppendixResponse;
        }

        if (dto.policyVersionStatus === PolicyVersionStatus.NEEDS_APPROVAL) {
            const fullApprovalConfigurations =
                await this.policyApprovalsCommerceService.getApprovalConfigurationsForPolicy(
                    policyId,
                );

            if (isEmpty(fullApprovalConfigurations)) {
                throw new NotFoundException('No approval configuration found for policy version');
            }
        }

        const updatedPolicyVersion = await this.policyVersionService.updatePolicyVersionStatus(
            account,
            dto,
            policyVersionId,
            policyId,
            user,
        );

        await this.processAdditionalActionsForPolicyVersionStatusChange(
            account,
            user,
            policy,
            validatedPolicyVersion,
            dto.policyVersionStatus,
        );

        return updatedPolicyVersion;
    }

    @ApiOperation({
        description: `Get the preview PDF version of the policy from work in progress.
        <br></br>
        Allowed Roles: [
            ${Role[Role.AUDITOR]},
            ${Role[Role.ACT_AS_READ_ONLY]},
            ${Role[Role.ADMIN]},
            ${Role[Role.POLICY_MANAGER]},
            ${Role[Role.WORKSPACE_ADMINISTRATOR]},
        ]
        `,
    })
    @ApiOkResponse({
        description: ApiResponse.OK,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponseDto,
    })
    @ApiProduces('application/pdf')
    @Roles(
        Role.AUDITOR,
        Role.ACT_AS_READ_ONLY,
        Role.ADMIN,
        Role.POLICY_MANAGER,
        Role.WORKSPACE_ADMINISTRATOR,
    )
    @CheckAbilities((ability: MongoAbility) => ability.can(Action.Read, Subject[Subject.Policy]))
    @Get(PoliciesRoute.GET_HTML_PDF_DOWNLOAD)
    async getPolicyVersionPdf(
        @GetAccount() account: Account,
        @Param('policyId', IsPositiveIntPipe)
        policyId: number,
        @Param('policyVersionId', IsPositiveIntPipe)
        policyVersionId: number,
    ): Promise<StreamableFile> {
        return this.policyVersionService.getPolicyVersionPDF(account, policyId, policyVersionId);
    }

    // rule skipped beacause this method does not create any resources

    @ApiOperation({
        description: `Creates the preview PDF version of the policy from a given html.
        <br></br>
        Allowed Roles: [
            ${Role[Role.AUDITOR]},
            ${Role[Role.ACT_AS_READ_ONLY]},
            ${Role[Role.ADMIN]},
            ${Role[Role.POLICY_MANAGER]},
            ${Role[Role.WORKSPACE_ADMINISTRATOR]},
        ]
        `,
    })
    @ApiCreatedResponse({
        description: ApiResponse.CREATED,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponseDto,
    })
    @ApiProduces('application/pdf')
    @Roles(
        Role.AUDITOR,
        Role.ACT_AS_READ_ONLY,
        Role.ADMIN,
        Role.POLICY_MANAGER,
        Role.WORKSPACE_ADMINISTRATOR,
    )
    @CheckAbilities((ability: MongoAbility) => ability.can(Action.Read, Subject[Subject.Policy]))
    @Post(PoliciesRoute.POST_POLICY_VERSION_HTML_TO_PDF)
    @ApiCreatedResponse({
        description: ApiResponse.CREATED,
        type: StreamableFile,
    })
    async createPolicyVersionPdfFromHtml(
        @GetAccount() account: Account,
        @Param('policyId', IsPositiveIntPipe)
        policyId: number,
        @Body()
        policyVersionPdfFromHtmlRequestDto: PolicyVersionPdfFromHtmlRequestDto,
    ): Promise<StreamableFile> {
        const { data, name } =
            await this.policyVersionService.createPolicyVersionPdfFromPolicyVersionHtml(
                account,
                policyId,
                policyVersionPdfFromHtmlRequestDto.html,
            );

        return new StreamableFile(data, {
            type: 'application/pdf',
            disposition: `attachment; filename="${name}"`,
            length: data.length,
        });
    }

    @ApiOperation({
        description: `Assign policy version, subversion and status
        <br><br>
        Allowed Roles: [
            ${Role[Role.TECHGOV]},
            ${Role[Role.ADMIN]},
            ${Role[Role.POLICY_MANAGER]},
            ${Role[Role.WORKSPACE_ADMINISTRATOR]},
        ]`,
    })
    @ApiOkResponse({
        type: PolicyVersionResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponseDto,
    })
    @ApiForbiddenResponse({
        description: ApiResponse.FORBIDDEN,
        type: ExceptionResponseDto,
    })
    @Roles(Role.TECHGOV, Role.ADMIN, Role.POLICY_MANAGER, Role.WORKSPACE_ADMINISTRATOR)
    @CheckAbilities((ability: MongoAbility) => ability.can(Action.Manage, Subject[Subject.Policy]))
    @Dto(PolicyVersionResponseDto)
    @Put(PoliciesRoute.PUT_POLICY_VERSION_SUBVERSION_STATUS)
    async putPolicyVersionVersionAndStatus(
        @GetAccount() account: Account,
        @GetUser() user: User,
        @Param('policyVersionId', IsPositiveIntPipe) policyVersionId: number,
        @Param('policyId', IsPositiveIntPipe) policyId: number,
        @Body() dto: PolicyVersionAssignVersionAndStatusRequestDto,
    ): Promise<PolicyVersion> {
        const isMultiApproversFlagEnabled = await this.isPolicyMultiApproversEnabled(user, account);
        if (isMultiApproversFlagEnabled) {
            const fullApprovalConfigurations =
                await this.policyApprovalsCommerceService.getApprovalConfigurationsForPolicy(
                    policyId,
                );

            if (isEmpty(fullApprovalConfigurations)) {
                throw new NotFoundError(
                    ErrorCode.ENTITY_NOT_FOUND,
                    'Error moving version from Draft to Needs Approval...',
                    {
                        policyId,
                        policyVersionId,
                    },
                    'warn',
                );
            }
        }

        try {
            // Get the current policy version first
            const policyVersion = await this.policyVersionService.getPolicyVersion(
                account,
                policyId,
                policyVersionId,
            );

            // Validate and assign owner BEFORE status transition
            await this.policyVersionService.validatePolicyVersionOwner(policyVersion);

            // Now proceed with the status transition
            const updatedPolicyVersion =
                await this.policyVersionService.updatePolicyVersionAssignVersionAndStatus(
                    account,
                    user,
                    policyVersion.id,
                    policyId,
                    dto,
                );

            // If the policy version does not require approval, it will automatically move to 'published' and notify the control owners.
            if (dto.requiresApproval) {
                await this.policyApprovalsCommerceService.startPolicyApprovalWorkflowFromLatestConfiguration(
                    policyId,
                    policyVersionId,
                    user,
                    account,
                );
            } else {
                await this.sendControlOwnersNotifications(policyId, account, user);
            }

            return updatedPolicyVersion;
        } catch (e) {
            throw new BadRequestException(e.message);
        }
    }

    @ApiOperation({
        description: `Create a draft policy version from existing policy
        <br><br>
        Allowed Roles: [
            ${Role[Role.TECHGOV]},
            ${Role[Role.ADMIN]},
            ${Role[Role.POLICY_MANAGER]},
            ${Role[Role.WORKSPACE_ADMINISTRATOR]},
        ]`,
    })
    @ApiCreatedResponse({
        description: ApiResponse.CREATED,
        type: PolicyVersionResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponseDto,
    })
    @ApiForbiddenResponse({
        description: ApiResponse.FORBIDDEN,
        type: ExceptionResponseDto,
    })
    @ApiPreconditionFailedResponse({
        description: ApiResponse.PRECONDITION_FAILED_AGENT,
        type: ExceptionResponseDto,
    })
    @Roles(Role.TECHGOV, Role.ADMIN, Role.POLICY_MANAGER, Role.WORKSPACE_ADMINISTRATOR)
    @CheckAbilities((ability: MongoAbility) => ability.can(Action.Manage, Subject[Subject.Policy]))
    @ApiConsumes('multipart/form-data')
    @ApiBody({
        schema: {
            type: 'object',
            properties: {
                file: {
                    type: 'array',
                    items: { type: 'string', format: 'binary' },
                },
                policyType: {
                    enum: getKeys(PolicyType),
                    example: PolicyType[PolicyType.BUILDER],
                    description: 'Policy type.',
                },
                policyVersionStatus: {
                    enum: getKeys(PolicyVersionStatus),
                    example: PolicyVersionStatus[PolicyVersionStatus.DRAFT],
                    description:
                        'Status of the policy version that will serve as the base for creating a new version.',
                },
            },
        },
    })
    @UseInterceptors(FileInterceptor('file', uploadPrivateFileMulterOptions))
    @Dto(PolicyVersionResponseDto)
    @Post(PoliciesRoute.POST_POLICY_VERSION_DRAFT)
    async postPolicyVersion(
        @GetAccount() account: Account,
        @GetUser() user: User,
        @Param('id', IsPositiveIntPipe) policyId: number,
        @Body() policyDraftVersionDto: PolicyDraftVersionRequestDto,
        @UploadedFile() file: UploadedFileType,
    ): Promise<PolicyVersion> {
        const policyVersionDraft: PolicyVersionDraftType = {
            policyType: policyDraftVersionDto.policyType,
            fileUpload: file,
            policyVersionStatus: policyDraftVersionDto.policyVersionStatus,
        };

        const policy = await this.policiesService.getPolicy(policyId);

        const draftVersion = await this.policyVersionService.createDraftPolicyVersion(
            user,
            policyId,
            account,
            policyVersionDraft,
            policy?.currentOwner?.id ?? null,
        );

        if (policyDraftVersionDto?.ownerId) {
            await this.policiesService.updatePolicyOwner(
                account,
                policyId,
                policyDraftVersionDto.ownerId,
            );
        }

        if (
            (await this.isPolicyMultiApproversEnabled(user, account)) &&
            policyVersionDraft.policyVersionStatus === PolicyVersionStatus.DRAFT &&
            policyDraftVersionDto.ownerId
        ) {
            await this.policyApprovalsCommerceService.createInitialApprovalConfiguration(
                policyDraftVersionDto.ownerId,
                policyId,
            );
        }
        return draftVersion;
    }

    @ApiOperation({
        description: `Update a policy version file
        <br><br>
        Allowed Roles: [
            ${Role[Role.TECHGOV]},
            ${Role[Role.ADMIN]},
            ${Role[Role.POLICY_MANAGER]},
            ${Role[Role.WORKSPACE_ADMINISTRATOR]}
        ]`,
    })
    @ApiOkResponse({
        type: PolicyVersionResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponseDto,
    })
    @Roles(Role.TECHGOV, Role.ADMIN, Role.POLICY_MANAGER, Role.WORKSPACE_ADMINISTRATOR)
    @CheckAbilities((ability: MongoAbility) => ability.can(Action.Manage, Subject[Subject.Policy]))
    @ApiConsumes('multipart/form-data')
    @ApiBody({
        schema: {
            type: 'object',
            properties: {
                file: {
                    type: 'array',
                    items: { type: 'string', format: 'binary' },
                },
            },
        },
    })
    @Dto(PolicyVersionResponseDto)
    @UseInterceptors(FileInterceptor('file', uploadPrivateFileMulterOptions))
    @Put(PoliciesRoute.PUT_POLICY_VERSION_UPLOAD_FILE)
    putPolicyVersionFile(
        @GetAccount() account: Account,
        @GetUser() user: User,
        @Param('policyId', IsPositiveIntPipe) policyId: number,
        @Param('policyVersionId', IsPositiveIntPipe) policyVersionId: number,
        @UploadedFile() file: UploadedFileType,
    ): Promise<PolicyVersion> {
        return this.policyVersionService.uploadPolicyVersionFile(
            account,
            user,
            policyId,
            policyVersionId,
            file,
        );
    }

    @ApiOperation({
        description: `Update a policy version external file
        <br><br>
        Allowed Roles: [
            ${Role[Role.TECHGOV]},
            ${Role[Role.ADMIN]},
            ${Role[Role.POLICY_MANAGER]},
            ${Role[Role.WORKSPACE_ADMINISTRATOR]}
        ]`,
    })
    @ApiOkResponse({
        type: PolicyVersionResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponseDto,
    })
    @Roles(Role.TECHGOV, Role.ADMIN, Role.POLICY_MANAGER, Role.WORKSPACE_ADMINISTRATOR)
    @CheckAbilities((ability: MongoAbility) => ability.can(Action.Manage, Subject[Subject.Policy]))
    @Dto(PolicyVersionResponseDto)
    @Put(PoliciesRoute.PUT_POLICY_VERSION_EXTERNAL_FILE)
    async putPolicyVersionExternalFile(
        @GetAccount() account: Account,
        @GetUser() user: User,
        @Param('policyId', IsPositiveIntPipe) policyId: number,
        @Param('policyVersionId', IsPositiveIntPipe) policyVersionId: number,
        @Body() dto: PolicyVersionExternalFileRequestDto,
    ): Promise<PolicyVersion | null> {
        const policyVersion = this.policyStatusService.uploadPolicyVersionExternalFile(
            account,
            user,
            policyId,
            policyVersionId,
            dto.externalFileId,
            dto.needsPersonnelAcknowledgement,

            dto.clientType,
            dto.changesExplanation,
        );
        // Versions imported from Bamboo do not require approval. These imported versions will automatically move to 'published' and notify the control owners.
        if (dto.clientType === ClientType.BAMBOO_HR) {
            await this.sendControlOwnersNotifications(policyId, account, user);
        }
        return policyVersion;
    }

    @ApiOperation({
        description: `Renews the current published version
        <br><br>
        Allowed Roles: [
            ${Role[Role.TECHGOV]},
            ${Role[Role.ADMIN]},
            ${Role[Role.POLICY_MANAGER]},
            ${Role[Role.WORKSPACE_ADMINISTRATOR]},
        ]`,
    })
    @ApiOkResponse({
        type: PolicyVersionResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponseDto,
    })
    @ApiForbiddenResponse({
        description: ApiResponse.FORBIDDEN,
        type: ExceptionResponseDto,
    })
    @Roles(Role.TECHGOV, Role.ADMIN, Role.POLICY_MANAGER, Role.WORKSPACE_ADMINISTRATOR)
    @CheckAbilities((ability: MongoAbility) => ability.can(Action.Manage, Subject[Subject.Policy]))
    @Dto(PolicyVersionResponseDto)
    @Put(PoliciesRoute.PUT_RENEWAL_DATE)
    async renewalCurrentPublishedVersion(
        @GetAccount() account: Account,
        @GetUser() user: User,
        @Param('versionId', IsPositiveIntPipe) versionId: number,
        @Param('policyId', IsPositiveIntPipe) policyId: number,
        @Body() dto: UpdatePolicyRenewalRequestDto,
    ): Promise<PolicyVersion> {
        const policyVersion = await this.policyVersionService.renewPolicyVersionAndStatusService(
            account,
            user,
            versionId,
            policyId,
            dto,
        );

        // If the policy version does not require approval, it will automatically move to 'published' and notify the control owners.
        if (dto.requiresApproval) {
            await this.policyApprovalsCommerceService.startPolicyApprovalWorkflowFromLatestConfiguration(
                policyId,
                policyVersion.id,
                user,
                account,
            );
        } else {
            await this.sendControlOwnersNotifications(policyId, account, user);
        }

        return policyVersion;
    }

    private async sendControlOwnersNotifications(
        policyId: number,
        account: Account,
        user: User,
    ): Promise<void> {
        try {
            const controls = await this.policiesService.getControlsFromPolicyByPolicyId(
                account,
                policyId,
            );

            if (!isEmpty(controls)) {
                await this.notificationsOrchestrationService.sendControlEvidenceNotificationToControlOwnersFromMultipleControls(
                    account,
                    user,
                    controls.map(control => control.id),
                );
            }
        } catch (error) {
            this.logger.warn(
                PolloMessage.msg('Error sending notifications to control owners')
                    .setAccountId(account.id)
                    .setError(error.message)
                    .setContext(this.constructor.name)
                    .setSubContext(this.sendControlOwnersNotifications.name),
            );
        }
    }

    private async isPolicyMultiApproversEnabled(user: User, account: Account): Promise<boolean> {
        return this.featureFlagService.evaluate(
            {
                name: FeatureFlag.POLICY_MULTI_APPROVERS,
                defaultValue: false,
                category: FeatureFlagCategory.NONE,
            },
            user,
            account,
        );
    }

    private async processAdditionalActionsForPolicyVersionStatusChange(
        account: Account,
        user: User,
        policy: Policy,
        policyVersion: PolicyVersion,
        policyVersionStatus: PolicyVersionStatus,
    ): Promise<void> {
        if (policyVersionStatus === PolicyVersionStatus.PUBLISHED) {
            return this.sendControlOwnersNotifications(policy.id, account, user);
        }
    }
}
