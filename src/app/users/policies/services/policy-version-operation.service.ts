import { ErrorCode } from '@drata/enums';
import { Injectable, NotFoundException } from '@nestjs/common';
import { Product } from 'app/companies/products/entities/product.entity';
import { ControlReadinessEvent } from 'app/grc/workflow-services/events/control-readiness.workflow.event';
import { User } from 'app/users/entities/user.entity';
import { PolicyGroupRepository } from 'app/users/groups/repositories/policy-group.repository';
import { PersonnelRepository } from 'app/users/personnel/repositories/personnel.repository';
import { GracePeriodSLARequestDto } from 'app/users/policies/dtos/grace-period-sla-request.dto';
import { PolicyVersionAssignVersionAndStatusRequestDto } from 'app/users/policies/dtos/policy-version-assign-version-and-status-request.dto';
import { PolicySLARequestDto } from 'app/users/policies/dtos/sla-request.dto';
import { UpdatePolicyRenewalRequestDto } from 'app/users/policies/dtos/update-policy-renewal-date-request.dto';
import { WeekTimeFrameSLARequestDto } from 'app/users/policies/dtos/week-time-frame-sla-request.dto';
import { ExternalPolicyFile } from 'app/users/policies/entities/external-policy-file.entity';
import { GracePeriodSLA } from 'app/users/policies/entities/grace-period-sla.entity';
import { P3MatrixSLA } from 'app/users/policies/entities/p3-matrix-sla.entity';
import { PolicyVersionContent } from 'app/users/policies/entities/policy-version-content.entity';
import { PolicyVersion } from 'app/users/policies/entities/policy-version.entity';
import { Policy } from 'app/users/policies/entities/policy.entity';
import { WeekTimeFrameSLA } from 'app/users/policies/entities/week-time-frame-sla.entity';
import { PolicyVersionAppendixResult } from 'app/users/policies/enums/policy-version-appendix-result.enum';
import { insertFigureDefaultCSSClass } from 'app/users/policies/helpers/policy-html-svg-styles.helper';
import {
    getErrorLogForInvalidStatus,
    getFormattedHtml,
} from 'app/users/policies/helpers/policy.helper';
import { PolicyRenewWithoutUpdatesEvent } from 'app/users/policies/observables/events/policy-renew-without-update.event';
import { PolicyVersionStatusApprovedEvent } from 'app/users/policies/observables/events/policy-version-status-approved.event';
import { PolicyVersionStatusCancelApprovalEvent } from 'app/users/policies/observables/events/policy-version-status-cancel-approval.event';
import { PolicyVersionStatusDraftCreatedEvent } from 'app/users/policies/observables/events/policy-version-status-draft-created.event';
import { PolicyVersionStatusDraftFinalizedEvent } from 'app/users/policies/observables/events/policy-version-status-draft-finalized.event';
import { PolicyVersionStatusPublishedEvent } from 'app/users/policies/observables/events/policy-version-status-published.event';
import { GracePeriodSLARepository } from 'app/users/policies/repositories/grace-period-sla.repository';
import { P3MatrixSLARepository } from 'app/users/policies/repositories/p3-matrix-sla.repository';
import { PolicyVersionContentRepository } from 'app/users/policies/repositories/policy-version-content.repository';
import { PolicyVersionRepository } from 'app/users/policies/repositories/policy-version.repository';
import { PolicyRepository } from 'app/users/policies/repositories/policy.repository';
import { WeekTimeFrameSLARepository } from 'app/users/policies/repositories/week-time-frame-sla.repository';
import { HtmlUrlReplacer } from 'app/users/policies/services/html-url-replacer';
import { PolicyPDFService } from 'app/users/policies/services/policy-pdf.service';
import { PolicyVersionValidationService } from 'app/users/policies/services/policy-version-validation.service';
import { GetSummarizedPolicyWithoutDeletedVersions } from 'app/users/policies/types/get-policy-summarized.type';
import { GetSlasByPolicyVersion } from 'app/users/policies/types/policy-slas.type';
import { PolicyVersionDraftType } from 'app/users/policies/types/policy-version-draft.type';
import { Account } from 'auth/entities/account.entity';
import { PolloAdapter } from 'commons/adapters/pollo.adapter';
import { TenancyTransaction } from 'commons/decorators/transaction';
import { UploadType } from 'commons/enums/upload-type.enum';
import { PolicyType } from 'commons/enums/users/policies/policy-type.enum';
import { PolicyVersionStatus } from 'commons/enums/users/policies/policy-version-status.enum';
import { InternalServerErrorException } from 'commons/exceptions/internal-server-error.exception';
import { PreconditionFailedException } from 'commons/exceptions/precondition-failed.exception';
import { validateOriginalFileNameOrFail } from 'commons/helpers/file.helper';
import {
    checkIsDocument,
    isFilePdf,
    sanitizeOriginalFileName,
} from 'commons/helpers/upload.helper';
import { AppService } from 'commons/services/app.service';
import { Downloader } from 'dependencies/downloader/downloader';
import { FileConverter } from 'dependencies/file-converter/file-converter';
import { FileConvertTypes } from 'dependencies/file-converter/types/file-convert-types.enum';
import { HtmlToPdfConverter } from 'dependencies/html-to-pdf-converter/html-to-pdf-converter';
import { UploaderPayloadType } from 'dependencies/uploader/types/uploader-payload.type';
import { Uploader } from 'dependencies/uploader/uploader';
import { cloneDeep, first, isEmpty, isNil, isUndefined, omit } from 'lodash';
import { Span } from 'nestjs-ddtrace';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { PolloLogger } from 'pollo-logger/pollo.logger';
import { Repository } from 'typeorm';

type UpdatePolicyVersionType = Partial<PolicyVersion>;

type PolicyVersionToUpdateForDraftType = {
    policyVersionStatus: PolicyVersionStatus;
    current: boolean;
    version?: number;
    requiresAcknowledgment?: boolean;
};

@Injectable()
export class PolicyVersionOperationService extends AppService {
    logger = PolloLogger.logger(this.constructor.name);

    constructor(
        private readonly policyVersionValidationService: PolicyVersionValidationService,
        private readonly downloader: Downloader,
        private readonly uploader: Uploader,
        private readonly fileConverter: FileConverter,
        private readonly htmlUrlReplacer: HtmlUrlReplacer,
        private readonly htmlToPdfConverter: HtmlToPdfConverter,
        private readonly policyPDFService: PolicyPDFService,
    ) {
        super();
    }

    async updatePolicyVersion(id: number, updateParams: UpdatePolicyVersionType): Promise<void> {
        await this.policyVersionRepository.update({ id }, updateParams);
    }

    async cancelApprovalById(
        account: Account,
        policyVersionId: number,
        policy: Policy,
        user: User,
        explanationOfChanges: string,
    ): Promise<PolicyVersion> {
        const policyVersionUpdate = {
            changesExplanation: explanationOfChanges,
            policyVersionStatus: PolicyVersionStatus.NEEDS_APPROVAL,
        } as Partial<PolicyVersion>;

        await this.updatePolicyVersion(policyVersionId, policyVersionUpdate);

        const updatedVersion = await this.policyVersionRepository.findOneOrFail({
            where: { id: policyVersionId },
            relations: ['approvedBy', 'externalPolicyFile'],
        });

        await this._eventBus.publish(
            new PolicyVersionStatusCancelApprovalEvent(account, user, policy, updatedVersion),
        );

        return updatedVersion;
    }

    @Span()
    @TenancyTransaction()
    async updatePolicyVersionStatusToApproved(
        account: Account,
        policy: Policy,
        policyVersion: PolicyVersion,
        user: User,
    ): Promise<void> {
        try {
            this.policyVersionValidationService.validateIsPolicyVersionRenewalDate(policyVersion);
            this.policyVersionValidationService.validatePolicyVersionContent(policyVersion);

            this.policyVersionValidationService.validateIsPolicyVersionStatusCorrect(
                [PolicyVersionStatus.NEEDS_APPROVAL],
                policyVersion.policyVersionStatus,
            );

            const updateDate = new Date();

            const policyVersionUpdate = {
                current: false,
                policyVersionStatus: PolicyVersionStatus.APPROVED,
                approvedAt: updateDate,
                approvedBy: user,
            };

            await this.updatePolicyVersion(policyVersion.id, policyVersionUpdate);

            const updatedVersion = await this.policyVersionRepository.findOneByOrFail({
                id: policyVersion.id,
            });

            await this._eventBus.publish(
                new PolicyVersionStatusApprovedEvent(account, user, policy, updatedVersion),
            );
        } catch (error) {
            const errorMessage = `Error approving policy version`;
            getErrorLogForInvalidStatus(
                account,
                this.logger,
                error,
                errorMessage,
                this.constructor.name,
                this.updatePolicyVersionStatusToApproved.name,
                { policyId: policy.id, policyVersionId: policyVersion.id },
            );
            throw error;
        }
    }

    @TenancyTransaction()
    async updatePolicyVersionStatusToPublished(
        account: Account,
        policy: Policy,
        policyVersion: PolicyVersion,
        user: User,
        sendAlerts?: boolean,
        requiresAcknowledgment?: boolean,
    ): Promise<PolicyVersionAppendixResult> {
        try {
            this.policyVersionValidationService.validatePolicyVersionContent(policyVersion);
            this.policyVersionValidationService.validateIsPolicyVersionRenewalDate(policyVersion);

            this.policyVersionValidationService.validateIsPolicyVersionStatusCorrect(
                [PolicyVersionStatus.APPROVED],
                policyVersion.policyVersionStatus,
            );

            const currentPublishedVersion =
                await this.policyVersionRepository.getCurrentPublishedPolicyVersionByPolicyId(
                    policy.id,
                    false,
                );

            if (!isNil(currentPublishedVersion)) {
                await this.updatePolicyVersion(currentPublishedVersion.id, {
                    current: false,
                });
                await this.deletePolicyVersionSLAsByPolicyVersionId(currentPublishedVersion.id);
            }

            const policyVersionUpdate = {
                current: true,
                policyVersionStatus: PolicyVersionStatus.PUBLISHED,
                publishedAt: new Date(),
                publishedBy: user,
                requiresAcknowledgment: requiresAcknowledgment,
            } as UpdatePolicyVersionType;

            await this.updatePolicyVersion(policyVersion.id, policyVersionUpdate);
            // Before call updatePolicyVersionFile() the policy version must be published since the published at is used to create the pdf
            const fileUploadedResult = await this.updatePolicyVersionFile(
                account,
                policyVersion.id,
                policy.id,
            );
            await this.publishPolicyVersionPublishedStatus(account, user, policy.id, sendAlerts);

            return fileUploadedResult;
        } catch (error) {
            this.handleIncorrectPasswordError(error, account, policy.id, policyVersion.id);
            const errorMessage = `Error publishing policy version policy id: ${policy.id} - policy version id: ${policyVersion.id}`;
            getErrorLogForInvalidStatus(
                account,
                this.logger,
                error,
                errorMessage,
                this.constructor.name,
                this.updatePolicyVersionStatusToPublished.name,
                { policyId: policy.id, policyVersionId: policyVersion.id },
            );
            if (error.code === ErrorCode.POLICY_VERSION_VERSION_INVALID_STATUS) {
                throw error;
            }

            throw new InternalServerErrorException(
                `${errorMessage}: ${error.message}`,
                ErrorCode.POLICY_VERSION_VERSION_NOT_UPDATED,
            );
        }
    }

    /**
     * Upload and set policy version file
     * @param account
     * @param policyVersionId
     * @param policyId
     * @param isBambooPolicy
     * @returns {Promise<void>}
     */

    async updatePolicyVersionFile(
        account: Account,
        policyVersionId: number,
        policyId: number,
        isBambooPolicy = false,
        isFromPublished = false, // In some cases, we're updating values for an already published version. For these scenarios, we don't need to validate whether the policy version is the latest.
    ): Promise<PolicyVersionAppendixResult> {
        try {
            // First, ensure that the policy version for which we want to update the file is part of the current policy.
            await this.policyVersionValidationService.validateIsVersionFromPolicy(
                policyId,
                policyVersionId,
            );

            // Secondly, verify that the policy is the most current version.
            // This method should be activated when the policy version changes to "PUBLISHED."
            // No draft version should be created at this stage.
            if (!isFromPublished) {
                await this.policyVersionValidationService.validatePolicyVersionIsLatest(
                    policyId,
                    policyVersionId,
                );
            }

            const policy = await this.policyRepository.getPolicyForAppendixPdf(policyId);

            if (isNil(policy)) {
                throw new NotFoundException(
                    ErrorCode.POLICY_NOT_FOUND,
                    `Error updating policy version file for policy id: ${policyId} not found`,
                );
            }

            const policyVersion = await this.policyVersionRepository.findOneOrFail({
                where: { id: policyVersionId },
                relations: ['versionContent'],
            });

            if (policyVersion.type === PolicyType.BUILDER) {
                await this.uploadPolicyVersionHtmlFile(policy, policyVersion, account);
            }

            // Try to upload the appendix file
            return await this.createAndUploadAppendixForTheCurrentPublishedVersion(
                policy,
                policyVersion,
                account,
                isBambooPolicy,
            );
        } catch (error) {
            const errorMessage = `Error updating policy version file for policy id: ${policyId} - policy version id: ${policyVersionId}`;
            this.logger.error(
                PolloMessage.msg(errorMessage)
                    .setAccountId(account.id)
                    .setError(error)
                    .setContext(this.constructor.name)
                    .setSubContext('updatePolicyVersionFile'),
            );
            throw new InternalServerErrorException(
                `${errorMessage}: ${error.message}`,
                ErrorCode.POLICY_VERSION_FILE_NOT_UPDATED,
            );
        }
    }

    private async createAndUploadAppendixForTheCurrentPublishedVersion(
        policy: Policy,
        policyVersion: PolicyVersion,
        account: Account,
        isBambooPolicy = false,
    ): Promise<PolicyVersionAppendixResult> {
        try {
            let policyVersionContent: PolicyVersionContent;
            if (isNil(policyVersion?.versionContent)) {
                policyVersionContent = await this.buildNewPolicyVersionContent(
                    account,
                    policy,
                    policyVersion,
                    isBambooPolicy,
                );
            } else {
                const uploadedAppendixFile = await this.policyPDFService.uploadPolicyAppendixPdf(
                    account,
                    policy,
                    policyVersion,
                    isBambooPolicy,
                );
                policyVersionContent = policyVersion.versionContent;
                policyVersionContent.appendixFile = uploadedAppendixFile.key;
            }

            await this.policyVersionContentRepository.save(policyVersionContent);

            return PolicyVersionAppendixResult.UPLOADED;
        } catch (error) {
            const errorType = this.handleIncorrectPasswordError(
                error,
                account,
                policy.id,
                policyVersion.id,
            );
            const errorMessage = `Error updating policy version file for policy id: ${policy.id} - policy version id: ${policyVersion.id}`;
            this.logger.warn(
                PolloMessage.msg(errorMessage)
                    .setAccountId(account.id)
                    .setError(error)
                    .setContext(this.constructor.name)
                    .setSubContext('createAndUploadAppendixForTheCurrentPublishedVersion'),
            );
            return errorType;
        }
    }

    @TenancyTransaction()
    async updatePolicyVersionStatusToNeedsApproval(
        account: Account,
        policyId: number,
        policyVersion: PolicyVersion,
        user: User,
        slaRequestDto?: PolicySLARequestDto,
    ): Promise<void> {
        try {
            this.policyVersionValidationService.validateIsPolicyVersionStatusCorrect(
                [PolicyVersionStatus.DRAFT, PolicyVersionStatus.APPROVED],
                policyVersion.policyVersionStatus,
            );

            const policyVersionUpdate: PolicyVersionToUpdateForDraftType = {
                policyVersionStatus: PolicyVersionStatus.NEEDS_APPROVAL,
                current: false,
            };

            if (policyVersion.policyVersionStatus === PolicyVersionStatus.DRAFT) {
                this.policyVersionValidationService.validatePolicyVersionContent(policyVersion);
                policyVersionUpdate.requiresAcknowledgment = true;
                policyVersionUpdate.version = 1;
            }

            await this.policyVersionRepository.update(
                { id: policyVersion.id },
                policyVersionUpdate,
            );

            if (!isNil(slaRequestDto)) {
                await this.insertPolicyVersionSLAs(policyId, policyVersion.id, slaRequestDto);
            }

            const updatedVersion = await this.policyVersionRepository.findOneByOrFail({
                id: policyVersion.id,
            });

            const policy = await this.policyRepository.getPolicyForVersionCreation(policyId);
            if (isNil(policy)) {
                throw new NotFoundException();
            }

            await this._eventBus.publish(
                new PolicyVersionStatusDraftFinalizedEvent(
                    account,
                    user,
                    policy,
                    updatedVersion,
                    true,
                    true,
                ),
            );
        } catch (error) {
            getErrorLogForInvalidStatus(
                account,
                this.logger,
                error,
                error.message,
                this.constructor.name,
                this.updatePolicyVersionStatusToNeedsApproval.name,
                { policyVersionId: policyVersion.id },
            );
            throw error;
        }
    }

    async updatePolicyVersionStatusOnNeedsApprovalToDraft(
        account: Account,
        policyVersion: PolicyVersion,
    ): Promise<void> {
        try {
            this.policyVersionValidationService.validateIsPolicyVersionStatusCorrect(
                [PolicyVersionStatus.NEEDS_APPROVAL, PolicyVersionStatus.APPROVED],
                policyVersion.policyVersionStatus,
            );

            const policyVersionUpdate = {
                policyVersionStatus: PolicyVersionStatus.DRAFT,
                current: false,
            } as UpdatePolicyVersionType;

            await this.updatePolicyVersion(policyVersion.id, policyVersionUpdate);
        } catch (error) {
            const errorMessage = `Error discarding policy version on needs approval state`;
            getErrorLogForInvalidStatus(
                account,
                this.logger,
                error,
                errorMessage,
                this.constructor.name,
                this.updatePolicyVersionStatusOnNeedsApprovalToDraft.name,
                { policyVersionId: policyVersion.id },
            );
            throw error;
        }
    }
    async getAllPersonnelActive() {
        return this.personnelRepository.getCurrentPersonnelCount();
    }

    async countPolicyPersonnelWithScopeGroups(policy: Policy) {
        return this.policyGroupRepository.getPersonnelFromGroupsByPolicy(policy);
    }

    async publishPolicyVersionPublishedStatus(
        account: Account,
        user: User,
        policyId: number,
        sendNotifications?: boolean,
    ) {
        const policy =
            await this.policyRepository.getPolicyWithCurrentPublishedVersionAndControlIdsByPolicyIdOrFail(
                policyId,
            );

        const currentPublishedVersion = first(policy.versions);
        if (isUndefined(currentPublishedVersion)) {
            throw new NotFoundException();
        }

        this._eventBus.publish(
            new PolicyVersionStatusPublishedEvent(
                account,
                user,
                policy,
                currentPublishedVersion,
                !!sendNotifications,
            ),
        );
        await this.publishWorkflowReadinessEvent(account, user, policy);
    }
    async publishWorkflowReadinessEvent(account: Account, user: User, policy: Policy) {
        try {
            const { id: workspaceId } = await this.productRepository.findOneOrFail({
                where: { company: { accountId: account.id } },
                select: { id: true },
            });

            policy.controls.forEach(({ id }) =>
                this._eventBus.publish(new ControlReadinessEvent(account, id, workspaceId, user)),
            );
        } catch (error) {
            this.logger.warn(
                PolloAdapter.acct(
                    'error while running control readiness workflow',
                    account,
                    'An published version had a error while publishing control readiness workflow',
                ).setError(error),
            );
        }
    }

    /**
     * Upload the file  by the provided key and return the key
     * @param key
     * @param account
     * @returns {Promise<string | null>}
     */
    async uploadFileAndReturnNewKey(key: string | null, account: Account): Promise<string | null> {
        try {
            if (isNil(key)) {
                return null;
            }

            let pdfKey: string | null | undefined;

            const buffer = await this.downloader.getPrivateFileBuffer(key);
            const isPdf = await isFilePdf(buffer);

            if (isPdf) {
                pdfKey = key;
            } else {
                const { signedUrl } = await this.downloader.getDownloadUrl(key);

                if (isNil(signedUrl)) {
                    return null;
                }

                const convertedFile = await this.fileConverter.convertFile(
                    account,
                    signedUrl,
                    FileConvertTypes.PDF,
                    {
                        File: signedUrl,
                    },
                );

                const uploadedFile = await this.uploader.uploadPrivateFileFromUrl(
                    convertedFile.Url,
                    convertedFile.FileName,
                    account.id,
                    UploadType.POLICY,
                );

                pdfKey = uploadedFile.key;
            }

            return pdfKey;
        } catch (error) {
            this.logger.error(PolloAdapter.acct(error.message, account).setError(error));
            return null;
        }
    }

    async updatePolicyVersionAssignVersionAndStatus(
        account: Account,
        user: User,
        policyVersion: PolicyVersion,
        policy: Policy,
        dto: PolicyVersionAssignVersionAndStatusRequestDto,
    ): Promise<PolicyVersion> {
        const currentPublishedVersion = policy.currentPublishedVersion();
        if (isNil(currentPublishedVersion)) {
            throw new NotFoundException();
        }

        const policyVersionUpdate: Partial<PolicyVersion> = {
            policyVersionStatus:
                !dto.isMaterialChange && !dto.requiresApproval
                    ? PolicyVersionStatus.PUBLISHED
                    : PolicyVersionStatus.NEEDS_APPROVAL,
            version: dto.isMaterialChange
                ? (currentPublishedVersion?.version ?? 0) + 1
                : currentPublishedVersion?.version,
            subVersion: dto.isMaterialChange ? 0 : (currentPublishedVersion?.subVersion ?? 0) + 1,
            changesExplanation: dto.explanationOfChanges,
            requiresAcknowledgment: dto.requiresAcknowledgment,
        };

        if (policyVersionUpdate.policyVersionStatus === PolicyVersionStatus.PUBLISHED) {
            await this.updatePolicyVersion(currentPublishedVersion.id, {
                current: false,
            });

            await this.deletePolicyVersionSLAsByPolicyVersionId(currentPublishedVersion.id);

            policyVersionUpdate.current = true;
            policyVersionUpdate.publishedAt = new Date();
            policyVersionUpdate.publishedBy = user;
        }

        await this.updatePolicyVersion(policyVersion.id, policyVersionUpdate);

        if (policyVersionUpdate.policyVersionStatus === PolicyVersionStatus.PUBLISHED) {
            await this.updatePolicyVersionFile(account, policyVersion.id, policy.id);
            await this.publishPolicyVersionPublishedStatus(
                account,
                user,
                policy.id,
                !!dto.shouldNotifyEmployees,
            );
        }

        const updatedVersion = await this.policyVersionRepository.findOneOrFail({
            where: { id: policyVersion.id },
            relations: ['approvedBy'],
        });

        this._eventBus.publish(
            new PolicyVersionStatusDraftFinalizedEvent(
                account,
                user,
                policy,
                updatedVersion,
                dto.requiresApproval,
                !!dto.requiresAcknowledgment,
            ),
        );

        return updatedVersion;
    }
    @TenancyTransaction()
    async renewPolicyVersionAndStatusProcess(
        account: Account,
        user: User,
        policy: Policy,
        dto: UpdatePolicyRenewalRequestDto,
    ): Promise<PolicyVersion> {
        const policyVersionStatus = !!dto.requiresApproval
            ? PolicyVersionStatus.NEEDS_APPROVAL
            : PolicyVersionStatus.APPROVED;

        const currentPublishedVersion = policy.currentPublishedVersion();
        if (isNil(currentPublishedVersion)) {
            throw new NotFoundException();
        }

        const newPolicyVersion = omit(cloneDeep(currentPublishedVersion), ['id']);
        newPolicyVersion.policy = policy;
        newPolicyVersion.createdAt = new Date();
        newPolicyVersion.approvedAt = null;
        newPolicyVersion.updatedAt = new Date();
        newPolicyVersion.policyVersionStatus = policyVersionStatus;
        newPolicyVersion.version = currentPublishedVersion?.version;
        newPolicyVersion.current = false;
        newPolicyVersion.subVersion = currentPublishedVersion.nextVersion(false).subVersion;
        newPolicyVersion.renewalDate = dto.renewalDate;
        newPolicyVersion.requiresAcknowledgment = dto.requiresAcknowledgment;

        const savedPolicyVersion = await this.policyVersionRepository.save(newPolicyVersion);

        await this.clonePolicyVersionSLAs(
            policy.id,
            currentPublishedVersion.id,
            savedPolicyVersion.id,
        );

        if (newPolicyVersion.policyVersionStatus === PolicyVersionStatus.APPROVED) {
            await this.updatePolicyVersionStatusToPublished(
                account,
                policy,
                savedPolicyVersion,
                user,
                dto.shouldNotifyEmployees,
            );
        }

        const updatedVersion = await this.policyVersionRepository.findOneOrFail({
            where: { id: savedPolicyVersion.id },
            relations: ['approvedBy'],
        });

        this._eventBus.publish(
            new PolicyVersionStatusDraftFinalizedEvent(
                account,
                user,
                policy,
                updatedVersion,
                dto.requiresApproval,
                dto.requiresAcknowledgment,
            ),
        );

        this._eventBus.publish(
            new PolicyRenewWithoutUpdatesEvent(
                account,
                user,
                policy,
                updatedVersion,
                dto.renewalDate,
                dto.requiresAcknowledgment,
                dto.requiresApproval,
            ),
        );

        return updatedVersion;
    }

    async saveDraftPolicyVersion(
        user: User,
        policy: Policy,
        account: Account,
        policyVersionDraftType: PolicyVersionDraftType,
        externalFile?: ExternalPolicyFile | null,
    ): Promise<PolicyVersion> {
        try {
            const draftPolicyVersion: Partial<PolicyVersion> = {
                policy: policy,
                policyVersionStatus: PolicyVersionStatus.DRAFT,
                current: false,
                description: policyVersionDraftType.description,
                html: policyVersionDraftType.html,
                htmlLastUpdated: null,
                type: policyVersionDraftType.policyType,
                owner: policyVersionDraftType.user,
                renewalDate: policyVersionDraftType.renewalDate,
                externalPolicyFile: externalFile,
                file: policyVersionDraftType.file,
                pdf: policyVersionDraftType.pdf,
                requiresAcknowledgment: null,
            };

            if (!isNil(policyVersionDraftType.fileUpload)) {
                await checkIsDocument(policyVersionDraftType.fileUpload);
                const sanitizedOriginalName = sanitizeOriginalFileName(
                    policyVersionDraftType.fileUpload.originalname,
                );
                validateOriginalFileNameOrFail(sanitizedOriginalName);
                const uploadedFile = await this.uploader.uploadPrivateFile(
                    policyVersionDraftType.fileUpload,
                    UploadType.POLICY,
                    account.id,
                );
                draftPolicyVersion.file = uploadedFile.key;
                draftPolicyVersion.pdf = await this.uploadFileAndReturnNewKey(
                    uploadedFile.key,
                    account,
                );
                draftPolicyVersion.originalFileName = sanitizedOriginalName;
            }

            const result = await this.policyVersionRepository.save(draftPolicyVersion);

            this._eventBus.publish(
                new PolicyVersionStatusDraftCreatedEvent(account, user, policy, result),
            );

            return result;
        } catch (error) {
            this.logger.error(
                PolloMessage.msg(error.message)
                    .setAccountId(account.id)
                    .setError(error)
                    .setContext(this.constructor.name)
                    .setSubContext('saveDraftPolicyVersion'),
            );
            throw error;
        }
    }

    /**
     * This method soft deletes policy version sla's (grace periods, week time frame and p3 matrix) if exists by policy version id
     * @param policyVersionId
     * @returns {Promise<void>}
     */
    async deletePolicyVersionSLAsByPolicyVersionId(policyVersionId: number): Promise<void> {
        const policyVersionSlas = await this.getSlasByPolicyVersion(policyVersionId);
        if (isNil(policyVersionSlas)) return;

        if (!isEmpty(policyVersionSlas.gracePeriodSlas)) {
            await this.gracePeriodSLARepository.softDelete({
                policyVersion: { id: policyVersionId },
            });
        }

        if (!isEmpty(policyVersionSlas.weekTimeFrameSlas)) {
            await this.weekTimeFrameSLARepository.softDelete({
                policyVersion: { id: policyVersionId },
            });
        }

        if (!isEmpty(policyVersionSlas.p3MatrixSlas)) {
            await this.p3MatrixSLARepository.softDelete({
                policyVersion: { id: policyVersionId },
            });
        }
    }

    /**
     * This method copies all the policy version sla's to a another policy version
     * @param policyId
     * @param originPolicyVersionId
     * @param targetPolicyVersionId
     * @returns {Promise<void>}
     */
    async clonePolicyVersionSLAs(
        policyId: number,
        originPolicyVersionId: number,
        targetPolicyVersionId: number,
    ): Promise<void> {
        const policyVersionSlas = await this.getSlasByPolicyVersion(originPolicyVersionId);
        if (isNil(policyVersionSlas)) return;

        await this.insertPolicyVersionGracePeriodSlas(
            policyVersionSlas.gracePeriodSlas,
            policyId,
            targetPolicyVersionId,
        );

        await this.insertPolicyWeekTimeFrameSlas(
            policyVersionSlas.weekTimeFrameSlas,
            policyId,
            targetPolicyVersionId,
        );

        await this.insertPolicyVersionP3MatrixSlas(
            policyVersionSlas.p3MatrixSlas,
            policyId,
            targetPolicyVersionId,
        );
    }

    /**
     * This method gets the sla's related to a policy version
     * @param policyVersionId
     * @returns {Promise<GetSlasByPolicyVersion>}
     */
    async getSlasByPolicyVersion(policyVersionId: number): Promise<GetSlasByPolicyVersion> {
        const [weekTimeFrameSlas, p3MatrixSlas, gracePeriodSlas] = await Promise.all([
            await this.weekTimeFrameSLARepository.find({
                where: { policyVersion: { id: policyVersionId } },
            }),
            await this.p3MatrixSLARepository.find({
                where: { policyVersion: { id: policyVersionId } },
            }),
            await this.gracePeriodSLARepository.find({
                where: { policyVersion: { id: policyVersionId } },
            }),
        ]);

        return {
            weekTimeFrameSlas,
            p3MatrixSlas,
            gracePeriodSlas,
        };
    }

    /**
     * Insert the policy version sla's (week time frame, p3 matrix, grace period)
     * @param policyId
     * @param policyVersionId
     * @param slaRequestDto
     * @param manager
     * @returns {Promise<void>}
     */
    @TenancyTransaction()
    async insertPolicyVersionSLAs(
        policyId: number,
        policyVersionId: number,
        slaRequestDto: PolicySLARequestDto,
    ): Promise<void> {
        const insertSLAPromises: Promise<void>[] = [];

        const policy = await this.policyRepository.getPolicyForVersionCreation(policyId);
        if (isNil(policy)) {
            throw new NotFoundException();
        }

        const policyVersion = await this.policyVersionRepository.findOneOrFail({
            where: { id: policyVersionId },
        });

        if (!isEmpty(slaRequestDto.weekTimeFrameSLARequests)) {
            insertSLAPromises.push(
                this.insertWeekTimeFrameSLARequests(policy, policyVersion, slaRequestDto),
            );
        }
        if (!isEmpty(slaRequestDto.gracePeriodSLARequests)) {
            insertSLAPromises.push(
                this.insertGracePeriodSLAs(policy, policyVersion, slaRequestDto),
            );
        }
        if (!isEmpty(slaRequestDto.p3MatrixPolicySLARequests)) {
            insertSLAPromises.push(this.insertP3MatrixSLAs(policy, policyVersion, slaRequestDto));
        }

        await Promise.all(insertSLAPromises);
    }

    /**
     * This method inserts policy version grace periods sla's from a P3MatrixSLA array
     * @param p3MatrixSlas
     * @param policyId
     * @param targetPolicyVersionId
     */
    private async insertPolicyVersionP3MatrixSlas(
        p3MatrixSlas: P3MatrixSLA[],
        policyId: number,
        targetPolicyVersionId: number,
    ) {
        if (!isEmpty(p3MatrixSlas)) {
            const slaRequestDto = new PolicySLARequestDto();
            slaRequestDto.p3MatrixPolicySLARequests = p3MatrixSlas.map(p3MatrixSLA => ({
                policyP3MatrixSLAId: p3MatrixSLA.policyP3MatrixSLA.id,
                matrixItems: [
                    {
                        definition: p3MatrixSLA.definition,
                        examples: p3MatrixSLA.examples,
                        severity: p3MatrixSLA.severity,
                        timeFrame: p3MatrixSLA.timeFrame,
                    },
                ],
            }));

            await this.insertPolicyVersionSLAs(policyId, targetPolicyVersionId, slaRequestDto);
        }
    }

    /**
     * This method inserts policy version grace periods sla's from a WeekTimeFrameSLA array
     * @param weekTimeFrameSlas This method inserts policy version grace periods sla's from a GracePeriodSLA entity
     * @param policyId
     * @param targetPolicyVersionId
     */
    private async insertPolicyWeekTimeFrameSlas(
        weekTimeFrameSlas: WeekTimeFrameSLA[],
        policyId: number,
        targetPolicyVersionId: number,
    ) {
        if (!isEmpty(weekTimeFrameSlas)) {
            const slaRequestDto = new PolicySLARequestDto();
            slaRequestDto.weekTimeFrameSLARequests =
                weekTimeFrameSlas?.map(sla => {
                    return {
                        timeFrame: sla.timeFrame,
                        policyWeekTimeFrameSLAId: sla.policyWeekTimeFrameSLA.id,
                    } as WeekTimeFrameSLARequestDto;
                }) || [];

            await this.insertPolicyVersionSLAs(policyId, targetPolicyVersionId, slaRequestDto);
        }
    }

    /**
     * This method inserts policy version grace periods sla's from a GracePeriodSLA array
     * @param gracePeriodSlas
     * @param policyId
     * @param policyVersionId
     */
    private async insertPolicyVersionGracePeriodSlas(
        gracePeriodSlas: GracePeriodSLA[],
        policyId: number,
        policyVersionId: number,
    ) {
        if (!isEmpty(gracePeriodSlas)) {
            const slaRequestDto = new PolicySLARequestDto();
            slaRequestDto.gracePeriodSLARequests =
                gracePeriodSlas?.map(sla => {
                    return {
                        gracePeriod: sla.gracePeriod,
                        policyGracePeriodSLAId: sla.policyGracePeriodSLA.id,
                    } as GracePeriodSLARequestDto;
                }) || [];

            await this.insertPolicyVersionSLAs(policyId, policyVersionId, slaRequestDto);
        }
    }

    /**
     * Insert week time frame sla's
     * @param policy
     * @param policyVersion
     * @param slaRequestDto
     * @param manager
     * @returns {Promise<void>}
     */
    private async insertWeekTimeFrameSLARequests(
        policy: GetSummarizedPolicyWithoutDeletedVersions,
        policyVersion: PolicyVersion,
        slaRequestDto: PolicySLARequestDto,
    ): Promise<void> {
        if (isNil(slaRequestDto.weekTimeFrameSLARequests)) {
            return;
        }

        const weekTimeFrameSLAs: WeekTimeFrameSLA[] = [];

        for (const weekTimeFrameSLARequest of slaRequestDto.weekTimeFrameSLARequests) {
            const weekSLA = new WeekTimeFrameSLA();
            const policyWeekTimeFrameSLA = policy.policyWeekTimeFrameSLAs.find(
                ps => ps.id === weekTimeFrameSLARequest.policyWeekTimeFrameSLAId,
            );
            if (!isNil(policyWeekTimeFrameSLA)) {
                weekSLA.policyWeekTimeFrameSLA = policyWeekTimeFrameSLA;
            }
            weekSLA.timeFrame = weekTimeFrameSLARequest.timeFrame;
            weekSLA.policyVersion = policyVersion;
            weekTimeFrameSLAs.push(weekSLA);
        }

        if (!isEmpty(weekTimeFrameSLAs)) {
            await this.weekTimeFrameSLARepository.save(weekTimeFrameSLAs, {
                reload: false,
            });
        }
    }

    /**
     * Insert p3 matrix sla's
     * @param policy
     * @param policyVersion
     * @param slaRequestDto
     * @param manager
     * @returns {Promise<void>}
     */
    private async insertP3MatrixSLAs(
        policy: GetSummarizedPolicyWithoutDeletedVersions,
        policyVersion: PolicyVersion,
        slaRequestDto: PolicySLARequestDto,
    ): Promise<void> {
        if (isNil(slaRequestDto.p3MatrixPolicySLARequests)) {
            return;
        }

        const p3SLAs: P3MatrixSLA[] = [];

        for (const p3MatrixPolicySLARequest of slaRequestDto.p3MatrixPolicySLARequests) {
            const policyP3MatrixSLA = policy.policyP3MatrixSLAs.find(
                p3 => p3.id === p3MatrixPolicySLARequest.policyP3MatrixSLAId,
            );
            for (const matrixItem of p3MatrixPolicySLARequest.matrixItems) {
                const p3MatrixSLA = new P3MatrixSLA();
                p3MatrixSLA.definition = matrixItem.definition;
                p3MatrixSLA.severity = matrixItem.severity;
                p3MatrixSLA.timeFrame = matrixItem.timeFrame;
                p3MatrixSLA.examples = matrixItem.examples;
                if (!isNil(policyP3MatrixSLA)) {
                    p3MatrixSLA.policyP3MatrixSLA = policyP3MatrixSLA;
                }
                p3MatrixSLA.policyVersion = policyVersion;
                p3SLAs.push(p3MatrixSLA);
            }
        }
        if (!isEmpty(p3SLAs)) {
            await this.p3MatrixSLARepository.save(p3SLAs, { reload: false });
        }
    }

    /**
     * Insert grace period sla's
     * @param policy
     * @param policyVersion
     * @param slaRequestDto
     * @param manager
     * @returns {Promise<void>}
     */
    private async insertGracePeriodSLAs(
        policy: GetSummarizedPolicyWithoutDeletedVersions,
        policyVersion: PolicyVersion,
        slaRequestDto: PolicySLARequestDto,
    ): Promise<void> {
        if (isNil(slaRequestDto.gracePeriodSLARequests)) {
            return;
        }

        const gracePeriodSLAs: GracePeriodSLA[] = [];

        for (const gracePeriodSLARequest of slaRequestDto.gracePeriodSLARequests) {
            const policyGracePeriodSLA = policy.policyGracePeriodSLAs.find(
                ps => ps.id === gracePeriodSLARequest.policyGracePeriodSLAId,
            );
            const gracePeriodSLA = new GracePeriodSLA();
            gracePeriodSLA.gracePeriod = gracePeriodSLARequest.gracePeriod;
            if (!isNil(policyGracePeriodSLA)) {
                gracePeriodSLA.policyGracePeriodSLA = policyGracePeriodSLA;
            }
            gracePeriodSLA.policyVersion = policyVersion;
            gracePeriodSLAs.push(gracePeriodSLA);
        }
        if (!isEmpty(gracePeriodSLAs)) {
            await this.gracePeriodSLARepository.save(gracePeriodSLAs, {
                reload: false,
            });
        }
    }

    /**
     * Upload the policy version html
     * @param {Account} account
     * @param {string} html
     * @param {Promise<UploaderPayloadType>}
     */
    async uploadPolicyVersionHtml(
        account: Account,
        html: string,
        filename: string,
    ): Promise<UploaderPayloadType> {
        const signedHtmlImageUrls = await this.htmlUrlReplacer.insertTokenToHtmlStringContent(html);

        const htmlTransformed = insertFigureDefaultCSSClass(signedHtmlImageUrls);

        const { data, mimetype } = await this.htmlToPdfConverter.convertToPdfBuffer(
            htmlTransformed,
            account,
        );
        return this.uploader.uploadPrivateFileFromBuffer(
            account.id,
            UploadType.POLICY,
            data,
            filename,
            mimetype,
        );
    }

    /**
     * Add the appendix file and new version to the policy version content
     * @param account
     * @param currentPolicy
     * @param newPolicyVersion
     * @returns {Promise<PolicyVersionContent>}
     */
    async buildNewPolicyVersionContent(
        account: Account,
        currentPolicy: Policy,
        newPolicyVersion: PolicyVersion,
        isBambooPolicy = false,
    ): Promise<PolicyVersionContent> {
        const uploadedAppendixFile = await this.policyPDFService.uploadPolicyAppendixPdf(
            account,
            currentPolicy,
            newPolicyVersion,
            isBambooPolicy,
        );

        const newPolicyVersionContent = new PolicyVersionContent();
        newPolicyVersionContent.appendixFile = uploadedAppendixFile.key;
        newPolicyVersionContent.version = newPolicyVersion;

        return newPolicyVersionContent;
    }

    /**
     * Upload policy version html file
     * @param policy
     * @param policyVersion
     * @param account
     */
    private async uploadPolicyVersionHtmlFile(
        policy: Policy,
        policyVersion: PolicyVersion,
        account: Account,
    ): Promise<void> {
        const templateId = !isNil(policy?.templateId) ? policy?.templateId : 0;
        const formattedHTml = policyVersion?.html
            ? getFormattedHtml(account.domain, policyVersion.html, templateId)
            : '';

        const uploadedFile = await this.uploadPolicyVersionHtml(
            account,
            formattedHTml,
            `${policy.name}.pdf`,
        );

        if (isNil(uploadedFile)) {
            throw new PreconditionFailedException(ErrorCode.FAILED_TO_UPLOAD_POLICY_VERSION_HTML);
        }

        policyVersion.file = uploadedFile.key;

        policyVersion.pdf = await this.uploadFileAndReturnNewKey(uploadedFile.key, account);
        policyVersion.originalFileName = uploadedFile.fileName;
        const policyVersionFileUpdate = {
            file: uploadedFile.key,
            pdf: uploadedFile.key,
        } as UpdatePolicyVersionType;

        await this.updatePolicyVersion(policyVersion.id, policyVersionFileUpdate);
    }

    handleIncorrectPasswordError(
        error: Error,
        account: Account,
        policyId: number,
        policyVersionId: number,
    ): PolicyVersionAppendixResult {
        if (error.message.includes('Password incorrect')) {
            const incorrectPasswordMessage =
                'The password file contains incorrect credentials. Upload failed.';
            this.logger.warn(
                PolloMessage.msg(incorrectPasswordMessage)
                    .setAccountId(account.id)
                    .setContext(this.constructor.name)
                    .setSubContext(this.updatePolicyVersionStatusToPublished.name)
                    .setIdentifier({ policyId, policyVersionId }),
            );
            return PolicyVersionAppendixResult.ERROR_PASSWORD;
        }
        return PolicyVersionAppendixResult.NOT_UPLOADED;
    }

    private get policyVersionRepository(): PolicyVersionRepository {
        return this.getCustomTenantRepository(PolicyVersionRepository);
    }
    private get policyVersionContentRepository(): PolicyVersionContentRepository {
        return this.getCustomTenantRepository(PolicyVersionContentRepository);
    }
    private get policyRepository(): PolicyRepository {
        return this.getCustomTenantRepository(PolicyRepository);
    }
    private get personnelRepository(): PersonnelRepository {
        return this.getCustomTenantRepository(PersonnelRepository);
    }
    private get policyGroupRepository(): PolicyGroupRepository {
        return this.getCustomTenantRepository(PolicyGroupRepository);
    }
    private get gracePeriodSLARepository(): GracePeriodSLARepository {
        return this.getCustomTenantRepository(GracePeriodSLARepository);
    }
    private get weekTimeFrameSLARepository(): WeekTimeFrameSLARepository {
        return this.getCustomTenantRepository(WeekTimeFrameSLARepository);
    }
    private get p3MatrixSLARepository(): P3MatrixSLARepository {
        return this.getCustomTenantRepository(P3MatrixSLARepository);
    }
    private get productRepository(): Repository<Product> {
        return this.getTenantRepository(Product);
    }
}
