import { Injectable } from '@nestjs/common';
import { User } from 'app/users/entities/user.entity';
import { PolicyVersion } from 'app/users/policies/entities/policy-version.entity';
import { Policy } from 'app/users/policies/entities/policy.entity';
import { Account } from 'auth/entities/account.entity';
import { EmailConfig } from 'commons/configs/email.config';
import { currentYear } from 'commons/helpers/date.helper';
import { getLanguage } from 'commons/helpers/language.helper';
import { formatAndEncodeHTMLStrings } from 'commons/helpers/security.helper';
import { fullName } from 'commons/helpers/user.helper';
import { AppService } from 'commons/services/app.service';
import { EmailService } from 'commons/services/email.service';
import { EmailOptionsType, EmailTimeSensitivity } from 'commons/types/email-options.type';
import config from 'config';
import _, { isEmpty } from 'lodash';
import moment from 'moment';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { format } from 'util';

@Injectable()
export class PolicyEmailsCoreService extends AppService {
    constructor(
        private readonly emailConfig: EmailConfig,
        private readonly emailService: EmailService,
    ) {
        super();
    }

    async sendEmailAboutPolicyNeedsApproval(
        account: Account,
        policy: Policy,
        policyVersion: PolicyVersion,
        approver: User,
        timeline: Date,
        owner: User,
        user: User,
    ): Promise<void> {
        const emailLanguage = getLanguage(approver.language, account.language);
        const explanationOfChanges = policyVersion.changesExplanation?.trim() ?? '';
        const hasExplanationOfChanges = !isEmpty(explanationOfChanges);

        const policyVersionNeedsApprovalEmail = hasExplanationOfChanges
            ? await this.emailConfig.policyVersionNeedsApprovalEmailV2(emailLanguage)
            : await this.emailConfig.policyVersionNeedsApprovalEmailWithoutExplanation(
                  emailLanguage,
              );

        const paragraphTwo = hasExplanationOfChanges
            ? formatAndEncodeHTMLStrings(
                  policyVersionNeedsApprovalEmail.paragraphTwo,
                  fullName(owner),
                  explanationOfChanges,
                  moment(timeline).format('ddd - MMMM D, YYYY'),
              )
            : formatAndEncodeHTMLStrings(
                  policyVersionNeedsApprovalEmail.paragraphTwo,
                  fullName(owner),
                  moment(timeline).format('ddd - MMMM D, YYYY'),
              );

        const policyName = policy.name;
        policyVersionNeedsApprovalEmail.subject = format(
            policyVersionNeedsApprovalEmail.subject,
            policyName,
            policyVersion.formattedVersion(),
        );
        const templateVariables = {
            title: formatAndEncodeHTMLStrings(
                policyVersionNeedsApprovalEmail.title,
                policyName,
                policyVersion.formattedVersion(),
            ),
            paragraphOne: formatAndEncodeHTMLStrings(
                policyVersionNeedsApprovalEmail.paragraphOne,
                fullName(approver),
                policyName,
                policyVersion.formattedVersion(),
                fullName(user),
            ),
            paragraphTwo,
            ctaUrl: `${config.get('url.webApp')}/governance/policies/builder/${policy.id}/overview`,
            btnText: policyVersionNeedsApprovalEmail.btnText,
            ...policyVersionNeedsApprovalEmail.templateCommon,
        };

        const emailOptions: EmailOptionsType = {
            ...policyVersionNeedsApprovalEmail,
            toEmail: approver.email,
            templateVariables,
            timeSensitivity: EmailTimeSensitivity.LOW,
        };
        await this.emailService.sendEmail(emailOptions, account);
    }

    /**
     * Sends an email notification to the policy owner when changes are requested for a policy version
     * @param {Account} account - The account context
     * @param {Policy} policy - The policy that needs changes
     * @param {PolicyVersion} policyVersion - The version of the policy that needs changes
     * @param {User} userWhoRequestedChanges - The user who requested the changes
     * @param {string} changesRequestedDetails - The details of the requested changes
     * @param {User} userToNotify - The user who reviewed the policy
     * @returns {Promise<void>}
     */
    async sendPolicyRequestChangesEmail(
        account: Account,
        policy: Policy,
        policyVersion: PolicyVersion,
        userWhoRequestedChanges: User,
        changesRequestedDetails: string,
        userToNotify: User,
    ): Promise<void> {
        const emailLanguage = getLanguage(userToNotify.language, account.language);
        const policyVersionRequestChangesEmail =
            await this.emailConfig.policyVersionRequestChangesEmail(emailLanguage);
        const policyName = policy.name;
        policyVersionRequestChangesEmail.subject = format(
            policyVersionRequestChangesEmail.subject,
            policyName,
            policyVersion.formattedVersion(),
        );
        const templateVariables = {
            title: formatAndEncodeHTMLStrings(
                policyVersionRequestChangesEmail.title,
                policyName,
                policyVersion.formattedVersion(),
            ),
            paragraphOne: formatAndEncodeHTMLStrings(
                policyVersionRequestChangesEmail.paragraphOne,
                fullName(userWhoRequestedChanges),
            ),
            paragraphTwo: formatAndEncodeHTMLStrings(
                policyVersionRequestChangesEmail.paragraphTwo,
                changesRequestedDetails,
            ),
            ctaUrl: `${config.get('url.webApp')}/governance/policies/builder/${policy.id}/overview`,
            btnText: policyVersionRequestChangesEmail.btnText,
            currentYear: currentYear(),
            ...policyVersionRequestChangesEmail.templateCommon,
        };

        const emailOptions: EmailOptionsType = {
            ...policyVersionRequestChangesEmail,
            toEmail: userToNotify.email,
            templateVariables,
            timeSensitivity: EmailTimeSensitivity.LOW,
        };
        await this.emailService.sendEmail(emailOptions, account);
    }

    async sendPolicyApprovalOverrideEmail(
        account: Account,
        owner: User,
        user: User,
        policy: Policy,
        policyVersion: PolicyVersion,
        explanation: string,
        tier?: string,
    ): Promise<void> {
        const hasTier = !_.isNil(tier);
        const emailLanguage = getLanguage(user.language, account.language);
        const policyApprovalOverrideEmail =
            await this.emailConfig.policyApprovalOverrideEmail(emailLanguage);
        const policyName = policy.name;
        const subject = _.compact([
            hasTier ? policyApprovalOverrideEmail.tierSubject : policyApprovalOverrideEmail.subject,
            tier,
            policyName,
            policyVersion.formattedVersion(),
        ]);
        policyApprovalOverrideEmail.subject = format(...subject);
        const templateVariables = {
            title: hasTier
                ? formatAndEncodeHTMLStrings(
                      policyApprovalOverrideEmail.tierTitle,
                      tier,
                      policyName,
                      policyVersion.formattedVersion(),
                  )
                : formatAndEncodeHTMLStrings(
                      policyApprovalOverrideEmail.title,
                      policyName,
                      policyVersion.formattedVersion(),
                  ),
            paragraphOne: formatAndEncodeHTMLStrings(
                policyApprovalOverrideEmail.paragraphOne,
                fullName(owner),
                explanation,
            ),
            ctaUrl: `${config.get('url.webApp')}/governance/policies/builder/${policy.id}/overview`,
            btnText: policyApprovalOverrideEmail.btnText,
            currentYear: currentYear(),
            ...policyApprovalOverrideEmail.templateCommon,
        };

        const emailOptions: EmailOptionsType = {
            ...policyApprovalOverrideEmail,
            toEmail: user.email,
            templateVariables,
            timeSensitivity: EmailTimeSensitivity.LOW,
        };
        await this.emailService.sendEmail(emailOptions, account);
    }

    async sendPolicyApprovedEmail(
        account: Account,
        policy: Policy,
        policyVersion: PolicyVersion,
        approvedAt: Date,
    ): Promise<void> {
        try {
            // Try to get owner from policy first, then from version as fallback
            const policyOwner = policy.currentOwner || policyVersion?.owner || null;

            // If no owner found in either place, log and return
            if (!policyOwner) {
                this.logger.warn(
                    PolloMessage.msg(
                        `Policy ${policy.id} (${policy.name}) has no assigned owner. Cannot send approval email.`,
                    )
                        .setAccountId(account.id)
                        .setContext(this.constructor.name)
                        .setSubContext('sendPolicyApprovedEmail')
                        .setIdentifier({
                            policyId: policy.id,
                            policyName: policy.name,
                        }),
                );
                return;
            }

            const emailLanguage = getLanguage(policyOwner.language, account.language);
            const policyVersionApprovedEmail =
                await this.emailConfig.policyVersionApprovedEmail(emailLanguage);
            const policyName = format(policy.name);
            policyVersionApprovedEmail.subject = format(
                policyVersionApprovedEmail.subject,
                policyName,
                policyVersion.formattedVersion(),
            );
            const templateVariables = {
                title: formatAndEncodeHTMLStrings(
                    policyVersionApprovedEmail.title,
                    policyName,
                    policyVersion.formattedVersion(),
                ),
                paragraphOne: formatAndEncodeHTMLStrings(
                    policyVersionApprovedEmail.paragraphOne,
                    approvedAt?.toDateString(),
                ),
                paragraphTwo: policyVersionApprovedEmail.paragraphTwo,
                ctaUrl: `${config.get('url.webApp')}/governance/policies/builder/${policy.id}/overview`,
                btnText: policyVersionApprovedEmail.btnText,
                currentYear: currentYear(),
                ...policyVersionApprovedEmail.templateCommon,
            };

            const emailOptions: EmailOptionsType = {
                ...policyVersionApprovedEmail,
                toEmail: policyOwner.email,
                templateVariables,
                timeSensitivity: EmailTimeSensitivity.LOW,
            };
            await this.emailService.sendEmail(emailOptions, account);
        } catch (error) {
            // Log error but don't throw - this prevents interrupting policy approval workflow
            this.logger.error(
                PolloMessage.msg(
                    `Failed to send policy approval email for policy ${policy.id} (${policy.name})`,
                )
                    .setAccountId(account.id)
                    .setError(error)
                    .setContext(this.constructor.name)
                    .setSubContext('sendPolicyApprovedEmail')
                    .setIdentifier({
                        policyId: policy.id,
                        policyVersionId: policyVersion.id,
                    }),
            );
        }
    }

    async sendPolicyApprovalCancelEmail(
        account: Account,
        owner: User,
        reviewer: User,
        policy: Policy,
        formattedVersion: string,
    ): Promise<void> {
        const emailLanguage = getLanguage(reviewer.language, account.language);
        const policyVersionCancelReviewAndApprovalEmail =
            await this.emailConfig.policyVersionCancelReviewAndApprovalEmail(emailLanguage);
        const policyName = policy.name;
        policyVersionCancelReviewAndApprovalEmail.subject = format(
            policyVersionCancelReviewAndApprovalEmail.subject,
            policyName,
            formattedVersion,
        );
        const templateVariables = {
            title: formatAndEncodeHTMLStrings(
                policyVersionCancelReviewAndApprovalEmail.title,
                policyName,
                formattedVersion,
            ),
            paragraphOne: formatAndEncodeHTMLStrings(
                policyVersionCancelReviewAndApprovalEmail.paragraphOne,
                fullName(owner),
            ),
            ctaUrl: `${config.get('url.webApp')}/governance/policies/builder/${policy.id}/overview`,
            btnText: policyVersionCancelReviewAndApprovalEmail.btnText,
            currentYear: currentYear(),
            ...policyVersionCancelReviewAndApprovalEmail.templateCommon,
        };

        const emailOptions: EmailOptionsType = {
            ...policyVersionCancelReviewAndApprovalEmail,
            toEmail: reviewer.email,
            templateVariables,
            timeSensitivity: EmailTimeSensitivity.LOW,
        };

        await this.emailService.sendEmail(emailOptions, account);
    }
}
