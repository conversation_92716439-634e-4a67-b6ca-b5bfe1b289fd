import { ErrorCode } from '@drata/enums';
import { Injectable, NotFoundException, StreamableFile } from '@nestjs/common';
import { EventBus } from '@nestjs/cqrs';
import { ApprovalStatusType } from 'app/approvals/v2/approvals-core/types/approval-status.enum';
import { PolicyApprovalsCommerceService } from 'app/policy-approvals/policy-approvals-commerce/services/policy-approvals-commerce.service';
import { User } from 'app/users/entities/user.entity';
import { PolicyVersionApprovalRequestDto } from 'app/users/policies/dtos/policy-version-approval-request.dto';
import { PolicyVersionAssignVersionAndStatusRequestDto } from 'app/users/policies/dtos/policy-version-assign-version-and-status-request.dto';
import { UpdatePolicyRenewalRequestDto } from 'app/users/policies/dtos/update-policy-renewal-date-request.dto';
import { ExternalPolicyFile } from 'app/users/policies/entities/external-policy-file.entity';
import { PolicyVersion } from 'app/users/policies/entities/policy-version.entity';
import { Policy } from 'app/users/policies/entities/policy.entity';
import { PolicyVersionAppendixResult } from 'app/users/policies/enums/policy-version-appendix-result.enum';
import {
    getErrorLogForInvalidStatus,
    getFormattedHtml,
    getHtmlImageCount,
    validateHtmlImagesLimitOnUpdate,
} from 'app/users/policies/helpers/policy.helper';
import { PolicyEditedAfterChangesRequestedEvent } from 'app/users/policies/observables/events/policy-edited-after-changes-requested.event';
import { PolicyTypeSwitchedEvent } from 'app/users/policies/observables/events/policy-type-switched.event';
import { PolicyVersionStatusNoRequiredApprovalEvent } from 'app/users/policies/observables/events/policy-version-status-no-required-approval.event';
import { PolicyVersionRepository } from 'app/users/policies/repositories/policy-version.repository';
import { PolicyRepository } from 'app/users/policies/repositories/policy.repository';
import { HtmlUrlReplacer } from 'app/users/policies/services/html-url-replacer';
import { PolicyEmailsCoreService } from 'app/users/policies/services/policy-emails-core.service';
import { PolicyEmailsService } from 'app/users/policies/services/policy-emails.service';
import { PolicyVersionOperationService } from 'app/users/policies/services/policy-version-operation.service';
import { PolicyVersionValidationService } from 'app/users/policies/services/policy-version-validation.service';
import { GetSummarizedPolicyWithoutDeletedVersions } from 'app/users/policies/types/get-policy-summarized.type';
import { PolicyAppendixResponse } from 'app/users/policies/types/policy-appendix-response.type';
import { PolicyHtml } from 'app/users/policies/types/policy-html.type';
import { PolicyVersionDraftType } from 'app/users/policies/types/policy-version-draft.type';
import { PolicyVersionHtml } from 'app/users/policies/types/policy-version-html.type';
import { UserRepository } from 'app/users/repositories/user.repository';
import { UsersCoreService } from 'app/users/services/users-core.service';
import { UsersService } from 'app/users/services/users.service';
import { Account } from 'auth/entities/account.entity';
import { CacheBusterWithPrefix } from 'cache/cache.decorator';
import { PolloAdapter } from 'commons/adapters/pollo.adapter';
import { TenancyTransaction } from 'commons/decorators/transaction';
import { Caches } from 'commons/enums/cache.enum';
import { UploadType } from 'commons/enums/upload-type.enum';
import { PolicyScope } from 'commons/enums/users/policies/policy-scope.enum';
import { PolicyType } from 'commons/enums/users/policies/policy-type.enum';
import { PolicyVersionStatus } from 'commons/enums/users/policies/policy-version-status.enum';
import { Role } from 'commons/enums/users/role.enum';
import { BadRequestException } from 'commons/exceptions/bad-request.exception';
import { InternalServerErrorException } from 'commons/exceptions/internal-server-error.exception';
import { PreconditionFailedException } from 'commons/exceptions/precondition-failed.exception';
import { validateOriginalFileNameOrFail } from 'commons/helpers/file.helper';
import { renewalDate as todaysDatePlusAMonth } from 'commons/helpers/policy.helper';
import { checkIsDocument, sanitizeOriginalFileName } from 'commons/helpers/upload.helper';
import { AppService } from 'commons/services/app.service';
import { UploadedFileType } from 'commons/types/uploaded-file.type';
import { HtmlToPdfConverter } from 'dependencies/html-to-pdf-converter/html-to-pdf-converter';
import { Uploader } from 'dependencies/uploader/uploader';
import { FeatureFlagCategory } from 'feature-flags/enums/feature-flag-category.enum';
import { FeatureFlag } from 'feature-flags/feature-flag.enum';
import { FeatureFlagService } from 'feature-flags/feature-flag.service';
import { cloneDeep, first, isEmpty, isNil, isUndefined, omit } from 'lodash';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { PolloLogger } from 'pollo-logger/pollo.logger';

@Injectable()
export class PolicyVersionService extends AppService {
    logger = PolloLogger.logger(this.constructor.name);

    constructor(
        private readonly eventBus: EventBus,
        private readonly featureFlagService: FeatureFlagService,
        private readonly htmlToPdfConverter: HtmlToPdfConverter,
        private readonly htmlUrlReplacer: HtmlUrlReplacer,
        private readonly policyApprovalsCommerceService: PolicyApprovalsCommerceService,
        private readonly policyEmailsCoreService: PolicyEmailsCoreService,
        private readonly policyEmailsService: PolicyEmailsService,
        private readonly policyVersionOperationService: PolicyVersionOperationService,
        private readonly policyVersionValidationService: PolicyVersionValidationService,
        private readonly uploader: Uploader,
        private readonly usersCoreService: UsersCoreService,
        private readonly usersService: UsersService,
    ) {
        super();
    }

    async createDraftPolicyVersion(
        user: User,
        policyId: number,
        account: Account,
        policyVersionDraftType: PolicyVersionDraftType,
        ownerId: number | null,
    ): Promise<PolicyVersion> {
        try {
            let policy: Policy;
            let externalFile: ExternalPolicyFile | null | undefined;
            let currentPublishedPolicyVersion: PolicyVersion | undefined;
            let policyOwner: User | null | undefined;
            const hasValidOwnerId = !isNil(ownerId);

            if (hasValidOwnerId && typeof ownerId === 'number') {
                policyOwner = await this.usersService.getUserWithRoles(ownerId, [
                    Role.ADMIN,
                    Role.TECHGOV,
                    Role.WORKSPACE_ADMINISTRATOR,
                    Role.POLICY_MANAGER,
                    Role.CONTROL_MANAGER,
                ]);
            }

            const lastPolicyVersion =
                await this.policyVersionRepository.getLatestPolicyVersionByPolicyId(policyId);

            if (lastPolicyVersion?.policyVersionStatus === PolicyVersionStatus.DRAFT) {
                throw new BadRequestException(
                    'Cannot create a new draft while another draft is active',
                );
            }

            switch (policyVersionDraftType.policyVersionStatus) {
                case PolicyVersionStatus.PUBLISHED:
                    policy =
                        await this.policyRepository.getPolicyWithCurrentPublishedVersionOwnerAndExternalFileByPolicyIdOrFail(
                            policyId,
                        );

                    currentPublishedPolicyVersion = first(policy.versions);
                    if (isNil(currentPublishedPolicyVersion)) {
                        throw new NotFoundException();
                    }

                    policyVersionDraftType.description = currentPublishedPolicyVersion?.description;
                    policyVersionDraftType.user = hasValidOwnerId
                        ? policyOwner
                        : currentPublishedPolicyVersion?.owner;
                    policyVersionDraftType.html = currentPublishedPolicyVersion?.html;
                    policyVersionDraftType.file = currentPublishedPolicyVersion?.file;
                    policyVersionDraftType.pdf = currentPublishedPolicyVersion?.pdf;
                    policyVersionDraftType.renewalDate = todaysDatePlusAMonth;
                    externalFile =
                        policyVersionDraftType.policyType === PolicyType.EXTERNAL
                            ? currentPublishedPolicyVersion.externalPolicyFile
                            : null;

                    break;

                case PolicyVersionStatus.DRAFT:
                    policy = await this.policyRepository.findOneOrFail({
                        where: { id: policyId },
                    });

                    policyVersionDraftType.description = policy?.currentDescription;
                    policyVersionDraftType.user = hasValidOwnerId
                        ? policyOwner
                        : policy?.currentOwner;
                    if (policyVersionDraftType.policyType === PolicyType.BUILDER) {
                        policyVersionDraftType.html = policy?.html;
                    }
                    policyVersionDraftType.renewalDate = todaysDatePlusAMonth;
                    break;

                default:
                    throw new BadRequestException('Policy version status is not correct');
            }

            const draftPolicyVersion =
                await this.policyVersionOperationService.saveDraftPolicyVersion(
                    user,
                    policy,
                    account,
                    policyVersionDraftType,
                    externalFile,
                );

            if (
                policyVersionDraftType.policyVersionStatus === PolicyVersionStatus.PUBLISHED &&
                !isNil(currentPublishedPolicyVersion)
            ) {
                await this.policyVersionOperationService.clonePolicyVersionSLAs(
                    policyId,
                    currentPublishedPolicyVersion.id,
                    draftPolicyVersion.id,
                );
            }

            return draftPolicyVersion;
        } catch (error) {
            this.logger.error(
                PolloMessage.msg(error.message)
                    .setAccountId(account.id)
                    .setError(error)
                    .setContext(this.constructor.name)
                    .setSubContext('createDraftPolicyVersion'),
            );
            throw error;
        }
    }

    @CacheBusterWithPrefix<PolicyAppendixResponse>({
        stores: [Caches.FRAMEWORK, Caches.LIST_CONTROLS, Caches.REQUIREMENTS],
    })
    async updatePolicyVersionStatus(
        account: Account,
        policyVersionApprovalRequestDto: PolicyVersionApprovalRequestDto,
        policyVersionId: number,
        policyId: number,
        user: User,
    ): Promise<PolicyAppendixResponse> {
        try {
            const [policy, policyVersion] = await Promise.all([
                this.policyRepository.findOneOrFail({
                    where: { id: policyId },
                    loadEagerRelations: false,
                }),
                this.policyVersionRepository.findOneOrFail({
                    where: { id: policyVersionId },
                    relations: ['approvedBy', 'owner'],
                }),
                this.policyVersionValidationService.validateIsVersionFromPolicy(
                    policyId,
                    policyVersionId,
                ),
                this.policyVersionValidationService.validatePolicyVersionIsLatest(
                    policyId,
                    policyVersionId,
                ),
            ]);

            this.policyVersionValidationService.validateIsPolicyActiveOrOutdated(policy);
            this.policyVersionValidationService.validatePolicyVersionHasOwner(policyVersion);

            let appendixFileUploadedResult = PolicyVersionAppendixResult.NOT_UPLOADED;
            switch (policyVersionApprovalRequestDto.policyVersionStatus) {
                case PolicyVersionStatus.APPROVED:
                    this.policyVersionValidationService.validateIsUserOwner(
                        user,
                        policyVersion.owner?.id,
                    );
                    await this.policyVersionOperationService.updatePolicyVersionStatusToApproved(
                        account,
                        policy,
                        policyVersion,
                        user,
                    );
                    break;
                case PolicyVersionStatus.DRAFT:
                    const { policyVersionStatus } = policyVersion;
                    const approvals =
                        await this.policyApprovalsCommerceService.getApprovalsForPolicy(
                            policy.id,
                            policyVersion.id,
                        );

                    const approval = first(approvals);
                    if (isNil(approval)) {
                        this.logger.warn(
                            PolloMessage.msg('No approvals found for policy version')
                                .setAccountId(account.id)
                                .setContext(this.constructor.name)
                                .setSubContext(this.updatePolicyVersionStatus.name)
                                .setMetadata({
                                    policyId: policy.id,
                                    policyVersionId: policyVersion.id,
                                }),
                        );
                    } else if (approval?.status === ApprovalStatusType.CHANGES_REQUESTED) {
                        // Handle CHANGES_REQUESTED status separately as it's not part of the cancel workflow
                        this.eventBus.publish(
                            new PolicyEditedAfterChangesRequestedEvent(
                                account,
                                user,
                                policy,
                                policyVersion,
                            ),
                        );

                        // Update the policy version status to DRAFT in the database because the approval is already completed.
                        await this.policyVersionOperationService.updatePolicyVersionStatusOnNeedsApprovalToDraft(
                            account,
                            policyVersion,
                        );
                    } else if (policyVersionStatus === PolicyVersionStatus.NEEDS_APPROVAL) {
                        await this.policyApprovalsCommerceService.cancelPolicyApprovalWorkflow(
                            account,
                            user,
                            policy.id,
                            policyVersion.id,
                            { notify: true },
                        );
                    }
                    break;
                case PolicyVersionStatus.NEEDS_APPROVAL:
                    await this.policyApprovalsCommerceService.startPolicyApprovalWorkflowFromLatestConfiguration(
                        policy.id,
                        policyVersion.id,
                        user,
                        account,
                    );
                    break;
                case PolicyVersionStatus.PUBLISHED:
                    this.policyVersionValidationService.validateIsUserOwner(
                        user,
                        policyVersion.owner?.id,
                    );
                    appendixFileUploadedResult =
                        await this.policyVersionOperationService.updatePolicyVersionStatusToPublished(
                            account,
                            policy,
                            policyVersion,
                            user,
                            policyVersionApprovalRequestDto.shouldNotifyEmployees,
                            policyVersionApprovalRequestDto.requiresAcknowledgment,
                        );
                    break;
                default:
                    throw new BadRequestException(
                        `The policy version status is invalid. Received status: ${
                            policyVersionApprovalRequestDto.policyVersionStatus
                        } policy Id: ${policyId} policy version Id: ${policyVersionId}
                        .Full details: ${JSON.stringify(policyVersionApprovalRequestDto)}`,
                    );
            }

            const updatedPolicyVersion = await this.getPolicyVersion(
                account,
                policyId,
                policyVersionId,
            );

            return {
                ...updatedPolicyVersion,
                appendixUploaded: appendixFileUploadedResult,
            } as PolicyAppendixResponse;
        } catch (error) {
            if (error instanceof PreconditionFailedException) {
                this.logger.warn(
                    PolloMessage.msg(error.message)
                        .setAccountId(account.id)
                        .setError(error)
                        .setContext(this.constructor.name)
                        .setSubContext('updatePolicyVersionStatus'),
                );
            } else {
                this.logger.error(
                    PolloMessage.msg(error.message)
                        .setAccountId(account.id)
                        .setError(error)
                        .setContext(this.constructor.name)
                        .setSubContext('updatePolicyVersionStatus'),
                );
            }
            throw error;
        }
    }

    async updatePolicyVersionExplanationOfChangesByPolicyId(
        account: Account,
        policyVersionId: number,
        policyId: number,
        user: User,
        explanationOfChanges: string,
    ): Promise<PolicyVersion> {
        try {
            const policy = await this.policyRepository.findOneOrFail({
                where: { id: policyId },
            });
            this.policyVersionValidationService.validateIsPolicyActiveOrOutdated(policy);

            const policyVersion = await this.policyVersionRepository.findOneOrFail({
                where: { id: policyVersionId },
                relations: ['approvedBy', 'owner'],
            });

            if (policyVersion.policyVersionStatus === PolicyVersionStatus.APPROVED) {
                this.policyVersionValidationService.validateIsUserOwner(
                    user,
                    policyVersion.owner?.id,
                );
            }

            return await this.policyVersionOperationService.cancelApprovalById(
                account,
                policyVersionId,
                policy,
                user,
                explanationOfChanges,
            );
        } catch (error) {
            this.logger.error(
                PolloMessage.msg(error.message)
                    .setAccountId(account.id)
                    .setError(error)
                    .setContext(this.constructor.name)
                    .setSubContext('updatePolicyVersionExplanationOfChangesByPolicyId'),
            );
            throw error;
        }
    }

    async updatePolicyVersionAssignVersionAndStatus(
        account: Account,
        user: User,
        policyVersionId: number,
        policyId: number,
        dto: PolicyVersionAssignVersionAndStatusRequestDto,
    ): Promise<PolicyVersion> {
        try {
            await this.policyVersionValidationService.validateIsVersionFromPolicy(
                policyId,
                policyVersionId,
            );

            await this.policyVersionValidationService.validatePolicyVersionIsLatest(
                policyId,
                policyVersionId,
            );
            const policy =
                await this.policyRepository.getPolicyWithCurrentPublishedVersionOwnerAndExternalFileByPolicyIdOrFail(
                    policyId,
                );

            this.policyVersionValidationService.validateIsPolicyActiveOrOutdated(policy);

            const policyVersion = await this.policyVersionRepository.findOneOrFail({
                where: { id: policyVersionId },
                relations: ['approvedBy'],
            });

            this.policyVersionValidationService.validatePolicyVersionContent(policyVersion);

            const updateResponse =
                await this.policyVersionOperationService.updatePolicyVersionAssignVersionAndStatus(
                    account,
                    user,
                    policyVersion,
                    policy,
                    dto,
                );

            const isFeatureFlagEnabled = await this.isPolicyMultiApproversEnabled(user, account);

            if (!isFeatureFlagEnabled) {
                await this.policyEmailsService.validateAndSendEmailAboutPolicyNeedsApproval(
                    account,
                    user,
                    policy,
                    policyVersion,
                    dto.explanationOfChanges,
                );
            }

            return updateResponse;
        } catch (error) {
            this.logger.error(
                PolloMessage.msg(error.message)
                    .setAccountId(account.id)
                    .setError(error)
                    .setContext(this.constructor.name)
                    .setSubContext('updatePolicyVersionAssignVersionAndStatus'),
            );
            throw error;
        }
    }

    @TenancyTransaction()
    async publishExistingExternalPolicyVersionByPolicyId(
        user: User,
        policyId: number,
        account: Account,
        changesExplanation: string,
        needsPersonnelAcknowledgement?: boolean | null,
        renewalDate?: string,
    ): Promise<PolicyHtml> {
        const policy =
            await this.policyRepository.getPolicyWithCurrentPublishedVersionOwnerAndExternalFileByPolicyIdOrFail(
                policyId,
            );
        const currentPublishedVersion = policy.currentPublishedVersion();

        if (isNil(currentPublishedVersion)) {
            throw new NotFoundException();
        }

        const isNewMajorVersion =
            !isNil(needsPersonnelAcknowledgement) && needsPersonnelAcknowledgement === true;

        const { version: newVersion, subVersion: newSubVersion } =
            currentPublishedVersion.nextVersion(isNewMajorVersion);

        const newCurrentPublishedVersion = omit(cloneDeep(currentPublishedVersion), ['id']);
        newCurrentPublishedVersion.publishedAt = new Date();
        newCurrentPublishedVersion.publishedBy = user;
        newCurrentPublishedVersion.current = true;
        newCurrentPublishedVersion.policyVersionStatus = PolicyVersionStatus.PUBLISHED;
        newCurrentPublishedVersion.policy = policy;
        newCurrentPublishedVersion.updatedAt = new Date();
        newCurrentPublishedVersion.createdAt = new Date();
        newCurrentPublishedVersion.version = newVersion;
        newCurrentPublishedVersion.subVersion = newSubVersion;
        newCurrentPublishedVersion.changesExplanation = changesExplanation;
        newCurrentPublishedVersion.renewalDate = !isNil(renewalDate)
            ? renewalDate
            : todaysDatePlusAMonth;
        newCurrentPublishedVersion.requiresAcknowledgment = !!needsPersonnelAcknowledgement;

        await this.policyVersionRepository.update(currentPublishedVersion.id, {
            current: false,
        });

        const policyVersion = await this.policyVersionRepository.save(newCurrentPublishedVersion);

        await this.policyVersionOperationService.publishPolicyVersionPublishedStatus(
            account,
            user,
            policyId,
            false,
        );
        this.policyVersionValidationService.validateIsPolicyVersionRenewalDate(policyVersion);
        return {
            id: policyId,
            html: newCurrentPublishedVersion.html,
            htmlLastUpdated: newCurrentPublishedVersion.htmlLastUpdated,
        } as PolicyHtml;
    }

    async publishExternalPolicyVersionByPolicyId(
        user: User,
        policyId: number,
        renewalDate: string,
        account: Account,
        isBambooPolicy = false,
    ): Promise<void> {
        try {
            const policy = await this.policyRepository.findOneOrFail({
                where: { id: policyId },
            });
            this.policyVersionValidationService.validateIsPolicyActiveOrOutdated(policy);

            const lastPolicyVersion =
                await this.policyVersionRepository.getLatestPolicyVersionByPolicyId(policyId);

            if (isNil(lastPolicyVersion)) {
                throw new NotFoundException();
            }

            this.policyVersionValidationService.validateIsPolicyVersionStatusCorrect(
                [PolicyVersionStatus.DRAFT],
                lastPolicyVersion.policyVersionStatus,
            );

            lastPolicyVersion.publishedAt = new Date();
            lastPolicyVersion.renewalDate = renewalDate;
            lastPolicyVersion.publishedBy = user;
            lastPolicyVersion.policyVersionStatus = PolicyVersionStatus.PUBLISHED;
            lastPolicyVersion.version = 1;
            lastPolicyVersion.current = true;
            lastPolicyVersion.requiresAcknowledgment = !!isBambooPolicy;

            const policyVersion = await this.policyVersionRepository.save(lastPolicyVersion);

            await this.policyVersionOperationService.updatePolicyVersionFile(
                account,
                policyVersion.id,
                policy.id,
                isBambooPolicy,
            );

            await this.policyVersionOperationService.publishPolicyVersionPublishedStatus(
                account,
                user,
                policyId,
            );
            this.policyVersionValidationService.validateIsPolicyVersionRenewalDate(policyVersion);
        } catch (error) {
            const errorMessage = `Error publishing external policy version: ${error.message}`;
            getErrorLogForInvalidStatus(
                account,
                this.logger,
                error,
                errorMessage,
                this.constructor.name,
                this.publishExternalPolicyVersionByPolicyId.name,
                { policyId: policyId },
            );
            throw new InternalServerErrorException(
                errorMessage,
                ErrorCode.POLICY_VERSION_VERSION_NOT_UPDATED,
            );
        }
    }

    async getPolicyVersion(
        account: Account,
        policyId: number,
        policyVersionId: number,
    ): Promise<PolicyVersion> {
        try {
            const policyVersion = await this.policyVersionRepository.findOneOrFail({
                where: { id: policyVersionId },
                relations: ['approvedBy', 'externalPolicyFile.connection'],
            });

            await this.policyVersionValidationService.validateIsVersionFromPolicy(
                policyId,
                policyVersionId,
            );

            const policyVersionSLAs =
                await this.policyVersionRepository.getPolicyVersionWithSLAs(policyVersionId);
            if (!isNil(policyVersionSLAs)) {
                policyVersion.gracePeriodSLAs = policyVersionSLAs.gracePeriodSLAs;
                policyVersion.p3MatrixSLAs = policyVersionSLAs.p3MatrixSLAs;
                policyVersion.weekTimeFrameSLAs = policyVersionSLAs.weekTimeFrameSLAs;
            }

            policyVersion.html = await this.signPolicyVersionHtmlImagesUrls(policyVersion.html);

            return policyVersion;
        } catch (error) {
            const errorMessage = 'Error getting policy version by id';
            this.logger.error(
                PolloMessage.msg(errorMessage)
                    .setAccountId(account.id)
                    .setError(error)
                    .setContext(this.constructor.name)
                    .setSubContext('getPolicyVersion'),
            );
            throw error;
        }
    }

    async getPersonnelCountForPolicyScope(account: Account, policyId: number): Promise<number> {
        try {
            const policy = await this.policyRepository.findOneOrFail({
                where: { id: policyId },
            });
            let personnelCount: number = 0;

            if (policy.scope === PolicyScope.GROUP) {
                personnelCount = (
                    await this.policyVersionOperationService.countPolicyPersonnelWithScopeGroups(
                        policy,
                    )
                ).length;
            } else if (policy.scope === PolicyScope.ALL) {
                personnelCount = await this.policyVersionOperationService.getAllPersonnelActive();
            }

            return personnelCount;
        } catch (error) {
            const errorMessage = 'Error getting users for policy scope';
            this.logger.error(
                PolloMessage.msg(errorMessage)
                    .setAccountId(account.id)
                    .setError(error)
                    .setContext(this.constructor.name)
                    .setSubContext('getPolicyVersion'),
            );
            throw error;
        }
    }

    async signPolicyVersionHtmlImagesUrls(html: string | null | undefined): Promise<string | null> {
        if (isNil(html) || isEmpty(html)) {
            return null;
        }
        return this.htmlUrlReplacer.insertTokenToHtmlStringContent(html);
    }

    async updatePolicyVersionHtmlByPolicyId(
        id: number,
        account: Account,
        user: User,
        html: string | undefined,
    ): Promise<PolicyVersionHtml> {
        try {
            validateHtmlImagesLimitOnUpdate(html);

            let lastUpdatedPolicyVersion =
                await this.policyVersionRepository.getLatestPolicyVersionByPolicyId(id);

            if (!isNil(lastUpdatedPolicyVersion)) {
                if (lastUpdatedPolicyVersion.policyVersionStatus === PolicyVersionStatus.APPROVED) {
                    this.policyVersionValidationService.validateIsUserOwner(
                        user,
                        lastUpdatedPolicyVersion.owner?.id,
                    );
                }

                await this.policyVersionRepository.update(lastUpdatedPolicyVersion.id, {
                    html,
                    htmlLastUpdated: new Date(),
                });

                lastUpdatedPolicyVersion = await this.policyVersionRepository.findOneOrFail({
                    where: { id: lastUpdatedPolicyVersion.id },
                });

                const policy = await this.policyRepository.findOneOrFail({
                    where: { id: id },
                });

                await this.policyVersionValidationService.policyFileSizeValidator(
                    lastUpdatedPolicyVersion?.html ?? '',
                    account,
                );

                const isFeatureFlagEnable = await this.isPolicyMultiApproversEnabled(user, account);

                if (
                    lastUpdatedPolicyVersion.policyVersionStatus === PolicyVersionStatus.APPROVED &&
                    isFeatureFlagEnable
                ) {
                    await this._eventBus.publish(
                        new PolicyVersionStatusNoRequiredApprovalEvent(
                            account,
                            user,
                            policy as unknown as GetSummarizedPolicyWithoutDeletedVersions,
                            lastUpdatedPolicyVersion,
                        ),
                    );
                }
            }
            const htmlImageCount = getHtmlImageCount(html);

            return {
                html: lastUpdatedPolicyVersion?.html ?? '',
                htmlLastUpdated: lastUpdatedPolicyVersion?.htmlLastUpdated ?? null,
                imageCount: htmlImageCount,
            };
        } catch (error) {
            const subContext = 'updatePolicyVersionHtmlByPolicyId';
            const polloMessage = PolloMessage.msg(error.message)
                .setAccountId(account.id)
                .setContext(this.constructor.name)
                .setSubContext(subContext);

            const warningErrorCodes = new Set([
                ErrorCode.USER_IS_NOT_POLICY_OWNER,
                ErrorCode.POLICY_IMAGE_LIMIT_EXCEEDED,
                ErrorCode.POLICY_FILE_SIZE_EXCEEDED,
                ErrorCode.FAILED_TO_GET_FILE_SIZE,
            ]);

            if (warningErrorCodes.has(error.code)) {
                this.logger.warn(polloMessage.setError(error));
                throw new BadRequestException(error.message);
            } else {
                this.logger.error(polloMessage.setError(error));
                throw new InternalServerErrorException(
                    'Error updating policy version HTML',
                    ErrorCode.POLICY_VERSION_VERSION_NOT_UPDATED,
                );
            }
        }
    }

    async updatePolicyVersionToAuthored(
        policyId: number,
        user: User,
        account: Account,
    ): Promise<PolicyVersion> {
        try {
            const policy = await this.policyRepository.findOneOrFail({ where: { id: policyId } });

            const lastUpdatedPolicyVersion =
                await this.policyVersionRepository.getLatestPolicyVersionByPolicyId(policyId);

            if (!lastUpdatedPolicyVersion) {
                throw new NotFoundException('Policy Version Not found');
            }

            await this.policyVersionRepository.update(lastUpdatedPolicyVersion.id, {
                type: PolicyType.BUILDER,
                htmlLastUpdated: new Date(),
                externalPolicyFile: null,
            });

            this._eventBus.publish(
                new PolicyTypeSwitchedEvent(
                    account,
                    user,
                    policy as unknown as GetSummarizedPolicyWithoutDeletedVersions,
                    'Authored',
                ),
            );

            return lastUpdatedPolicyVersion;
        } catch (error) {
            const errorMessage = 'Error updating policy version to Authored';
            this.logger.error(
                PolloMessage.msg(error.message)
                    .setAccountId(account.id)
                    .setError(error)
                    .setContext(this.constructor.name)
                    .setSubContext('updatePolicyVersionToAuthored'),
            );
            throw new InternalServerErrorException(
                errorMessage,
                ErrorCode.POLICY_VERSION_VERSION_NOT_UPDATED,
            );
        }
    }

    async getPolicyVersionTemplateHtmlByPolicyId(
        account: Account,
        policyId: number,
    ): Promise<PolicyVersionHtml> {
        try {
            const currentPolicyVersion =
                await this.policyVersionRepository.getLatestPolicyVersionByPolicyId(policyId);

            if (isNil(currentPolicyVersion)) {
                throw new NotFoundException();
            }

            let policyVersionHtml = currentPolicyVersion?.html ?? '';

            policyVersionHtml = await this.signPolicyVersionHtmlImagesUrls(policyVersionHtml);

            const imageCount = getHtmlImageCount(policyVersionHtml);
            return {
                htmlLastUpdated: currentPolicyVersion.htmlLastUpdated,
                imageCount,
                html: policyVersionHtml,
            };
        } catch (error) {
            const errorMessage = 'Failed to get policy version HTML';
            this.logger.error(
                PolloAdapter.acct('Error getting policy version HTML:', account).setError(error),
            );
            throw new InternalServerErrorException(
                errorMessage,
                ErrorCode.FAILED_TO_GET_POLICY_VERSION_VERSION_HTML,
            );
        }
    }

    async getPolicyVersionPDF(
        account: Account,
        policyId: number,
        policyVersionId: number,
    ): Promise<StreamableFile> {
        const { html, name, templateId } = await this.getPolicyVersionHtmlById(
            account,
            policyId,
            policyVersionId,
        );

        if (isEmpty(html) || isNil(html)) {
            throw new NotFoundException(`HTML for policy version ${policyVersionId} is empty`);
        }

        const formattedHtml = getFormattedHtml(account.domain, html, templateId ?? 0);

        const { data } = await this.htmlToPdfConverter.convertToPdfBuffer(formattedHtml, account);

        return new StreamableFile(data, {
            type: 'application/pdf',
            disposition: `attachment; filename="${name}"`,
            length: data.length,
        });
    }

    async getPolicyVersionHtmlById(
        account: Account,
        policyId: number,
        policyVersionId: number,
    ): Promise<{ html: string | null; name: string; templateId?: number | null }> {
        try {
            const policy = await this.policyRepository.findOneOrFail({
                where: { id: policyId },
            });
            const version = await this.policyVersionRepository.findOneOrFail({
                where: { id: policyVersionId },
                relations: ['approvedBy'],
            });

            if (isUndefined(version.html)) {
                throw new NotFoundException(ErrorCode.FAILED_TO_GET_POLICY_VERSION_VERSION_HTML);
            }

            version.html = await this.signPolicyVersionHtmlImagesUrls(version.html);

            return { html: version.html, name: policy.name, templateId: policy.templateId };
        } catch (error) {
            const errorMessage = 'Failed to get policy version HTML for PDF Download';
            this.logger.error(PolloAdapter.acct(errorMessage, account).setError(error));
            throw error;
        }
    }

    /**
     * Creates a policy version pdf from a given policy version html
     * @param account
     * @param policyId
     * @param html
     * @returns { data: Buffer; name: string }
     */
    async createPolicyVersionPdfFromPolicyVersionHtml(
        account: Account,
        policyId: number,
        html: string,
    ): Promise<{ data: Buffer; name: string }> {
        try {
            if (isNil(html) || isEmpty(html)) {
                throw new BadRequestException('Policy version html is needed.');
            }

            const policy = await this.policyRepository.findOneOrFail({
                where: { id: policyId },
            });

            const { data } = await this.htmlToPdfConverter.convertToPdfBuffer(html, account);

            return { data: data, name: policy.name };
        } catch (error) {
            const errorMessage = 'Failed to create policy version PDF from html';
            this.logger.error(PolloAdapter.acct(errorMessage, account).setError(error));
            throw error;
        }
    }

    async uploadPolicyVersionFile(
        account: Account,
        user: User,
        policyId: number,
        policyVersionId: number,
        file: UploadedFileType,
    ): Promise<PolicyVersion> {
        try {
            const policy = await this.policyRepository.findOneOrFail({
                where: { id: policyId },
            });
            this.policyVersionValidationService.validateIsPolicyActiveOrOutdated(policy);

            await this.policyVersionValidationService.validateIsVersionFromPolicy(
                policyId,
                policyVersionId,
            );

            const policyVersion = await this.policyVersionRepository.findOneOrFail({
                where: { id: policyVersionId },
            });

            await checkIsDocument(file);

            const originalPolicyType = policyVersion.type;

            const sanitizedOriginalName = sanitizeOriginalFileName(file.originalname);
            validateOriginalFileNameOrFail(sanitizedOriginalName);

            const uploadedFile = await this.uploader.uploadPrivateFile(
                file,
                UploadType.POLICY,
                account.id,
            );

            policyVersion.file = uploadedFile.key;
            policyVersion.pdf = await this.policyVersionOperationService.uploadFileAndReturnNewKey(
                uploadedFile.key,
                account,
            );
            policyVersion.originalFileName = sanitizedOriginalName;
            policyVersion.type = PolicyType.UPLOADED;
            policyVersion.externalPolicyFile = null;

            const policyVersionResponse = await this.policyVersionRepository.save(policyVersion);

            if (originalPolicyType !== PolicyType.UPLOADED) {
                this._eventBus.publish(
                    new PolicyTypeSwitchedEvent(
                        account,
                        user,
                        policy as unknown as GetSummarizedPolicyWithoutDeletedVersions,
                        'Uploaded',
                    ),
                );
            }
            return policyVersionResponse;
        } catch (error) {
            this.logger.error(
                PolloMessage.msg(error.message)
                    .setAccountId(account.id)
                    .setError(error)
                    .setContext(this.constructor.name)
                    .setSubContext('uploadPolicyVersionFile'),
            );
            throw error;
        }
    }

    async renewPolicyVersionAndStatusService(
        account: Account,
        user: User,
        policyVersionId: number,
        policyId: number,
        dto: UpdatePolicyRenewalRequestDto,
    ): Promise<PolicyVersion> {
        try {
            const policy =
                await this.policyRepository.getPolicyWithCurrentPublishedVersionOwnerAndExternalFileByPolicyIdOrFail(
                    policyId,
                );

            this.policyVersionValidationService.validateIsPolicyActiveOrOutdated(policy);

            await this.policyVersionValidationService.validateIsVersionFromPolicy(
                policyId,
                policyVersionId,
            );

            await this.policyVersionValidationService.validatePolicyVersionIsLatest(
                policyId,
                policyVersionId,
            );

            const policyVersion = await this.policyVersionRepository.findOneOrFail({
                where: { id: policyVersionId },
                relations: ['approvedBy'],
            });

            this.policyVersionValidationService.validatePolicyVersionContent(policyVersion);

            return await this.policyVersionOperationService.renewPolicyVersionAndStatusProcess(
                account,
                user,
                policy,
                dto,
            );
        } catch (error) {
            this.logger.error(
                PolloMessage.msg(error.message)
                    .setAccountId(account.id)
                    .setError(error)
                    .setContext(this.constructor.name)
                    .setSubContext('updatePolicyRenewalDateVersionAssignVersionAndStatus'),
            );
            throw error;
        }
    }

    /**
     * Sends emails to all reviewers when a policy version review and approval process is canceled
     *
     * This method retrieves the policy and policy version information, then sends a notification email to each
     * reviewer in the provided list. The email informs reviewers that they no longer need to review the policy.
     *
     * @param account - The account associated with the policy
     * @param user - The user who canceled the review (typically the policy owner)
     * @param reviewerIds - Array of user IDs for the reviewers who were assigned to review the policy
     * @param policyId - The ID of the policy whose review was canceled
     * @param policyVersionId - The ID of the specific policy version whose review was canceled
     * @returns Promise
     */
    async sendPolicyVersionCancelReviewAndApprovalEmail(
        account: Account,
        user: User,
        reviewerIds: number[],
        policyId: number,
        policyVersionId: number,
    ) {
        const policy = await this.policyRepository.findOneOrFail({
            where: { id: policyId },
        });

        const policyVersion = await this.policyVersionRepository.findOneOrFail({
            where: { id: policyVersionId },
        });

        const reviewUsers = await this.userRepository.getUsersByIds(reviewerIds);
        await Promise.allSettled(
            reviewUsers
                .filter(reviewer => !isNil(reviewer))
                .map(async reviewer => {
                    await this.policyEmailsService.sendPolicyVersionCancelReviewAndApprovalEmail(
                        account,
                        user,
                        reviewer,
                        policy,
                        policyVersion.formattedVersion(),
                    );
                }),
        );
    }

    /**
     * Validates if a policy version has an owner and if not, assigns it the owner of the policy entity
     * @param policyVersion - The policy version to validate and potentially update
     * @returns Promise<PolicyVersion> - The updated policy version if owner was assigned, otherwise the original policy version
     */
    public async validatePolicyVersionOwner(policyVersion: PolicyVersion): Promise<PolicyVersion> {
        try {
            if (!isNil(policyVersion.owner)) {
                return policyVersion;
            }

            const policy = await this.policyRepository.getPolicyByPolicyVersionId(policyVersion.id);

            if (isNil(policy.currentOwner)) {
                const errorMessage = [
                    'Cannot transition policy version status:',
                    'Both policy and policy version lack assigned owners.',
                    'Please assign an owner before proceeding.',
                    `Policy ID: ${policy.id}, Policy Version ID: ${policyVersion.id}`,
                ].join(' ');
                throw new PreconditionFailedException(ErrorCode.POLICY_OWNER_MISSING, errorMessage);
            }

            policyVersion.owner = policy.currentOwner;
            return await this.policyVersionRepository.save(policyVersion);
        } catch (error) {
            this.logger.error(
                PolloMessage.msg(error.message)
                    .setError(error)
                    .setContext(this.constructor.name)
                    .setSubContext('validatePolicyVersionOwner'),
            );
            throw error;
        }
    }

    private async isPolicyMultiApproversEnabled(user: User, account: Account): Promise<boolean> {
        return this.featureFlagService.evaluate(
            {
                name: FeatureFlag.POLICY_MULTI_APPROVERS,
                defaultValue: false,
                category: FeatureFlagCategory.NONE,
            },
            user,
            account,
        );
    }

    private get policyRepository(): PolicyRepository {
        return this.getCustomTenantRepository(PolicyRepository);
    }
    private get policyVersionRepository(): PolicyVersionRepository {
        return this.getCustomTenantRepository(PolicyVersionRepository);
    }
    private get userRepository(): UserRepository {
        return this.getCustomTenantRepository(UserRepository);
    }
}
