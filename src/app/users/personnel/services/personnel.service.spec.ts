import { ComplianceCheckType, EmploymentStatus } from '@drata/enums';
import { HttpService } from '@nestjs/axios';
import { BadRequestException, ConflictException } from '@nestjs/common';
import { EventBus } from '@nestjs/cqrs';
import { TestingModule } from '@nestjs/testing';
import { Company } from 'app/companies/entities/company.entity';
import { CompaniesCoreService } from 'app/companies/services/companies-core.service';
import { CompaniesService } from 'app/companies/services/companies.service';
import { ComplianceCheckExclusionComplianceCheckType } from 'app/compliance-check-exclusions/entities/compliance-check-exclusion-compliance-check-type.entity';
import { ComplianceCheckExclusion } from 'app/compliance-check-exclusions/entities/compliance-check-exclusion.entity';
import * as ComplianceChecksExclusionHelper from 'app/compliance-check-exclusions/helpers/compliance-check-exclusion.helper';
import { ComplianceCheckExclusionsCoreService } from 'app/compliance-check-exclusions/services/compliance-check-exclusions-core.service';
import { CreateEventCoreService } from 'app/create-event/create-event-core.service';
import { CustomFieldsSubmissionCsvCoreService } from 'app/custom-fields/services/custom-fields-submission-csv-core.service';
import { CustomFieldsSubmissionsCSVService } from 'app/custom-fields/services/custom-fields-submission-csv.service';
import { EdrCoreService } from 'app/edr/services/edr-core.service';
import { MonitorInstance } from 'app/monitors/entities/monitor-instance.entity';
import { SecurityTrainingComplianceChecksCoreService } from 'app/security-training/services/security-training-compliance-checks-core.service';
import { User } from 'app/users/entities/user.entity';
import { PersonnelContractDateRequestDto } from 'app/users/personnel/dtos/personnel-contract-date-request.dto';
import { ComplianceCheck } from 'app/users/personnel/entities/compliance-check.entity';
import { Personnel } from 'app/users/personnel/entities/personnel.entity';
import * as PersonnelHelper from 'app/users/personnel/helpers/personnel.helper';
import { PersonnelTrainingChecks } from 'app/users/personnel/helpers/personnel.helper';
import { ComplianceCheckRepository } from 'app/users/personnel/repositories/compliance-check.repository';
import { PersonnelRepository } from 'app/users/personnel/repositories/personnel.repository';
import { ComplianceChecksCoreService } from 'app/users/personnel/services/compliance-checks-core.service';
import { ComplianceChecksOrchestrationService } from 'app/users/personnel/services/compliance-checks-orchestration.service';
import { DeviceComplianceCheckOrchestrationService } from 'app/users/personnel/services/device-compliance-check-orchestration.service';
import { PersonnelCoreService } from 'app/users/personnel/services/personnel-core.service';
import { PersonnelDetailsPDFService } from 'app/users/personnel/services/personnel-details-pdf.service';
import { PersonnelTicketService } from 'app/users/personnel/services/personnel-tickets.service';
import { PersonnelService } from 'app/users/personnel/services/personnel.service';
import { ComplianceCheckWithExclusion } from 'app/users/personnel/types/compliance-check-with-exclusion.type';
import { ExtendedPersonnel } from 'app/users/personnel/types/extended-personnel.type';
import { PoliciesCoreService } from 'app/users/policies/services/policies-core.service';
import { UserPoliciesService } from 'app/users/policies/services/user-policies.service';
import { UserIdentitiesService } from 'app/users/services/user-identities.service';
import { UsersService } from 'app/users/services/users.service';
import { EntryCoreService } from 'auth/services/entry-core.service';
import { EntryService } from 'auth/services/entry.service';
import { EmailConfig } from 'commons/configs/email.config';
import { NotFoundException } from 'commons/exceptions/not-found.exception';
import { createAppTestingModule } from 'commons/helpers/app-testing-module.helper';
import { MockType } from 'commons/mocks/types/mock.type';
import { EmailService } from 'commons/services/email.service';
import { Downloader } from 'dependencies/downloader/downloader';
import { Uploader } from 'dependencies/uploader/uploader';
import { FeatureFlagService } from 'feature-flags/feature-flag.service';
import moment, { Moment } from 'moment';
import { TenancyContextMock } from 'tenancy/contexts/__mocks__/tenancy.context';
import { TenancyContext } from 'tenancy/contexts/tenancy.context';

describe('PersonnelService', () => {
    describe('updateContractDates', () => {
        let personnelService: PersonnelService;
        let personnel: Personnel;
        let userIdentitiesTypes: Array<string> = [];
        let eventBus: EventBus;
        let complianceCheckExclusionType1: ComplianceCheckExclusionComplianceCheckType;
        let complianceCheckExclusion: ComplianceCheckExclusion;
        let complianceCheckRepository: MockType<ComplianceCheckRepository>;
        let personnelRepository: MockType<PersonnelRepository>;

        beforeEach(async () => {
            const getUserIdentitiesServiceMock = () => ({
                getUserIdentitiesByClientTypes: () => {
                    return userIdentitiesTypes;
                },
            });

            const tenancyContextMock = TenancyContextMock();
            const module: TestingModule = await createAppTestingModule({
                providers: [
                    PersonnelService,
                    {
                        provide: TenancyContext,
                        useValue: tenancyContextMock,
                    },
                    {
                        provide: ComplianceChecksOrchestrationService,
                        useFactory: () => ({
                            getComplianceChecksByPersonnel: jest.fn(() => {}),
                            computeComplianceChecksForPersonnel: jest.fn(() => {}),
                            updateFullCompliance: jest.fn(() => {}),
                        }),
                    },
                    {
                        provide: ComplianceChecksCoreService,
                        useFactory: () => ({}),
                    },
                    {
                        provide: ComplianceCheckExclusionsCoreService,
                        useFactory: () => ({
                            getCurrentExclusionsForPersonnel: jest.fn(() => {
                                return [complianceCheckExclusion];
                            }),
                            computeComplianceChecksForPersonnel: jest.fn(() => {}),
                        }),
                    },
                    {
                        provide: UserIdentitiesService,
                        useValue: getUserIdentitiesServiceMock(),
                    },
                    { provide: EntryService, useValue: {} },
                    { provide: EntryCoreService, useValue: {} },
                    {
                        provide: Uploader,
                        useValue: {},
                    },
                    {
                        provide: Downloader,
                        useValue: {},
                    },
                    {
                        provide: HttpService,
                        useValue: {},
                    },
                    {
                        provide: EmailService,
                        useValue: {},
                    },

                    {
                        provide: EmailConfig,
                        useValue: {},
                    },
                    {
                        provide: UsersService,
                        useFactory: () => ({
                            removeUserFromRequestOwnerList: jest.fn(() => {}),
                        }),
                    },
                    { provide: CreateEventCoreService, useValue: {} },
                    {
                        provide: PersonnelTicketService,
                        useValue: {},
                    },
                    {
                        provide: CompaniesCoreService,
                        useValue: {},
                    },
                    {
                        provide: CompaniesService,
                        useValue: {},
                    },

                    {
                        provide: PoliciesCoreService,
                        useValue: {},
                    },
                    {
                        provide: UserPoliciesService,
                        useValue: {},
                    },
                    {
                        provide: SecurityTrainingComplianceChecksCoreService,
                        useValue: {},
                    },
                    {
                        provide: EdrCoreService,
                        useValue: {},
                    },
                    {
                        provide: PersonnelDetailsPDFService,
                        useValue: {},
                    },
                    {
                        provide: CustomFieldsSubmissionsCSVService,
                        useValue: {},
                    },
                    {
                        provide: FeatureFlagService,
                        useValue: {},
                    },
                    {
                        provide: DeviceComplianceCheckOrchestrationService,
                        useValue: {},
                    },
                    { provide: PersonnelCoreService, useValue: {} },
                    { provide: CustomFieldsSubmissionCsvCoreService, useValue: {} },
                ],
            }).compile();

            complianceCheckRepository =
                tenancyContextMock.getCustomRepository(ComplianceCheckRepository);
            complianceCheckRepository.getComplianceChecksByPersonnel?.mockReturnValue([
                new ComplianceCheck(),
            ]);

            personnelRepository = tenancyContextMock.getCustomRepository(PersonnelRepository);
            personnelRepository.getPersonnelWithAllRoles?.mockImplementation(() => {
                return personnel;
            });
            personnelRepository.getPersonnelWithOnlyActiveRoles?.mockImplementation(() => {
                return personnel;
            });
            personnelRepository.getAllPersonnelWithAllRoles?.mockImplementation(() => {
                return [new Personnel()];
            });

            personnelService = await module.resolve<PersonnelService>(PersonnelService);
            eventBus = module.get<EventBus>(EventBus);

            complianceCheckExclusionType1 = new ComplianceCheckExclusionComplianceCheckType();
            complianceCheckExclusion = new ComplianceCheckExclusion();
            complianceCheckExclusionType1.type = ComplianceCheckType.ACCEPTED_POLICIES;
            complianceCheckExclusion.complianceCheckTypes = [complianceCheckExclusionType1];
        });

        describe('updateContractDates', () => {
            test('it should throw NotFoundException when the personnel is not found', async () => {
                await expect(
                    personnelService.updateContractDates(null, null, 0, null),
                ).rejects.toThrow(NotFoundException);
            });
            describe('validatePersonnelContractDates', () => {
                let personnelDto = null;
                const user = new User();
                const dateFormat = 'YYYY-MM-DD';
                const today: Moment = moment();
                const futureDateFormatted: string = today.add(5, 'days').format(dateFormat);
                const pastDateFormatted: string = today.subtract(5, 'days').format(dateFormat);
                const todayFormatted: string = today.format(dateFormat);

                beforeEach(() => {
                    personnelDto = new PersonnelContractDateRequestDto();
                    personnel = new Personnel();

                    personnelDto.startDate = pastDateFormatted;
                    personnelDto.separationDate = todayFormatted;

                    personnel.user = new User();
                    personnel.user.id = 9;
                });

                afterEach(() => {
                    userIdentitiesTypes = [];
                });

                test('it should throw BadRequestException when the startDate is null', async () => {
                    personnelDto.startDate = null;
                    await expect(
                        personnelService.updateContractDates(null, null, 1, personnelDto),
                    ).rejects.toThrow(BadRequestException);
                });
                test('it should throw BadRequestException when the separateDate is a future date', async () => {
                    personnelDto.startDate = pastDateFormatted;
                    personnelDto.separationDate = futureDateFormatted;

                    await expect(
                        personnelService.updateContractDates(null, null, 1, personnelDto),
                    ).rejects.toThrow(BadRequestException);
                });
                test('it should throw BadRequestException when the startDate is greater than separationDate', async () => {
                    personnelDto.startDate = futureDateFormatted;
                    personnelDto.separationDate = pastDateFormatted;

                    await expect(
                        personnelService.updateContractDates(null, null, 1, personnelDto),
                    ).rejects.toThrow(BadRequestException);
                });
                test('it should throw BadRequestException when the employee is future employee and has separationDate', async () => {
                    personnel.employmentStatus = EmploymentStatus.FUTURE_HIRE;

                    await expect(
                        personnelService.updateContractDates(null, null, 1, personnelDto),
                    ).rejects.toThrow(BadRequestException);
                });
                test('it should throw BadRequest when update startDate to future date when separationDate is included in request', async () => {
                    personnelDto.startDate = futureDateFormatted;
                    personnelDto.separationDate = futureDateFormatted;
                    personnel.employmentStatus = EmploymentStatus.CURRENT_EMPLOYEE;

                    await expect(
                        personnelService.updateContractDates(null, null, 1, personnelDto),
                    ).rejects.toThrow(BadRequestException);
                });
                test('it should throw ConflictExecption when the employmentStatus is unknown', async () => {
                    personnel.employmentStatus = EmploymentStatus.UNKNOWN;

                    await expect(
                        personnelService.updateContractDates(null, null, 1, personnelDto),
                    ).rejects.toThrow(ConflictException);
                });
                test('it should throw ConflictExecption when the employmentStatus is out of scope', async () => {
                    personnel.employmentStatus = EmploymentStatus.OUT_OF_SCOPE;

                    await expect(
                        personnelService.updateContractDates(null, null, 1, personnelDto),
                    ).rejects.toThrow(ConflictException);
                });
                test('it should throw ConflictExecption when update contract dates and person is already on HRIS', async () => {
                    personnel.employmentStatus = EmploymentStatus.CURRENT_EMPLOYEE;
                    userIdentitiesTypes.push('type1');

                    await expect(
                        personnelService.updateContractDates(null, null, 1, personnelDto),
                    ).rejects.toThrow(ConflictException);
                });
                test('it should throw BadRequestException when employee is a former employee but the separationDate is null', async () => {
                    personnelDto.separationDate = null;
                    personnel.employmentStatus = EmploymentStatus.FORMER_EMPLOYEE;

                    await expect(
                        personnelService.updateContractDates(null, null, 1, personnelDto),
                    ).rejects.toThrow(BadRequestException);
                });
                test('it should throw BadRequestException when employee is a former contractor but the separationDate is null', async () => {
                    personnelDto.separationDate = null;
                    personnel.employmentStatus = EmploymentStatus.FORMER_CONTRACTOR;

                    await expect(
                        personnelService.updateContractDates(null, null, 1, personnelDto),
                    ).rejects.toThrow(BadRequestException);
                });
                test('it should throw BadRequestException when employee is a special former employee but the separationDate is null', async () => {
                    personnelDto.separationDate = null;
                    personnel.employmentStatus = EmploymentStatus.SPECIAL_FORMER_EMPLOYEE;

                    await expect(
                        personnelService.updateContractDates(null, null, 1, personnelDto),
                    ).rejects.toThrow(BadRequestException);
                });
                test('it should throw BadRequestException when employee is a special former contractor but the separationDate is null', async () => {
                    personnelDto.separationDate = null;
                    personnel.employmentStatus = EmploymentStatus.SPECIAL_FORMER_CONTRACTOR;

                    await expect(
                        personnelService.updateContractDates(null, null, 1, personnelDto),
                    ).rejects.toThrow(BadRequestException);
                });

                describe('self changes', () => {
                    test('it should throw ConflictException when the authenticated user update separationDate', async () => {
                        user.id = 1;
                        personnel.user.id = 1;

                        await expect(
                            personnelService.updateContractDates(null, user, 1, personnelDto),
                        ).rejects.toThrow(ConflictException);
                    });
                    test('it should throw ConflictException when the authenticated user set a future date to your startDate', async () => {
                        user.id = 1;
                        personnel.user.id = 1;
                        personnelDto.separationDate = null;
                        personnelDto.startDate = futureDateFormatted;

                        await expect(
                            personnelService.updateContractDates(null, user, 1, personnelDto),
                        ).rejects.toThrow(ConflictException);
                    });
                });
                test('it should call event bus when the startDate has been changed', async () => {
                    personnel.startDate = today.add(1, 'day').format(dateFormat);
                    personnelDto.separationDate = null;

                    const publish = jest.spyOn(eventBus, 'publish');

                    await personnelService.updateContractDates(null, user, 5, personnelDto);
                    expect(publish).toBeCalledTimes(8);
                });
            });
        });
        describe('getPersonnelWithTrainingAndCompliance', () => {
            test('Should assign missing properties for the given extendedPersonnel array', () => {
                jest.spyOn(PersonnelHelper, 'getTrainingsComplianceChecks').mockReturnValueOnce({
                    securityTrainingChecks: [0, 3],
                    hipaaTrainingChecks: [4, 5],
                    nistAiTrainingChecks: [1, 2],
                } as unknown as PersonnelTrainingChecks);
                jest.spyOn(
                    ComplianceChecksExclusionHelper,
                    'filterComplianceChecksWithExclusions',
                ).mockReturnValueOnce([9, 8, 7] as unknown as ComplianceCheckWithExclusion[]);

                const extendedPersonnel = [{ prop1: 1 }] as unknown as ExtendedPersonnel[];
                const company = {} as unknown as Company;
                const output = personnelService['getPersonnelWithTrainingAndCompliance'](
                    extendedPersonnel,
                    company,
                    ['compliance tests'] as unknown as MonitorInstance[],
                    [],
                    [],
                );
                expect(output).toEqual([
                    {
                        agentEnabled: null,
                        complianceChecksWithExclusions: [9, 8, 7],
                        complianceTests: ['compliance tests'],
                        hipaaTrainingComplianceChecks: [4, 5],
                        manualUploadEnabled: null,
                        nistAiTrainingComplianceChecks: [1, 2],
                        prop1: 1,
                        securityTrainingComplianceChecks: [0, 3],
                    },
                ]);
            });
        });
    });
});
