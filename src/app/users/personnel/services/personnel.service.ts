import {
    ComplianceCheckStatus,
    ComplianceCheckType,
    CustomFieldsSection,
    EmploymentStatus,
    Error<PERSON>ode,
    EventCategory,
    EventType,
} from '@drata/enums';
import { HttpService } from '@nestjs/axios';
import {
    BadRequestException,
    ConflictException,
    Injectable,
    InternalServerErrorException,
    UnprocessableEntityException,
} from '@nestjs/common';
import { SecurityTrainingData } from 'app/apis/classes/security-training/security-training-data.class';
import { IdentityServiceUser } from 'app/apis/interfaces/identity-service-user.interface';
import { ConnectionEntity } from 'app/companies/connections/entities/connection.entity';
import { Company } from 'app/companies/entities/company.entity';
import { TrainingType } from 'app/companies/entities/training-type.enum';
import { CompaniesCoreService } from 'app/companies/services/companies-core.service';
import { ComplianceCheckExclusion } from 'app/compliance-check-exclusions/entities/compliance-check-exclusion.entity';
import {
    filterComplianceChecksWithExclusions,
    getComplianceChecksWithExclusions,
} from 'app/compliance-check-exclusions/helpers/compliance-check-exclusion.helper';
import { ComplianceCheckExclusionsCoreService } from 'app/compliance-check-exclusions/services/compliance-check-exclusions-core.service';
import { CreateEventCoreService } from 'app/create-event/create-event-core.service';
import { CreateEvent } from 'app/create-event/decorators/create-event.decorator';
import {
    getFormatedValue,
    isCustomFieldsEnabled,
} from 'app/custom-fields/helpers/custom-fields.helper';
import { CustomFieldsLocationsRepository } from 'app/custom-fields/repositories/custom-fields-locations.repository';
import { CustomFieldsSubmissionCsvCoreService } from 'app/custom-fields/services/custom-fields-submission-csv-core.service';
import { CustomFieldValueType } from 'app/custom-fields/types/custom-field-value.type';
import { DeviceDocument } from 'app/devices/entities/device-document.entity';
import { Device } from 'app/devices/entities/device.entity';
import { DevicePersonnelUpdatedEvent } from 'app/devices/observables/events/device-personnel-updated.event';
import { DeviceDocumentRepository } from 'app/devices/repositories/device-document.repository';
import { DeviceRepository } from 'app/devices/repositories/device.repository';
import { EdrCoreService } from 'app/edr/services/edr-core.service';
import { eventDescriptions } from 'app/events/configs/event-descriptions.config';
import { MonitorInstanceExclusion } from 'app/monitors/entities/monitor-instance-exclusion.entity';
import { MonitorInstance } from 'app/monitors/entities/monitor-instance.entity';
import { MonitorInstanceExclusionRepository } from 'app/monitors/repositories/monitor-instance-exclusion.repository';
import { MonitorInstanceRepository } from 'app/monitors/repositories/monitor-instance.repository';
import { TrainingCampaign } from 'app/security-training/entities/training-campaign.entity';
import { TrainingComplianceCheckRepository } from 'app/security-training/repositories/training-compliance-check.repository';
import { SecurityTrainingComplianceChecksCoreService } from 'app/security-training/services/security-training-compliance-checks-core.service';
import { Ticket } from 'app/tickets/entities/ticket.entity';
import { PersonnelWithFailedMfaRequestDto } from 'app/users/dtos/personnel-with-failed-mfa-request.dto';
import { UserDocumentTypeByTrainingCampaignType } from 'app/users/entities/user-document-training-campaign-type.map';
import { DocumentTypePrefix } from 'app/users/entities/user-document-type-prefix.map';
import { UserDocumentTypeToComplianceCheckType } from 'app/users/entities/user-document-type-to-compliance-check-type.map';
import { UserDocument } from 'app/users/entities/user-document.entity';
import { User } from 'app/users/entities/user.entity';
import { GroupPersonnelRepository } from 'app/users/groups/repositories/group-personnel.repository';
import { UserDocumentDownloadedEvent } from 'app/users/observables/events/user-document-downloaded.event';
import { PersonnelBulkActionRequestDto } from 'app/users/personnel/dtos/personnel-bulk-action-request.dto';
import { PersonnelBulkActionStatusRequestDto } from 'app/users/personnel/dtos/personnel-bulk-action-status-request.dto';
import { PersonnelContractDateRequestDto } from 'app/users/personnel/dtos/personnel-contract-date-request.dto';
import { PersonnelForApTaskRequestDto } from 'app/users/personnel/dtos/personnel-for-ap-task.request.dto';
import { PersonnelPolicyAcceptanceReportResponseDto } from 'app/users/personnel/dtos/personnel-policy-acceptance-report-response.dto';
import { PersonnelRequestDto } from 'app/users/personnel/dtos/personnel-request.dto';
import { PersonnelResetStatusRequestDto } from 'app/users/personnel/dtos/personnel-reset-status-request.dto';
import { PersonnelSpecialRequestDto } from 'app/users/personnel/dtos/personnel-special-request.dto';
import { PersonnelStatusRequestDto } from 'app/users/personnel/dtos/personnel-status-request.dto';
import { PersonnelWithDevicesRequestDto } from 'app/users/personnel/dtos/personnel-with-devices-request.dto';
import { PersonnelData } from 'app/users/personnel/entities/personnel-data.entity';
import { Personnel } from 'app/users/personnel/entities/personnel.entity';
import {
    getLatestTrainingCompletionDate,
    getTrainingsComplianceChecks,
    resolveTrainingComplianceKeys,
} from 'app/users/personnel/helpers/personnel.helper';
import { SeparationDateChangedEvent } from 'app/users/personnel/observables/events/asset-personnel-separation-date-changed.event';
import { AssetPersonnelUpdatedEvent } from 'app/users/personnel/observables/events/asset-personnel-updated.event';
import { FormerPersonnelAddedEvent } from 'app/users/personnel/observables/events/former-personnel-added.event';
import { HipaaTrainingResetEvent } from 'app/users/personnel/observables/events/hipaa-training-reset.event';
import { HipaaTrainingUpdatedEvent } from 'app/users/personnel/observables/events/hipaa-training-updated.event';
import { HiredDateChangedEvent } from 'app/users/personnel/observables/events/hired-date-changed.event';
import { NistAiTrainingResetEvent } from 'app/users/personnel/observables/events/nist-ai-training-reset.event';
import { NistAiTrainingUpdatedEvent } from 'app/users/personnel/observables/events/nist-ai-training-updated.event';
import { PersonnelEmploymentStatusUpdatedEvent } from 'app/users/personnel/observables/events/personnel-employment-status-updated.event';
import { PersonnelIncompliantEvent } from 'app/users/personnel/observables/events/personnel-incompliant.event';
import { PersonnelSendReminderEmailEvent } from 'app/users/personnel/observables/events/personnel-send-reminder-email.event';
import { SecurityAwarenessTrainingResetEvent } from 'app/users/personnel/observables/events/security-awareness-training-reset.event';
import { SecurityTrainingUpdatedEvent } from 'app/users/personnel/observables/events/security-training-updated.event';
import { ComplianceCheckRepository } from 'app/users/personnel/repositories/compliance-check.repository';
import { PersonnelTicketRepository } from 'app/users/personnel/repositories/personnel-ticket.repository';
import { PersonnelRepository } from 'app/users/personnel/repositories/personnel.repository';
import { ComplianceChecksCoreService } from 'app/users/personnel/services/compliance-checks-core.service';
import { ComplianceChecksOrchestrationService } from 'app/users/personnel/services/compliance-checks-orchestration.service';
import { DeviceComplianceCheckOrchestrationService } from 'app/users/personnel/services/device-compliance-check-orchestration.service';
import { PersonnelCoreService } from 'app/users/personnel/services/personnel-core.service';
import { PersonnelDetailsPDFService } from 'app/users/personnel/services/personnel-details-pdf.service';
import { PersonnelTicketService } from 'app/users/personnel/services/personnel-tickets.service';
import { NO_PERSONNEL_PROVIDED_FOR_AUDIT_SAMPLE } from 'app/users/personnel/services/personnel.constants';
import { PersonnelValidType } from 'app/users/personnel/services/types/personnel-valid.type';
import { BulkActionErrorType } from 'app/users/personnel/types/bulk-action-error.type';
import { ComplianceCheckWithExclusion } from 'app/users/personnel/types/compliance-check-with-exclusion.type';
import { ExtendedPersonnel } from 'app/users/personnel/types/extended-personnel.type';
import { PersonnelStatusStats } from 'app/users/personnel/types/personnel-status-stats.type';
import { PersonnelTableAllType } from 'app/users/personnel/types/personnel-table-all.type';
import { UserPolicyVersion } from 'app/users/policies/entities/user-policy-version.entity';
import { PoliciesCoreService } from 'app/users/policies/services/policies-core.service';
import { UserPoliciesService } from 'app/users/policies/services/user-policies.service';
import { UserDocumentRepository } from 'app/users/repositories/user-document.repository';
import { UserIdentitiesService } from 'app/users/services/user-identities.service';
import { UsersService } from 'app/users/services/users.service';
import { BaseUserDataType } from 'app/users/types/base-user-data.type';
import { personnelReportsCsvWorkflowV1 } from 'app/worker/workflows';
import { Account } from 'auth/entities/account.entity';
import { Entry } from 'auth/entities/entry.entity';
import {
    getProviderTypeClientTypeMap,
    mapHipaaTrainingToClientType,
    mapNistAiTrainingToClientType,
    mapSecurityTrainingToClientType,
} from 'auth/helpers/provider-type.helper';
import { StackOneSecurityTrainingTypes } from 'auth/helpers/stack-one/stack-one-provider-type.helper';
import { EntryCoreService } from 'auth/services/entry-core.service';
import { CacheBusterWithPrefix } from 'cache/cache.decorator';
import { PolloAdapter } from 'commons/adapters/pollo.adapter';
import { Benchmark } from 'commons/benchmark/benchmark';
import { ExtractedRequestProps } from 'commons/decorators/get-request-notifier-props.decorator';
import { PaginationRequestDto } from 'commons/dtos/pagination-request.dto';
import { AuditorPersonnelStatus } from 'commons/enums/auditors/auditor-personnel-status.enum';
import { ClientType } from 'commons/enums/auth/client-type.enum';
import { ProviderType } from 'commons/enums/auth/provider-type.enum';
import { AutopilotTaskType } from 'commons/enums/autopilot/autopilot-task-type.enum';
import { BackgroundCheckType } from 'commons/enums/background-check/background-check-type.enum';
import { Caches } from 'commons/enums/cache.enum';
import { DeviceDocumentType } from 'commons/enums/device-document-type.enum';
import { HipaaTrainingType } from 'commons/enums/hipaa-training-type.enum';
import { MimeType } from 'commons/enums/mime-type.enum';
import { NistAITrainingType } from 'commons/enums/nistai-training-type.enum';
import { PersonnelTicketType } from 'commons/enums/personnel/personnel-ticket-type.enum';
import { SecurityTrainingType } from 'commons/enums/security-training-type.enum';
import { ControlTestId } from 'commons/enums/test-enum';
import { TrainingCampaignType } from 'commons/enums/training-campaign-type.enum';
import { UploadType } from 'commons/enums/upload-type.enum';
import { UserDocumentType } from 'commons/enums/users/user-document-type.enum';
import { ConflictException as OurConflictException } from 'commons/exceptions/conflict.exception';
import { NotFoundException } from 'commons/exceptions/not-found.exception';
import { isEmptyArray } from 'commons/helpers/array.helper';
import { createZipBuffer } from 'commons/helpers/buffer.helper';
import {
    fileNameDate,
    isDate,
    isFirstDateBeforeSecond,
    isFutureDate,
    isFutureDateString,
    momentWithoutTime,
    normalizeDate,
} from 'commons/helpers/date.helper';
import { getFilenameFromDirectory } from 'commons/helpers/download.helper';
import { shouldEmitEvent } from 'commons/helpers/event.helper';
import { personnelComplianceTests } from 'commons/helpers/monitor.helper';
import {
    allStrictFormerStatuses,
    checkPersonnelValidForUpdate,
    filterActiveDevices,
    getEmploymentStatusFromContractDates,
    getSwitchedStatus,
    isCompliant,
    isInACurrentStatus,
    isInAFormerStatus,
    isInASpecialFormerStatus,
    isInASpecialStatus,
    isInAStrictFormerStatus,
    isOutOfScope,
    isOutOfScopeAdmin,
    isSelfAssignStatus,
    isUnknownStatus,
    needsStatusChange,
    personnelFromFutureHireToCurrentStatus,
    personnelToCurrentStatus,
} from 'commons/helpers/personnel.helper';
import { promiseAllInBatches } from 'commons/helpers/promise.helper';
import { iEquals } from 'commons/helpers/string.helper';
import { getTemporalClient } from 'commons/helpers/temporal/client';
import { unfoldAsync } from 'commons/helpers/unfold.helper';
import { sanitizeFileName } from 'commons/helpers/upload.helper';
import { userHasPoliciesAllocated } from 'commons/helpers/user-policies.helper';
import { fullName } from 'commons/helpers/user.helper';
import { IFeatureFlagChecker } from 'commons/interfaces/feature-flag-checker.interface';
import { AppService } from 'commons/services/app.service';
import { ConditionalCsvDataSetType } from 'commons/types/conditional-csv-data-set.type';
import { CsvDataSetType } from 'commons/types/csv-data-set.type';
import { ExtendedPaginationType } from 'commons/types/extended-pagination.type';
import { FileBufferType } from 'commons/types/file-buffer.type';
import { Nullable } from 'commons/types/nullable.type';
import { PaginationType } from 'commons/types/pagination.type';
import config from 'config';
import stringify from 'csv-stringify/lib/sync';
import { Downloader } from 'dependencies/downloader/downloader';
import { DownloaderPayloadType } from 'dependencies/downloader/types/downloader-payload.interface';
import { Uploader } from 'dependencies/uploader/uploader';
import { FeatureFlagService } from 'feature-flags/feature-flag.service';
import { FeatureFlagType } from 'feature-flags/types/feature-flag.type';
import { chunk, cloneDeep, flatten, isEmpty, isNil } from 'lodash';
import moment from 'moment';
import { In, IsNull, Not, Repository } from 'typeorm';
import { format } from 'util';
@Injectable()
export class PersonnelService extends AppService {
    private readonly OUT_OF_SCOPE_STATES = [
        EmploymentStatus.OUT_OF_SCOPE,
        EmploymentStatus.FUTURE_HIRE,
        EmploymentStatus.SERVICE_ACCOUNT,
    ];

    constructor(
        private readonly complianceChecksOrchestrationService: ComplianceChecksOrchestrationService,
        private readonly complianceChecksCoreService: ComplianceChecksCoreService,
        private readonly complianceCheckExclusionsCoreService: ComplianceCheckExclusionsCoreService,
        private readonly userIdentitiesService: UserIdentitiesService,
        private readonly entryCoreService: EntryCoreService,
        private readonly uploader: Uploader,
        private readonly downloader: Downloader,
        private readonly httpService: HttpService,
        private readonly usersService: UsersService,
        private readonly createEventCoreService: CreateEventCoreService, // needed by the @CreateEvent decorator
        private readonly personnelTicketService: PersonnelTicketService,
        private readonly companiesCoreService: CompaniesCoreService,
        private readonly policiesCoreService: PoliciesCoreService,
        private readonly userPoliciesService: UserPoliciesService,
        private readonly securityTrainingComplianceChecksCoreService: SecurityTrainingComplianceChecksCoreService,
        private readonly edrCoreService: EdrCoreService,
        private readonly personnelDetailsPDFService: PersonnelDetailsPDFService,
        private readonly featureFlagService: FeatureFlagService,
        private readonly customFieldsSubmissionCsvCoreService: CustomFieldsSubmissionCsvCoreService,
        private readonly deviceComplianceCheckOrchestrationService: DeviceComplianceCheckOrchestrationService,
        private readonly personnelCoreService: PersonnelCoreService,
    ) {
        super();
    }

    /**
     * @deprecated Use PersonnelCoreService.getPersonnelById
     *
     * @param id
     * @returns
     */
    async getPersonnelById(id: number): Promise<Personnel> {
        const personnel = await this.personnelRepository.findOneByPersonnelIdOrFail(id);

        await this.setPersonnelGroups(personnel);

        return personnel;
    }

    /**
     * @deprecated Use PersonnelCoreService.getPersonnelByIdForComplianceTests
     **/
    async getPersonnelByIdForComplianceTests(id: number): Promise<Personnel> {
        const personnel =
            await this.personnelRepository.findOneByPersonnelIdForComplianceTestsOrFail(id);

        await this.setPersonnelGroups(personnel);

        return personnel;
    }

    /**
     * @deprecated Use PersonnelCoreService.setPersonnelGroups
     **/
    async setPersonnelGroups(personnel: Personnel): Promise<void> {
        const groups = await this.groupPersonnelRepository.findGroupsByPersonnelId(personnel.id);
        personnel.groups = groups;
    }

    /**
     *
     * @param id
     * @returns {Personnel}
     */
    async getPersonnelForPDFDownload(id: number): Promise<Personnel> {
        const personnel = await this.personnelRepository.getPersonnelByIdForPDFDownload(id);

        const isEdrConnectionActive = await this.edrCoreService.isConnectionActive();

        if (isEdrConnectionActive) {
            await this.addEdrAgentInformationToDevices(personnel);
        }

        const groups = await this.groupPersonnelRepository.findGroupsByPersonnelId(id);
        personnel.groups = groups; // this change was to improve the latency when there are a lot of groups associated to the personnel

        return personnel;
    }

    private async addEdrAgentInformationToDevices(personnel: Personnel) {
        const deviceIds = personnel.devices.map(device => device.id);

        let devicesAssignments = [];

        if (!isEmpty(deviceIds)) {
            devicesAssignments = await this.deviceRepository.getEdrAgentsByDeviceIds(deviceIds);
        }

        personnel.devices.forEach(personnelDevice => {
            const deviceAssignment = devicesAssignments.find(
                device => (device.id = personnelDevice.id),
            );

            if (!isNil(deviceAssignment)) {
                personnelDevice = deviceAssignment;
            }
        });
    }

    /**
     * @deprecated Use PersonnelCoreService.getPersonnelByIds
     *
     * @param ids
     * @returns
     */
    async getPersonnelByIds(ids: number[]): Promise<Personnel[]> {
        const allPersonnel: Personnel[] = [];
        const chunkIds = chunk(ids, 50);
        for (const chunkId of chunkIds) {
            const personnel =
                // eslint-disable-next-line no-await-in-loop
                await this.personnelRepository.findByIdsWithOptions(chunkId, {
                    setDevices: true,
                    setGroups: true,
                });
            allPersonnel.push(...personnel);
        }
        return allPersonnel;
    }

    /**
     * @deprecated Use PersonnelCoreService.getPersonnelByGroupIds
     *
     * @param groupIds
     * @returns
     */
    async getPersonnelByGroupIds(groupIds: number[]): Promise<Personnel[]> {
        const personnelIds = await this.personnelRepository.getPersonnelIdsByGroupIds(groupIds);
        return this.getPersonnelByIds(personnelIds);
    }

    /**
     * @deprecated Use ComplianceChecksOrchestrationService.getPersonnelWithComplianceTests
     */
    async getPersonnelWithComplianceTests(
        account: Account,
        id: number,
    ): Promise<ExtendedPersonnel> {
        const { securityTraining, hipaaTraining, nistaiTraining } =
            await this.companiesCoreService.getCurrentCompanyWithoutRelationsOrFail();

        const personnel: ExtendedPersonnel = await this.getPersonnelByIdForComplianceTests(id);
        personnel.complianceTests = await this.getPersonnelComplianceTests();

        personnel.complianceChecksWithExclusions =
            await this.getComplianceChecksWithExclusionsForPersonnel(personnel);

        if (!isNil(securityTraining)) {
            personnel.securityTrainingComplianceChecks =
                await this.getPersonnelTrainingComplianceChecks(
                    personnel.id,
                    ComplianceCheckType.SECURITY_TRAINING,
                    securityTraining,
                );
        }

        if (!isNil(hipaaTraining)) {
            personnel.hipaaTrainingComplianceChecks =
                await this.getPersonnelTrainingComplianceChecks(
                    personnel.id,
                    ComplianceCheckType.HIPAA_TRAINING,
                    hipaaTraining,
                );
        }

        if (!isNil(nistaiTraining)) {
            personnel.nistAiTrainingComplianceChecks =
                await this.getPersonnelTrainingComplianceChecks(
                    personnel.id,
                    ComplianceCheckType.NIST_AI_TRAINING,
                    nistaiTraining,
                );
        }

        await this.addDocumentsAndEdrAgentToSinglePersonnel(personnel);

        return personnel;
    }

    /**
     * @deprecated Use PersonnelCoreService.getPersonnelTrainingComplianceChecks
     */
    private async getPersonnelTrainingComplianceChecks(
        personnelId: number,
        trainingType: ComplianceCheckType,
        training: SecurityTrainingType | HipaaTrainingType | NistAITrainingType,
    ) {
        const { clientType, trainingCampaign, complianceCheckKey } = resolveTrainingComplianceKeys(
            trainingType,
            training,
        );

        if (isNil(clientType)) {
            return [];
        }

        const personnelWithTrainingComplianceChecks = await this.getTrainingChecksForPersonnel(
            trainingCampaign,
            personnelId,
            clientType,
        );

        if (isNil(personnelWithTrainingComplianceChecks)) {
            return [];
        }

        return personnelWithTrainingComplianceChecks[complianceCheckKey];
    }

    /**
     * @deprecated Use ComplianceChecksOrchestrationService.getPersonnelByUserId
     *
     * @param {number} userId
     * @returns {Promise<ExtendedPersonnel>}
     */
    async getPersonnelByUserId(userId: number): Promise<ExtendedPersonnel> {
        const personnel: ExtendedPersonnel =
            await this.personnelRepository.findOneByUserIdOrFail(userId);
        const personnelComplianceCheckWithExclusions =
            await this.getComplianceChecksWithExclusionsForPersonnel(personnel);

        personnel.complianceChecksWithExclusions = personnelComplianceCheckWithExclusions;

        return personnel;
    }

    /**
     * @deprecated Use ComplianceChecksOrchestrationService.getComplianceChecksWithExclusionsForPersonnel
     *
     * @param {Personnel} personnel
     * @returns {Promise<ComplianceCheckWithExclusion[]>}
     */
    async getComplianceChecksWithExclusionsForPersonnel(
        personnel: Personnel,
    ): Promise<ComplianceCheckWithExclusion[]> {
        let { complianceChecks } = personnel;

        if (isNil(complianceChecks)) {
            complianceChecks =
                await this.complianceCheckRepository.getComplianceChecksByPersonnel(personnel);
        }

        const company = await this.companyRepository.findOneBy({});

        const complianceCheckExclusions =
            await this.complianceCheckExclusionsCoreService.getCurrentExclusionsForPersonnel(
                company.id,
                [personnel],
            );

        return getComplianceChecksWithExclusions(complianceCheckExclusions, complianceChecks);
    }

    /**
     * @deprecated Use PersonnelCoreService.findByUserIds
     *
     * @param userIds
     * @returns
     */
    findByUserIds(userIds: number[]): Promise<Personnel[]> {
        return this.personnelRepository.findByUserIds(userIds);
    }

    /**
     * @deprecated Use PersonnelCoreService.getPersonnelByEmail
     *
     * @param email
     * @returns
     */
    getPersonnelByEmail(email: string): Promise<Personnel> {
        // find the personnel by user email
        return this.personnelRepository.findOneByEmail(email, true);
    }

    /**
     * @deprecated Use PersonnelCoreService.getPersonnelByEmailNoFail
     *
     * @param string email
     */
    getPersonnelByEmailNotFail(email: string): Promise<Personnel> {
        // find the personnel by user email
        return this.personnelRepository.findOneByEmail(email, false);
    }

    /**
     * @deprecated Use PersonnelCoreService.getPersonnelByPersonalEmail
     *
     * @param string email
     * @param boolean fail
     */
    getPersonnelByPersonalEmail(email: string, fail = false): Promise<Personnel> {
        return this.personnelRepository.findPersonnelByPersonalEmail(email, fail);
    }

    /**
     * @deprecated Use PersonnelCoreService.getPersonnelByName
     *
     * @param object fullName
     * @param boolean fail
     */
    getPersonnelByName(
        name: { firstName: string; lastName: string },
        fail = false,
    ): Promise<Personnel> {
        return this.personnelRepository.findPersonnelByName(name, fail);
    }

    /**
     * @deprecated Use PersonnelCoreService.findByUserEmails
     *
     * @param {string[]} usersEmail
     */
    findByUserEmails(usersEmail: string[]): Promise<Personnel[]> {
        return this.personnelRepository.findByUserEmails(usersEmail);
    }

    /**
     * @deprecated Use PersonnelCoreService.findByIdentityIds
     *
     * Caution: This query is specialized for the identity sync process.
     *
     * @param identityIds
     */
    findByIdentityIds(identityIds: string[], connection: ConnectionEntity): Promise<Personnel[]> {
        // get the identities by an array of ids
        return this.personnelRepository.findByIdentityIds(identityIds, connection);
    }

    /**
     * @deprecated Use PersonnelCoreService.findByConnectionId
     *
     * @param id
     * @returns
     */
    findByConnectionId(id: number): Promise<Personnel[]> {
        return this.personnelRepository.findByConnectionId(id);
    }

    /**
     * @deprecated Use PersonnelCoreService.findByClientType
     **/
    findByClientType(clientType: ClientType): Promise<Personnel[]> {
        return this.personnelRepository.findByClientType(clientType);
    }

    /**
     * @deprecated Use PersonnelCoreService.findFormerPersonnelByIdentityUser
     *
     * @param options
     * @returns
     */
    findFormerPersonnelByIdentityUser(
        identityUser: IdentityServiceUser,
    ): Promise<Personnel | null> {
        return this.personnelRepository.findFormerPersonnelByIdentityUser(identityUser);
    }

    /**
     * @deprecated Use PersonnelCoreService.findAllExcludeByIdentityIds
     */
    findAllExcludeByIdentityIds(ids: string[], connection: ConnectionEntity): Promise<Personnel[]> {
        // get the identities by an array of ids
        return this.personnelRepository.findAllExcludeByIdentityIds(ids, connection);
    }

    /**
     * @deprecated Use PersonnelCoreService.getCurrentPersonnelCount
     *
     * @return {Promise<number>}
     */
    getCurrentPersonnelCount(): Promise<number> {
        return this.personnelRepository.getCurrentPersonnelCount();
    }

    /**
     * @deprecated Use PersonnelCoreService.listPersonnel
     *
     * @param dto
     */
    async listPersonnel(
        dto: PersonnelRequestDto,
        getComplianceChecks = true,
        excludeIds?: number[],
    ): Promise<PaginationType<Personnel>> {
        const personnel = await this.personnelRepository.listPersonnel(
            dto,
            getComplianceChecks,
            excludeIds,
        );

        if (dto?.isEdrConnectionActive) {
            await this.addDocumentsAndEdrAgent(personnel);
        }

        return personnel;
    }

    async listPersonnelForApTask(
        dto: PersonnelForApTaskRequestDto,
        type: AutopilotTaskType,
    ): Promise<Personnel[]> {
        let personnel: PaginationType<Personnel>;

        switch (type) {
            case AutopilotTaskType.EMPLOYEES_POLICIES_APPROVED:
            case AutopilotTaskType.EMPLOYEES_CODE_OF_CONDUCT_POLICY:
            case AutopilotTaskType.CONTRACTORS_CODE_OF_CONDUCT_POLICY:
            case AutopilotTaskType.EMPLOYEES_DATA_PROTECTION_POLICY:
            case AutopilotTaskType.EMPLOYEES_ACCEPTABLE_USE_POLICY:
            case AutopilotTaskType.CONTRACTORS_ACCEPTABLE_USE_POLICY:
            case AutopilotTaskType.CONTRACTORS_DATA_PROTECTION_POLICY: {
                personnel = await this.personnelRepository.listPersonnelWithUnacceptedPolicies(dto);
                break;
            }
            case AutopilotTaskType.EMPLOYEES_SECURITY_COMPLIANCE:
            case AutopilotTaskType.EMPLOYEES_BACKGROUND_CHECK_COMPLIANCE:
            case AutopilotTaskType.CONTRACTORS_BACKGROUND_CHECK_COMPLIANCE: {
                personnel = await this.personnelRepository.listPersonnelForSecurityCompliance(dto);
                break;
            }
            case AutopilotTaskType.INTERNAL_INFRASTRUCTURE_IDENTITIES_REMOVED: {
                personnel = await this.personnelRepository.listPlainPersonnel(dto);
                break;
            }
            default: {
                personnel = {
                    data: [],
                    limit: 0,
                    page: dto.page,
                    total: 0,
                };
            }
        }

        return personnel.data;
    }

    /**
     *
     * @param dto
     */
    async listPersonnelWithFailedMfa(
        dto: PersonnelWithFailedMfaRequestDto,
        excludeIds?: number[],
    ): Promise<Personnel[]> {
        const personnel = await this.personnelRepository.listPersonnelFailedMfa(dto, excludeIds);

        return personnel.data;
    }

    async listPersonnelWithDevices(
        dto: PersonnelWithDevicesRequestDto,
        getComplianceChecks = true,
        excludeIds?: number[],
    ): Promise<Personnel[]> {
        const personnel = await this.personnelRepository.listPersonnelWithDevices(
            dto,
            getComplianceChecks,
            excludeIds,
        );

        if (dto?.isEdrConnectionActive) {
            await this.addDocumentsAndEdrAgent(personnel);
        }

        return personnel.data;
    }

    private async addDocumentsAndEdrAgent(personnel: PaginationType<Personnel>) {
        const deviceIds = personnel.data
            .map(person => person.devices.map(device => device.id))
            .flat();

        if (deviceIds.length > 0) {
            const { documents, edrDevices } = await this.getDocumentsAndEdrDevices(deviceIds);

            personnel.data.forEach(
                person =>
                    (person.devices = person.devices.map(device => {
                        return this.setDeviceAndAgentData(device, documents, edrDevices);
                    })),
            );
        }
    }

    /**
     * @deprecated Use PersonnelCoreService.addDocumentsAndEdrAgentToSinglePersonnel
     **/
    private async addDocumentsAndEdrAgentToSinglePersonnel(personnel: Personnel) {
        const deviceIds = personnel.devices.map(device => device.id);

        if (deviceIds.length > 0) {
            const { documents, edrDevices } =
                await this.personnelCoreService.getDocumentsAndEdrDevicesOptimized(deviceIds);
            personnel.devices = personnel.devices.map(device => {
                return this.setDeviceAndAgentData(device, documents, edrDevices);
            });
        }
    }

    /**
     * @deprecated Use PersonnelCoreService.getDocumentsAndEdrDevices
     *
     * @param deviceIds
     * @returns
     */
    private async getDocumentsAndEdrDevices(
        deviceIds: number[],
    ): Promise<{ documents: DeviceDocument[]; edrDevices: Device[] }> {
        const documents = await this.deviceDocumentRepository.getAllDocumentsByDeviceIdsAndType(
            deviceIds,
            DeviceDocumentType.ANTIVIRUS_EVIDENCE,
        );

        const edrDevices = await this.deviceRepository.getEdrAgentsByDeviceIds(deviceIds);

        return { documents, edrDevices };
    }

    /**
     * @deprecated Use PersonnelCoreService.setDeviceAndAgentData
     *
     * @param device
     * @param documents
     * @param edrDevices
     * @returns
     */
    private setDeviceAndAgentData(
        device: Device,
        documents: DeviceDocument[],
        edrDevices: Device[],
    ): Device {
        device.documents = documents.filter(doc => doc.device.id === device.id);

        const deviceWithAgent = edrDevices.find(edrDevice => edrDevice.id === device.id);

        device.agentAssignation = deviceWithAgent?.agentAssignation;

        return device;
    }

    /**
     * @deprecated Use PersonnelCoreService.getPersonnelByEmploymentStatuses
     *
     * @param {EmploymentStatus[]} employmentStatuses
     * @returns {Promise<Personnel[]>}
     */
    getPersonnelByEmploymentStatuses(employmentStatuses: EmploymentStatus[]): Promise<Personnel[]> {
        return this.personnelRepository.findByEmploymentStatuses(employmentStatuses);
    }

    /**
     * @param {Account} account
     * @param {PersonnelRequestDto} dto
     */
    async listTablePersonnel(
        account: Account,
        dto: PersonnelRequestDto,
    ): Promise<PaginationType<ExtendedPersonnel>> {
        try {
            const company = await this.companiesCoreService.getCurrentCompanyWithoutRelations();

            const isEdrConnectionActive = await this.edrCoreService.isConnectionActive();

            const allPersonnel: PaginationType<ExtendedPersonnel> =
                await this.personnelRepository.listPersonnelV2({ ...dto, isEdrConnectionActive });

            if (isEdrConnectionActive) {
                await this.addDocumentsAndEdrAgent(allPersonnel);
            }

            const personnelIds = allPersonnel.data.map(personnel => personnel.id.toString());

            const isHipaaFrameworkEnabled = await this.companiesCoreService.getIsTrainingEnabled(
                TrainingType.HIPAA_TRAINING,
            );

            const isNistAiFrameworkEnabled = await this.companiesCoreService.getIsTrainingEnabled(
                TrainingType.NIST_AI_TRAINING,
            );

            const [
                complianceTests,
                complianceCheckExclusionsByPersonnelId,
                personnelWithTrainingChecks,
            ] = await Promise.all([
                this.getPersonnelComplianceTests(),
                this.complianceCheckExclusionsCoreService.getCurrentExclusionsForPersonnel(
                    company.id,
                    allPersonnel.data,
                ),

                this.personnelRepository.getTrainingChecksByPersonnelIds(
                    personnelIds,
                    isHipaaFrameworkEnabled,
                    isNistAiFrameworkEnabled,
                ),
            ]);

            allPersonnel.data = this.getPersonnelWithTrainingAndCompliance(
                allPersonnel.data,
                company,
                complianceTests,
                complianceCheckExclusionsByPersonnelId,
                personnelWithTrainingChecks,
            );
            return allPersonnel;
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(`Something went wrong while getting personnel.`, account)
                    .setError(error)
                    .setContext(this.constructor.name)
                    .setSubContext(this.listTablePersonnel.name)
                    .setIdentifier({ dto }),
            );

            throw new UnprocessableEntityException(
                ErrorCode.CONFLICT_MISMATCH_DATA,
                error?.message,
            );
        }
    }

    private getPersonnelWithTrainingAndCompliance(
        personnel: ExtendedPersonnel[],
        company: Nullable<Company>,
        complianceTests: MonitorInstance[],
        complianceCheckExclusions: ComplianceCheckExclusion[],
        personnelWithTrainingChecks: Personnel[],
    ): ExtendedPersonnel[] {
        return personnel.map(person => {
            const { securityTrainingChecks, hipaaTrainingChecks, nistAiTrainingChecks } =
                getTrainingsComplianceChecks(person.id, company, personnelWithTrainingChecks);

            return {
                ...person,
                complianceTests,
                agentEnabled: company?.agentEnabled ?? null,
                manualUploadEnabled: company?.manualUploadEnabled ?? null,
                complianceChecksWithExclusions: filterComplianceChecksWithExclusions(
                    person,
                    complianceCheckExclusions,
                ),
                securityTrainingComplianceChecks: securityTrainingChecks,
                hipaaTrainingComplianceChecks: hipaaTrainingChecks,
                nistAiTrainingComplianceChecks: nistAiTrainingChecks,
            };
        }) as ExtendedPersonnel[];
    }

    /**
     *
     * @param {Account} account
     * @param {User} user
     * @param {PersonnelRequestDto} dto
     * @param {string} accountId
     */
    async listTablePersonnelWithAdditionalData(
        account: Account,
        user: User,
        dto: PersonnelRequestDto,
        _accountId: string,
    ): Promise<ExtendedPaginationType<ExtendedPersonnel, PersonnelTableAllType>> {
        try {
            const [paginatedPersonnel, failedComplianceCheckTypes, offboardingEvidenceForFormer] =
                await Promise.all([
                    this.listTablePersonnel(account, dto),
                    this.getFailingComplianceChecksForPersonnel(dto),
                    this.getFailingOffboardingEvidenceComplianceForPersonnel(dto),
                ]);

            if (!isNil(offboardingEvidenceForFormer)) {
                failedComplianceCheckTypes.push(offboardingEvidenceForFormer);
            }
            return {
                ...paginatedPersonnel,
                dataAll: {
                    failedComplianceCheckTypes,
                    currentTotal: await this.getCurrentPersonnelCount(),
                },
            };
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(`Something went wrong while processing personnel.`, account)
                    .setError(error)
                    .setContext(this.constructor.name)
                    .setSubContext(this.listTablePersonnelWithAdditionalData.name)
                    .setIdentifier({ dto, user: user.email }),
            );

            throw new UnprocessableEntityException(
                ErrorCode.CONFLICT_MISMATCH_DATA,
                error?.message,
            );
        }
    }

    async getFailedComplianceCheckTypes(dto: PersonnelRequestDto) {
        const [failedComplianceCheckTypes, offboardingEvidenceForFormer] = await Promise.all([
            this.getFailingComplianceChecksForPersonnel(dto),
            this.getFailingOffboardingEvidenceComplianceForPersonnel(dto),
        ]);

        if (!isNil(offboardingEvidenceForFormer)) {
            failedComplianceCheckTypes.push(offboardingEvidenceForFormer);
        }

        return failedComplianceCheckTypes;
    }

    countPersonnel(dto: PersonnelRequestDto) {
        return this.personnelRepository.countByPersonnelRequestDto(dto);
    }

    /**
     * @deprecated Use PersonnelCoreService.getPersonnelComplianceTests
     **/
    getPersonnelComplianceTests(): Promise<MonitorInstance[]> {
        return this.monitorInstanceRepository.findByAutopilotTaskTypes(personnelComplianceTests);
    }

    /**
     *
     * @param {string[]} userIds
     * @returns {Promise<MonitorInstanceExclusion[]>}
     */
    getPersonnelExcludedBackgroundChecks(userIds: string[]): Promise<MonitorInstanceExclusion[]> {
        return this.exclusionRepository.getMonitorExclusionsByTargetIdsAndTestIds(
            null, // we do not require the account here
            userIds,
            [
                ControlTestId.COMPLIANCE_EMPLOYEES_BACKGROUND_CHECK_COMPLIANCE.valueOf(),
                ControlTestId.COMPLIANCE_CONTRACTORS_BACKGROUND_CHECK_COMPLIANCE.valueOf(),
            ],
        );
    }

    getFailingComplianceChecksForPersonnel(dto: PersonnelRequestDto): Promise<number[]> {
        return this.complianceCheckRepository.getFailedComplianceCheckTypesByFilters(dto);
    }

    getFailingOffboardingEvidenceComplianceForPersonnel(dto: PersonnelRequestDto): Promise<number> {
        return this.complianceCheckRepository.getFailedOffboardingEvidenceComplianceCheckTypeFilters(
            dto,
        );
    }

    /**
     * @deprecated Use PersonnelCoreService.savePersonnel
     *
     * @param {Personnel} personnel
     * @returns {Promise<ExtendedPersonnel>}
     */
    savePersonnel(personnel: Personnel): Promise<ExtendedPersonnel> {
        return this.personnelRepository.save(personnel);
    }

    /**
     * @deprecated Use PersonnelCoreService.findByPendingOrMissingHrisBackgroundCheck
     **/
    findByPendingOrMissingHrisBackgroundCheck(
        targetConnection: ConnectionEntity,
        backgroundCheckType: BackgroundCheckType,
    ): Promise<Personnel[]> {
        return this.personnelRepository.findByPendingOrMissingHrisBackgroundCheck(
            targetConnection,
            backgroundCheckType,
        );
    }

    findByPendingBackgroundCheck(backgroundCheckType: BackgroundCheckType): Promise<Personnel[]> {
        return this.personnelRepository.findByPendingBackgroundCheck(backgroundCheckType);
    }

    /**
     *
     * @param {Personnel} personnel
     * @param {User} user
     * @param {PersonnelBulkActionStatusRequestDto} dto
     * @returns {void}
     */
    private isValidPersonnelForChangeStatus(
        personnel: Personnel,
        user: User,
        dto: PersonnelBulkActionStatusRequestDto,
    ): void {
        // sanity check if the authed user's ID is the same as the ID of the user that we're trying to update
        const isInAFormerOrSpecialStatus =
            isInAFormerStatus(dto.employmentStatus) || isInASpecialStatus(dto.employmentStatus);
        if (user.id === personnel.user.id && isInAFormerOrSpecialStatus) {
            throw new OurConflictException(
                `Cannot update your own status to a former status`,
                ErrorCode.PERSONNEL_SELF_STATUS_TO_FORMER_STATUS,
            );
        }
        if (personnel.employmentStatus === dto.employmentStatus && !isInAFormerOrSpecialStatus) {
            throw new OurConflictException(
                `The user already has the same employment status as the target status`,
                ErrorCode.PERSONNEL_ALREADY_HAS_SAME_STATUS,
            );
        }
        if (personnel.employmentStatus === EmploymentStatus.FUTURE_HIRE) {
            throw new OurConflictException(
                `Cannot update a Future Hire user status`,
                ErrorCode.PERSONNEL_IS_FUTURE_HIRE,
            );
        }
    }

    /**
     * @deprecated Use PersonnelCoreService.getAllPersonnelWithAllRoles
     */
    getAllPersonnelWithAllRoles(
        ids: number[],
        skip?: number,
        limit?: number,
        employmentStatuses?: EmploymentStatus[],
    ): Promise<Personnel[]> {
        return this.personnelRepository.getAllPersonnelWithAllRoles(ids, {
            skip,
            limit,
            employmentStatuses,
        });
    }

    /**
     * @deprecated Use BulkActionsCommerceService.restorePersonnelDevices
     *
     * @param account
     * @param personnelIds
     */
    async restorePersonnelDevices(account: Account, personnelIds: number[]): Promise<void> {
        const devices = await this.deviceRepository.find({
            relations: ['personnel', 'identifiers'],
            where: {
                personnel: {
                    id: In(personnelIds),
                },
                deletedAt: Not(IsNull()),
            },
            withDeleted: true,
        });

        for await (const device of devices) {
            await this.deviceComplianceCheckOrchestrationService.findAndRestoreDeletedDevice(
                device.personnel,
                device.identifiers,
                account,
            );
        }
    }

    /**
     * @deprecated Use PersonnelBulkActionsCommerceService.prepareMultiplePersonnelForUpdateStatus
     *
     * @param {Account} account
     * @param {User} user
     * @param {PersonnelBulkActionStatusRequestDto} dto
     * @param {number[]} personnelIds
     * @returns {Promise<{
        validPersonnel: {
            personnel: Personnel;
            originalPersonnel: Personnel;
        }[];
        errors: BulkActionErrorType[];
    }>}
     */
    async prepareMultiplePersonnelForUpdateStatus(
        account: Account,
        user: User,
        dto: PersonnelBulkActionStatusRequestDto,
        personnelIds: number[],
        separateUser = true,
    ): Promise<{
        validPersonnel: PersonnelValidType[];
        errors: BulkActionErrorType[];
    }> {
        let errors: BulkActionErrorType[] = [];
        let preparedPersonnelSet: {
            personnel: Personnel;
            originalPersonnel: Personnel;
            snapshotMoment: Date;
        }[] = [];

        // fetch the current state of the personnel
        const allPersonnel =
            await this.personnelRepository.getAllPersonnelWithAllRoles(personnelIds);
        for (const originalPersonnel of allPersonnel) {
            const preparedDataForPersonnel: any = { originalPersonnel };
            try {
                this.isValidPersonnelForChangeStatus(originalPersonnel, user, dto);
                // sanity check if the authed user's ID is the same as the ID of the user that we're trying to update
                if (
                    user.id === originalPersonnel.user.id &&
                    !isSelfAssignStatus(originalPersonnel.employmentStatus)
                ) {
                    // can't change your own status
                    throw new OurConflictException(
                        'Cannot change your own status',
                        ErrorCode.PERSONNEL_SELF_STATUS_TO_FORMER_STATUS,
                    );
                }
                // grab a snapshot of this moment
                preparedDataForPersonnel.snapshotMoment = new Date();

                preparedDataForPersonnel.personnel = {
                    ...originalPersonnel,
                } as ExtendedPersonnel;

                if (dto.employmentStatus === EmploymentStatus.UNKNOWN && dto.separationDate) {
                    throw new OurConflictException(
                        `Cannot update separationDate on ${
                            EmploymentStatus[dto.employmentStatus]
                        } status`,
                        ErrorCode.CONFLICT_SEPARATION_DATE_WITH_UNKNOWN_STATUS,
                    );
                }

                if (isDate(dto.separationDate) && isInACurrentStatus(dto.employmentStatus)) {
                    dto.employmentStatus = getSwitchedStatus(dto.employmentStatus);
                }

                if (
                    isDate(dto.separationDate) &&
                    isInACurrentStatus(originalPersonnel.employmentStatus) &&
                    separateUser
                ) {
                    /**switching from current to former */
                    // eslint-disable-next-line no-await-in-loop
                    await this.usersService.doSeparation(account, originalPersonnel.user, user);
                }
                preparedPersonnelSet.push(preparedDataForPersonnel);
            } catch (exception) {
                errors.push({
                    error: exception,
                    targetId: originalPersonnel.id.toString(),
                });
            }
        }

        const validationResult = await this.validateUpdateStatusForMultiplePersonnel(
            dto,
            preparedPersonnelSet.map(p => p.personnel),
        );
        // update the list of valid personnel
        preparedPersonnelSet = preparedPersonnelSet.filter(pp =>
            validationResult.validPersonnel.find(vp => vp.id === pp.personnel.id),
        );
        errors = errors.concat(validationResult.errors);
        let publishedPolicyVersionsMap = new Map<number, UserPolicyVersion[]>();

        // check if marked OUT_OF_SCOPE
        if (isOutOfScope(dto.employmentStatus)) {
            preparedPersonnelSet.forEach(({ personnel }) => {
                // set the message
                personnel.notHumanReason = dto.notHumanReason;
                // set the user
                personnel.reasonProvider = user;
            });

            // some client user will manage this personnel
            await this.usersService.restoreEmployeeRoleToUsers(
                preparedPersonnelSet.map(pp => pp.personnel.user),
            );
        } else if (separateUser) {
            // check for separation
            if (
                isInAFormerStatus(dto.employmentStatus) ||
                isInASpecialStatus(dto.employmentStatus)
            ) {
                if (!dto.separationDate || !isDate(dto.separationDate)) {
                    /**interrupt the bulk action since this is needed for all target personnel
                     * This should not be execute since there is a same validation from the DTO.
                     * **/
                    throw new OurConflictException(
                        `separationDate is required for ${EmploymentStatus[dto.employmentStatus]}`,
                        ErrorCode.SEPARATION_DATE_IS_REQUIRED,
                    );
                }
                const separationDate = normalizeDate(dto.separationDate);
                const hireDate = normalizeDate(dto.startDate);
                const currentDate = normalizeDate();

                /**
                 * interrupt the bulk action since this is needed for all target personnel
                 * only allow separation dates equal or after hire date up to the current date
                 */
                if (separationDate > currentDate || separationDate < hireDate) {
                    throw new OurConflictException(
                        `separationDate must be equal or after the hire date up to the current date`,
                        ErrorCode.INVALID_SEPARATION_DATE_FOR_CURRENT_DATE,
                    );
                }
                preparedPersonnelSet.forEach(({ personnel }) => {
                    // set a new separate date
                    personnel.separationDate = dto.separationDate;
                    // set a new hire date
                    personnel.startDate = dto.startDate;
                });

                // remove all their roles
                await this.usersService.deleteAllUserRolesForMultipleUsers(
                    preparedPersonnelSet.map(pp => pp.personnel.user),
                );
                await this.usersService.deleteAllUserRolesAndPermissionsForMultipleUsers(
                    account,
                    user,
                    preparedPersonnelSet.map(pp => pp.personnel.user),
                );
            }
            const personnelFromFormerToCurrent = [];
            preparedPersonnelSet.forEach(({ personnel }) => {
                if (personnelToCurrentStatus(personnel.employmentStatus, dto.employmentStatus)) {
                    // clear out the separation date
                    personnel.separationDate = null;
                    personnelFromFormerToCurrent.push(personnel);
                }
            });
            await this.usersService.restoreEmployeeRoleToUsers(
                personnelFromFormerToCurrent.map(p => p.user),
            );

            await this.restorePersonnelDevices(
                account,
                personnelFromFormerToCurrent.map(p => p.id),
            );

            const batchSize = 50;
            const promises = [];
            // publish ALL-scoped and GROUP-scoped missing user policy versions
            for (const personnelBatch of chunk(personnelFromFormerToCurrent, batchSize)) {
                promises.push(
                    this.userPoliciesService.publishForPersonnelSet(account, personnelBatch),
                );
            }
            const publishedVersionsMaps = await Promise.all([promises]);

            // merge them
            publishedPolicyVersionsMap = new Map<number, UserPolicyVersion[]>(
                publishedVersionsMaps.flatMap(publishedVersionMap => [...publishedVersionMap]),
            );

            preparedPersonnelSet.forEach(({ personnel }) => {
                // clear irrelevant data
                personnel.notHumanReason = null;
                personnel.reasonProvider = null;
            });
        }

        const validPersonnel: {
            personnel: Personnel;
            originalPersonnel: Personnel;
            userPolicyVersions?: UserPolicyVersion[];
        }[] = preparedPersonnelSet.map(preparedPersonnel => {
            // We have detected a manual override, set the flag
            preparedPersonnel.personnel.statusUpdatedAt = preparedPersonnel.snapshotMoment;
            // set the status
            preparedPersonnel.personnel.employmentStatus = dto.employmentStatus;
            // remove unnecessary property
            delete preparedPersonnel.snapshotMoment;

            return {
                ...preparedPersonnel,
                userPolicyVersions: publishedPolicyVersionsMap.get(preparedPersonnel.personnel.id),
            };
        });

        return { validPersonnel, errors };
    }

    /**
     *
     * @param account
     * @param user
     * @param personnelId
     * @param dto
     */
    async updateStatus(
        account: Account,
        user: User,
        personnelId: number,
        dto: PersonnelStatusRequestDto,
    ): Promise<ExtendedPersonnel> {
        // fetch the current state of the personnel
        const originalPersonnel =
            await this.personnelRepository.getPersonnelWithAllRoles(personnelId);

        // sanity check if the authed user's ID is the same as the ID of the user that we're trying to update
        if (
            user.id === originalPersonnel.user.id &&
            !isSelfAssignStatus(originalPersonnel.employmentStatus)
        ) {
            // can't change your own status
            throw new ConflictException();
        }
        // grab a snapshot of this moment
        const snapshotMoment = new Date();
        // copy to mess with it with TypeORM
        let personnel = {
            ...originalPersonnel,
        } as ExtendedPersonnel;

        if (dto.employmentStatus === EmploymentStatus.UNKNOWN && dto.separationDate) {
            throw new ConflictException(
                `Cannot update separationDate on ${EmploymentStatus[dto.employmentStatus]} status`,
            );
        }

        if (isDate(dto.separationDate) && isInACurrentStatus(dto.employmentStatus)) {
            dto.employmentStatus = getSwitchedStatus(dto.employmentStatus);
        }

        await this.validateUpdateStatus(dto, personnel);
        let userPolicyVersions: Map<number, UserPolicyVersion[]> = new Map<
            number,
            UserPolicyVersion[]
        >();
        // check if marked OUT_OF_SCOPE
        if (this.OUT_OF_SCOPE_STATES.includes(dto.employmentStatus)) {
            await this.usersService.removeUserFromRequestOwnerList(personnel.user.id);
            // set the message
            personnel.notHumanReason = dto.notHumanReason;
            // set the user
            personnel.reasonProvider = user;
            // some client user will manage this personnel?
            await this.usersService.restoreUserEmployeeRole(personnel.user);
        } else {
            // check for separation
            if (
                isInAFormerStatus(dto.employmentStatus) ||
                isInASpecialStatus(dto.employmentStatus)
            ) {
                if (!dto.separationDate || !isDate(dto.separationDate)) {
                    throw new ConflictException(
                        `separationDate is required for ${EmploymentStatus[dto.employmentStatus]}`,
                    );
                }

                const separationDate = normalizeDate(dto.separationDate);
                const hireDate = normalizeDate(dto.startDate ?? personnel.startDate);
                const currentDate = normalizeDate();

                // only allow separation dates equal or after hire date up to the current date
                if (separationDate > currentDate || separationDate < hireDate) {
                    throw new ConflictException(
                        `separationDate must be equal or after the hire date up to the current date`,
                    );
                }

                // set a separate date
                personnel.separationDate = dto.separationDate;

                // remove all their roles, controls
                await this.usersService.doSeparation(account, personnel.user, user);
            } else if (
                personnelToCurrentStatus(personnel.employmentStatus, dto.employmentStatus) ||
                personnelFromFutureHireToCurrentStatus(
                    personnel.employmentStatus,
                    dto.employmentStatus,
                )
            ) {
                // clear out the separation date
                personnel.separationDate = null;
                personnel.separatedAt = null;
                // grant them the employee role
                await this.usersService.restoreUserEmployeeRole(personnel.user);
                // distribute any missing ALL-scoped and GROUP-scoped user policy versions
                userPolicyVersions = await this.userPoliciesService.publishForPersonnel(
                    account,
                    personnel,
                );
            }
            // clear irrelevant data
            personnel.notHumanReason = null;
            personnel.reasonProvider = null;
        }
        // We have detected a manual override, set the flag
        personnel.statusUpdatedAt = snapshotMoment;
        // set the status
        personnel.employmentStatus = dto.employmentStatus;
        // update the personnel record
        await this.personnelRepository.save(personnel);
        // fetch the updated data
        personnel = await this.personnelRepository.getPersonnelWithOnlyActiveRoles(personnelId);

        /**
         * If there are new policy versions allocated to this personnel. We need
         * to set this variable to false to trigger compliance check recomputation.
         *
         * If no policy versions were allocated, set it to true.
         */
        const exclusionsOnly = !userHasPoliciesAllocated(personnel.id, userPolicyVersions);
        await this.complianceChecksOrchestrationService.computeComplianceChecksForPersonnel(
            account,
            [personnel],
            exclusionsOnly,
            [ComplianceCheckType.ACCEPTED_POLICIES],
        );
        await this.complianceChecksOrchestrationService.updateFullCompliance(personnel.user);
        // emit the UserRolesUpdated event here
        this._eventBus.publish(
            new PersonnelEmploymentStatusUpdatedEvent(account, user, originalPersonnel, dto),
        );
        // emit event to update asset
        this._eventBus.publish(new AssetPersonnelUpdatedEvent(account, personnel));
        this._eventBus.publish(new DevicePersonnelUpdatedEvent(account, originalPersonnel, dto));
        // Add compliance tests, now is needed for validations
        personnel.complianceTests = await this.getPersonnelComplianceTests();

        const personnelComplianceCheckWithExclusions =
            await this.getComplianceChecksWithExclusionsForPersonnel(personnel);

        personnel.complianceChecksWithExclusions = personnelComplianceCheckWithExclusions;

        // return the newly updated personnel record
        return personnel;
    }

    @CreateEvent<Personnel>(
        EventType.FORCE_SYNC_ON_A_PERSON,
        EventCategory.PERSONNEL,
        eventDescriptions[EventType.FORCE_SYNC_ON_A_PERSON],
    )
    async resetSyncStatus(
        account: Account,
        user: User,
        personnelId: number,
    ): Promise<ExtendedPersonnel> {
        const personnel = await this.getPersonnelByIdWithUser(personnelId);

        if (user.id === personnel.user?.id) {
            throw new ConflictException('You cannot reset yourself');
        }

        personnel.statusUpdatedAt = null;

        this.log(
            `Reset HRIS/IdP sync for ${personnel.user?.firstName} ${personnel.user?.lastName}`,
            account,
        );

        await this.personnelRepository.save(personnel);

        return this.getPersonnelWithComplianceTests(account, personnelId);
    }

    @CreateEvent<Personnel[]>(
        EventType.FORCE_SYNC_ON_SOME,
        EventCategory.PERSONNEL,
        eventDescriptions[EventType.FORCE_SYNC_ON_SOME],
    )
    async resetSomeSyncStatus(
        account: Account,
        requestDto: PersonnelResetStatusRequestDto,
    ): Promise<Personnel[]> {
        const { personnelIds } = requestDto;

        const personnel =
            await this.personnelRepository.resetPersonnelStatusUpdatedAt(personnelIds);

        this.log(`Reset HRIS/IdP sync for some personnel`, account, personnelIds);

        return personnel;
    }

    @CreateEvent<Personnel[]>(
        EventType.FORCE_SYNC_ON_ALL,
        EventCategory.PERSONNEL,
        eventDescriptions[EventType.FORCE_SYNC_ON_ALL],
    )
    async resetAllSyncStatus(
        account: Account,
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        user: User,
    ): Promise<Personnel[]> {
        const allPersonnel = await this.personnelRepository.find({
            where: {
                statusUpdatedAt: Not(IsNull()),
                employmentStatus: Not(
                    In([EmploymentStatus.OUT_OF_SCOPE, EmploymentStatus.SERVICE_ACCOUNT]),
                ),
            },
        });

        const personnelIds = allPersonnel.map(p => p.id);

        const personnel =
            await this.personnelRepository.resetPersonnelStatusUpdatedAt(personnelIds);

        this.log(`Reset HRIS/IdP sync for all personnel`, account);

        return personnel;
    }

    @CacheBusterWithPrefix<ExtendedPersonnel>({
        store: Caches.LIST_CONTROLS,
    })
    async updateContractDates(
        account: Account,
        user: User,
        personnelId: number,
        dto: PersonnelContractDateRequestDto,
    ): Promise<ExtendedPersonnel> {
        const snapshotMoment = new Date();
        let personnel = await this.personnelRepository.getPersonnelWithAllRoles(personnelId);

        if (!personnel) {
            throw new NotFoundException(ErrorCode.ENTITY_NOT_FOUND);
        }

        if (
            isUnknownStatus(personnel.employmentStatus) &&
            personnel.employmentStatus !== EmploymentStatus.OUT_OF_SCOPE
        ) {
            throw new ConflictException(
                `Cannot update dates on ${EmploymentStatus[personnel.employmentStatus]} status`,
            );
        }

        await this.validatePersonnelAndContractDates(personnel, user?.id, dto);

        const personnelStatusDto = await this.buildPersonnelStatusDtoForUpdate(personnel, dto);

        const isConnectedToHRIS = await this.isConnectedToHRIS(personnel.user.id);
        // if statusUpdatedAt is null and is connected to HRIS the user needs to be updated on the HRIS system
        if (isNil(personnel.statusUpdatedAt) && isConnectedToHRIS) {
            throw new ConflictException(
                'Cannot update contract dates when person is already on HRIS',
            );
        }

        if (
            isInAFormerStatus(personnel.employmentStatus) &&
            !dto.separationDate &&
            !isFutureDateString(dto.startDate)
        ) {
            throw new BadRequestException(
                `Separation date is required for ${
                    EmploymentStatus[personnel.employmentStatus]
                } status`,
            );
        }

        if (
            personnel.employmentStatus !== EmploymentStatus.FUTURE_HIRE &&
            personnel.separationDate &&
            personnel.startDate &&
            personnel.separationDate < personnel.startDate
        ) {
            throw new ConflictException(`Start date must be before separation date`);
        }

        // if everything is ok, set manual change flag
        personnel.statusUpdatedAt = snapshotMoment;

        const updatedPersonnel = await this.updateStatus(
            account,
            user,
            personnel.id,
            personnelStatusDto,
        );

        personnel = cloneDeep(updatedPersonnel);

        personnel.startDate = personnelStatusDto.startDate;
        personnel.separationDate = personnelStatusDto.separationDate;
        personnel.startedAt = !isNil(personnelStatusDto.startDate)
            ? new Date(personnelStatusDto.startDate)
            : null;
        personnel.separatedAt = !isNil(personnelStatusDto.separationDate)
            ? new Date(personnelStatusDto.separationDate)
            : null;

        await this.personnelRepository.save(personnel);

        personnel = await this.personnelRepository.getPersonnelWithOnlyActiveRoles(personnelId);

        if (dto.startDate !== personnel.startDate) {
            this._eventBus.publish(new HiredDateChangedEvent(account, user));
        }
        if (dto.separationDate !== personnel.separationDate) {
            this._eventBus.publish(new SeparationDateChangedEvent(account, user));
        }
        // if everything is ok, set manual change flag
        personnel.statusUpdatedAt = snapshotMoment;
        await this.personnelRepository.save(personnel);

        const newEmploymentStatus = getEmploymentStatusFromContractDates(personnel, dto);

        // update employment status if change detected
        if (newEmploymentStatus !== personnel.employmentStatus) {
            const personnelStatusRequestDto = new PersonnelStatusRequestDto();
            personnelStatusRequestDto.employmentStatus = newEmploymentStatus;

            if (newEmploymentStatus === EmploymentStatus.FUTURE_HIRE) {
                personnelStatusRequestDto.notHumanReason = 'This personnel has a future hire date.';
            }

            if (dto.separationDate) {
                personnelStatusRequestDto.separationDate = dto.separationDate;
            }
            personnel = await this.updateStatus(
                account,
                user,
                personnel.id,
                personnelStatusRequestDto,
            );
        } else {
            personnel = await this.personnelRepository.getPersonnelWithOnlyActiveRoles(personnelId);

            const personnelComplianceCheckWithExclusions =
                await this.getComplianceChecksWithExclusionsForPersonnel(personnel);
            personnel.complianceChecksWithExclusions = personnelComplianceCheckWithExclusions;
        }

        return personnel;
    }

    /**
     *
     * @param account
     * @param user
     * @param id
     */
    async sendReminderEmail(account: Account, user: User, id: number): Promise<void> {
        // fetch the personnel record
        const personnel =
            await this.personnelRepository.findOneByPersonnelIdOrFailForReminderEmail(id);

        this.validateSendReminderPersonnel(personnel);

        // emit PersonnelIncompliantEvent event
        this._eventBus.publish(new PersonnelIncompliantEvent(account, user, personnel));
        // emit PersonnelSendReminderEmailEvent event
        this._eventBus.publish(new PersonnelSendReminderEmailEvent(account, user, personnel));
    }

    /**
     *
     * @param {Personnel} personnel
     * @param {User} user
     */
    validateSendReminderPersonnel(personnel: Personnel): void {
        // check if personnel is in correct employment status
        if (
            personnel.employmentStatus === EmploymentStatus.FORMER_EMPLOYEE ||
            personnel.employmentStatus === EmploymentStatus.FORMER_CONTRACTOR ||
            personnel.employmentStatus === EmploymentStatus.SPECIAL_FORMER_EMPLOYEE ||
            personnel.employmentStatus === EmploymentStatus.SPECIAL_FORMER_CONTRACTOR
        ) {
            // don't remind a not applicable employment status
            throw new OurConflictException(
                'Personnel employment status needs to be active',
                ErrorCode.PERSONNEL_IS_FORMER,
            );
        } else if (isOutOfScope(personnel.employmentStatus)) {
            throw new OurConflictException(
                'Personnel employment status needs to be active',
                ErrorCode.PERSONNEL_IS_OUT_OF_SCOPE,
            );
        }
        // get the "full compliance" check value
        const fullyCompliant = isCompliant(personnel, ComplianceCheckType.FULL_COMPLIANCE);
        // sanity check if the personnel record is already in "full compliance"
        if (fullyCompliant === ComplianceCheckStatus.PASS) {
            // don't remind a fully compliant user
            throw new OurConflictException(
                'User is already fully compliant',
                ErrorCode.CONFLICT_PERSONNEL_OK,
            );
        }
    }

    /**
     * @deprecated Use PersonnelCoreService.setAgentInterval
     *
     * @param personnel
     * @returns
     */
    async setAgentInterval(personnel: Personnel): Promise<Personnel> {
        // set the agent interval
        personnel.agentInterval = config.get('agent.defaultInterval');
        // save the personnel entity
        await this.personnelRepository.save(personnel);
        // return the updated personnel here
        return personnel;
    }

    private async personnelDetailPdfFile(
        personnelStatus: AuditorPersonnelStatus,
        originalPersonnel: Personnel,
        documentsToDownload: any[],
        account: Account,
        doneTickets: Ticket[],
        user: User,
    ) {
        const customFieldsValues: CustomFieldValueType[] = [];
        const hasCustomFieldsEnabled = await isCustomFieldsEnabled(account);
        if (hasCustomFieldsEnabled) {
            const customFieldSubmission =
                await this.customFieldLocationRepository.getCustomFieldLocationsWithPersonnelSubmissions(
                    originalPersonnel.id,
                );

            if (!isNil(customFieldSubmission)) {
                const sectionData = customFieldSubmission.filter(
                    placement => placement.section === CustomFieldsSection.PERSONNEL_DETAILS,
                );

                sectionData?.forEach(section => {
                    const value = getFormatedValue({
                        type: section.customField.fieldType,
                        value: section.submission?.submission?.value ?? null,
                        code: section.customField.currency?.code,
                        options: section.customField.options,
                    });
                    const customFieldValue = {
                        name: section.customField.name,
                        submissionValue: section.submission?.submission?.value ? value : '-',
                    };
                    if (customFieldValue.submissionValue) {
                        customFieldsValues.push(customFieldValue);
                    }
                });
            }
        }

        if (isNil(personnelStatus)) {
            return (Promise as any).allSettled([
                this.personnelDetailsPDFService.getPersonnelDetailPdfFile(
                    account,
                    originalPersonnel,
                    documentsToDownload,
                    doneTickets,
                    customFieldsValues,
                ),
                ...documentsToDownload.map(document =>
                    this.getPersonnelDocument(account, user, document),
                ),
            ]);
        }

        if (
            personnelStatus === AuditorPersonnelStatus.CURRENT ||
            personnelStatus === AuditorPersonnelStatus.HIRED
        ) {
            return (Promise as any).allSettled([
                this.personnelDetailsPDFService.getPersonnelDetailPdfFile(
                    account,
                    originalPersonnel,
                    documentsToDownload,
                    doneTickets,
                    customFieldsValues,
                    personnelStatus,
                ),
                ...documentsToDownload.map(document =>
                    this.getPersonnelDocument(account, user, document),
                ),
            ]);
        }

        if (personnelStatus === AuditorPersonnelStatus.FORMER) {
            return (Promise as any).allSettled([
                this.personnelDetailsPDFService.getPersonnelDetailPdfFile(
                    account,
                    originalPersonnel,
                    documentsToDownload,
                    doneTickets,
                    customFieldsValues,
                    personnelStatus,
                ),
                ...documentsToDownload.map(document =>
                    this.getPersonnelDocument(account, user, document),
                ),
            ]);
        }
    }

    /**
     *
     * @param account
     * @param user
     * @param document
     * @returns
     */
    private async getPersonnelDocument(
        account: Account,
        user: User,
        document: UserDocument,
    ): Promise<FileBufferType> {
        const documentTypePrefix = DocumentTypePrefix.get(document.type);

        const documents = await this.downloader.getDownloadUrl(document.file);

        // document prefix is to avoid file overwriting
        // when there are two files with the same name
        const filename = documentTypePrefix + getFilenameFromDirectory(document.file);

        const file = await this.httpService
            .get(documents.signedUrl, { responseType: 'arraybuffer' })
            .toPromise();

        return {
            stream: file.data,
            filename,
        };
    }

    /**
     *
     * @param account
     * @param personnelId
     * @returns
     */
    async getPersonnelDetailsDocumentsZip(
        account: Account,
        personnelId: number,
        requestorUser: User,
    ): Promise<DownloaderPayloadType> {
        const originalPersonnel = await this.getPersonnelForPDFDownload(personnelId);

        const { user } = originalPersonnel;

        const zipData = await this.getPersonnelDetailsZip(
            originalPersonnel,
            account,
            requestorUser,
        );

        const userName = `${user.firstName} ${user.lastName}`;

        const fileName = sanitizeFileName(`${fileNameDate()}-${userName.replace(' ', '_')}.zip`);

        const allZip = await createZipBuffer(flatten(zipData));

        const uploadedFile = await this.uploader.uploadPrivateFileFromBuffer(
            account.id,
            UploadType.EVENT,
            allZip,
            fileName,
            config.get('archive.contentType'),
        );

        return this.downloader.getDownloadUrl(uploadedFile.key);
    }

    async getPersonnelDetailsZip(
        originalPersonnel: Personnel,
        account: Account,
        requestorUser: User,
        personnelStatus?: AuditorPersonnelStatus,
    ): Promise<FileBufferType[]> {
        const { user } = originalPersonnel;

        const hiredDocTypes = [UserDocumentType.SEC_TRAINING];

        const formerDocTypes = [UserDocumentType.OFFBOARDING_EVIDENCE];

        const docTypes = [
            UserDocumentType.SEC_TRAINING,
            UserDocumentType.PASSWORD_MANAGER_EVIDENCE,
            UserDocumentType.AUTO_UPDATES_EVIDENCE,
            UserDocumentType.HARD_DRIVE_ENCRYPTION_EVIDENCE,
            UserDocumentType.ANTIVIRUS_EVIDENCE,
            UserDocumentType.LOCK_SCREEN_EVIDENCE,
            UserDocumentType.MFA_EVIDENCE,
        ];

        const isHipaaFrameworkEnabled = await this.companiesCoreService.getIsTrainingEnabled(
            TrainingType.HIPAA_TRAINING,
        );

        if (isHipaaFrameworkEnabled) {
            docTypes.push(UserDocumentType.HIPAA_TRAINING_EVIDENCE);
        }

        const isNistAiFrameworkEnabled = await this.companiesCoreService.getIsTrainingEnabled(
            TrainingType.NIST_AI_TRAINING,
        );

        if (isNistAiFrameworkEnabled) {
            docTypes.push(UserDocumentType.NIST_AI_TRAINING_EVIDENCE);
        }

        const isStrictFormer = allStrictFormerStatuses.includes(originalPersonnel.employmentStatus);

        if (isNil(personnelStatus) && isStrictFormer) {
            docTypes.push(UserDocumentType.OFFBOARDING_EVIDENCE);
        }

        let offboardingTicketsFiles = [];
        if (isNil(personnelStatus)) {
            offboardingTicketsFiles = await this.personnelTicketService.getTicketsFiles(
                account,
                [originalPersonnel.id],
                requestorUser,
            );
        }

        let usedDocTypes = [];
        if (personnelStatus === AuditorPersonnelStatus.HIRED) {
            usedDocTypes = hiredDocTypes;
        } else if (personnelStatus === AuditorPersonnelStatus.FORMER) {
            usedDocTypes = formerDocTypes;
        } else {
            usedDocTypes = docTypes;
        }

        const documentsToDownload = await this.getDocumentsToDownload(
            originalPersonnel,
            usedDocTypes,
        );

        const personnelSecTrainingComplianceChecks = await this.getTrainingChecksForPersonnel(
            TrainingCampaignType.SECURITY_TRAINING,
            originalPersonnel.id,
        );

        originalPersonnel.securityTrainingComplianceChecks =
            personnelSecTrainingComplianceChecks?.securityTrainingComplianceChecks ?? [];

        const doneTickets = await this.getTicketsDoneForUserByType(
            originalPersonnel.user.id,
            PersonnelTicketType.OFFBOARDING,
        );

        let personnelData = null;
        try {
            personnelData = await this.personnelDetailPdfFile(
                personnelStatus,
                originalPersonnel,
                documentsToDownload,
                account,
                doneTickets,
                user,
            );
        } catch (error) {
            this.error(error, account, { user });
        }

        personnelData = personnelData
            .filter(({ status }) => status === 'fulfilled')
            .map(({ value }) => value);

        return isNil(personnelStatus)
            ? [...personnelData, ...offboardingTicketsFiles]
            : personnelData;
    }

    async getDocumentsToDownload(personnel: Personnel, docTypes: UserDocumentType[]): Promise<any> {
        const response = [];
        const filteredOutDeviceDocTypes = docTypes.filter(
            d => !DeviceDocument.getDocumentTypes().includes(d),
        );

        if (!isEmpty(filteredOutDeviceDocTypes)) {
            const filteredOutresponse = await this.userDocumentRepository.getAllDocuments(
                personnel.user.id,
                filteredOutDeviceDocTypes,
            );

            response.push(...filteredOutresponse);
        }

        const filteredInDeviceDocTypes = docTypes.filter(d =>
            DeviceDocument.getDocumentTypes().includes(d),
        );

        if (!isEmpty(filteredInDeviceDocTypes)) {
            const filteredInresponse = await this.deviceDocumentRepository.getAllDevicesDocuments(
                personnel.id,
                filteredInDeviceDocTypes,
            );

            response.push(...filteredInresponse);
        }

        return response;
    }

    /**
     *
     * @param user
     * @param account
     * @param requestDto
     */
    async createSpecialFormerPersonnel(
        user: User,
        account: Account,
        requestDto: PersonnelSpecialRequestDto,
    ): Promise<Personnel> {
        const personnelUser = await this.createSpecialFormerUserByEmail(requestDto, account);
        let personnel = new Personnel();
        const personnelData = new PersonnelData();
        personnelData.osVersion = null;
        personnelData.serialNumber = null;
        personnel.user = personnelUser;
        personnel.data = personnelData;
        personnel.complianceChecks = [];
        this.populatePersonnel(personnel, requestDto);
        personnel = await this.savePersonnel(personnel);
        const neededCompliance = [
            ComplianceCheckType.FULL_COMPLIANCE,
            ComplianceCheckType.BG_CHECK,
            ComplianceCheckType.SECURITY_TRAINING,
            ComplianceCheckType.HIPAA_TRAINING,
            ComplianceCheckType.OFFBOARDING,
            ComplianceCheckType.NIST_AI_TRAINING,
        ];

        await this.complianceChecksCoreService.createComplianceChecks(neededCompliance, personnel);

        this._eventBus.publish(new FormerPersonnelAddedEvent(account, user, personnel));

        return personnel;
    }

    /**
     *
     * @param {User} user
     * @param {Account} account
     * @param {PersonnelSpecialRequestDto} requestDto
     * @param {number} personnelId
     * @return {Promise<ExtendedPersonnel>}
     */
    async updateSpecialFormerPersonnel(
        user: User,
        account: Account,
        requestDto: PersonnelSpecialRequestDto,
        personnelId: number,
    ): Promise<ExtendedPersonnel> {
        const personnel = await this.personnelRepository.findOneOrFail({
            where: { id: personnelId },
        });
        if (!isInASpecialStatus(personnel.employmentStatus)) {
            throw new ConflictException('This personnel cannot be updated');
        }
        this.populatePersonnel(personnel, requestDto);
        await this.populatePersonnelUser(account, personnel.user, requestDto);
        const updatedPersonnel = await this.savePersonnel(personnel);

        const personnelComplianceCheckWithExclusions =
            await this.getComplianceChecksWithExclusionsForPersonnel(personnel);

        updatedPersonnel.complianceChecksWithExclusions = personnelComplianceCheckWithExclusions;

        return updatedPersonnel;
    }

    /**
     *
     * @param requestDto
     * @returns
     */
    async getAllPersonnelForCsvReport(
        requestDto: PersonnelRequestDto,
        account: Account,
        user: User,
    ): Promise<ConditionalCsvDataSetType<Personnel>> {
        /**
         * heavy process need to measure time
         */
        const benchmark = new Benchmark();
        benchmark.start();

        try {
            const personnel: ExtendedPersonnel[] = await unfoldAsync(async page => {
                const { data } = await this.personnelRepository.listPersonnel({
                    ...requestDto,
                    limit: 100,
                    page,
                });

                const hasCustomFieldsEnabled = await isCustomFieldsEnabled(account);

                if (hasCustomFieldsEnabled) {
                    await this.customFieldsSubmissionCsvCoreService.getCustomFieldsAndSubmissionsColumnsForCSVPersonnel(
                        data,
                        account,
                    );
                }
                return isEmpty(data) ? undefined : [data, page + 1];
            }, 1).then(flatten);

            for (const person of personnel) {
                const devices = filterActiveDevices(person);
                person.devices = devices;
                person.devicesCount = devices.length;
                person.complianceChecks =
                    // eslint-disable-next-line no-await-in-loop
                    await this.getComplianceChecksWithExclusionsForPersonnel(person);

                if (
                    !person.complianceChecks.some(
                        check => check.type === ComplianceCheckType.FULL_COMPLIANCE,
                    )
                ) {
                    this.logger.warn(
                        PolloAdapter.acct(
                            'Found personnel without full compliance',
                            account,
                        ).setIdentifier({ id: person.id, compliance: person.complianceChecks }),
                    );
                }
            }

            const [todaysDate] = new Date().toISOString().split('T');
            const filename = format(config.get('reports.personnelReportFileName'), todaysDate);

            this.sendUserDocumentDownloadedEvent(account, user, filename);

            benchmark.end();
            /**
             * https://drata.atlassian.net/browse/ENG-39247
             */
            this.log(`getAllPersonnelForCsvReport completed`, account, {
                requestDto,
                totalPersonnel: personnel.length,
                executionTime: benchmark.timeString(),
            });

            return {
                data: personnel,
                filename,
                conditions: [
                    await this.companiesCoreService.getIsTrainingEnabled(
                        TrainingType.HIPAA_TRAINING,
                    ),

                    await this.companiesCoreService.getIsTrainingEnabled(
                        TrainingType.NIST_AI_TRAINING,
                    ),
                ],
            };
        } catch (error) {
            /**
             * https://drata.atlassian.net/browse/ENG-39247
             */
            this.logger.error(
                PolloAdapter.acct(
                    'Something went wrong while generating personnel csv report',
                    account,
                )
                    .setContext(this.constructor.name)
                    .setSubContext(this.getAllPersonnelForCsvReport.name)
                    .setError(error),
            );

            /**
             * throw exception - we don't need to return a response
             */
            throw new InternalServerErrorException();
        }
    }

    async getPersonnelReport(
        dto: PersonnelRequestDto,
        account: Account,
        user: User,
        requestMetadata: ExtractedRequestProps,
    ): Promise<DownloaderPayloadType | string> {
        return this.getPersonnelReportByWorkflow(dto, account, user, requestMetadata);
    }

    private async getPersonnelReportByWorkflow(
        dto: PersonnelRequestDto,
        account: Account,
        user: User,
        requestMetadata: ExtractedRequestProps,
    ): Promise<string> {
        const { url = '', method = '', requestId = '' } = requestMetadata;

        this.logger.log(
            PolloAdapter.acct('Generating personnel report in temporal', account)
                .setAccountId(account.id)
                .setSubContext(this.getPersonnelReportByWorkflow.name),
        );
        const temporalClient = await getTemporalClient(account.domain);
        const { workflowId } = await temporalClient.startWorkflow(personnelReportsCsvWorkflowV1, {
            taskQueue: config.get('temporal.taskQueues.temporal-default'),
            args: [
                {
                    dto,
                    account,
                    user,
                    requestMetadata: { url, method, requestId },
                },
            ],
            memo: { accountId: account.id, domain: account.domain },
        });
        return workflowId;
    }

    async getAllPersonnelForPreAuditPackage(): Promise<Personnel[]> {
        return this.personnelRepository.getAllPersonnelForPreAuditPackage();
    }

    /**
     * deprecated use PersonnelCoreService.resetTrainingDocuments
     **/
    async resetTrainingDocuments(
        account: Account,
        complianceCheck: ComplianceCheckType,
        user: User,
    ): Promise<void> {
        let userDocumentType: UserDocumentType;

        if (complianceCheck === ComplianceCheckType.SECURITY_TRAINING) {
            userDocumentType = UserDocumentType.SEC_TRAINING;
        } else if (complianceCheck === ComplianceCheckType.HIPAA_TRAINING) {
            userDocumentType = UserDocumentType.HIPAA_TRAINING_EVIDENCE;
        } else if (complianceCheck === ComplianceCheckType.NIST_AI_TRAINING) {
            userDocumentType = UserDocumentType.NIST_AI_TRAINING_EVIDENCE;
        }

        const userDocuments = await this.userDocumentRepository.find({
            where: {
                type: userDocumentType,
                user: { id: user.id },
            },
        });

        if (!isEmpty(userDocuments)) {
            this.logger.log(
                PolloAdapter.acct(
                    `Security Training documents found for user with id ${user.id},
                    deleting ${userDocuments.length} documents...`,
                    account,
                ),
            );

            for (const document of userDocuments) {
                document.renewalDate = new Date().toISO8601String();

                // eslint-disable-next-line no-await-in-loop
                await this.userDocumentRepository.save(document);
            }
        }
    }

    /**
     * @deprecated use PersonnelCoreService.resetTrainingDocumentsForUsers
     */
    async resetTrainingDocumentsForUsers(
        account: Account,
        complianceCheck: ComplianceCheckType,
        userIds: number[],
    ): Promise<void> {
        let userDocumentType: UserDocumentType;

        if (complianceCheck === ComplianceCheckType.SECURITY_TRAINING) {
            userDocumentType = UserDocumentType.SEC_TRAINING;
        } else if (complianceCheck === ComplianceCheckType.HIPAA_TRAINING) {
            userDocumentType = UserDocumentType.HIPAA_TRAINING_EVIDENCE;
        } else if (complianceCheck === ComplianceCheckType.NIST_AI_TRAINING) {
            userDocumentType = UserDocumentType.NIST_AI_TRAINING_EVIDENCE;
        }

        const userDocuments = await this.userDocumentRepository.getAllDocumentsOfTypeManyUsers(
            userDocumentType,
            userIds,
        );

        if (isEmpty(userDocuments)) {
            return;
        }

        //organize documents by user after having received all docs
        const userDocumentsByUser = userIds.map(id => ({
            userId: id,
            documents: userDocuments.filter(document => document.user.id === id),
        }));

        await promiseAllInBatches(userDocumentsByUser, 5, async currentUser => {
            this.logger.log(
                PolloAdapter.acct(
                    `Security Training documents found for user with id ${currentUser.userId},
                    deleting ${currentUser.documents.length} documents...`,
                    account,
                ),
            );

            await this.userDocumentRepository.update(
                {
                    id: In(currentUser.documents.map(document => document.id)),
                },
                {
                    renewalDate: new Date().toISO8601String(),
                },
            );
        });
    }

    /**
     * @deprecated use ComplianceChecksOrchestrationService.resetTraining
     */
    async resetTraining(
        account: Account,
        user: User,
        complianceCheckType: ComplianceCheckType,
        personnelId: number,
    ): Promise<ExtendedPersonnel> {
        const personnel = await this.getPersonnelByIdWithUser(personnelId);

        await this.updateComplianceForTraining(account, personnel, complianceCheckType);

        this.sendTrainingEvent(account, user, personnel.user, complianceCheckType);

        return this.getPersonnelWithComplianceTests(account, personnelId); // get updated data
    }

    /**
     * @deprecated use ComplianceChecksOrchestrationService.resetTrainingForMultiple
     */
    async resetTrainingForMultiple(
        account: Account,
        user: User,
        complianceCheckType: ComplianceCheckType,
    ): Promise<void> {
        const personnel =
            await this.personnelRepository.getCurrentPersonnelFullyCompliance(complianceCheckType);

        await this.updateComplianceForTrainingForMultiple(account, personnel, complianceCheckType);

        this.sendTrainingEvent(account, user, {} as User, complianceCheckType);
    }

    /**
     *
     * @param {Account} account
     * @param {User} user
     * @param {ComplianceCheckType} complianceCheckType
     */
    async resetTrainingForAll(
        account: Account,
        user: User,
        complianceCheckType: ComplianceCheckType,
    ): Promise<void> {
        const activePersonnel = await this.personnelRepository.getCurrentPersonnel();

        await this.updateComplianceForTrainingForMultiple(
            account,
            activePersonnel,
            complianceCheckType,
        );

        this.sendTrainingEvent(account, user, {} as User, complianceCheckType);
    }

    async getCurrentPersonnel(): Promise<Personnel[]> {
        return this.personnelRepository.getCurrentPersonnel();
    }

    /**
     * @deprecated Use PersonnelCoreService.findAllByIds
     *
     * @param {number[]} ids
     * @returns {Promise<Personnel[]>}
     */
    findAllByIds(ids: number[]): Promise<Personnel[]> {
        return this.personnelRepository.findAllByIds(ids);
    }

    /**
     * @deprecated Use PersonnelCoreService.findAllForAuditValidation
     */
    findAllForAuditValidation(ids: number[]): Promise<Personnel[]> {
        return this.personnelRepository.findAllForAuditValidation(ids);
    }

    /**
     * @deprecated Use PersonnelCoreService.getPersonnelIdsByFilters
     *
     * @param requestDto
     * @returns
     */
    getPersonnelIdsByFilters(
        requestDto: Partial<PersonnelBulkActionRequestDto>,
    ): Promise<number[]> {
        return this.personnelRepository.getPersonnelIdsByFilters(requestDto);
    }

    /**
     *
     * @param {string} firstDate
     * @param {string} secondDate
     * @returns {Promise<Personnel[]>}
     */
    findAllFormerPersonnelByStatusUpdatedAtDateRange(
        firstDate: string,
        secondDate: string,
    ): Promise<Personnel[]> {
        return this.personnelRepository.findAllFormerPersonnelByStatusUpdatedAtDateRange(
            firstDate,
            secondDate,
        );
    }

    /**
     * @deprecated Use PersonnelCoreService.listCurrentNonCompliantPersonnel
     *
     * @param {EmploymentStatusOptionsFilter} statuses
     * @returns {Promise<Personnel[]>}
     */
    async listCurrentNonCompliantPersonnel(): Promise<Personnel[]> {
        const nonCompliantPersonnel =
            await this.personnelRepository.getCurrentNonCompliantPersonnel();
        return this.personnelRepository.getPersonnelForNotifications(nonCompliantPersonnel);
    }

    /**
     * @deprecated use ComplianceCheckOrchestrationService.updateComplianceForTraining
     */
    async updateComplianceForTraining(
        account: Account,
        personnel: Personnel,
        complianceCheckType: ComplianceCheckType,
    ): Promise<void> {
        const { user, id: personnelId } = personnel;

        await Promise.all([
            this.complianceChecksOrchestrationService.failComplianceCheck(
                user,
                complianceCheckType,
                { account },
            ),

            this.resetTrainingDocuments(account, complianceCheckType, user),

            this.securityTrainingComplianceChecksCoreService.resetTrainingsChecks(
                personnelId,
                complianceCheckType,
            ),
        ]);
    }

    /**
     * @deprecated use ComplianceCheckOrchestrationService.updateComplianceForTrainingForMultiple
     */
    async updateComplianceForTrainingForMultiple(
        account: Account,
        multiplePersonnel: Personnel[],
        complianceCheckType: ComplianceCheckType,
    ): Promise<void> {
        const personnelIds: number[] = [];
        const users: User[] = [];

        for (const personnel of multiplePersonnel) {
            const { id, user } = personnel;
            personnelIds.push(id);
            users.push(user);
        }

        await Promise.all([
            this.complianceChecksCoreService.failComplianceCheckForUsers(
                users,
                complianceCheckType,
            ),
            this.securityTrainingComplianceChecksCoreService.resetTrainingChecksForManyPersonnel(
                personnelIds,
                complianceCheckType,
            ),
            this.resetTrainingDocumentsForUsers(
                account,
                complianceCheckType,
                users.map(user => user.id),
            ),
        ]);
    }

    private async manualUploadExists(
        checkType: ComplianceCheckType,
        userId: number,
        account: Account,
    ): Promise<boolean> {
        let userDocumentType = null;

        // loop over map to find the UserDocumentType for the ComplianceCheckType
        for (const [key, value] of UserDocumentTypeToComplianceCheckType.entries()) {
            if (value === checkType) {
                userDocumentType = key;
                break;
            }
        }
        if (isNil(userDocumentType)) {
            return false;
        }
        const existingDocuments = await this.usersService.listAllDocumentsOfType(
            userId,
            userDocumentType,
        );
        if (isEmpty(existingDocuments)) {
            return false;
        }
        this.log(
            // eslint-disable-next-line max-len
            `Manually uploaded data found for (user id, document type, compliance type) = (${userId}, ${UserDocumentType[userDocumentType]}, ${ComplianceCheckType[checkType]})`,
            account,
        );
        return true;
    }

    /**
     * @deprecated use PersonnelBulkActionCommerceService.areConnectedUsersToProviderType
     *
     * @param userIds
     * @param providerType
     * @returns
     */
    async areConnectedUsersToProviderType(
        userIds: number[],
        providerType: ProviderType,
    ): Promise<{ userId: number; isConnected: boolean }[]> {
        const userIdentitiesForMultipleUsers =
            await this.userIdentitiesService.getUserIdentitiesByClientTypesAndUserIds(
                userIds,
                getProviderTypeClientTypeMap().get(providerType),
            );
        return userIdentitiesForMultipleUsers.map(userData => {
            return {
                userId: userData.userId,
                isConnected: !isEmptyArray(userData.userIdentities),
            };
        });
    }

    private async isConnectedToProviderType(
        userId: number,
        providerType: ProviderType,
    ): Promise<boolean> {
        const userIdentity = await this.userIdentitiesService.getUserIdentitiesByClientTypes(
            userId,
            getProviderTypeClientTypeMap().get(providerType),
        );
        return !isEmptyArray(userIdentity);
    }

    /**
     * @deprecated use PersonnelBulkActionCommerceService.areConnectedToHRIS
     *
     * @param userIds
     * @returns
     */
    areConnectedToHRIS(userIds: number[]): Promise<{ userId: number; isConnected: boolean }[]> {
        return this.areConnectedUsersToProviderType(userIds, ProviderType.HRIS);
    }

    private isConnectedToHRIS(userId: number): Promise<boolean> {
        return this.isConnectedToProviderType(userId, ProviderType.HRIS);
    }

    private async validateUpdateStatus(
        dto: PersonnelStatusRequestDto,
        personnel: Personnel,
    ): Promise<boolean> {
        const isConnectedToHRIS = await this.isConnectedToHRIS(personnel.user.id);
        if (isConnectedToHRIS) {
            checkPersonnelValidForUpdate(dto.employmentStatus, personnel);
            return true;
        }
    }

    /**
     *
     * @param {PersonnelBulkActionStatusRequestDto} dto
     * @param {Personnel[]} multiplePersonnel
     * @returns {Promise<{validPersonnel: Personnel[];errors: BulkActionErrorType[];}>}
     */
    private async validateUpdateStatusForMultiplePersonnel(
        dto: PersonnelBulkActionStatusRequestDto,
        multiplePersonnel: Personnel[],
    ): Promise<{
        validPersonnel: Personnel[];
        errors: BulkActionErrorType[];
    }> {
        const validPersonnel = [];
        const errors = [];
        const areConnectedToHRIS = await this.areConnectedToHRIS(
            multiplePersonnel.map(p => p.user.id),
        );

        const arePersonnelConnectedToHRIS = areConnectedToHRIS.map(ui => {
            return {
                ...ui,
                personnel: multiplePersonnel.find(mp => mp.user.id === ui.userId),
            };
        });

        for (const { isConnected: isConnectedToHRIS, personnel } of arePersonnelConnectedToHRIS) {
            try {
                if (isConnectedToHRIS) {
                    checkPersonnelValidForUpdate(
                        dto.employmentStatus,
                        personnel,
                        isEmpty(dto.startDate),
                    );
                    validPersonnel.push(personnel);
                } else if (isInASpecialStatus(personnel.employmentStatus)) {
                    throw new OurConflictException(
                        `Cannot update the employment status of a special former personnel.`,
                        ErrorCode.PERSONNEL_IS_FORMER_SPECIAL,
                    );
                }
                validPersonnel.push(personnel);
            } catch (exception) {
                errors.push({
                    error: exception,
                    targetId: personnel.id,
                });
            }
        }
        return { validPersonnel, errors };
    }

    /**
     *
     * @param dto
     * @param account
     */
    private async createSpecialFormerUserByEmail(
        dto: PersonnelSpecialRequestDto,
        account: Account,
    ): Promise<User> {
        const { email } = dto;

        // get entry and if there is one bail out
        let entry = await this.entryCoreService.getEntryWithoutRelationsByEmail(email);
        if (!isNil(entry)) {
            throw new ConflictException('User already exists');
        }

        entry = await this.entryCoreService.createEntryByEmail(email, account);
        return this.usersService.createSpecialFormerEmployeeUser(dto, entry, account);
    }

    /**
     * @deprecated Use PersonnelOrchestrationService.createAdminEntryByEmail
     *
     * @param dto
     * @param account
     * @returns
     */
    async createAdminEntryByEmail(dto: BaseUserDataType, account: Account): Promise<Entry> {
        const { email } = dto;

        // get entry and if there is one bail out
        let entry = await this.entryCoreService.getEntryWithoutRelationsByEmail(email);
        if (!isNil(entry)) {
            throw new ConflictException('User already exists');
        }

        entry = await this.entryCoreService.createEntryByEmail(email, account);
        return entry;
    }

    /**
     *
     * @param personnel
     * @param requestDto
     */
    private populatePersonnel(personnel: Personnel, requestDto: PersonnelSpecialRequestDto) {
        const { status, separationDate, hireDate } = requestDto;
        personnel.employmentStatus = status;
        personnel.separationDate = separationDate;
        personnel.startDate = hireDate;
    }

    /**
     *  @deprecated use ComplianceCheckOrchestrationService.logSaveAgentOperation
     **/
    private logSaveAgentOperation(
        account: Account,
        message: string,
        identifier: unknown,
        benchmark: Benchmark,
    ): void {
        const pollogLog = PolloAdapter.acct(message, account, this.constructor.name).setSubContext(
            account.companyName,
        );
        if (!isNil(identifier)) {
            pollogLog.setIdentifier(identifier);
        }
        if (!isNil(benchmark)) {
            pollogLog.setExecutionTime(benchmark.time());
        }
        this.logger.log(pollogLog);
    }

    private async populatePersonnelUser(
        account: Account,
        user: User,
        requestDto: PersonnelSpecialRequestDto,
    ): Promise<void> {
        const { email, firstName, lastName } = requestDto;
        const originalEmail = user.email;

        if (!iEquals(originalEmail, email)) {
            // get entry by the new email and if there is one bail out
            const entryFromDto = await this.entryCoreService.getEntryWithoutRelationsByEmail(email);
            if (!isNil(entryFromDto)) {
                throw new ConflictException('Email already exists');
            }

            // update the user's email
            user.email = email;
            // update the entry's email
            const entry =
                await this.entryCoreService.getEntryWithoutRelationsByEmailOrFail(originalEmail);
            entry.email = email;
            await this.entryCoreService.saveEntry(entry);
        }
        user.firstName = firstName;
        user.lastName = lastName;
        await this.usersService.saveUser(user, account.id);
    }

    /**
     *
     * @param personnel
     */
    isArchivable(personnel: Personnel): boolean {
        return isInAFormerStatus(personnel.employmentStatus) && !isOutOfScopeAdmin(personnel);
    }

    private async validatePersonnelAndContractDates(
        personnel: Personnel,
        userId: number,
        dto: PersonnelContractDateRequestDto,
    ) {
        if (!dto.startDate) {
            throw new BadRequestException(`Start date is required`);
        }

        if (isFutureDateString(dto.separationDate)) {
            throw new BadRequestException(`Cannot set separation date to a date in future`);
        }

        if (
            dto.startDate &&
            dto.separationDate &&
            isFirstDateBeforeSecond(dto.separationDate, dto.startDate)
        ) {
            throw new BadRequestException(`Start date must be before separation date`);
        }

        if (personnel.employmentStatus === EmploymentStatus.FUTURE_HIRE && dto.separationDate) {
            throw new BadRequestException(
                `Cannot set separation date on ${
                    EmploymentStatus[personnel.employmentStatus]
                } status`,
            );
        }

        if (
            isInACurrentStatus(personnel.employmentStatus) &&
            isFutureDateString(dto.startDate) &&
            dto.separationDate
        ) {
            throw new BadRequestException(
                `Cannot update start date to future date when separation date is included in request`,
            );
        }

        if (isUnknownStatus(personnel.employmentStatus)) {
            throw new ConflictException(
                `Cannot update dates on ${EmploymentStatus[personnel.employmentStatus]} status`,
            );
        }

        if (
            (isInAStrictFormerStatus(personnel.employmentStatus) ||
                isInASpecialFormerStatus(personnel.employmentStatus)) &&
            !dto.separationDate &&
            !isFutureDateString(dto.startDate)
        ) {
            throw new BadRequestException(
                `Separation date is required for ${
                    EmploymentStatus[personnel.employmentStatus]
                } status`,
            );
        }

        if (userId === personnel.user.id) {
            if (!isEmpty(dto.separationDate) || !isEmpty(personnel.separationDate)) {
                throw new ConflictException(`Cannot update separation date on self`);
            }

            if (momentWithoutTime(dto.startDate) > momentWithoutTime()) {
                throw new ConflictException(`Cannot set start date on a future date on self`);
            }
        }

        const isConnectedToHRIS = await this.isConnectedToHRIS(personnel.user.id);

        if (isNil(personnel.statusUpdatedAt) && isConnectedToHRIS) {
            throw new ConflictException(
                'Cannot update contract dates when person is already on HRIS',
            );
        }
    }

    /**
     * @deprecated Use ComplianceChecksOrchestrationService.updateAllPersonnelFullyComplianceCheck
     */
    async updateAllPersonnelFullyComplianceCheck(): Promise<void> {
        const allPersonnel = await this.personnelRepository.find();
        for (const personnel of allPersonnel) {
            // eslint-disable-next-line no-await-in-loop
            await this.complianceChecksOrchestrationService.updateFullCompliance(personnel.user);
        }
    }

    /**
     * @deprecated Use PersonnelCoreService.sendTrainingEvent
     *
     * @param account
     * @param user
     * @param target
     * @param complianceCheck
     */
    public sendTrainingEvent(
        account: Account,
        user: User,
        target: User,
        complianceCheck: ComplianceCheckType,
    ): void {
        switch (complianceCheck) {
            case ComplianceCheckType.SECURITY_TRAINING:
                this._eventBus.publish(
                    new SecurityAwarenessTrainingResetEvent(account, user, target),
                );
                break;
            case ComplianceCheckType.HIPAA_TRAINING:
                this._eventBus.publish(new HipaaTrainingResetEvent(account, user, target));
                break;
            case ComplianceCheckType.NIST_AI_TRAINING:
                this._eventBus.publish(new NistAiTrainingResetEvent(account, user, target));
                break;
            default:
                break;
        }
    }

    /**
     * @deprecated Use ComplianceCheckOrchestrationService.saveSecurityTrainingData
     *
     * @param {Personnel} personnel
     * @param {Account} account
     * @param {TrainingCampaign} trainingCampaign
     * @param {SecurityTrainingData} data
     * @param {string} fileName
     * @param {boolean} isInitialSync
     */
    async saveSecurityTrainingData(
        personnel: Personnel,
        account: Account,
        trainingCampaign: TrainingCampaign,
        data: SecurityTrainingData,
        fileName: string,
        fileNameWithoutExtension: string,
        company: Company,
        isInitialSync?: boolean,
    ): Promise<void> {
        const { isCompliance, url } = data;

        await this.securityTrainingComplianceChecksCoreService.saveTrainingComplianceCheckByPersonnel(
            account,
            trainingCampaign,
            personnel,
            data,
            fileNameWithoutExtension,
        );

        const { securityTraining, hipaaTraining, nistaiTraining } = company;
        const isSecurityTraining =
            trainingCampaign.type === TrainingCampaignType.SECURITY_TRAINING &&
            [
                SecurityTrainingType.KNOWBE4,
                SecurityTrainingType.CURRICULA,
                ...StackOneSecurityTrainingTypes,
            ].includes(securityTraining ?? 0);
        const isHipaaTraining =
            trainingCampaign.type === TrainingCampaignType.HIPAA_TRAINING &&
            [HipaaTrainingType.KNOWBE4].includes(hipaaTraining);
        const isNistAiTraining =
            trainingCampaign.type === TrainingCampaignType.NIST_AI_TRAINING &&
            [NistAITrainingType.KNOWBE4].includes(nistaiTraining);

        const canUploadUserDocumentFromUrl = isCompliance && !isNil(url);

        const shouldUploadUserDocumentFromUrl = canUploadUserDocumentFromUrl
            ? isSecurityTraining || isHipaaTraining || isNistAiTraining
            : false;

        if (shouldUploadUserDocumentFromUrl) {
            const userDocumentType = UserDocumentTypeByTrainingCampaignType.get(
                trainingCampaign.type,
            );

            /**
             * verify if document should be saved locally
             */
            const userHasValidTrainingDocument = await this.userHasValidTrainingDocument(
                trainingCampaign.type,
                personnel.user,
                fileNameWithoutExtension,
            );

            if (!userHasValidTrainingDocument || isInitialSync) {
                const benchmark = new Benchmark();
                await this.usersService.uploadUserDocumentFromUrl(
                    url,
                    fileName,
                    userDocumentType,
                    account,
                    personnel.user,
                    data,
                );
                benchmark.end();

                this.logSaveAgentOperation(
                    account,
                    `Upload user document operation benchmark`,
                    {
                        personnelId: personnel.id,
                    },
                    benchmark,
                );
            }
        }
    }

    async deleteSecurityTrainingChecksByPersonnel(
        campaignId: number,
        personnelIdsToExclude: number[],
    ): Promise<void> {
        await this.securityTrainingComplianceChecksCoreService.deleteSecurityTrainingChecksByPersonnel(
            campaignId,
            personnelIdsToExclude,
        );
    }

    /**
     * @deprecated use ComplianceCheckOrchestrationService.updateSecurityTrainingComplianceCheck
     *
     * @param account
     * @param connection
     * @param personnel
     * @returns
     */
    async updateSecurityTrainingComplianceCheck(
        account: Account,
        connection: ConnectionEntity,
        personnel: Personnel,
        securityTraining: SecurityTrainingType,
    ): Promise<void> {
        const securityTrainingCheck = personnel.complianceChecks.find(
            complianceCheck => complianceCheck.type === ComplianceCheckType.SECURITY_TRAINING,
        );

        const clientType = mapSecurityTrainingToClientType(securityTraining);
        if (isNil(clientType)) {
            this.log(`Security Training Fail Compliance Check ${personnel.user.email}`, account);

            return;
        }

        const personnelSecurityTrainingComplianceChecks =
            await this.trainingComplianceCheckRepository.getAssignmentChecksByPersonnel(
                personnel.id,
                { campaignType: TrainingCampaignType.SECURITY_TRAINING },
            );

        if (isEmpty(personnelSecurityTrainingComplianceChecks)) {
            this.log(
                `No SECURITY trainings where found for ${personnel.user.email}, skipping...`,
                account,
            );
            return;
        }

        const isSecurityTrainingFullyCompliant =
            await this.securityTrainingComplianceChecksCoreService.hasPersonnelAllCompletedAssignments(
                personnel.id,
                TrainingCampaignType.SECURITY_TRAINING,
                clientType,
            );

        if (isSecurityTrainingFullyCompliant) {
            const usersDocuments = await this.userDocumentRepository.getAllDocumentsOfTypeManyUsers(
                UserDocumentType.SEC_TRAINING,
                [personnel.user?.id] as number[],
            );
            const latestCompletionDate = getLatestTrainingCompletionDate(
                personnelSecurityTrainingComplianceChecks,
                usersDocuments,
            );

            if (
                securityTrainingCheck?.status === ComplianceCheckStatus.PASS &&
                latestCompletionDate <= securityTrainingCheck?.completionDate
            ) {
                this.log(
                    `The Security Training Compliance has already been passed, ${personnel.user.email}`,
                    account,
                );
                return;
            }

            await this.complianceChecksOrchestrationService.passComplianceCheck(
                personnel.user,
                ComplianceCheckType.SECURITY_TRAINING,
                latestCompletionDate,
                true,
            );

            this.log(`Security Training Pass Compliance Check ${personnel.user.email}`, account);
        } else {
            await this.complianceChecksOrchestrationService.failComplianceCheck(
                personnel.user,
                ComplianceCheckType.SECURITY_TRAINING,
                { account },
            );

            this.log(`Security Training Fail Compliance Check ${personnel.user.email}`, account);
        }
        // emit the event
        this._eventBus.publish(new SecurityTrainingUpdatedEvent(account, personnel, connection));
    }

    /**
     * @deprecated use ComplianceCheckOrchestrationService.updateHipaaTrainingComplianceCheck
     *
     * @param account
     * @param connection
     * @param personnel
     * @returns
     */
    async updateHipaaTrainingComplianceCheck(
        account: Account,
        connection: ConnectionEntity,
        personnel: Personnel,
        hipaaTraining: HipaaTrainingType,
    ): Promise<void> {
        const hipaaTrainingCheck = personnel.complianceChecks.find(
            complianceCheck => complianceCheck.type === ComplianceCheckType.HIPAA_TRAINING,
        );

        const clientType = mapHipaaTrainingToClientType(hipaaTraining);
        if (isNil(clientType)) {
            this.log(`Hipaa Training Fail Compliance Check ${personnel.user.email}`, account);

            return;
        }

        const personnelHipaaTrainingComplianceChecks =
            await this.trainingComplianceCheckRepository.getAssignmentChecksByPersonnel(
                personnel.id,
                { campaignType: TrainingCampaignType.HIPAA_TRAINING },
            );

        if (isEmpty(personnelHipaaTrainingComplianceChecks)) {
            this.log(
                `No HIPAA trainings where found for ${personnel.user.email}, skipping...`,
                account,
            );
            return;
        }

        const isHipaaTrainingFullyCompliant =
            await this.securityTrainingComplianceChecksCoreService.hasPersonnelAllCompletedAssignments(
                personnel.id,
                TrainingCampaignType.HIPAA_TRAINING,
                clientType,
            );

        if (isHipaaTrainingFullyCompliant) {
            const usersDocuments = await this.userDocumentRepository.getAllDocumentsOfTypeManyUsers(
                UserDocumentType.HIPAA_TRAINING_EVIDENCE,
                [personnel.user?.id] as number[],
            );
            const latestCompletionDate = getLatestTrainingCompletionDate(
                personnelHipaaTrainingComplianceChecks,
                usersDocuments,
            );

            if (
                hipaaTrainingCheck.status === ComplianceCheckStatus.PASS &&
                latestCompletionDate < hipaaTrainingCheck?.completionDate
            ) {
                this.log(
                    `The Hipaa Training Compliance has already been passed, ${personnel.user.email}`,
                    account,
                );

                return;
            }

            await this.complianceChecksOrchestrationService.passComplianceCheck(
                personnel.user,
                ComplianceCheckType.HIPAA_TRAINING,
                latestCompletionDate,
                true,
            );

            this.log(`Hipaa Training Pass Compliance Check ${personnel.user.email}`, account);
        } else {
            await this.complianceChecksOrchestrationService.failComplianceCheck(
                personnel.user,
                ComplianceCheckType.HIPAA_TRAINING,
                { account },
            );

            this.log(`Hipaa Training Fail Compliance Check ${personnel.user.email}`, account);
        }
        // emit the event
        this._eventBus.publish(new HipaaTrainingUpdatedEvent(account, personnel, connection));
    }

    /**
     * @deprecated use ComplianceCheckOrchestrationService.updateNistAiTrainingComplianceCheck
     *
     * @param account
     * @param connection
     * @param personnel
     * @returns
     */
    async updateNistAiTrainingComplianceCheck(
        account: Account,
        connection: ConnectionEntity,
        personnel: Personnel,
        nistAiTraining: NistAITrainingType,
    ): Promise<void> {
        const nistAiTrainingCheck = personnel.complianceChecks.find(
            complianceCheck => complianceCheck.type === ComplianceCheckType.NIST_AI_TRAINING,
        );

        const clientType = mapNistAiTrainingToClientType(nistAiTraining);
        if (isNil(clientType)) {
            this.log(`NistAI Training Fail Compliance Check ${personnel.user.email}`, account);

            return;
        }

        const personnelNistAITrainingComplianceChecks =
            await this.trainingComplianceCheckRepository.getAssignmentChecksByPersonnel(
                personnel.id,
                { campaignType: TrainingCampaignType.NIST_AI_TRAINING },
            );

        if (isEmpty(personnelNistAITrainingComplianceChecks)) {
            this.log(
                `No NIST_AI trainings where found for ${personnel.user.email}, skipping...`,
                account,
            );
            return;
        }

        const isNistAiTrainingFullyCompliant =
            await this.securityTrainingComplianceChecksCoreService.hasPersonnelAllCompletedAssignments(
                personnel.id,
                TrainingCampaignType.NIST_AI_TRAINING,
                clientType,
            );

        if (isNistAiTrainingFullyCompliant) {
            const usersDocuments = await this.userDocumentRepository.getAllDocumentsOfTypeManyUsers(
                UserDocumentType.NIST_AI_TRAINING_EVIDENCE,
                [personnel.user?.id] as number[],
            );
            const latestCompletionDate = getLatestTrainingCompletionDate(
                personnelNistAITrainingComplianceChecks,
                usersDocuments,
            );

            if (
                nistAiTrainingCheck.status === ComplianceCheckStatus.PASS &&
                latestCompletionDate < nistAiTrainingCheck?.completionDate
            ) {
                this.log(
                    `The NistAI Training Compliance has already been passed, ${personnel.user.email}`,
                    account,
                );

                return;
            }

            await this.complianceChecksOrchestrationService.passComplianceCheck(
                personnel.user,
                ComplianceCheckType.NIST_AI_TRAINING,
                latestCompletionDate,
                true,
            );

            this.log(`NistAI Training Pass Compliance Check ${personnel.user.email}`, account);
        } else {
            await this.complianceChecksOrchestrationService.failComplianceCheck(
                personnel.user,
                ComplianceCheckType.NIST_AI_TRAINING,
                { account },
            );

            this.log(`NistAI Training Fail Compliance Check ${personnel.user.email}`, account);
        }
        // emit the event
        this._eventBus.publish(new NistAiTrainingUpdatedEvent(account, personnel, connection));
    }

    getTicketsDoneForUserByType(userId: number, ticketType: PersonnelTicketType): Promise<any> {
        return this.personnelTicketRepository.getTicketsDoneForUserByType(userId, ticketType);
    }

    /**
     * @deprecated Use PersonnelCoreService.getTrainingChecksForPersonnel
     */
    async getTrainingChecksForPersonnel(
        trainingCampaignType: TrainingCampaignType,
        personnelId: number,
        clientType?: ClientType,
    ): Promise<Personnel> {
        return this.personnelRepository.getTrainingComplianceChecksPersonnel(
            trainingCampaignType,
            personnelId,
            clientType,
        );
    }

    async getCurrentPersonnelComplianceStats(): Promise<PersonnelStatusStats> {
        return this.personnelRepository.getCurrentPersonnelComplianceStats();
    }

    /**
     * @deprecated Use PersonnelOrchestrationService.createPolicyAcceptanceReport
     */
    async createPolicyAcceptanceReport(
        dto: PersonnelRequestDto,
        account: Account,
        user: User,
        personnelIds?: number[],
    ): Promise<CsvDataSetType> {
        let personnelList: Personnel[] = [];
        let totalItems = 0;
        let page = 1;

        do {
            const personnelPage =
                // eslint-disable-next-line no-await-in-loop
                await this.personnelRepository.getPolicyAcceptanceReportPersonnelData(
                    dto,
                    page,
                    100,
                    personnelIds,
                );

            page++;
            totalItems = personnelPage.total;
            personnelList = personnelList.concat(personnelPage.data);
        } while (personnelList.length !== totalItems);

        const policies =
            await this.policiesCoreService.getActiveFrameworkPoliciesWithCurrentPublishedVersion(
                true,
            );

        const sortedPolicies = policies.sort((policyA, policyB) =>
            policyA.name.localeCompare(policyB.name),
        );

        const todaysDate = new Date().toISOString().split('T')[0];

        const filename = `Personnel-Policy-Acknowledgment-${todaysDate}`;

        // Don't emit the event if report is for an audit
        if (!personnelIds) {
            this.sendUserDocumentDownloadedEvent(account, user, filename);
        }

        return {
            data: personnelList,
            filename,
            additionalData: { policies: sortedPolicies },
        };
    }

    async getPolicyAcceptanceReport(
        dto: PersonnelRequestDto,
        account: Account,
        user: User,
    ): Promise<DownloaderPayloadType> {
        try {
            const { data, additionalData, filename } = await this.createPolicyAcceptanceReport(
                dto,
                account,
                user,
            );

            const hasCustomFieldsEnabled = await isCustomFieldsEnabled(account);

            if (hasCustomFieldsEnabled) {
                await this.customFieldsSubmissionCsvCoreService.getCustomFieldsAndSubmissionsColumnsForCSVPersonnel(
                    data,
                    account,
                );
            }
            const list = data.map(personnel =>
                new PersonnelPolicyAcceptanceReportResponseDto().build(personnel, additionalData),
            );
            const csvBuffer = Buffer.from(
                stringify(list, {
                    header: true,
                    quoted: true,
                }),
                'utf8',
            );

            const uploadedFile = await this.uploader.uploadPrivateFileFromBuffer(
                String(account.id),
                UploadType.POLICY_ACCEPTANCE_CSV,
                csvBuffer,
                filename,
                MimeType.CSV_FILE,
            );

            return await this.downloader.getDownloadUrl(uploadedFile.key, {
                expirationTime: config.get('email.urlTimeToLive'),
            });
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(`getPolicyAcceptanceReport failed: ${error.message}`, account)
                    .setError(error)
                    .setIdentifier(error),
            );
            throw error;
        }
    }

    /**
     * @deprecated Use PersonnelOrchestrationService.sendUserDocumentDownloadedEvent
     */
    sendUserDocumentDownloadedEvent(account: Account, user: User, filename: string): void {
        if (shouldEmitEvent(account, user)) {
            this._eventBus.publish(
                new UserDocumentDownloadedEvent(account, user, filename, {
                    name: filename,
                } as UserDocument),
            );
        }
    }

    private async buildPersonnelStatusDtoForUpdate(
        personnel: ExtendedPersonnel,
        dto: PersonnelContractDateRequestDto,
    ): Promise<PersonnelStatusRequestDto> {
        let needToUpdateStatus = false;
        const personnelStatusDto = new PersonnelStatusRequestDto();

        needToUpdateStatus = needsStatusChange(
            personnel.employmentStatus,
            dto.startDate,
            dto.separationDate,
        );

        if (!needToUpdateStatus) {
            personnelStatusDto.employmentStatus = personnel.employmentStatus;
            personnelStatusDto.startDate = dto.startDate;
            personnelStatusDto.separationDate = dto.separationDate;
        } else {
            const updatedStatus = getSwitchedStatus(
                personnel.employmentStatus,
                dto.startDate,
                dto.status,
            );

            personnelStatusDto.employmentStatus = updatedStatus;
            personnelStatusDto.startDate = dto.startDate;

            if (updatedStatus === EmploymentStatus.FUTURE_HIRE) {
                personnelStatusDto.notHumanReason = 'This personnel has a future hire date.';
                personnelStatusDto.separationDate = null;
            } else {
                personnelStatusDto.separationDate = dto.separationDate;
            }

            // clean not human reason from former future hire status
            if (
                personnel.employmentStatus === EmploymentStatus.FUTURE_HIRE &&
                !isFutureDate(moment(dto.startDate).toDate())
            ) {
                personnelStatusDto.notHumanReason = null;
            }
        }
        return personnelStatusDto;
    }

    /**
     * @deprecated Use personnelCoreService.getActivePersonnelByComplianceCheckType
     */
    async getActivePersonnelByComplianceCheckType(
        pagination: PaginationRequestDto,
        complianceCheckType: ComplianceCheckType,
    ): Promise<PaginationType<Personnel>> {
        return this.personnelRepository.getActivePersonnelByComplianceCheckType(
            pagination,
            complianceCheckType,
        );
    }

    /**
     * @deprecated Use personnelCoreService.getPersonnelNamesFromAuditSampleDataByIds
     */
    async getPersonnelNamesFromAuditSampleDataByIds(personnelIds: number[]): Promise<string> {
        if (isEmpty(personnelIds)) {
            return NO_PERSONNEL_PROVIDED_FOR_AUDIT_SAMPLE;
        }

        const personnelList = await this.personnelRepository.getPersonnelNamesByIds(personnelIds);

        if (isEmpty(personnelList)) {
            throw new NotFoundException(ErrorCode.PERSONNEL_NOT_FOUND);
        }

        return personnelList.map(personnel => fullName(personnel.user)).join(', ');
    }

    /**
     * @deprecated Use ComplianceCheckOrchestrationService.userHasValidTrainingDocument
     *
     * logic that decides if a user already have a document we want
     * to upload or/and create locally.
     * @param trainingCampaignType
     * @param user
     * @param filename
     * @returns
     */
    async userHasValidTrainingDocument(
        trainingCampaignType: TrainingCampaignType,
        user: User,
        filename: string,
    ): Promise<boolean> {
        /**
         * find and compare if incoming certificate should be added
         */
        const userDocumentType = UserDocumentTypeByTrainingCampaignType.get(trainingCampaignType);
        const trainingDocuments = await this.usersService.listAllDocumentsOfType(
            user.id,
            userDocumentType,
        );

        /**
         * we only need to confirm that the same document exists
         * if it's deleted, expired or not valid, compute compliance check
         * will handle that
         */
        const trainingDocument = trainingDocuments.find(
            (doc: UserDocument) => doc.name === filename,
        );

        return !isEmpty(trainingDocument);
    }

    /**
     * @deprecated Use personnelCoreService.getPersonnelByIdWithUser
     */
    private async getPersonnelByIdWithUser(id: number): Promise<Personnel> {
        return this.personnelRepository.findOneOrFail({
            relations: {
                user: true,
            },
            where: { id },
            loadEagerRelations: false,
        });
    }

    /**
     * deprecated use PersonnelCoreService.buildCheckFFParam
     **/
    private buildCheckFFParam(
        account: Account,
        featureFlag: FeatureFlagType<boolean>,
        context: string,
    ): IFeatureFlagChecker {
        return {
            featureFlag,
            account,
            featureFlagService: this.featureFlagService,
            logger: this.logger,
            context,
        };
    }

    private get personnelRepository(): PersonnelRepository {
        return this.getCustomTenantRepository(PersonnelRepository);
    }

    private get complianceCheckRepository(): ComplianceCheckRepository {
        return this.getCustomTenantRepository(ComplianceCheckRepository);
    }

    private get trainingComplianceCheckRepository(): TrainingComplianceCheckRepository {
        return this.getCustomTenantRepository(TrainingComplianceCheckRepository);
    }

    private get monitorInstanceRepository(): MonitorInstanceRepository {
        return this.getCustomTenantRepository(MonitorInstanceRepository);
    }

    private get exclusionRepository(): MonitorInstanceExclusionRepository {
        return this.getCustomTenantRepository(MonitorInstanceExclusionRepository);
    }

    private get userDocumentRepository(): UserDocumentRepository {
        return this.getCustomTenantRepository(UserDocumentRepository);
    }

    private get deviceDocumentRepository(): DeviceDocumentRepository {
        return this.getCustomTenantRepository(DeviceDocumentRepository);
    }

    private get personnelTicketRepository(): PersonnelTicketRepository {
        return this.getCustomTenantRepository(PersonnelTicketRepository);
    }

    private get groupPersonnelRepository(): GroupPersonnelRepository {
        return this.getCustomTenantRepository(GroupPersonnelRepository);
    }

    private get deviceRepository(): DeviceRepository {
        return this.getCustomTenantRepository(DeviceRepository);
    }

    private get customFieldLocationRepository(): CustomFieldsLocationsRepository {
        return this.getCustomTenantRepository(CustomFieldsLocationsRepository);
    }

    private get companyRepository(): Repository<Company> {
        return this.getTenantRepository(Company);
    }
}
