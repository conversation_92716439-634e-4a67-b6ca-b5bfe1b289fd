import { ErrorCode } from '@drata/enums';
import { Injectable, PreconditionFailedException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ApiClientService } from 'app/api-client/api-client.service';
import { BaseUserIdentity } from 'app/apis/classes/base-user-identity.class';
import { IdentityDomain } from 'app/apis/interfaces/identity-domain.interface';
import { IdentityGroup } from 'app/apis/interfaces/identity-group.interface';
import { IdentityServiceUser } from 'app/apis/interfaces/identity-service-user.interface';
import { IIdentityServices } from 'app/apis/interfaces/identity-services.interface';
import { ConnectionMetadata } from 'app/companies/connections/classes/connection-metadata.class';
import { IdpConnectionAdminMismatchResolutionRequestDto } from 'app/companies/connections/dtos/idp-connection-admin-mismatch-resolution-request.dto';
import { IdpConnectionAdminMismatchResolutionSelfRequestDto } from 'app/companies/connections/dtos/idp-connection-admin-mismatch-resolution-self-request.dto';
import { IdpConnectionAdminMismatchSearchRequestDto } from 'app/companies/connections/dtos/idp-connection-admin-mismatch-search-request.dto';
import { IdpConnectionDomainsSyncRequestDto } from 'app/companies/connections/dtos/idp-connection-domains-sync-request.dto';
import { IdpConnectionGroupsSyncRequestDto } from 'app/companies/connections/dtos/idp-connection-goups-sync-request.dto';
import { IdpConnectionGroupsSearchRequestDto } from 'app/companies/connections/dtos/idp-connection-groups-search-request.dto';
import { IdpConnectionSyncStatsResponseDto } from 'app/companies/connections/dtos/idp-connection-sync-stats-response.dto';
import { ConnectionEntity } from 'app/companies/connections/entities/connection.entity';
import { IdpConnectionAdminMismatchResolutionStatus } from 'app/companies/connections/enums/idp-connection-admin-mismatch-resolution-status.enum';
import { IdpConnectionTokenValidationStatus } from 'app/companies/connections/enums/idp-connection-token-validation-status.enum';
import { needsIdpConnectionConfirmation } from 'app/companies/connections/helpers/idp-connection.helper';
import { IdpConnectionDomainSettingSavedEvent } from 'app/companies/connections/observables/events/idp-connection-domain-setting-saved.event';
import { IdpConnectionPersonnelSettingSavedEvent } from 'app/companies/connections/observables/events/idp-connection-personnel-setting-saved.event';
import { IdpConnectionUsersMismatchedResolvedEvent } from 'app/companies/connections/observables/events/idp-connection-users-mismatched-resolved.event';
import { IdpConnectionUsersMismatchedEvent } from 'app/companies/connections/observables/events/idp-connection-users-mismatched.event';
import { ConnectionsRepository } from 'app/companies/connections/repositories/connections.repository';
import { IdpConnectionAdminMismatchResolutionType } from 'app/companies/connections/types/idp-connection-admin-mismatch-resolution.type';
import { IdpConnectionAdminMismatchType } from 'app/companies/connections/types/idp-connection-admin-mismatch.type';
import { IdpConnectionConfirmationStepsStateType } from 'app/companies/connections/types/idp-connection-confirmation-steps-state.type';
import { IdpConnectionStepValidationType } from 'app/companies/connections/types/idp-connection-step-validation.type';
import { CompaniesService } from 'app/companies/services/companies.service';
import { ClientUnauthorizedException } from 'app/synchronizations/exceptions/client-unauthorized.exception';
import { User } from 'app/users/entities/user.entity';
import { GroupRepository } from 'app/users/groups/repositories/group.repository';
import { PersonnelRepository } from 'app/users/personnel/repositories/personnel.repository';
import { UsersCoreService } from 'app/users/services/users-core.service';
import { Account } from 'auth/entities/account.entity';
import { Token } from 'auth/entities/token.entity';
import { EntryCoreService } from 'auth/services/entry-core.service';
import { isEmail } from 'class-validator';
import { PolloAdapter } from 'commons/adapters/pollo.adapter';
import { EmailConfig } from 'commons/configs/email.config';
import { ClientType } from 'commons/enums/auth/client-type.enum';
import { ProviderType } from 'commons/enums/auth/provider-type.enum';
import { ConnectionState } from 'commons/enums/auth/state-type.enum';
import { TokenType } from 'commons/enums/auth/token-type.enum';
import { SettledStatus } from 'commons/enums/notifications/settled-status.enum';
import { Role } from 'commons/enums/users/role.enum';
import { BadDecryptException } from 'commons/exceptions/bad-decrypt.exception';
import { BadRequestException } from 'commons/exceptions/bad-request.exception';
import { ConflictException } from 'commons/exceptions/conflict.exception';
import { InternalServerErrorException } from 'commons/exceptions/internal-server-error.exception';
import { NotFoundException } from 'commons/exceptions/not-found.exception';
import { UnauthorizedException } from 'commons/exceptions/unauthorized.exception';
import { hasExpired } from 'commons/helpers/date.helper';
import { getDomainFromEmail } from 'commons/helpers/domain.helper';
import { decrypt, encrypt } from 'commons/helpers/security.helper';
import { capitalize } from 'commons/helpers/string.helper';
import { hasRole } from 'commons/helpers/user.helper';
import { AppService } from 'commons/services/app.service';
import { EmailService } from 'commons/services/email.service';
import { EmailOptionsType } from 'commons/types/email-options.type';
import config from 'config';
import { difference, intersection, isEmpty, isNil } from 'lodash';
import moment from 'moment';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { Repository } from 'typeorm';

@Injectable()
export class IdpConnectionsService extends AppService {
    constructor(
        private readonly apiClientService: ApiClientService,
        private readonly companiesService: CompaniesService,
        private readonly entryCoreService: EntryCoreService,
        private readonly emailConfig: EmailConfig,
        private readonly emailService: EmailService,
        @InjectRepository(Token)
        private readonly tokenRepository: Repository<Token>,
        private readonly usersCoreService: UsersCoreService,
    ) {
        super();
    }

    /**
     *
     * @param account
     * @param user
     * @param connectionId
     * @returns {Promise<IdpConnectionAdminMismatchType[]>}
     */
    async getIdpAdminsMismatch(
        account: Account,
        user: User,
        connectionId: number,
        shouldSentEvent = false,
    ): Promise<IdpConnectionAdminMismatchType[]> {
        try {
            const connection = await this.getIdpConnectionPendingConfirmationById(connectionId);

            const tenantAdmins = await this.usersCoreService.getAdminUsers(
                false,
                false,
                false,
                true,
            );

            this.logger.log(
                PolloAdapter.acct('Get the list of tenant admin emails not found in IdP', account)
                    .setIdentifier({
                        connectionId: connection.id,
                        connectionState: ConnectionState[connection.state],
                        clientType: ClientType[connection.clientType],
                        providerType: ProviderType[connection.providerType],
                    })
                    .setMetadata({ tenantAdmins: tenantAdmins })
                    .setContext(this.constructor.name)
                    .setSubContext(this.getIdpAdminsMismatch.name),
            );

            const api: IIdentityServices = await this.apiClientService.api(connection, account);

            const idpAdmins: BaseUserIdentity[] = [];

            const resolvedPromises = await Promise.allSettled(
                tenantAdmins.map(tenantAdmin =>
                    api.getUsers({
                        domain: getDomainFromEmail(tenantAdmin.email),
                        isMultiDomain: false,
                        maxResults: 1,
                        nextPageToken: null,
                        query: `email=${tenantAdmin.email}`,
                    }),
                ),
            );

            resolvedPromises.forEach(p => {
                if (p.status === SettledStatus.REJECTED) {
                    throw new InternalServerErrorException(
                        p.reason,
                        ErrorCode.INTERNAL_SERVER_ERROR,
                    );
                } else {
                    const { data: dataUsers } = p.value;
                    idpAdmins.push(...(dataUsers as BaseUserIdentity[]));
                }
            });

            const tenantAdminsReadOnlyMap = await this.usersCoreService.getUsersReadOnlyForRole(
                Role.ADMIN,
            );

            const idpConnectionAdminsMismatch: IdpConnectionAdminMismatchType[] = tenantAdmins
                .filter(
                    ta =>
                        !idpAdmins.find(
                            ia => ta.email.toLowerCase() === ia.getPrimaryEmail().toLowerCase(),
                        ),
                )
                .map(ta => {
                    return {
                        tenantAdminEmailAddressNotFoundInIdp: ta.email,
                        tenantAdminIsReadyOnly: tenantAdminsReadOnlyMap.get(ta.id) || false,
                    };
                });

            this.logger.log(
                PolloAdapter.acct('List of tenant admin emails not found in IdP retrieved', account)
                    .setIdentifier({
                        connectionId: connection.id,
                        connectionState: ConnectionState[connection.state],
                        clientType: ClientType[connection.clientType],
                        providerType: ProviderType[connection.providerType],
                    })
                    .setMetadata({
                        idpConnectionAdminsMismatch: idpConnectionAdminsMismatch,
                    })
                    .setContext(this.constructor.name)
                    .setSubContext(this.getIdpAdminsMismatch.name),
            );

            const company = await this.companiesService.getCompanyByAccountId(account.id);

            if (shouldSentEvent && !isEmpty(idpConnectionAdminsMismatch)) {
                this._eventBus.publish(
                    new IdpConnectionUsersMismatchedEvent(
                        account,
                        user,
                        connection,
                        !isNil(company.multiDomain),
                        idpConnectionAdminsMismatch.length,
                    ),
                );
            }

            return idpConnectionAdminsMismatch;
        } catch (error) {
            this.logger.error(
                PolloMessage.msg(error.message, this.getIdpAdminsMismatch.name).setError(error),
            );
            throw error;
        }
    }

    /**
     *
     * @param account
     * @param connectionId
     * @param tenantAdminEmailToResolve
     * @param searchEmailPrefix
     * @returns {Promise<string[]> }
     */
    async searchIdpEmailsByPrefixForAdminMismatchResolution(
        account: Account,
        connectionId: number,
        requestDto: IdpConnectionAdminMismatchSearchRequestDto,
    ): Promise<string[]> {
        try {
            const connection = await this.getIdpConnectionPendingConfirmationById(connectionId);

            const { tenantAdminEmailToResolve, searchEmailPrefix } = requestDto;

            this.logger.log(
                PolloAdapter.acct(
                    'Search emails by prefix in IdP for admin mismatch resolution',
                    account,
                )
                    .setIdentifier({
                        connectionId: connection.id,
                        connectionState: ConnectionState[connection.state],
                        clientType: ClientType[connection.clientType],
                        providerType: ProviderType[connection.providerType],
                    })
                    .setMetadata({
                        tenantAdminEmailToResolve: tenantAdminEmailToResolve,
                        searchEmailPrefix: searchEmailPrefix,
                    })
                    .setContext(this.constructor.name)
                    .setSubContext(this.searchIdpEmailsByPrefixForAdminMismatchResolution.name),
            );

            const tenantAdminUserToResolve =
                await this.usersCoreService.getUserByEmailNoFail(tenantAdminEmailToResolve);

            if (isNil(tenantAdminUserToResolve)) {
                throw new NotFoundException(ErrorCode.USER_NOT_FOUND);
            }

            if (!hasRole(tenantAdminUserToResolve, [Role.ADMIN])) {
                throw new ConflictException(
                    `Cannot search since ${tenantAdminUserToResolve.email} is not an admin`,
                    ErrorCode.VALIDATION,
                );
            }

            const idpUsersEmails = (
                await this.searchIdpUsers(
                    connection,
                    account,
                    getDomainFromEmail(tenantAdminUserToResolve.email),
                    false,
                    config.get('sync.maxResults'),
                    null,
                    searchEmailPrefix ? `email:${searchEmailPrefix}*` : undefined,
                )
            ).map(idpUser => idpUser.getPrimaryEmail()?.toLowerCase());

            const idpUsersEmailsAlreadyExistingInDb = (
                await this.usersCoreService.getUsersByEmails(idpUsersEmails)
            ).map(u => u.email.toLowerCase());

            return difference(idpUsersEmails, idpUsersEmailsAlreadyExistingInDb);
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(
                    error.message,
                    account,
                    this.searchIdpEmailsByPrefixForAdminMismatchResolution.name,
                ).setError(error),
            );
            throw error;
        }
    }

    /**
     *
     * @param account
     * @param user
     * @param dto
     * @param connectionId
     * @returns {Promise<IdpConnectionAdminMismatchResolutionType> }
     */
    async resolveIdpAdminMismatch(
        account: Account,
        user: User,
        dto: IdpConnectionAdminMismatchResolutionRequestDto,
        connectionId: number,
    ): Promise<IdpConnectionAdminMismatchResolutionType> {
        try {
            const { tenantAdminEmailToResolve, idpAdminEmailForResolution } = dto;

            this.validateIdpAdminMismatchResolutionEmails(
                tenantAdminEmailToResolve,
                idpAdminEmailForResolution,
            );

            const connection = await this.getIdpConnectionPendingConfirmationById(connectionId);
            const { clientType, providerType } = connection;

            this.logger.log(
                PolloAdapter.acct('Resolve admin user IdP mismatch', account)
                    .setIdentifier({
                        connectionId: connection.id,
                        connectionState: ConnectionState[connection.state],
                        clientType: ClientType[connection.clientType],
                        providerType: ProviderType[connection.providerType],
                    })
                    .setMetadata({
                        tenantAdminEmailToResolve: tenantAdminEmailToResolve,
                        idpAdminEmailForResolution: idpAdminEmailForResolution,
                    })
                    .setContext(this.constructor.name)
                    .setSubContext(this.resolveIdpAdminMismatch.name),
            );

            const isSelfResolve = tenantAdminEmailToResolve === user.email ? true : false;

            let tenantAdminUserToResolve: User;

            if (isSelfResolve) {
                tenantAdminUserToResolve = user;
            } else {
                tenantAdminUserToResolve =
                    await this.usersCoreService.getUserByEmailNoFail(tenantAdminEmailToResolve);
            }

            if (isNil(tenantAdminUserToResolve)) {
                throw new NotFoundException(ErrorCode.USER_NOT_FOUND);
            }

            let idpAdminMismatchResolutionStatus =
                IdpConnectionAdminMismatchResolutionStatus.ADMIN_MISMATCH_RESOLUTION_EMAIL_SENT;

            if (!isSelfResolve) {
                await this.updateUserForIdpAdminMismatchResolution(
                    account,
                    tenantAdminUserToResolve,
                    idpAdminEmailForResolution,
                );
                idpAdminMismatchResolutionStatus =
                    IdpConnectionAdminMismatchResolutionStatus.ADMIN_MISMATCH_RESOLVED;
            }

            const { validationTokenId } = await this.sendIdpAminMismatchResolutionEmail(
                account,
                connectionId,
                tenantAdminUserToResolve,
                idpAdminEmailForResolution,
                clientType,
                providerType,
                isSelfResolve,
                tenantAdminEmailToResolve,
            );

            return {
                tenantAdminEmailToResolve: tenantAdminUserToResolve.email,
                idpAdminEmailForResolution: idpAdminEmailForResolution,
                idpConnectionAdminMismatchResolutionStatus: idpAdminMismatchResolutionStatus,
                validationTokenId: validationTokenId,
            };
        } catch (error) {
            this.logger.error(
                PolloMessage.msg(error.message, this.resolveIdpAdminMismatch.name).setError(error),
            );
            throw error;
        }
    }

    /**
     *
     * @param account
     * @param user
     * @param dto
     * @param tokenId
     * @param connectionId
     * @returns {Promise<IdpConnectionAdminMismatchResolutionType> }
     */
    async resolveIdpAdminMismatchSelf(
        account: Account,
        user: User,
        dto: IdpConnectionAdminMismatchResolutionSelfRequestDto,
        tokenId: string,
        connectionId: number,
    ): Promise<IdpConnectionAdminMismatchResolutionType> {
        try {
            const idpTokenVerificationStatus = await this.validateToken(
                user.email,
                tokenId,
                TokenType.IDP_CONNECTION_ADMIN_MISMATCH_RESOLUTION,
            );

            const { encryptedEmailAddresses } = dto;

            const decryptedEmailAddresses: {
                tenantAdminEmailToResolve: string;
                idpAdminEmailForResolution: string;
            } = JSON.parse(decrypt(encryptedEmailAddresses));

            this.validateIdpAdminMismatchResolutionEmails(
                decryptedEmailAddresses.tenantAdminEmailToResolve,
                decryptedEmailAddresses.idpAdminEmailForResolution,
            );

            const connection = await this.getIdpConnectionPendingConfirmationById(connectionId);

            this.logger.log(
                PolloAdapter.acct('Self-resolving admin IdP mismatch', account)
                    .setIdentifier({
                        connectionId: connection.id,
                        connectionState: ConnectionState[connection.state],
                        clientType: ClientType[connection.clientType],
                        providerType: ProviderType[connection.providerType],
                    })
                    .setMetadata({
                        tenantAdminEmailToResolve:
                            decryptedEmailAddresses.tenantAdminEmailToResolve,
                        idpAdminEmailForResolution:
                            decryptedEmailAddresses.idpAdminEmailForResolution,
                    })
                    .setContext(this.constructor.name)
                    .setSubContext(this.resolveIdpAdminMismatchSelf.name),
            );

            this.validateIdpAdminMismatchResolutionEmails(
                decryptedEmailAddresses.tenantAdminEmailToResolve,
                decryptedEmailAddresses.idpAdminEmailForResolution,
            );

            let idpConnectionAdminResolutionStatus: IdpConnectionAdminMismatchResolutionStatus =
                IdpConnectionAdminMismatchResolutionStatus.ADMIN_MISMATCH_ALREADY_RESOLVED;

            let validationTokenId: string | null = null;

            if (idpTokenVerificationStatus === IdpConnectionTokenValidationStatus.TOKEN_EXPIRED) {
                const { clientType, providerType } = connection;

                ({ validationTokenId } = await this.sendIdpAminMismatchResolutionEmail(
                    account,
                    connectionId,
                    user,
                    decryptedEmailAddresses.idpAdminEmailForResolution,
                    clientType,
                    providerType,
                    true,
                ));

                idpConnectionAdminResolutionStatus =
                    IdpConnectionAdminMismatchResolutionStatus.ADMIN_MISMATCH_RESOLUTION_EMAIL_RESENT;
            } else if (
                idpTokenVerificationStatus === IdpConnectionTokenValidationStatus.TOKEN_VALIDATED
            ) {
                await this.updateUserForIdpAdminMismatchResolution(
                    account,
                    user,
                    decryptedEmailAddresses.idpAdminEmailForResolution,
                );

                idpConnectionAdminResolutionStatus =
                    IdpConnectionAdminMismatchResolutionStatus.ADMIN_MISMATCH_RESOLVED;
            } else if (
                idpTokenVerificationStatus === IdpConnectionTokenValidationStatus.TOKEN_ALREADY_USED
            ) {
                idpConnectionAdminResolutionStatus =
                    IdpConnectionAdminMismatchResolutionStatus.ADMIN_MISMATCH_ALREADY_RESOLVED;
            }

            return {
                tenantAdminEmailToResolve: decryptedEmailAddresses.tenantAdminEmailToResolve,
                idpAdminEmailForResolution: decryptedEmailAddresses.idpAdminEmailForResolution,
                idpConnectionAdminMismatchResolutionStatus: idpConnectionAdminResolutionStatus,
                validationTokenId: validationTokenId,
            };
        } catch (error) {
            this.logger.error(
                PolloMessage.msg(error.message, this.resolveIdpAdminMismatchSelf.name).setError(
                    error,
                ),
            );
            if (error instanceof BadDecryptException) {
                throw new BadRequestException('Malformed validation info', ErrorCode.VALIDATION);
            } else throw error;
        }
    }

    /**
     *
     * @param account
     * @param user
     * @param connectionId
     * @returns {Promise<IdpConnectionStepValidationType>}
     */
    async validateUpdateIdpConnectionStepAdminMismatch(
        account: Account,
        user: User,
        connectionId: number,
    ): Promise<IdpConnectionStepValidationType> {
        try {
            const tenantAdminEmailAddressesNotFoundInIdp = (
                await this.getIdpAdminsMismatch(account, user, connectionId)
            ).map(ta => ta.tenantAdminEmailAddressNotFoundInIdp);

            let tenantAdminAddressesToResolve: string[] = [];

            if (!hasRole(user, [Role.SERVICE_USER])) {
                tenantAdminAddressesToResolve.push(user.email);
            } else {
                tenantAdminAddressesToResolve = (
                    await this.usersCoreService.getAdminUsers(false, false, true, true)
                ).map(ta => ta.email);
            }

            this.logger.log(
                PolloAdapter.acct(
                    'Validate if IdP connection admin mismatch issues has been resolved',
                    account,
                )
                    .setContext(this.constructor.name)
                    .setSubContext(this.validateUpdateIdpConnectionStepAdminMismatch.name),
            );

            const canMoveToNextStep = await this.checkIfAtLeastOneAdminIsResolved(
                tenantAdminAddressesToResolve,
                tenantAdminEmailAddressesNotFoundInIdp,
            );

            this.logger.log(
                PolloAdapter.acct(
                    'Validated if IdP connection admin mismatch issues are resolved',
                    account,
                )
                    .setContext(this.constructor.name)
                    .setSubContext(this.validateUpdateIdpConnectionStepAdminMismatch.name),
            );

            return {
                canMoveToNextStep,
                tenantAdminsToLoseAccess: tenantAdminEmailAddressesNotFoundInIdp,
            };
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(
                    error.message,
                    account,
                    this.validateUpdateIdpConnectionStepAdminMismatch.name,
                ).setError(error),
            );
            throw error;
        }
    }

    /**
     *
     * @param account
     * @param user
     * @param connectionId
     * @returns {Promise<IdpConnectionConfirmationStepsStateType>}
     */
    async updateIdpConnectionStepAdminMismatch(
        account: Account,
        user: User,
        connectionId: number,
    ): Promise<IdpConnectionConfirmationStepsStateType> {
        try {
            const connection = await this.getIdpConnectionPendingConfirmationById(connectionId);

            if (!needsIdpConnectionConfirmation(connection.clientType)) {
                throw new PreconditionFailedException(`Connection doesn't need IdP confirmation`);
            }

            this.logger.log(
                PolloAdapter.acct('Updating admin mismatch connection step', account)
                    .setContext(this.constructor.name)
                    .setSubContext(this.updateIdpConnectionStepAdminMismatch.name),
            );

            const { canMoveToNextStep, tenantAdminsToLoseAccess } =
                await this.validateUpdateIdpConnectionStepAdminMismatch(
                    account,
                    user,
                    connectionId,
                );

            if (!canMoveToNextStep) {
                if (!hasRole(user, [Role.SERVICE_USER])) {
                    throw new ConflictException(
                        'Admin email must exist in idp to continue',
                        ErrorCode.VALIDATION,
                    );
                } else {
                    throw new ConflictException(
                        'At least one admin must exist in idp to continue',
                        ErrorCode.VALIDATION,
                    );
                }
            }

            const metadata = await this.updateIdPConnectionStateMetadata(connection, {
                adminMismatchResolved: true,
            });

            this.logger.log(
                PolloAdapter.acct('Updated admin mismatch connection step', account)
                    .setContext(this.constructor.name)
                    .setSubContext(this.updateIdpConnectionStepAdminMismatch.name),
            );

            const company = await this.companiesService.getCompanyByAccountId(account.id);

            this._eventBus.publish(
                new IdpConnectionUsersMismatchedResolvedEvent(
                    account,
                    user,
                    connection,
                    !isNil(company.multiDomain),
                    tenantAdminsToLoseAccess.length,
                ),
            );

            return metadata.idpConnectionConfirmationStepsState;
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(
                    error.message,
                    account,
                    this.updateIdpConnectionStepAdminMismatch.name,
                ).setError(error),
            );
            throw error;
        }
    }

    /**
     *
     * @param account
     * @param connectionId
     * @returns {Promise<string[]> }
     */
    async getIdpDomains(account: Account, connectionId: number): Promise<string[]> {
        try {
            const connection = await this.getIdpConnectionById(connectionId);

            this.logger.log(
                PolloAdapter.acct('Get IdP connection domains list', account)
                    .setIdentifier({
                        connectionId: connection.id,
                        connectionState: ConnectionState[connection.state],
                        clientType: ClientType[connection.clientType],
                        providerType: ProviderType[connection.providerType],
                    })
                    .setContext(this.constructor.name)
                    .setSubContext(this.getIdpDomains.name),
            );

            const api: IIdentityServices = await this.apiClientService.api(connection, account);

            await api.initializeDomainsClient();

            let nextPageToken = null;
            const domainsInIdp: IdentityDomain[] = [];

            do {
                // eslint-disable-next-line no-await-in-loop
                const { data: dataDomains, token } = await api.getDomains(
                    config.get('sync.maxResults'),
                    nextPageToken,
                );
                domainsInIdp.push(...dataDomains);
                nextPageToken = token;
            } while (nextPageToken);

            const domainsInIdpArray = domainsInIdp.map(d => d.domainName);

            this.logger.log(
                PolloAdapter.acct('Domains retrieved from IdP connection', account)
                    .setIdentifier({
                        connectionId: connection.id,
                        connectionState: ConnectionState[connection.state],
                        clientType: ClientType[connection.clientType],
                        providerType: ProviderType[connection.providerType],
                    })
                    .setMetadata({
                        domainsInIdp: domainsInIdpArray,
                    })
                    .setContext(this.constructor.name)
                    .setSubContext(this.getIdpDomains.name),
            );

            return domainsInIdpArray;
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(error.message, account, this.getIdpDomains.name).setError(error),
            );
            if (error.constructor === ClientUnauthorizedException) {
                throw new BadRequestException(
                    'Unable to establish IdP connection. Please review connection details and permissions',
                    ErrorCode.IDP_CLIENT_UNAUTHORIZED,
                );
            } else throw error;
        }
    }

    /**
     *
     * @param account
     * @param user
     * @param dto
     * @param connectionId
     * @returns {Promise<IdpConnectionStepValidationType>}
     */
    async validateUpdateIdpConnectionStepMultiDomainSync(
        account: Account,
        user: User,
        dto: IdpConnectionDomainsSyncRequestDto,
        connectionId: number,
    ): Promise<IdpConnectionStepValidationType> {
        try {
            const { hasMultiDomain } = dto;
            const connection = await this.getIdpConnectionPendingConfirmationById(connectionId);

            this.logger.log(
                PolloAdapter.acct(
                    'Validate if IdP connection multi-domain option can be set',
                    account,
                )
                    .setIdentifier({
                        connectionId: connection.id,
                        connectionState: ConnectionState[connection.state],
                        clientType: ClientType[connection.clientType],
                        providerType: ProviderType[connection.providerType],
                    })
                    .setMetadata({ hasMultiDomain })
                    .setContext(this.constructor.name)
                    .setSubContext(this.validateUpdateIdpConnectionStepMultiDomainSync.name),
            );

            let canMoveToNextStep = true,
                tenantAdminsToLoseAccess: string[] = [];

            if (!hasMultiDomain) {
                ({
                    atLeastOneAdminInAccountDomain: canMoveToNextStep,
                    tenantAdminsNotInAccountDomain: tenantAdminsToLoseAccess,
                } = await this.checkIfAtLeastOneAdminInAccountDomain(account, user, connectionId));
            }

            this.logger.log(
                PolloAdapter.acct(
                    'Validated IdP connection multi-domain option can be set',
                    account,
                )
                    .setIdentifier({
                        connectionId: connection.id,
                        connectionState: ConnectionState[connection.state],
                        clientType: ClientType[connection.clientType],
                        providerType: ProviderType[connection.providerType],
                    })
                    .setMetadata({
                        atLeastOneAdminInAccountDomain: canMoveToNextStep,
                        tenantAdminsNotInAccountDomain: tenantAdminsToLoseAccess,
                    })
                    .setContext(this.constructor.name)
                    .setSubContext(this.validateUpdateIdpConnectionStepMultiDomainSync.name),
            );

            return {
                canMoveToNextStep,
                tenantAdminsToLoseAccess,
            };
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(
                    error.message,
                    account,
                    this.validateUpdateIdpConnectionStepMultiDomainSync.name,
                ).setError(error),
            );
            throw error;
        }
    }

    /**
     *
     * @param account
     * @param user
     * @param dto
     * @param connectionId
     * @returns {Promise<IdpConnectionConfirmationStepsStateType>}
     */
    async updateIdpConnectionStepMultiDomainSync(
        account: Account,
        user: User,
        dto: IdpConnectionDomainsSyncRequestDto,
        connectionId: number,
    ): Promise<IdpConnectionConfirmationStepsStateType> {
        try {
            const connection = await this.getIdpConnectionPendingConfirmationById(connectionId);

            if (!needsIdpConnectionConfirmation(connection.clientType)) {
                throw new PreconditionFailedException(`Connection doesn't need IdP confirmation`);
            }

            const { hasMultiDomain } = dto;

            this.logger.log(
                PolloAdapter.acct('Updating has multi-domain option for IdP connection', account)
                    .setIdentifier({
                        connectionId: connection.id,
                        connectionState: ConnectionState[connection.state],
                        clientType: ClientType[connection.clientType],
                        providerType: ProviderType[connection.providerType],
                    })
                    .setMetadata({
                        hasMultiDomain: hasMultiDomain,
                    })
                    .setContext(this.constructor.name)
                    .setSubContext(this.updateIdpConnectionStepMultiDomainSync.name),
            );

            if (
                !hasMultiDomain &&
                !(await this.checkIfAtLeastOneAdminInAccountDomain(account, user, connectionId))
            ) {
                throw new ConflictException(
                    'At least one admin must be in IdP selected domain(s)',
                    ErrorCode.VALIDATION,
                );
            }

            let company = await this.companiesService.getCompanyByAccountId(account.id);

            const prevMultiDomainValue = !isNil(company.multiDomain);

            if (prevMultiDomainValue !== hasMultiDomain) {
                company = await this.companiesService.updateCompanyMultiDomain(
                    account.id,
                    hasMultiDomain,
                );
            }

            const metadata = await this.updateIdPConnectionStateMetadata(connection, {
                domainSyncSelected: true,
            });

            metadata.multiDomain = hasMultiDomain;
            connection.setMetadata(metadata);
            await this.connectionRepository.save(connection);

            this.logger.log(
                PolloAdapter.acct('Updated has multi-domain option for IdP connection', account)
                    .setIdentifier({
                        connectionId: connection.id,
                        connectionState: ConnectionState[connection.state],
                        clientType: ClientType[connection.clientType],
                        providerType: ProviderType[connection.providerType],
                    })
                    .setContext(this.constructor.name)
                    .setSubContext(this.updateIdpConnectionStepMultiDomainSync.name),
            );

            this._eventBus.publish(
                new IdpConnectionDomainSettingSavedEvent(
                    account,
                    user,
                    connection,
                    prevMultiDomainValue,
                    hasMultiDomain ? 'All domains' : `Only ${account.domain}`,
                ),
            );

            return metadata.idpConnectionConfirmationStepsState;
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(
                    error.message,
                    account,
                    this.updateIdpConnectionStepMultiDomainSync.name,
                ).setError(error),
            );
            throw error;
        }
    }

    /**
     *
     * @param account
     * @param connectionId
     * @param user
     * @returns {Promise<IdpConnectionConfirmationStepsInfoType> }
     */
    async getIdpGroups(
        account: Account,
        connectionId: number,
        email: string | null = null,
        prefix?: string,
    ): Promise<IdentityGroup[]> {
        try {
            const connection = await this.getIdpConnectionPendingConfirmationById(connectionId);

            this.logger.log(
                PolloAdapter.acct('Get IdP connection group list', account)
                    .setIdentifier({
                        connectionId: connection.id,
                        connectionState: ConnectionState[connection.state],
                        clientType: ClientType[connection.clientType],
                        providerType: ProviderType[connection.providerType],
                    })
                    .setMetadata({
                        prefix,
                    })
                    .setContext(this.constructor.name)
                    .setSubContext(this.getIdpGroups.name),
            );

            const company = await this.companiesService.getCompanyByAccountId(account.id);
            const isMultiDomain = !isNil(company.multiDomain);

            const api: IIdentityServices = await this.apiClientService.api(connection, account);
            await api.initializeGroupsClient(account);

            let nextPageToken = null;
            const groupsInIdp: IdentityGroup[] = [];

            if (!isNil(prefix)) {
                // search groups
                const { data: dataGroups } = await api.getGroups({
                    domain: account.domain,
                    isMultiDomain,
                    maxResults: config.get('sync.maxResults'),
                    nextPageToken,
                    query: prefix?.length ? `name:${prefix}*` : undefined,
                });

                groupsInIdp.push(...dataGroups);
            } else if (isNil(email)) {
                // All groups
                do {
                    // eslint-disable-next-line no-await-in-loop
                    const { data: dataGroups, token } = await api.getGroups({
                        domain: account.domain,
                        isMultiDomain,
                        maxResults: config.get('sync.maxResults'),
                        nextPageToken,
                    });

                    groupsInIdp.push(...dataGroups);
                    nextPageToken = token;
                } while (nextPageToken);
            } else {
                // Groups for given email
                do {
                    // eslint-disable-next-line no-await-in-loop
                    const { data: dataGroups, token } = await api.getGroupsForUser(
                        {
                            domain: getDomainFromEmail(email),
                            isMultiDomain: false,
                            maxResults: config.get('sync.maxResults'),
                            nextPageToken,
                        },
                        email,
                    );

                    groupsInIdp.push(...dataGroups);
                    nextPageToken = token;
                } while (nextPageToken);
            }

            this.logger.log(
                PolloAdapter.acct('Groups retrieved from IdP connection', account)
                    .setIdentifier({
                        connectionId: connection.id,
                        connectionState: ConnectionState[connection.state],
                        clientType: ClientType[connection.clientType],
                        providerType: ProviderType[connection.providerType],
                    })
                    .setMetadata({
                        groupsInIdp: groupsInIdp,
                    })
                    .setContext(this.constructor.name)
                    .setSubContext(this.getIdpGroups.name),
            );

            return groupsInIdp;
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(error.message, account, this.getIdpGroups.name).setError(error),
            );
            if (error.constructor === ClientUnauthorizedException) {
                throw new BadRequestException(
                    'Unable to establish IdP connection. Please review connection details and permissions',
                    ErrorCode.IDP_CLIENT_UNAUTHORIZED,
                );
            } else throw error;
        }
    }

    /**
     *
     * @param account
     * @param connectionId
     * @param requestDto
     * @returns {Promise<string[]>}
     */
    async searchIdpGroups(
        account: Account,
        connectionId: number,
        requestDto: IdpConnectionGroupsSearchRequestDto,
    ): Promise<string[]> {
        try {
            let { searchGroupsPrefix } = requestDto;

            searchGroupsPrefix = encodeURIComponent(searchGroupsPrefix).replace(/%20/g, '+');

            searchGroupsPrefix = decodeURIComponent(searchGroupsPrefix).replace(/'/g, "\\'");

            this.logger.log(
                PolloAdapter.acct('Search IdP connection group list names', account)
                    .setMetadata({
                        searchGroupsPrefix,
                    })
                    .setContext(this.constructor.name)
                    .setSubContext(this.searchIdpGroups.name),
            );

            const groupsInIdp = await this.getIdpGroups(
                account,
                connectionId,
                null,
                searchGroupsPrefix,
            );

            return groupsInIdp
                .map(g => String(g.name))
                .sort(function (a, b) {
                    return a.toLowerCase().localeCompare(b.toLowerCase(), 'en', {
                        numeric: true,
                    });
                });
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(error.message, account, this.searchIdpGroups.name).setError(
                    error,
                ),
            );
            throw error;
        }
    }

    /**
     *
     * @param account
     * @param connectionId
     * @param email
     * @returns {Promise<string[]>}
     */
    async getIdpGroupsName(
        account: Account,
        connectionId: number,
        email: string | null = null,
    ): Promise<string[]> {
        try {
            this.logger.log(
                PolloAdapter.acct('Get IdP connection group list names', account)
                    .setContext(this.constructor.name)
                    .setSubContext(this.getIdpGroupsName.name),
            );

            const groupsInIdp = await this.getIdpGroups(account, connectionId, email);

            return groupsInIdp.map(g => g.name);
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(error.message, account, this.getIdpGroupsName.name).setError(
                    error,
                ),
            );
            throw error;
        }
    }

    /**
     *
     * @param account
     * @param user
     * @param dto
     * @param connectionId
     * @returns {Promise<IdpConnectionGroupsSyncRequestDto>}
     */
    async validateUpdateIdpConnectionStepGroupSync(
        account: Account,
        user: User,
        dto: IdpConnectionGroupsSyncRequestDto,
        connectionId: number,
    ): Promise<IdpConnectionStepValidationType> {
        try {
            const connection = await this.getIdpConnectionPendingConfirmationById(connectionId);
            const { isSyncSpecificGroupsSelected, idpGroupsToSync } = dto;

            this.logger.log(
                PolloAdapter.acct('Validate if IdP connection groups option can be set', account)
                    .setIdentifier({
                        connectionId: connection.id,
                        connectionState: ConnectionState[connection.state],
                        clientType: ClientType[connection.clientType],
                        providerType: ProviderType[connection.providerType],
                    })
                    .setMetadata({
                        isSyncSpecificGroupsSelected,
                        idpGroupsToSync,
                    })
                    .setContext(this.constructor.name)
                    .setSubContext(this.validateUpdateIdpConnectionStepGroupSync.name),
            );

            let canMoveToNextStep = true,
                tenantAdminsToLoseAccess: string[] = [];

            if (isSyncSpecificGroupsSelected) {
                ({
                    atLeastOneAdminInSelectedGroups: canMoveToNextStep,
                    tenantAdminsNotInSelectedGroup: tenantAdminsToLoseAccess,
                } = await this.checkIfAtLeastOneAdminInSelectedGroups(
                    account,
                    user,
                    connectionId,
                    idpGroupsToSync,
                ));
            }

            this.logger.log(
                PolloAdapter.acct('Validated if IdP connection groups option can be set', account)
                    .setIdentifier({
                        connectionId: connection.id,
                        connectionState: ConnectionState[connection.state],
                        clientType: ClientType[connection.clientType],
                        providerType: ProviderType[connection.providerType],
                    })
                    .setMetadata({
                        atLeastOneAdminInAccountDomain: canMoveToNextStep,
                        tenantAdminsNotInAccountDomain: tenantAdminsToLoseAccess,
                    })
                    .setContext(this.constructor.name)
                    .setSubContext(this.validateUpdateIdpConnectionStepGroupSync.name),
            );

            return {
                canMoveToNextStep,
                tenantAdminsToLoseAccess,
            };
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(
                    error.message,
                    account,
                    this.validateUpdateIdpConnectionStepGroupSync.name,
                ).setError(error),
            );
            throw error;
        }
    }

    /**
     *
     * @param account
     * @param user
     * @param dto
     * @param connectionId
     * @returns {Promise<IdpConnectionConfirmationStepsStateType>}
     */
    async updateIdpConnectionStepGroupSync(
        account: Account,
        user: User,
        dto: IdpConnectionGroupsSyncRequestDto,
        connectionId: number,
    ): Promise<IdpConnectionConfirmationStepsStateType> {
        try {
            const connection = await this.getIdpConnectionPendingConfirmationById(connectionId);

            if (!needsIdpConnectionConfirmation(connection.clientType)) {
                throw new PreconditionFailedException(`Connection doesn't need IdP confirmation`);
            }

            const { isSyncSpecificGroupsSelected, idpGroupsToSync } = dto;

            if (isEmpty(idpGroupsToSync) && isSyncSpecificGroupsSelected) {
                throw new BadRequestException(
                    'idpGroupsToSync is empty: You must select at least one group to sync',
                );
            }

            this.logger.log(
                PolloAdapter.acct('Updating groups to sync for IdP connection', account)
                    .setIdentifier({
                        connectionId: connection.id,
                        connectionState: ConnectionState[connection.state],
                        clientType: ClientType[connection.clientType],
                        providerType: ProviderType[connection.providerType],
                    })
                    .setMetadata({
                        idpGroupsToSync: idpGroupsToSync,
                    })
                    .setContext(this.constructor.name)
                    .setSubContext(this.updateIdpConnectionStepGroupSync.name),
            );

            let idpGroups: IdentityGroup[];
            let validIdpGroupsToSync: IdentityGroup[] = [];

            if (isSyncSpecificGroupsSelected) {
                idpGroups = await this.getIdpGroups(account, connectionId);
                validIdpGroupsToSync = idpGroups.filter(idpG =>
                    idpGroupsToSync.includes(idpG.name),
                );
            }

            if (validIdpGroupsToSync.length !== idpGroupsToSync.length) {
                throw new ConflictException(
                    'All groups provided must exist in the IdP',
                    ErrorCode.VALIDATION,
                );
            }

            if (
                isSyncSpecificGroupsSelected &&
                !(await this.checkIfAtLeastOneAdminInSelectedGroups(
                    account,
                    user,
                    connectionId,
                    idpGroupsToSync,
                ))
            ) {
                throw new ConflictException(
                    'At least one admin must be in IdP selected group(s)',
                    ErrorCode.VALIDATION,
                );
            }

            const metadata = await this.updateIdPConnectionStateMetadata(connection, {
                groupSyncSelected: true,
            });

            metadata.groupIdList = validIdpGroupsToSync.map(group => group.email);
            connection.setMetadata(metadata);
            await this.connectionRepository.save(connection);

            this.logger.log(
                PolloAdapter.acct('Updated groups to sync for IdP connection', account)
                    .setIdentifier({
                        connectionId: connection.id,
                        connectionState: ConnectionState[connection.state],
                        clientType: ClientType[connection.clientType],
                        providerType: ProviderType[connection.providerType],
                    })
                    .setContext(this.constructor.name)
                    .setSubContext(this.updateIdpConnectionStepGroupSync.name),
            );

            const company = await this.companiesService.getCompanyByAccountId(account.id);

            this._eventBus.publish(
                new IdpConnectionPersonnelSettingSavedEvent(
                    account,
                    user,
                    connection,
                    !isNil(company.multiDomain),
                    idpGroupsToSync.length,
                    isSyncSpecificGroupsSelected ? 'Specific groups' : 'All personnel',
                ),
            );

            return metadata.idpConnectionConfirmationStepsState;
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(
                    error.message,
                    account,
                    this.updateIdpConnectionStepGroupSync.name,
                ).setError(error),
            );
            throw error;
        }
    }

    /**
     *
     * @param account
     * @param connectionId
     * @returns {Promise<IdpConnectionSyncStatsResponseDto>}
     */
    async getIdpSyncStats(
        account: Account,
        connectionId: number,
    ): Promise<IdpConnectionSyncStatsResponseDto> {
        try {
            const connection = await this.getIdpConnectionById(connectionId);

            const result = await this.getIsPersonnelSyncOrResyncInProgress(
                account,
                connection,
                new IdpConnectionSyncStatsResponseDto(),
            );

            if (result.isSyncInProgress || result.isResyncInProgress) {
                return result;
            }

            const meta = connection.getMetadata();

            this.logger.log(
                PolloAdapter.acct('Get IdP connection personnel sync stats', account)
                    .setIdentifier({
                        connectionId: connection.id,
                        connectionState: ConnectionState[connection.state],
                        clientType: ClientType[connection.clientType],
                        providerType: ProviderType[connection.providerType],
                    })
                    .setContext(this.constructor.name)
                    .setSubContext(this.getIdpSyncStats.name),
            );

            if (!isNil(meta.groupIdList)) {
                const groups = await this.groupsRepository
                    .createQueryBuilder('groups')
                    .loadRelationCountAndMap('groups.total', 'groups.personnel')
                    .where('groups.email IN(:...emails)', {
                        emails: meta.groupIdList,
                    })
                    .getMany();

                result.groups = groups.map(g => ({
                    email: g.email,
                    name: g.name,
                    total: g['total'],
                }));
            }

            const personnel = await this.personnelRepository.findByConnectionId(connection.id);

            const company = await this.companiesService.getCompanyByAccountId(account.id);

            // if multi_domains get all from idp
            if (company.multiDomain && meta.multiDomain) {
                const api: IIdentityServices = await this.apiClientService.api(connection, account);
                await api.initializeDomainsClient();

                const domainsInIdp = await this.getIdpDomains(account, connectionId);
                const mapCount = {};

                // map domains name to count faster
                for (const d of domainsInIdp) {
                    mapCount[d] = 0;
                }

                // count personnel by domain need to split email
                for (const p of personnel) {
                    if (!p.user) continue;
                    const domain = p.user.email.split('@')[1];
                    if (mapCount.hasOwnProperty(domain)) {
                        mapCount[domain]++;
                    }
                }
                result.domains = Object.keys(mapCount)
                    .filter(key => mapCount[key] > 0)
                    .map(key => ({
                        name: key,
                        total: mapCount[key],
                    }));

                // default domain not need to split
            } else {
                result.domains.push({
                    name: account.domain,
                    total: personnel.length,
                });
            }

            this.logger.log(
                PolloAdapter.acct('Retrieved IdP connection personnel sync stats', account)
                    .setIdentifier({
                        connectionId: connection.id,
                        connectionState: ConnectionState[connection.state],
                        clientType: ClientType[connection.clientType],
                        providerType: ProviderType[connection.providerType],
                    })
                    .setContext(this.constructor.name)
                    .setSubContext(this.getIdpSyncStats.name),
            );

            return result;
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(
                    error.message,
                    account,
                    this.updateIdpConnectionStepGroupSync.name,
                ).setError(error),
            );
            if (error.constructor === ClientUnauthorizedException) {
                throw new BadRequestException(
                    'Unable to establish IdP connection. Please review connection details and permissions',
                    ErrorCode.IDP_CLIENT_UNAUTHORIZED,
                );
            } else throw error;
        }
    }

    private async getIsPersonnelSyncOrResyncInProgress(
        account: Account,
        connection: ConnectionEntity,
        result: IdpConnectionSyncStatsResponseDto,
    ): Promise<IdpConnectionSyncStatsResponseDto> {
        try {
            const meta = connection.getMetadata();

            if (isNil(meta.lastSyncedAt)) {
                result.isSyncInProgress = true;
                return result;
            }

            const CACHE_KEY = `resync:${account.id}`;
            const isLocked = await this._cacheService.isLocked(CACHE_KEY);

            if (isLocked) {
                result.isResyncInProgress = true;
                return result;
            }

            return result;
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(
                    error.message,
                    account,
                    this.updateIdpConnectionStepGroupSync.name,
                ).setError(error),
            );
            throw error;
        }
    }

    /**
     *
     * @param tenantAdminEmailToResolve
     * @param idpAdminEmailForResolution
     */
    private validateIdpAdminMismatchResolutionEmails(
        tenantAdminEmailToResolve: string,
        idpAdminEmailForResolution: string,
    ) {
        if (
            !isEmail(tenantAdminEmailToResolve) ||
            !isEmail(idpAdminEmailForResolution) ||
            getDomainFromEmail(tenantAdminEmailToResolve) !==
                getDomainFromEmail(idpAdminEmailForResolution) ||
            tenantAdminEmailToResolve === idpAdminEmailForResolution
        ) {
            throw new BadRequestException(
                `Invalid tenantAdminEmailToResolve and/or idpAdminEmailForResolution provided for resolution`,
                ErrorCode.VALIDATION,
            );
        }
    }

    /**
     *
     * @param connection
     * @param account
     * @param searchPrimaryDomain
     * @param searchIsMultiDomain
     * @param searchMaxResult
     * @param searchNextPageToken
     * @param searchQuery
     * @returns {Promise<IdentityServiceUser[]>}
     */
    private async searchIdpUsers(
        connection: ConnectionEntity,
        account: Account,
        searchPrimaryDomain: string,
        searchIsMultiDomain: boolean,
        searchMaxResult: number,
        searchNextPageToken: string | null,
        searchQuery?: string,
    ): Promise<IdentityServiceUser[]> {
        try {
            this.logger.log(
                PolloAdapter.acct('Search users in IdP', account)
                    .setIdentifier({
                        connectionId: connection.id,
                        connectionState: ConnectionState[connection.state],
                        clientType: ClientType[connection.clientType],
                        providerType: ProviderType[connection.providerType],
                    })
                    .setMetadata({
                        searchPrimaryDomain: searchPrimaryDomain,
                        searchIsMultiDomain: searchIsMultiDomain,
                        searchMaxResult: searchMaxResult,
                        searchNextPageToken: searchNextPageToken,
                        searchQuery: searchQuery,
                    })
                    .setContext(this.constructor.name)
                    .setSubContext(this.searchIdpUsers.name),
            );

            const api: IIdentityServices = await this.apiClientService.api(connection, account);

            const { data: idpUsers } = await api.getUsers({
                domain: searchPrimaryDomain,
                isMultiDomain: searchIsMultiDomain,
                maxResults: searchMaxResult,
                nextPageToken: searchNextPageToken,
                query: searchQuery,
            });

            this.logger.log(
                PolloAdapter.acct(`IdP search returned ${idpUsers.length} users`, account)
                    .setIdentifier({
                        connectionId: connection.id,
                        connectionState: ConnectionState[connection.state],
                        clientType: ClientType[connection.clientType],
                        providerType: ProviderType[connection.providerType],
                    })
                    .setContext(this.constructor.name)
                    .setSubContext(this.searchIdpUsers.name),
            );

            return idpUsers;
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(error.message, account, this.searchIdpUsers.name).setError(error),
            );
            throw error;
        }
    }

    /**
     *
     * @param account
     * @param connectionId
     * @param adminUserToResolve
     * @param idpAdminEmailForResolution
     * @param clientType
     * @param providerType
     * @param isSelfResolve
     * @param tenantAdminEmailToResolve
     */
    private async sendIdpAminMismatchResolutionEmail(
        account: Account,
        connectionId: number,
        adminUserToResolve: User,
        idpAdminEmailForResolution: string,
        clientType: ClientType,
        providerType: ProviderType,
        isSelfResolve: boolean,
        tenantAdminEmailToResolve?: string,
    ): Promise<{ validationTokenId: string | null }> {
        try {
            this.logger.log(
                PolloAdapter.acct('Sending email for admin IdP mismatch resolution', account)
                    .setIdentifier({
                        connectionId: connectionId,
                        clientType: ClientType[clientType],
                        providerType: ProviderType[providerType],
                    })
                    .setMetadata({
                        tenantAdminEmailToResolve: adminUserToResolve.email,
                        idpAdminEmailForResolution: idpAdminEmailForResolution,
                    })
                    .setContext(this.constructor.name)
                    .setSubContext(this.sendIdpAminMismatchResolutionEmail.name),
            );

            let ctaUrl: string = '';
            let validationTokenId: string | null = null;

            const idpConnectionAdminMismatchResolutionEmailConfig =
                await this.emailConfig.idpConnectionAdminMismatchResolutionEmail(
                    adminUserToResolve.language,
                    adminUserToResolve.firstName,
                    isSelfResolve,
                    isSelfResolve ? undefined : idpAdminEmailForResolution,
                    tenantAdminEmailToResolve,
                );

            if (isSelfResolve) {
                ({ ctaUrl, validationTokenId } =
                    await this.generateIdpAdminMismatchSelfResolutionUrl(
                        connectionId,
                        adminUserToResolve.email,
                        idpAdminEmailForResolution,
                        clientType,
                        providerType,
                    ));
            }

            const templateVariables = {
                ...idpConnectionAdminMismatchResolutionEmailConfig,
                ...idpConnectionAdminMismatchResolutionEmailConfig.templateCommon,
                ...(isSelfResolve
                    ? {
                          ctaUrl: ctaUrl,
                      }
                    : {
                          paragraphTwo:
                              idpConnectionAdminMismatchResolutionEmailConfig.paragraphTwo,
                      }),
            };

            const toEmail = isSelfResolve ? idpAdminEmailForResolution : adminUserToResolve.email;

            const emailOptions: EmailOptionsType = {
                ...idpConnectionAdminMismatchResolutionEmailConfig,
                toEmail,
                templateVariables: templateVariables,
            };

            await this.emailService.sendEmail(emailOptions, account);

            return { validationTokenId };
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(
                    error.message,
                    account,
                    this.sendIdpAminMismatchResolutionEmail.name,
                ).setError(error),
            );
            throw error;
        }
    }

    /**
     *
     * @param tenantAdminUserToResolve
     * @param idpAdminEmailForResolution
     */
    private async updateUserForIdpAdminMismatchResolution(
        account: Account,
        tenantAdminUserToResolve: User,
        idpAdminEmailForResolution: string,
    ) {
        try {
            this.logger.log(
                PolloMessage.msg('Update admin user for IdP mismatch resolution')
                    .setIdentifier({
                        userId: tenantAdminUserToResolve.id,
                        userEmail: tenantAdminUserToResolve.email,
                    })
                    .setMetadata({
                        idpAdminEmailForResolution: idpAdminEmailForResolution,
                    })
                    .setContext(this.constructor.name)
                    .setSubContext(this.updateUserForIdpAdminMismatchResolution.name),
            );

            if (!hasRole(tenantAdminUserToResolve, [Role.ADMIN])) {
                throw new ConflictException(
                    `Cannot resolve ${tenantAdminUserToResolve.email} since it is not an admin`,
                    ErrorCode.VALIDATION,
                );
            }

            if (await this.usersCoreService.getUserByEmailNoFail(idpAdminEmailForResolution)) {
                throw new ConflictException(
                    `Cannot resolve since email for IdP admin mismatch resolution already exists`,
                    ErrorCode.ENTRY_EXISTS,
                );
            }

            const entry = await this.entryCoreService.getEntryWithoutRelationsByEmailOrFail(
                tenantAdminUserToResolve.email,
            );

            entry.email = idpAdminEmailForResolution;
            await this.entryCoreService.saveEntry(entry);

            tenantAdminUserToResolve.email = idpAdminEmailForResolution;
            await this.usersCoreService.saveUser(tenantAdminUserToResolve, account.id);
        } catch (error) {
            this.logger.error(
                PolloMessage.msg(
                    error.message,
                    this.updateUserForIdpAdminMismatchResolution.name,
                ).setError(error),
            );
            throw error;
        }
    }

    /**
     *
     * @param tenantAdminEmailAddresses
     * @param tenantAdminEmailAddressesNotFoundInIdp
     * @returns {Promise<boolean>}
     */
    private async checkIfAtLeastOneAdminIsResolved(
        tenantAdminEmailAddresses: string[],
        tenantAdminEmailAddressesNotFoundInIdp: string[],
    ): Promise<boolean> {
        try {
            if (
                intersection(tenantAdminEmailAddresses, tenantAdminEmailAddressesNotFoundInIdp)
                    .length < tenantAdminEmailAddresses.length
            ) {
                return true;
            } else {
                return false;
            }
        } catch (error) {
            this.logger.error(
                PolloMessage.msg(
                    error.message,
                    this.checkIfAtLeastOneAdminIsResolved.name,
                ).setError(error),
            );
            throw error;
        }
    }

    /**
     *
     * @param account
     * @param user
     * @returns
     */
    private async checkIfAtLeastOneAdminInAccountDomain(
        account: Account,
        user: User,
        connectionId: number,
    ): Promise<{
        atLeastOneAdminInAccountDomain: boolean;
        tenantAdminsNotInAccountDomain: string[];
    }> {
        const tenantAdminEmails = (
            await this.usersCoreService.getAdminUsers(false, false, true, true)
        ).map(t => t.email);

        const tenantAdminEmailAddressesNotFoundInIdp = (
            await this.getIdpAdminsMismatch(account, user, connectionId)
        ).map(ta => ta.tenantAdminEmailAddressNotFoundInIdp);

        const tenantAdminsNotInAccountDomain: string[] = [];

        tenantAdminEmails.forEach(tenantAdminEmail => {
            if (
                getDomainFromEmail(tenantAdminEmail) !== account.domain &&
                !tenantAdminEmailAddressesNotFoundInIdp.includes(tenantAdminEmail)
            ) {
                tenantAdminsNotInAccountDomain.push(tenantAdminEmail);
            }
        });

        let atLeastOneAdminInAccountDomain: boolean;
        if (hasRole(user, [Role.SERVICE_USER])) {
            atLeastOneAdminInAccountDomain =
                intersection(tenantAdminEmails, tenantAdminsNotInAccountDomain).length <
                tenantAdminEmails.length;
        } else {
            atLeastOneAdminInAccountDomain = !tenantAdminsNotInAccountDomain.includes(user.email);
        }
        return {
            atLeastOneAdminInAccountDomain: atLeastOneAdminInAccountDomain,
            tenantAdminsNotInAccountDomain: tenantAdminsNotInAccountDomain,
        };
    }

    /**
     *
     * @param account
     * @param user
     * @param connectionId
     * @returns
     */
    private async checkIfAtLeastOneAdminInSelectedGroups(
        account: Account,
        user: User,
        connectionId: number,
        idpGroupsToSync: string[],
    ): Promise<{
        atLeastOneAdminInSelectedGroups: boolean;
        tenantAdminsNotInSelectedGroup: string[];
    }> {
        try {
            let tenantAdminEmails = (
                await this.usersCoreService.getAdminUsers(false, false, true, true)
            ).map(t => t.email);

            const tenantAdminEmailAddressesNotFoundInIdp = (
                await this.getIdpAdminsMismatch(account, user, connectionId)
            ).map(ta => ta.tenantAdminEmailAddressNotFoundInIdp);

            const tenantAdminsNotInSelectedGroups: string[] = [];

            const company = await this.companiesService.getCompanyByAccountId(account.id);

            const tenantAdminsWithIdpGroups = await Promise.all(
                tenantAdminEmails.map(async email => {
                    const idpGroups = await this.getIdpGroupsName(account, connectionId, email);
                    return { email, idpGroups };
                }),
            );

            tenantAdminEmails = tenantAdminEmails.filter(
                tenantAdminEmail =>
                    !tenantAdminEmailAddressesNotFoundInIdp.includes(tenantAdminEmail),
            );

            for (const tenantAdmin of tenantAdminsWithIdpGroups) {
                if (
                    intersection(idpGroupsToSync, tenantAdmin.idpGroups).length === 0 &&
                    !tenantAdminEmailAddressesNotFoundInIdp.includes(tenantAdmin.email) &&
                    ((!company.multiDomain &&
                        getDomainFromEmail(tenantAdmin.email) === account.domain) ||
                        company.multiDomain)
                ) {
                    tenantAdminsNotInSelectedGroups.push(tenantAdmin.email);
                }
            }

            let atLeastOneAdminInSelectedGroups: boolean;
            if (hasRole(user, [Role.SERVICE_USER])) {
                atLeastOneAdminInSelectedGroups =
                    intersection(tenantAdminEmails, tenantAdminsNotInSelectedGroups).length <
                    tenantAdminEmails.length;
            } else {
                atLeastOneAdminInSelectedGroups = !tenantAdminsNotInSelectedGroups.includes(
                    user.email,
                );
            }
            return {
                atLeastOneAdminInSelectedGroups: atLeastOneAdminInSelectedGroups,
                tenantAdminsNotInSelectedGroup: tenantAdminsNotInSelectedGroups,
            };
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(
                    error.message,
                    account,
                    this.generateIdpAdminMismatchSelfResolutionUrl.name,
                ).setError(error),
            );
            throw error;
        }
    }

    /**
     *
     * @param connectionId
     * @returns {Promise<ConnectionEntity>}
     */
    private async getIdpConnectionPendingConfirmationById(
        connectionId: number,
    ): Promise<ConnectionEntity> {
        try {
            const connection = await this.getIdpConnectionById(connectionId);

            if (connection.state !== ConnectionState.CONFIGURED_PENDING_CONFIRMATION) {
                throw new ConflictException(
                    `Connection state is not ${
                        ConnectionState[ConnectionState.CONFIGURED_PENDING_CONFIRMATION]
                    }`,
                    ErrorCode.VALIDATION,
                );
            }

            return connection;
        } catch (error) {
            this.logger.error(
                PolloMessage.msg(
                    error.message,
                    this.getIdpConnectionPendingConfirmationById.name,
                ).setError(error),
            );
            throw error;
        }
    }

    /**
     *
     * @param connectionId
     * @returns {Promise<ConnectionEntity>}
     */
    private async getIdpConnectionById(connectionId: number): Promise<ConnectionEntity> {
        try {
            const connection = await this.connectionRepository.findOne({
                where: { id: connectionId },
            });

            if (!connection) {
                throw new NotFoundException(ErrorCode.CONNECTION_NOT_FOUND);
            }

            if (connection.providerType !== ProviderType.IDENTITY) {
                throw new BadRequestException(
                    `Only ${ProviderType[ProviderType.IDENTITY]} connection types are supported`,
                    ErrorCode.VALIDATION,
                );
            }

            return connection;
        } catch (error) {
            this.logger.error(
                PolloMessage.msg(error.message, this.getIdpConnectionById.name).setError(error),
            );
            throw error;
        }
    }

    /**
     *
     * @param email
     * @returns {Promise<Token>}
     */
    private async generateIdpVerificationToken(email: string): Promise<Token> {
        try {
            const entry = await this.entryCoreService.getEntryWithoutRelationsByEmailOrFail(email);

            const verificationToken = await this.tokenRepository.findOne({
                where: {
                    type: TokenType.IDP_CONNECTION_ADMIN_MISMATCH_RESOLUTION,
                    entry: {
                        email: email,
                    },
                },
            });

            if (!isNil(verificationToken)) {
                await this.tokenRepository.remove(verificationToken);
            }

            const expiresAt = moment(new Date())
                .add(config.get('tokens.magicLinkExpiration'), 'hour')
                .toDate();

            const token = new Token();
            token.type = TokenType.IDP_CONNECTION_ADMIN_MISMATCH_RESOLUTION;
            token.expiresAt = expiresAt;
            token.entry = entry;
            return await this.tokenRepository.save(token);
        } catch (error) {
            this.logger.error(
                PolloMessage.msg(error.message, this.generateIdpVerificationToken.name).setError(
                    error,
                ),
            );
            throw error;
        }
    }

    /**
     *
     * @param connectionId
     * @param tenantAdminEmailToResolve
     * @param idpAdminEmailForResolution
     * @param clientType
     * @param providerType
     * @returns {Promise<string>}
     */
    private async generateIdpAdminMismatchSelfResolutionUrl(
        connectionId: number,
        tenantAdminEmailToResolve: string,
        idpAdminEmailForResolution: string,
        clientType: ClientType,
        providerType: ProviderType,
    ): Promise<{
        ctaUrl: string;
        validationTokenId: string;
    }> {
        try {
            const token = await this.generateIdpVerificationToken(tenantAdminEmailToResolve);

            const encryptedEmailAddresses = encrypt(
                JSON.stringify({
                    tenantAdminEmailToResolve,
                    idpAdminEmailForResolution,
                }),
            );

            const ctaUrl = `${config.get(
                'url.webApp',
            )}/account-settings/connections/connection?connId=${connectionId}&provId=${
                ClientType[clientType]
            }&activeTab=manage&isEdit=true&provTypeSelected=${capitalize(
                ProviderType[providerType],
                true,
            )}&tokenId=${token.id}&validation=${encryptedEmailAddresses}`;

            return {
                ctaUrl,
                validationTokenId: token.id,
            };
        } catch (error) {
            this.logger.error(
                PolloMessage.msg(
                    error.message,
                    this.generateIdpAdminMismatchSelfResolutionUrl.name,
                ).setError(error),
            );
            throw error;
        }
    }

    /**
     *
     * @param connection
     * @param idpConnectionConfirmationStepsState
     */
    private async updateIdPConnectionStateMetadata(
        connection: ConnectionEntity,
        idpConnectionConfirmationStepsState: IdpConnectionConfirmationStepsStateType,
    ): Promise<ConnectionMetadata> {
        try {
            const metadata = connection.getMetadata();
            const idpConnectionConfirmationStepsMeta =
                metadata?.idpConnectionConfirmationStepsState ?? {};
            metadata.idpConnectionConfirmationStepsState = {
                ...idpConnectionConfirmationStepsMeta,
                ...idpConnectionConfirmationStepsState,
            };
            connection.setMetadata(metadata);
            await this.connectionRepository.save(connection);

            return connection.getMetadata();
        } catch (error) {
            this.logger.error(
                PolloMessage.msg(
                    error.message,
                    this.updateIdPConnectionStateMetadata.name,
                ).setError(error),
            );
            throw error;
        }
    }

    /**
     *
     * @param email
     * @param tokenId
     * @param tokenType
     * @returns {Promise<IdpConnectionTokenValidationStatus>}
     */
    private async validateToken(
        email: string,
        tokenId: string,
        tokenType: TokenType,
    ): Promise<IdpConnectionTokenValidationStatus> {
        try {
            this.logger.log(
                PolloMessage.msg('Validating token')
                    .setIdentifier({
                        email: email,
                        tokenId: tokenId,
                        tokenType: tokenType,
                    })
                    .setContext(this.constructor.name)
                    .setSubContext(this.validateToken.name),
            );

            const verificationToken = await this.tokenRepository.findOne({
                where: {
                    id: tokenId,
                    type: tokenType,
                    entry: { email: email },
                },
                withDeleted: true,
            });

            if (isNil(verificationToken)) {
                throw new UnauthorizedException(ErrorCode.TOKEN_NOT_FOUND);
            }

            if (!isNil(verificationToken.deletedAt)) {
                return IdpConnectionTokenValidationStatus.TOKEN_ALREADY_USED;
            }

            if (hasExpired(verificationToken.expiresAt)) {
                return IdpConnectionTokenValidationStatus.TOKEN_EXPIRED;
            }

            await this.tokenRepository.softRemove(verificationToken);

            return IdpConnectionTokenValidationStatus.TOKEN_VALIDATED;
        } catch (error) {
            this.logger.warn(
                PolloMessage.msg(error.message, this.validateToken.name).setError(error),
            );
            throw error;
        }
    }

    private get connectionRepository(): ConnectionsRepository {
        return this.getCustomTenantRepository(ConnectionsRepository);
    }

    private get groupsRepository(): GroupRepository {
        return this.getCustomTenantRepository(GroupRepository);
    }

    private get personnelRepository(): PersonnelRepository {
        return this.getCustomTenantRepository(PersonnelRepository);
    }
}
