import { ProviderType } from 'commons/enums/auth/provider-type.enum';

import { EmploymentStatus } from '@drata/enums';
import {
    BadRequestException,
    ConflictException,
    NotFoundException,
    PreconditionFailedException,
} from '@nestjs/common';
import { TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { ApiClientService } from 'app/api-client/api-client.service';
import { IdentityServiceUser as IdentityUser } from 'app/apis/interfaces/identity-service-user.interface';
import { IdpConnectionAdminMismatchResolutionRequestDto } from 'app/companies/connections/dtos/idp-connection-admin-mismatch-resolution-request.dto';
import { IdpConnectionAdminMismatchSearchRequestDto } from 'app/companies/connections/dtos/idp-connection-admin-mismatch-search-request.dto';
import { IdpConnectionDomainsSyncRequestDto } from 'app/companies/connections/dtos/idp-connection-domains-sync-request.dto';
import { IdpConnectionGroupsSyncRequestDto } from 'app/companies/connections/dtos/idp-connection-goups-sync-request.dto';
import { IdpConnectionGroupsSearchRequestDto } from 'app/companies/connections/dtos/idp-connection-groups-search-request.dto';
import { ConnectionEntity } from 'app/companies/connections/entities/connection.entity';
import { IdpConnectionAdminMismatchResolutionStatus } from 'app/companies/connections/enums/idp-connection-admin-mismatch-resolution-status.enum';
import { ConnectionsRepository } from 'app/companies/connections/repositories/connections.repository';
import { IdpConnectionsService } from 'app/companies/connections/services/idp-connections.service';
import { CompaniesService } from 'app/companies/services/companies.service';
import { EDRService } from 'app/edr/services/edr.service';
import { UserAccessData } from 'app/users/entities/user-access-data.class';
import { UserRole } from 'app/users/entities/user-role.entity';
import { User } from 'app/users/entities/user.entity';
import { PersonnelRepository } from 'app/users/personnel/repositories/personnel.repository';
import { UsersCoreService } from 'app/users/services/users-core.service';
import { Account } from 'auth/entities/account.entity';
import { Token } from 'auth/entities/token.entity';
import { EntryCoreService } from 'auth/services/entry-core.service';
import { EntryService } from 'auth/services/entry.service';
import { CacheService } from 'cache/cache.service';
import { EmailConfig } from 'commons/configs/email.config';
import { ClientType } from 'commons/enums/auth/client-type.enum';
import { ConnectionState } from 'commons/enums/auth/state-type.enum';
import { Role } from 'commons/enums/users/role.enum';
import { createAppTestingModule } from 'commons/helpers/app-testing-module.helper';
import { MockType } from 'commons/mocks/types/mock.type';
import { EmailService } from 'commons/services/email.service';
import { mock } from 'jest-mock-extended';
import { noop } from 'lodash';
import { TenancyContextMock } from 'tenancy/contexts/__mocks__/tenancy.context';
import { TenancyContext } from 'tenancy/contexts/tenancy.context';
import { factory, useSeeding } from 'typeorm-seeding';

class IdpUser implements IdentityUser {
    primaryEmail: string;
    firstName: string;

    constructor(primaryEmail: string, firstName: string) {
        this.primaryEmail = primaryEmail;
        this.firstName = firstName;
    }
    getId(): string | null {
        throw new Error('Method not implemented.');
    }
    getFirstName(): string | null {
        throw new Error('Method not implemented.');
    }
    getLastName(): string | null {
        throw new Error('Method not implemented.');
    }
    getFullName(): string | null {
        throw new Error('Method not implemented.');
    }
    getEmails(): string[] {
        throw new Error('Method not implemented.');
    }
    getAvatarUrl(): string | null {
        throw new Error('Method not implemented.');
    }
    getExternalIds<T>(): T[] | null {
        throw new Error('Method not implemented.');
    }
    getJobTitle(): string | null {
        throw new Error('Method not implemented.');
    }
    isActive(): boolean {
        throw new Error('Method not implemented.');
    }
    isContractor(): boolean {
        throw new Error('Method not implemented.');
    }
    isGeneric(): boolean {
        throw new Error('Method not implemented.');
    }
    getEmploymentStatus(): EmploymentStatus {
        throw new Error('Method not implemented.');
    }
    explainEmploymentStatus(): string {
        throw new Error('Method not implemented.');
    }
    hasMfa(): boolean {
        throw new Error('Method not implemented.');
    }
    getAccessData(): UserAccessData | null {
        throw new Error('Method not implemented.');
    }
    createdAt(): Date | null {
        throw new Error('Method not implemented.');
    }
    getStartDate(): string {
        throw new Error('Method not implemented.');
    }
    getSeparationDate(): string | null {
        throw new Error('Method not implemented.');
    }

    getPrimaryEmail(): string {
        return this.primaryEmail;
    }
}

const DefaultConnectionId = 1;

const groups = [
    {
        name: 'shelfset group',
        email: '<EMAIL>',
    },
    {
        name: 'Drata group',
        email: '<EMAIL>',
    },
];

// IDP users
const users: IdentityUser[] = [
    new IdpUser('<EMAIL>', 'user one'),
    new IdpUser('<EMAIL>', 'user two'),
];

// Admin Tenant User
const userAdmin = new User();
userAdmin.email = '<EMAIL>';
userAdmin.firstName = 'Admin';
const adminRole = new UserRole();
adminRole.role = Role.ADMIN;
userAdmin.roles = [adminRole];

// Auditor Tenant User
const userAuditor = new User();
userAuditor.email = '<EMAIL>';
userAuditor.firstName = 'Auditor';
const auditorRole = new UserRole();
auditorRole.role = Role.AUDITOR;
userAuditor.roles = [auditorRole];

// Admin to resolve Tenant User
const userAdminToResolve = new User();
userAdminToResolve.email = '<EMAIL>';
userAdminToResolve.firstName = 'Admin to Resolve';
adminRole.role = Role.ADMIN;
userAdminToResolve.roles = [adminRole];

const usersTenant = [userAdmin, userAuditor, userAdminToResolve];

let idpConnectionsService: IdpConnectionsService;
let apiClientService: any;
let account: Account;
let connectionsRepository: MockType<ConnectionsRepository>;

const companiesService = {
    getCompanyByAccountId: () => account,
};
const entryService = {};
const entryCoreService = {};
const eDRService = {};
const emailService = {
    sendEmail: jest.fn(),
};
const tokenRepository = {
    findOne: () => null,
};
const usersService = {
    getUserByEmailNoFail: (email: string) => {
        return usersTenant.find(i => i.email === email);
    },
    getUsersByEmails: (emails: string[]) => {
        return usersTenant.filter(i => emails.includes(i.email));
    },
};
const emailConfig = {
    idpConnectionAdminMismatchResolutionEmail: () => ({}),
};

beforeAll(async () => {
    await useSeeding({ configName: './src/tests/ormconfig-unit-tests' });

    account = await factory(Account)().make();
});

beforeEach(async () => {
    apiClientService = {
        api: () => {
            return {
                initializeGroupsClient: () => noop(),
                getGroups: () => ({ data: groups }),
                getUsers: () => ({ data: users }),
            };
        },
    };

    const tenancyContextMock = TenancyContextMock();
    const module: TestingModule = await createAppTestingModule({
        providers: [
            IdpConnectionsService,
            {
                provide: TenancyContext,
                useValue: tenancyContextMock,
            },
            {
                provide: ApiClientService,
                useValue: apiClientService,
            },
            {
                provide: CompaniesService,
                useValue: companiesService,
            },
            {
                provide: EntryService,
                useValue: entryService,
            },
            {
                provide: EntryCoreService,
                useValue: entryCoreService,
            },
            {
                provide: EDRService,
                useValue: eDRService,
            },
            {
                provide: EmailConfig,
                useValue: emailConfig,
            },
            {
                provide: EmailService,
                useValue: emailService,
            },
            {
                provide: getRepositoryToken(Token),
                useValue: tokenRepository,
            },
            {
                provide: UsersCoreService,
                useValue: usersService,
            },
            {
                provide: CacheService,
                useValue: mock<CacheService>(),
            },
        ],
    }).compile();

    idpConnectionsService = module.get<IdpConnectionsService>(IdpConnectionsService);

    connectionsRepository = tenancyContextMock.getCustomRepository(ConnectionsRepository);

    jest.spyOn(idpConnectionsService, 'generateIdpVerificationToken' as any).mockResolvedValue(
        'token',
    );
});

afterEach(() => {
    jest.clearAllMocks();
});

const createGoogleIdpConnectionPending = (): ConnectionEntity => {
    const connectionIdp = new ConnectionEntity();
    connectionIdp.providerType = ProviderType.IDENTITY;
    connectionIdp.clientType = ClientType.GOOGLE;
    connectionIdp.state = ConnectionState.CONFIGURED_PENDING_CONFIRMATION;
    connectionIdp.id = DefaultConnectionId;
    return connectionIdp;
};

const createOktaIdpConnectionActive = (): ConnectionEntity => {
    const connectionIdp = new ConnectionEntity();
    connectionIdp.providerType = ProviderType.IDENTITY;
    connectionIdp.clientType = ClientType.OKTA_IDENTITY;
    connectionIdp.state = ConnectionState.CONFIGURED_PENDING_CONFIRMATION;
    connectionIdp.id = DefaultConnectionId;
    return connectionIdp;
};

/*
    Search groups ordered alphanumeric
*/
describe('Search Idp groups', () => {
    /*
        method: searchIdpGroups
    */
    test('get idp groups without searchGroupsPrefix must get first 2 groups names order alphanumeric', async () => {
        const connectionIdp = createGoogleIdpConnectionPending();
        connectionsRepository.findOne?.mockResolvedValue(connectionIdp);

        const dto = new IdpConnectionGroupsSearchRequestDto();
        const groupsIdp = await idpConnectionsService.searchIdpGroups(
            account,
            DefaultConnectionId,
            dto,
        );

        expect(groupsIdp).toHaveLength(2);
        expect(groupsIdp[0]).toEqual('Drata group');
        expect(groupsIdp[1]).toEqual('shelfset group');
    });

    test('search with searchGroupsPrefix equal "groups" must get two groups', async () => {
        const connectionIdp = createGoogleIdpConnectionPending();
        connectionsRepository.findOne?.mockResolvedValue(connectionIdp);

        const dto = new IdpConnectionGroupsSearchRequestDto();
        dto.searchGroupsPrefix = 'groups';
        const groupsIdp = await idpConnectionsService.searchIdpGroups(
            account,
            DefaultConnectionId,
            dto,
        );

        expect(groupsIdp).toHaveLength(2);
    });
});

describe('Search Idp users', () => {
    /*
        Scenario: need to know admins tenant, that are not in idp admins
    */

    /* method: searchIdpEmailsByPrefixForAdminMismatchResolution */
    test('must fail if not send the tenantAdminEmailToResolve param', async () => {
        const connectionIdp = createGoogleIdpConnectionPending();
        connectionsRepository.findOne?.mockResolvedValue(connectionIdp);

        const dto = new IdpConnectionAdminMismatchSearchRequestDto();
        await expect(
            idpConnectionsService.searchIdpEmailsByPrefixForAdminMismatchResolution(
                account,
                DefaultConnectionId,
                dto,
            ),
        ).rejects.toThrow(new NotFoundException('Not Found'));
    });

    test('must fail if tenantAdminEmailToResolve is not a admin', async () => {
        const connectionIdp = createGoogleIdpConnectionPending();
        connectionsRepository.findOne?.mockResolvedValue(connectionIdp);

        const dto = new IdpConnectionAdminMismatchSearchRequestDto();
        dto.tenantAdminEmailToResolve = '<EMAIL>';
        await expect(
            idpConnectionsService.searchIdpEmailsByPrefixForAdminMismatchResolution(
                account,
                DefaultConnectionId,
                dto,
            ),
        ).rejects.toThrow(
            new ConflictException('Cannot <NAME_EMAIL> is not an admin'),
        );
    });

    test('must get a list of idp users', async () => {
        const connectionIdp = createGoogleIdpConnectionPending();
        connectionsRepository.findOne?.mockResolvedValue(connectionIdp);

        const dto = new IdpConnectionAdminMismatchSearchRequestDto();
        dto.tenantAdminEmailToResolve = '<EMAIL>';
        const admins =
            await idpConnectionsService.searchIdpEmailsByPrefixForAdminMismatchResolution(
                account,
                DefaultConnectionId,
                dto,
            );
        expect(admins).toHaveLength(2);
    });

    test('if the admin is into the idp users must get a list of idp users without him', async () => {
        const connectionIdp = createGoogleIdpConnectionPending();
        connectionsRepository.findOne?.mockResolvedValue(connectionIdp);

        // add the admin into the idp users
        users.push(new IdpUser('<EMAIL>', 'Admin'));

        const dto = new IdpConnectionAdminMismatchSearchRequestDto();
        dto.tenantAdminEmailToResolve = '<EMAIL>';
        const admins =
            await idpConnectionsService.searchIdpEmailsByPrefixForAdminMismatchResolution(
                account,
                DefaultConnectionId,
                dto,
            );

        expect(admins).toHaveLength(2);
        expect(admins).not.toContain('<EMAIL>');
    });
});

describe('Resolve Idp admins mismatch', () => {
    /*
        Scenario: resolve tenants admins that are not in idp admins
    */

    /* method: resolveIdpAdminMismatch */
    test('must fail if not send the tenantAdminEmailToResolve or tenantAdminEmailToResolve param', async () => {
        const connectionIdp = createGoogleIdpConnectionPending();
        connectionsRepository.findOne?.mockResolvedValue(connectionIdp);

        const dto = new IdpConnectionAdminMismatchResolutionRequestDto();
        await expect(
            idpConnectionsService.resolveIdpAdminMismatch(
                account,
                userAdmin,
                dto,
                connectionIdp.id,
            ),
        ).rejects.toThrow(
            new BadRequestException(
                'Invalid tenantAdminEmailToResolve and/or idpAdminEmailForResolution provided for resolution',
            ),
        );
    });
    test('must fail if not send the tenantAdminEmailToResolve or tenantAdminEmailToResolve param', async () => {
        const connectionIdp = createGoogleIdpConnectionPending();
        connectionsRepository.findOne?.mockResolvedValue(connectionIdp);

        const dto = new IdpConnectionAdminMismatchResolutionRequestDto();
        await expect(
            idpConnectionsService.resolveIdpAdminMismatch(
                account,
                userAdmin,
                dto,
                connectionIdp.id,
            ),
        ).rejects.toThrow(
            new BadRequestException(
                'Invalid tenantAdminEmailToResolve and/or idpAdminEmailForResolution provided for resolution',
            ),
        );
    });

    test('must user not found if tenantAdminEmailToResolve not exists', async () => {
        const connectionIdp = createGoogleIdpConnectionPending();
        connectionsRepository.findOne?.mockResolvedValue(connectionIdp);

        const dto = new IdpConnectionAdminMismatchResolutionRequestDto();
        dto.idpAdminEmailForResolution = '<EMAIL>';
        dto.tenantAdminEmailToResolve = '<EMAIL>';
        await expect(
            idpConnectionsService.resolveIdpAdminMismatch(
                account,
                userAdmin,
                dto,
                connectionIdp.id,
            ),
        ).rejects.toThrow(new NotFoundException('Not Found'));
    });

    test('must resolve adminToResolve sending email', async () => {
        const connectionIdp = createGoogleIdpConnectionPending();
        connectionsRepository.findOne?.mockResolvedValue(connectionIdp);

        const dto = new IdpConnectionAdminMismatchResolutionRequestDto();
        dto.idpAdminEmailForResolution = '<EMAIL>';
        dto.tenantAdminEmailToResolve = '<EMAIL>';
        const result = await idpConnectionsService.resolveIdpAdminMismatch(
            account,
            userAdmin,
            dto,
            connectionIdp.id,
        );
        expect(emailService.sendEmail).toBeCalled();

        expect(result.tenantAdminEmailToResolve).toEqual('<EMAIL>');
        expect(result.idpAdminEmailForResolution).toEqual('<EMAIL>');
        expect(result.idpConnectionAdminMismatchResolutionStatus).toEqual(
            IdpConnectionAdminMismatchResolutionStatus.ADMIN_MISMATCH_RESOLUTION_EMAIL_SENT,
        );
    });
});

describe('updateIdpConnectionStepAdminMismatch', () => {
    test('Can not update idpConnectionConfirmationStepsState metadata because connection does not need confirmation', async () => {
        const nonGoogleIdpConnection = createOktaIdpConnectionActive();
        connectionsRepository.findOne?.mockResolvedValue(nonGoogleIdpConnection);

        await expect(
            idpConnectionsService.updateIdpConnectionStepAdminMismatch(
                account,
                userAdmin,
                nonGoogleIdpConnection.id,
            ),
        ).rejects.toThrow(
            new PreconditionFailedException(`Connection doesn't need IdP confirmation`),
        );
    });
});

describe('updateIdpConnectionStepMultiDomainSync', () => {
    test('Can not update idpConnectionConfirmationStepsState metadata because connection does not need confirmation', async () => {
        const nonGoogleIdpConnection = createOktaIdpConnectionActive();
        connectionsRepository.findOne?.mockResolvedValue(nonGoogleIdpConnection);

        await expect(
            idpConnectionsService.updateIdpConnectionStepMultiDomainSync(
                account,
                userAdmin,
                {} as IdpConnectionDomainsSyncRequestDto,
                nonGoogleIdpConnection.id,
            ),
        ).rejects.toThrow(
            new PreconditionFailedException(`Connection doesn't need IdP confirmation`),
        );
    });
});

describe('updateIdpConnectionStepGroupSync', () => {
    test('Can not update idpConnectionConfirmationStepsState metadata because connection does not need confirmation', async () => {
        const nonGoogleIdpConnection = createOktaIdpConnectionActive();
        connectionsRepository.findOne?.mockResolvedValue(nonGoogleIdpConnection);

        await expect(
            idpConnectionsService.updateIdpConnectionStepGroupSync(
                account,
                userAdmin,
                {} as IdpConnectionGroupsSyncRequestDto,
                nonGoogleIdpConnection.id,
            ),
        ).rejects.toThrow(
            new PreconditionFailedException(`Connection doesn't need IdP confirmation`),
        );
    });
});

describe('getIdpSyncStats', () => {
    test('returns only domains with positive personnel counts when multi-domain enabled', async () => {
        // Arrange
        const connectionIdp = createGoogleIdpConnectionPending();
        const meta = { multiDomain: true, lastSyncedAt: new Date() } as any;
        connectionIdp.setMetadata(meta);
        connectionsRepository.findOne?.mockResolvedValue(connectionIdp);

        // Enable company-level multi-domain
        (account as any).multiDomain = new Date();

        // Mock API domains client
        apiClientService.api = () => ({
            initializeDomainsClient: () => noop(),
            getDomains: () => ({
                data: [
                    { domainName: 'drata.com' },
                    { domainName: 'socpilot.com' },
                    { domainName: 'empty.com' },
                ],
                token: null,
            }),
        });

        // Mock personnel under the connection
        // obtain the repository mock from the TenancyContext used by the service
        const tenancyContextMock = (idpConnectionsService as any)._tenancyContext;
        const repo = tenancyContextMock.getCustomRepository(PersonnelRepository);

        const personnel = [
            { user: { email: '<EMAIL>' } },
            { user: { email: '<EMAIL>' } },
            { user: { email: '<EMAIL>' } },
        ];

        // If we have the repo mock via tenancy context, use it; otherwise fallback (should not happen in this suite)
        if (repo && repo.findByConnectionId) {
            repo.findByConnectionId.mockResolvedValue(personnel);
        }

        // Act
        const stats = await idpConnectionsService.getIdpSyncStats(account, DefaultConnectionId);

        // Assert
        const expectedCounts = personnel.reduce<Record<string, number>>((acc, p) => {
            const domain = p.user.email.split('@')[1];
            acc[domain] = (acc[domain] ?? 0) + 1;
            return acc;
        }, {});
        const returnedDomains = stats.domains.map(d => d.name);

        // No zero-count domains
        expect(returnedDomains).not.toContain('empty.com');
        // All returned domains have positive totals and match computed counts
        stats.domains.forEach(d => {
            expect(d.total).toBeGreaterThan(0);
            expect(d.total).toBe(expectedCounts[d.name]);
        });
        // The number of returned domains equals the number of domains with > 0 count
        const nonZeroCountDomains = Object.keys(expectedCounts).length;
        expect(stats.domains).toHaveLength(nonZeroCountDomains);
    });

    test('returns empty array when all domains have zero personnel', async () => {
        // Arrange
        const connectionIdp = createGoogleIdpConnectionPending();
        const meta = { multiDomain: true, lastSyncedAt: new Date() } as any;
        connectionIdp.setMetadata(meta);
        connectionsRepository.findOne?.mockResolvedValue(connectionIdp);
        (account as any).multiDomain = new Date();

        apiClientService.api = () => ({
            initializeDomainsClient: () => noop(),
            getDomains: () => ({
                data: [{ domainName: 'drata.com' }, { domainName: 'socpilot.com' }],
                token: null,
            }),
        });

        const tenancyContextMock = (idpConnectionsService as any)._tenancyContext;
        const repo = tenancyContextMock.getCustomRepository(PersonnelRepository);
        repo.findByConnectionId.mockResolvedValue([]);

        // Act
        const stats = await idpConnectionsService.getIdpSyncStats(account, DefaultConnectionId);

        // Assert
        expect(stats.domains).toHaveLength(0);
    });
});
