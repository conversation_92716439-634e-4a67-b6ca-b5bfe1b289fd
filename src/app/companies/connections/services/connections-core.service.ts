import { ErrorCode } from '@drata/enums';
import { BadRequestException, Injectable } from '@nestjs/common';
import { ApiClientService } from 'app/api-client/api-client.service';
import { ApiClientFactory } from 'app/apis/factories/api-client.factory';
import { OAuth2Api } from 'app/apis/providers/oauth2.api';
import { ConnectionMetadata } from 'app/companies/connections/classes/connection-metadata.class';
import { ConnectionProviderTypeEntity } from 'app/companies/connections/entities/connection-provider-type.entity';
import { ConnectionEntity } from 'app/companies/connections/entities/connection.entity';
import { ConnectionsRepository } from 'app/companies/connections/repositories/connections.repository';
import { ConnectionFilterData } from 'app/companies/connections/types/connection-filter-data.type';
import { GetConnectionParams } from 'app/companies/connections/types/get-connection-params.interface';
import { EventsService } from 'app/events/events.service';
import { ControlTestInstanceHistory } from 'app/monitors/entities/control-test-instance-history.entity';
import { ControlTestInstanceHistoryRepository } from 'app/monitors/repositories/control-test-instance-history.repository';
import { Account } from 'auth/entities/account.entity';
import { ClientTypeDescription } from 'auth/entities/client-type-description-map.enum';
import { PolloAdapter } from 'commons/adapters/pollo.adapter';
import { ClientType } from 'commons/enums/auth/client-type.enum';
import { ProviderType } from 'commons/enums/auth/provider-type.enum';
import { ConnectionState } from 'commons/enums/auth/state-type.enum';
import { NotFoundException } from 'commons/exceptions/not-found.exception';
import {
    GithubEnterpriseClientTypes,
    needsConnectionInDraft,
    needsMisconfiguredConnection,
} from 'commons/helpers/connection.helper';
import { filterConnectionsForCurrentProduct, getProductId } from 'commons/helpers/products.helper';
import { promiseEmptyArray } from 'commons/helpers/query.helper';
import { AppService } from 'commons/services/app.service';
import { cloneDeep, isEmpty, isNil } from 'lodash';
import { In, IsNull, Not, UpdateResult } from 'typeorm';

@Injectable()
export class ConnectionsCoreService extends AppService {
    constructor(
        private readonly factory: ApiClientFactory,
        private readonly apiClientService: ApiClientService,
        private readonly eventsService: EventsService,
    ) {
        super();
    }

    /**
     * Get a set of "connected" connections by their client type including deleted connections,
     *
     * @param clientType
     * @param account
     * @returns
     */
    async getConnectionsByClientTypeWithDeleted(
        clientType: ClientType,
        account?: Account,
    ): Promise<ConnectionEntity[]> {
        try {
            const connections = await this.connectionRepository.find({
                withDeleted: true,
                where: {
                    clientType,
                    connectedAt: Not(IsNull()),
                },
            });

            if (!isNil(account)) {
                this.logger.log(
                    PolloAdapter.acct('Return connections filtered by current product', account),
                );
                return filterConnectionsForCurrentProduct(account, connections);
            }

            this.logger.log(PolloAdapter.acct('Return all connections', account));
            return connections;
        } catch (error) {
            this.logger.log(
                PolloAdapter.acct(`Failed to get connection: ${error.message}`, account).setError(
                    error,
                ),
            );
            return [];
        }
    }

    /**
     * Updates the connection metadata updating all the connection provided with the new metadata.
     *
     * Note: This method could lead to unstable connection state due old versions of the connection instances.
     * See https://drata.atlassian.net/browse/ENG-53244
     *
     */

    async updateConnectionMetadata(
        connection: ConnectionEntity,
        metadataToUpdate: ConnectionMetadata,
    ): Promise<void> {
        const connectionToUpdate = cloneDeep<ConnectionEntity>(connection);

        connectionToUpdate.setMetadata(metadataToUpdate);

        await this.connectionRepository.save(connectionToUpdate);
    }

    getActiveConnectionByClientTypeNoFail(
        clientType: ClientType,
    ): Promise<ConnectionEntity | null> {
        return this.connectionRepository.findOneBy({
            clientType,
            connectedAt: Not(IsNull()),
        });
    }

    /**
     * Atomic transaction to update only the metadata attribute of a connection.
     */
    async updateOnlyConnectionMetadata(
        connectionId: number,
        metadata: ConnectionMetadata,
    ): Promise<UpdateResult> {
        return this.connectionRepository.update(
            { id: connectionId },
            { metadata: ConnectionMetadata.stringify(metadata) },
        );
    }

    async updateNonDeletedConnection(
        account: Account,
        connection: ConnectionEntity,
    ): Promise<void> {
        const conn = await this.connectionRepository.findOne({
            where: { id: connection.id },
            withDeleted: true,
        });

        if (isNil(conn) || !isNil(conn.deletedAt)) {
            this.logger.error(
                PolloAdapter.acct(
                    `Connection with id ${connection.id} not found or deleted`,
                    account,
                ),
            );
            throw new NotFoundException(ErrorCode.CONNECTION_NOT_FOUND);
        }

        await this.connectionRepository.save(connection);
    }

    getSingleSignOnConnection(): Promise<ConnectionEntity | null> {
        return this.connectionRepository.findOneBy({
            providerType: ProviderType.SINGLE_SIGN_ON,
            connectedAt: Not(IsNull()),
        });
    }

    getEnterpriseSSOConnection(): Promise<ConnectionEntity | null> {
        return this.connectionRepository.findOneBy({
            providerType: ProviderType.ENTERPRISE_SSO,
            connectedAt: Not(IsNull()),
        });
    }

    getActiveConnectionsByClientTypes(clientTypes: ClientType[]): Promise<ConnectionEntity[]> {
        return this.connectionRepository.findBy({
            connectedAt: Not(IsNull()),
            clientType: In(clientTypes),
        });
    }

    async getConnectionsByClientTypeAndProductId(
        clientType: ClientType,
        productId: number,
    ): Promise<ConnectionEntity[]> {
        return this.connectionRepository.findBy({
            clientType,
            connectedAt: Not(IsNull()),
            products: { id: productId },
        });
    }

    async createUserAccessReviewCsvConnection(account: Account) {
        const [storedConnection] = await this.connectionRepository.getConnectionsByClientType(
            ClientType.UAR_CSV,
        );

        if (storedConnection) {
            this.log('The Dummy CSV connection was already created', account);

            return storedConnection;
        }

        const enabledAt = new Date();
        const providerTypes = new ConnectionProviderTypeEntity();
        providerTypes.providerType = ProviderType.USER_ACCESS_REVIEW;
        providerTypes.enabledAt = enabledAt;

        const manualConnection = new ConnectionEntity();
        manualConnection.clientType = ClientType.UAR_CSV;
        manualConnection.providerType = ProviderType.USER_ACCESS_REVIEW;
        manualConnection.connectionProviderTypes = [providerTypes];
        manualConnection.metadata = '{}';
        manualConnection.accountId = account.id;
        manualConnection.state = ConnectionState.ACTIVE;
        manualConnection.connectedAt = enabledAt;

        return this.connectionRepository.save(manualConnection);
    }

    public getConnectionHistory(): Promise<ConnectionEntity[]> {
        return this.connectionRepository.find({ withDeleted: true });
    }

    public async createUnfinishedConnection(
        account: Account,
        clientType: ClientType,
        providerTypes: ProviderType[],
        metadata?: ConnectionMetadata,
        clientId?: string,
    ): Promise<ConnectionEntity | null> {
        let connection: ConnectionEntity | null;

        connection = await this.connectionRepository.getConnectionByProviderTypesAndClientType(
            providerTypes,
            clientType,
        );

        /**
         * If this is a GitHub Enterprise connection and it is active - we have to null out the result
         * in order to support multi-instance. The above method returns all connections of this type
         * and works correctly if the connection is in the second stage - so do this sanity check here.
         */
        if (
            !isNil(connection) &&
            GithubEnterpriseClientTypes.includes(connection.clientType) &&
            connection.state === ConnectionState.ACTIVE &&
            !isNil(connection.connectedAt)
        ) {
            connection = null;
        }

        if (isNil(connection)) {
            connection = new ConnectionEntity();
            connection.clientType = clientType;
            connection.providerType = providerTypes[0];
            connection.accountId = account.id;
            connection.ranking = null; // set ranking to null for new connections

            if (needsConnectionInDraft(connection.clientType)) {
                connection.state = ConnectionState.IN_PROGRESS;
            } else if (needsMisconfiguredConnection(connection.clientType)) {
                connection.state = ConnectionState.MISCONFIGURED;
            } else {
                connection.state = ConnectionState.ACTIVE;
            }

            connection.connectionProviderTypes = providerTypes.map(pt => {
                const connectionProviderType = new ConnectionProviderTypeEntity();
                connectionProviderType.providerType = pt;

                return connectionProviderType;
            });
        }

        if (!isNil(metadata)) {
            connection.setMetadata(metadata);
        }

        if (!isNil(clientId)) {
            connection.clientId = clientId;
        }

        return this.connectionRepository.save(connection);
    }

    public async getConnectionsByProviderType(
        providerType: ProviderType,
        account?: Account,
    ): Promise<ConnectionEntity[]> {
        const connections = await this.connectionRepository.getConnectionsByProviderTypesV2({
            providerTypes: [providerType],
            includeUser: true,
        });

        if (!isNil(account)) {
            return filterConnectionsForCurrentProduct(account, connections);
        }

        return connections;
    }

    public getConnectionsByClientTypes(
        clientTypes: ClientType[],
        productId?: number,
    ): Promise<ConnectionEntity[]> {
        if (isEmpty(clientTypes)) {
            return promiseEmptyArray<ConnectionEntity>();
        }

        return this.connectionRepository.getConnectionsByClientTypes(clientTypes, productId);
    }

    public async getConnectionsByClientType(
        clientType: ClientType,
        account?: Account,
    ): Promise<ConnectionEntity[]> {
        const connections = await this.connectionRepository.findBy({
            clientType,
            connectedAt: Not(IsNull()),
        });

        if (!isNil(account)) {
            return filterConnectionsForCurrentProduct(account, connections);
        }

        return connections;
    }

    public async getConnectionWriteAccessByClientTypes(
        clientTypes: ClientType[],
    ): Promise<ConnectionEntity | null> {
        if (isNil(clientTypes)) {
            return null;
        }
        const connections = await this.connectionRepository.findBy({
            clientType: In(clientTypes),
            connectedAt: Not(IsNull()),
        });

        for (const conn of connections) {
            if (isNil(conn.deletedAt) && conn.getMetadata().writeAccessEnabled) {
                return conn;
            }
        }
        return null;
    }

    public getConnectionById(id: number): Promise<ConnectionEntity> {
        return this.connectionRepository.findOneByOrFail({ id });
    }

    public async getConnectionByProviderType(
        providerType: ProviderType,
    ): Promise<ConnectionEntity> {
        const [connection] = await this.connectionRepository.getConnectionsByProviderTypesV2({
            providerTypes: [providerType],
            connectedOnly: true,
        });

        return connection;
    }

    public getConnectionByClientType(clientType: ClientType): Promise<ConnectionEntity> {
        return this.connectionRepository.findOneByOrFail({
            clientType,
        });
    }

    public async updateConnection(connection: ConnectionEntity): Promise<void> {
        await this.connectionRepository.save(connection);
    }

    public getActiveCustomConnectionWithCustomResourcesByConnectionId(
        connectionId: number,
        customResourceId?: number,
    ): Promise<ConnectionEntity | null> {
        return this.connectionRepository.getActiveCustomConnectionWithCustomResourcesByConnectionId(
            connectionId,
            customResourceId,
        );
    }

    public getTicketingConnectionById(id: number): Promise<ConnectionEntity> {
        return this.connectionRepository.getTicketingConnectionById(id);
    }

    public getTicketingConnectionByDomain(domain: string): Promise<ConnectionEntity> {
        return this.connectionRepository.getTicketingConnectionByDomain(domain);
    }

    public getConnectionByProviderTypeWithoutRelations(
        providerType: ProviderType,
    ): Promise<ConnectionEntity | null> {
        return this.connectionRepository.findOne({
            where: { providerType, connectedAt: Not(IsNull()) },
            loadEagerRelations: false,
        });
    }

    public getTicketingConnectionByDomainAndProduct(
        account: Account,
        domain: string,
    ): Promise<ConnectionEntity> {
        return this.connectionRepository.getTicketingConnectionByDomainAndProduct(account, domain);
    }

    public getActiveAndHealthyConnectionsByIds(
        connectionIds: Array<number>,
    ): Promise<ConnectionEntity[]> {
        if (isEmpty(connectionIds)) {
            return Promise.resolve([]);
        }

        return this.connectionRepository.getActiveAndHealthyConnectionsWithParams({
            connectionIds,
        });
    }

    public getConnectionsByProviderTypes(
        providerTypes: ProviderType[],
        productId?: number,
        successfulOnly = false,
        sortByRanking = false,
    ): Promise<ConnectionEntity[]> {
        return this.connectionRepository.getConnectionsByProviderTypesV2({
            providerTypes,
            connectedOnly: true,
            successfulOnly,
            productIds: !isNil(productId) ? [productId] : [],
            sortByRanking,
        });
    }

    getConnections(params: GetConnectionParams): Promise<ConnectionEntity[]> {
        const { providerTypes, clientTypes, clientIds } = params;

        return this.connectionRepository.find({
            where: {
                providerType: In(providerTypes),
                clientType: In(clientTypes),
                clientId: In(clientIds),
                deletedAt: IsNull(),
                connectedAt: Not(IsNull()),
            },
        });
    }

    async getActiveConnectionsByProductId(account: Account): Promise<ConnectionEntity[]> {
        const organizationalConnection =
            await this.connectionRepository.getOrganizationalActiveConnections();

        const productActiveConnections =
            await this.connectionRepository.getActiveConnectionsByProductId(getProductId(account));

        return [...organizationalConnection, ...productActiveConnections];
    }

    getConnectionsByProviderTypeAndProductIds(
        providerType: ProviderType,
        productIds?: number[],
    ): Promise<ConnectionEntity[]> {
        return this.connectionRepository.getConnectionsByProviderTypesV2({
            providerTypes: [providerType],
            productIds,
            includeUser: true,
            connectedOnly: true,
        });
    }

    // this returns a single connection that is the highest ranked
    // TODO: will be removed and replaced with getIdentityProviderConnections()
    // when multi IdP is implemented and we are fetching ranked connections
    getIdentityProviderConnection(): Promise<ConnectionEntity> {
        return this.connectionRepository.getConnectionHighestRanked([ProviderType.IDENTITY]);
    }

    // this returns multiple connections ordered by ranking
    // TODO: consume this in synchronizations when multi IdP is implemented and we are fetching ranked connections
    getIdentityProviderConnections(): Promise<ConnectionEntity[]> {
        return this.connectionRepository.getConnectionsRanked([ProviderType.IDENTITY]);
    }

    getActiveConnectionsWithExclusions(): Promise<ConnectionEntity[]> {
        return this.connectionRepository.find({
            where: { connectedAt: Not(IsNull()) },
            relations: ['monitorInstanceExclusions'],
        });
    }

    getConnectionsByClientTypesWithProducts(
        clientTypes: ClientType[],
    ): Promise<ConnectionEntity[]> {
        if (isEmpty(clientTypes)) {
            return promiseEmptyArray();
        }

        return this.connectionRepository.getConnectionsByClientTypesWithProducts(clientTypes);
    }

    async getConnectionIdsWithWriteAccessByClientTypes(
        clientTypes: ClientType[],
    ): Promise<number[]> {
        if (isNil(clientTypes)) {
            return null;
        }

        const connections = await this.connectionRepository.find({
            where: {
                clientType: In(clientTypes),
                connectedAt: Not(IsNull()),
            },
            select: {
                id: true,
                metadata: true,
            },
        });

        return connections
            .filter(conn => conn.getMetadata().writeAccessEnabled)
            .map(conn => conn.id);
    }

    async getIncompleteConnectionOrFail(params: GetConnectionParams): Promise<ConnectionEntity> {
        const incompleteConnection = this.connectionRepository.getIncompleteConnection(params);

        if (isNil(incompleteConnection)) {
            return Promise.reject(new NotFoundException(ErrorCode.CONNECTION_NOT_FOUND));
        } else {
            return incompleteConnection;
        }
    }

    public getActiveConnections(): Promise<ConnectionEntity[]> {
        return this.connectionRepository.findBy({ connectedAt: Not(IsNull()) });
    }

    async getActiveConnectionsWithProducts(): Promise<ConnectionEntity[]> {
        return this.connectionRepository.getAllActiveConnectionsWithProducts();
    }

    public getActiveAndHealthyConnections(): Promise<ConnectionEntity[]> {
        return this.connectionRepository.getActiveAndHealthyConnectionsWithParams();
    }

    async getConnectionsTestInfo(
        account: Account,
        controlTestInstanceId: number,
        fetchLatestConnections?: boolean,
    ): Promise<ConnectionFilterData[]> {
        try {
            const dbName = account?.databaseName;

            let latestControlTestInstance: ControlTestInstanceHistory | null = null;
            if (fetchLatestConnections) {
                const instanceHistory =
                    await this.controlTestInstanceHistoryRepository.getControlTestInstanceHistoryByControlTestInstanceId(
                        controlTestInstanceId,
                    );
                if (!isNil(instanceHistory)) {
                    latestControlTestInstance = instanceHistory;
                }
            }

            const testHistoryId = latestControlTestInstance?.id;
            const useTestHistory =
                fetchLatestConnections && latestControlTestInstance && testHistoryId;

            const connectionClientIds = useTestHistory
                ? await this.connectionRepository.getClientIdsByControlTestInstanceHistory(
                      dbName,
                      testHistoryId,
                  )
                : await this.connectionRepository.getClientIdsByControlTestInstance(
                      dbName,
                      controlTestInstanceId,
                  );

            if (!Array.isArray(connectionClientIds) || connectionClientIds.length === 0) {
                this.logger.log(
                    PolloAdapter.acct(
                        `No clientId were found for control test instance = ${controlTestInstanceId}`,
                        account,
                    )
                        .setContext(this.constructor.name)
                        .setSubContext(this.getConnectionsTestInfo.name)
                        .setIdentifier({ controlTestInstanceId }),
                );
            }

            const connectionClientTypes = useTestHistory
                ? await this.connectionRepository.getClientTypesByControlTestInstanceHistory(
                      dbName,
                      testHistoryId,
                  )
                : await this.connectionRepository.getClientTypesByControlTestInstance(
                      dbName,
                      controlTestInstanceId,
                  );

            if (!Array.isArray(connectionClientTypes) || connectionClientTypes.length === 0) {
                this.logger.log(
                    PolloAdapter.acct(
                        `No clientTypes were found for control test instance = ${controlTestInstanceId}`,
                        account,
                    )
                        .setContext(this.constructor.name)
                        .setSubContext(this.getConnectionsTestInfo.name)
                        .setIdentifier({ controlTestInstanceId }),
                );
            }

            this.logger.log(
                PolloAdapter.acct('Mapping connection data', account)
                    .setContext(this.constructor.name)
                    .setSubContext(this.getConnectionsTestInfo.name),
            );

            const maxLength = Math.max(connectionClientIds.length, connectionClientTypes.length);
            const connectionDataMap: ConnectionFilterData[] = [];

            for (let i = 0; i < maxLength; i++) {
                const connection = connectionClientIds[i];
                const clientType = connectionClientTypes[i]?.client_type;
                const clientTypeDeletedAt = connectionClientTypes[i]?.deleted_at;

                connectionDataMap.push({
                    clientIds: {
                        id: connection?.id,
                        clientId: connection?.client_id ?? '',
                        clientAlias: connection?.client_alias ?? '',
                        deletedAt: connection?.deleted_at,
                    },
                    clientType: {
                        name: ClientTypeDescription[clientType],
                        clientType: clientType,
                        deletedAt: clientTypeDeletedAt,
                    },
                });
            }

            const controlTestWithoutConnections =
                await this.eventsService.getTestEventsWithoutConnection(
                    account,
                    controlTestInstanceId,
                );

            if (!isEmpty(controlTestWithoutConnections)) {
                this.logger.log(
                    PolloAdapter.acct('Adding No connection information', account)
                        .setContext(this.constructor.name)
                        .setSubContext(this.getConnectionsTestInfo.name),
                );
                connectionDataMap.unshift({
                    clientType: { name: 'No Connection', clientType: 0 },
                    clientIds: { clientId: 'No connection', clientAlias: '' },
                });
            }
            return connectionDataMap;
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(
                    `Could not get test info for controlTestInstanceId = ${controlTestInstanceId}`,
                    account,
                )
                    .setContext(this.constructor.name)
                    .setSubContext(this.getConnectionsTestInfo.name)
                    .setIdentifier({ controlTestInstanceId })
                    .setError(error),
            );

            throw new BadRequestException(
                `Connection failed for controlTestInstanceId = ${controlTestInstanceId}`,
            );
        }
    }

    async getActiveConnectionsWithCustomResourceByWorkspaceIds(
        productIds: Array<number>,
        errorConnections = false,
    ): Promise<ConnectionEntity[]> {
        const organizationalConnection =
            await this.connectionRepository.getOrganizationalActiveConnections();

        const productActiveConnections =
            await this.connectionRepository.getActiveConnectionsWithCustomResourceByWorkspaceIds(
                productIds,
                errorConnections,
            );

        return [...organizationalConnection, ...productActiveConnections];
    }

    async refreshConnectionMetadata(connectionId: number, account: Account): Promise<string> {
        const connection = await this.connectionRepository.findOneByOrFail({
            id: connectionId,
        });

        let updatedMetadata: ConnectionMetadata;
        let api = null;

        switch (connection.clientType) {
            case ClientType.CURRICULA:
            case ClientType.GITLAB:
            case ClientType.GITLAB_ON_PREM:
                api = (await this.factory.api(connection, account)) as OAuth2Api;
                await api.updateMetadata();
                updatedMetadata = api.getConnectionMetadata();
                break;
            default:
                api = await this.apiClientService.refresh(connection, account);
                updatedMetadata = api.getConnectionMetadata();
        }

        connection.setMetadata(updatedMetadata);
        await this.connectionRepository.save(connection, { reload: false });
        return connection.metadata;
    }

    getConnectionsByRecipeId(recipeId: number): Promise<ConnectionEntity[]> {
        return this.connectionRepository.getConnectionsByRecipeId(recipeId);
    }

    getConnectionByIdAndCustomResourceId(
        workspaceId: number,
        connectionId: number,
        customResourceId: number,
    ): Promise<ConnectionEntity | null> {
        return this.connectionRepository.findOne({
            where: {
                id: connectionId,
                providerType: ProviderType.CUSTOM,
                customResources: {
                    id: customResourceId,
                },
                products: {
                    id: workspaceId,
                },
            },
            relations: ['customResources'],
        });
    }

    private get connectionRepository(): ConnectionsRepository {
        return this.getCustomTenantRepository(ConnectionsRepository);
    }

    private get controlTestInstanceHistoryRepository(): ControlTestInstanceHistoryRepository {
        return this.getCustomTenantRepository(ControlTestInstanceHistoryRepository);
    }

    /**
     * Get the newest, non-failing connection of a client type.
     *
     * This is useful for the times before a connection has been finalized, but
     * we need to make requests for data to complete the process.
     *
     * @param clientType
     * @returns
     */
    getNewestNonFailingConnectionByClientTypeNoFail(
        clientType: ClientType,
    ): Promise<ConnectionEntity | null> {
        return this.connectionRepository.findOne({
            where: { clientType, failedAt: IsNull() },
            order: { id: 'DESC' },
        });
    }
}
