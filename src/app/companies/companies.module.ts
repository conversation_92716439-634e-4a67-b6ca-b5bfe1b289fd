import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ApisModule } from 'app/apis/apis.module';
import { AppModule } from 'app/app.module';
import { AssetsCoreModule } from 'app/assets/assets-core.module';
import { AuditHubValidationModule } from 'app/audit-hub/audit-hub-validation.module';
import { AuditHubOrchestrationModule } from 'app/audit-hub/orchestration/audit-hub-orchestration.module';
import { CompaniesCoreModule } from 'app/companies/companies-core.module';
import { CompaniesOrchestrationModule } from 'app/companies/companies-orchestration.module';
import { CompanyArchivesCoreModule } from 'app/companies/company-archives-core.module';
import { ConnectionsCoreModule } from 'app/companies/connections/connections-core.module';
import { ConnectionsCoverdashController } from 'app/companies/connections/connections-coverdash.controller';
import { ConnectionsOrchestrationModule } from 'app/companies/connections/connections-orchestration.module';
import { ConnectionsTicketingModule } from 'app/companies/connections/connections-ticketing.module';
import { ConnectionsController } from 'app/companies/connections/connections.controller';
import { ConnectionsPublicController } from 'app/companies/connections/connections.public.controller';
import { IdpConnectionsController } from 'app/companies/connections/idp-connections.controller';
import { AnalyticsConnectionEstablishedHandler } from 'app/companies/connections/observables/handlers/analytics-connection-created.handler';
import { AnalyticsConnectionRemovedHandler } from 'app/companies/connections/observables/handlers/analytics-connection-deleted.handler';
import { AnalyticsConnectionUpdatedHandler } from 'app/companies/connections/observables/handlers/analytics-connection-updated.handler';
import { AnalyticsConnectionViewedHandler } from 'app/companies/connections/observables/handlers/analytics-connection-viewed.handler';
import { AnalyticsIdpConnectionDomainSettingSavedHandler } from 'app/companies/connections/observables/handlers/analytics-idp-connection-domain-setting-saved.handler';
import { AnalyticsIdpConnectionDomainsMismatchedHandler } from 'app/companies/connections/observables/handlers/analytics-idp-connection-domains-mismatched.handler';
import { AnalyticsIdpConnectionPersonnelSettingSavedHandler } from 'app/companies/connections/observables/handlers/analytics-idp-connection-personnel-setting-saved.handler';
import { AnalyticsIdpConnectionRankedHandler } from 'app/companies/connections/observables/handlers/analytics-idp-connection-ranked.handler';
import { AnalyticsIdpConnectionUsersMismatchedResolvedHandler } from 'app/companies/connections/observables/handlers/analytics-idp-connection-users-mismatched-resolved.handler';
import { AnalyticsIdpConnectionUsersMismatchedHandler } from 'app/companies/connections/observables/handlers/analytics-idp-connection-users-mismatched.handler';
import { ConnectionsAnalyticsHandler } from 'app/companies/connections/observables/handlers/connection-analytics.handler';
import { ConnectionEnableInfrastructureCustomTestHandler } from 'app/companies/connections/observables/handlers/connection-enable-infrastructure-custom-test.handler';
import { ConnectionEstablishedLogHandler } from 'app/companies/connections/observables/handlers/connection-established-log.handler';
import { ConnectionHealthCheckCompletedHandler } from 'app/companies/connections/observables/handlers/connection-health-check-completed.handler';
import { ConnectionIdpRankingUpdatedHandler } from 'app/companies/connections/observables/handlers/connection-idp-ranking-updated.handler';
import { ConnectionRemovedLogHandler } from 'app/companies/connections/observables/handlers/connection-removed-log.handler';
import { ConnectionUpdatedLogHandler } from 'app/companies/connections/observables/handlers/connection-updated-log.handler';
import { ConnectionWorkspaceAddedLogHandler } from 'app/companies/connections/observables/handlers/connection-workspace-added-log-handler';
import { ConnectionWorkspaceRemovedLogHandler } from 'app/companies/connections/observables/handlers/connection-workspace-removed-log-handler';
import { OnboardingAutocompleteConnectionStepHandler } from 'app/companies/connections/observables/handlers/onboarding-autocomplete-connection-step.handler';
import { ProviderConnectionErrorHandler } from 'app/companies/connections/observables/handlers/provider-connection-error.handler';
import { PackagesController } from 'app/companies/connections/packages/packages.controller';
import { ConnectionsRepository } from 'app/companies/connections/repositories/connections.repository';
import { ConnectionRegionsRunnerService } from 'app/companies/connections/services/connection-regions-runner.service';
import { ConnectionSettingRegionService } from 'app/companies/connections/services/connection-setting-region.service';
import { ConnectionSettingService } from 'app/companies/connections/services/connection-setting.service';
import { ConnectionsService } from 'app/companies/connections/services/connections.service';
import { CoverdashAccountService } from 'app/companies/connections/services/coverdash-account.service';
import { CoverdashPolicyService } from 'app/companies/connections/services/coverdash-policy.service';
import { IdpConnectionsService } from 'app/companies/connections/services/idp-connections.service';
import { PackagesService } from 'app/companies/connections/services/packages.service';
import { AdminOnboardingController } from 'app/companies/controllers/admin-onboarding.controller';
import { CompaniesController } from 'app/companies/controllers/companies.controller';
import { CompaniesPublicController } from 'app/companies/controllers/public/companies.public.controller';
import { MergedevController } from 'app/companies/mergedev/mergedev.controller';
import { MergedevService } from 'app/companies/mergedev/mergedev.service';
import { AiDataShareEntitlementDisabledHandler } from 'app/companies/observables/handlers/ai-data-share-feature-disabled.handler';
import { AnalyticsAdminOnboardingStepCompletedHandler } from 'app/companies/observables/handlers/analytics-admin-onboarding-step-completed.handler';
import { AnalyticsCompanyDataUpdatedHandler } from 'app/companies/observables/handlers/analytics-company-data-updated.handler';
import { CompanyArchiveAllDownloadedHandler } from 'app/companies/observables/handlers/company-all-downloaded.handler';
import { CompanyDataUpdatedEventHandler } from 'app/companies/observables/handlers/company-data-updated-event.handler';
import { CompanyDataUpdatedHandler } from 'app/companies/observables/handlers/company-data-updated.handler';
import { CompanyDocumentDeletedHandler } from 'app/companies/observables/handlers/company-document-deleted.handler';
import { CompanyDocumentDownloadedHandler } from 'app/companies/observables/handlers/company-document-downloaded.handler';
import { CompanyDocumentUploadedHandler } from 'app/companies/observables/handlers/company-document-uploaded.handler';
import { CompanyHipaaDataUpdatedHandler } from 'app/companies/observables/handlers/company-hipaa-training-data-updated.handler';
import { CompanyHumanResourcesDataUpdatedHandler } from 'app/companies/observables/handlers/company-human-resources-data-updated.handler';
import { CompanyKeyPersonnelUpdatedHandler } from 'app/companies/observables/handlers/company-key-personnel-updated.handler';
import { CompanyLogoUpdatedEventHandler } from 'app/companies/observables/handlers/company-logo-updated-event.handler';
import { CompanyLogoUpdatedHandler } from 'app/companies/observables/handlers/company-logo-updated.handler';
import { CompanyNistAiDataUpdatedHandler } from 'app/companies/observables/handlers/company-nist-ai-training-data-updated.handler';
import { CompanySecurityDataUpdatedHandler } from 'app/companies/observables/handlers/company-security-data-updated.handler';
import { CompanySecurityReportSettingUpdatedHandler } from 'app/companies/observables/handlers/company-security-report-setting-updated.handler';
import { CompanySecurityReportShareableUpdatedHandler } from 'app/companies/observables/handlers/company-security-report-sharable-update.handler';
import { CompanySecurityReportShareTokenUpdatedHandler } from 'app/companies/observables/handlers/company-security-report-share-token-updated.handler';
import { CompanyUserRolesUpdatedHandler } from 'app/companies/observables/handlers/company-user-roles-updated.handler';
import { CompanyWorkstationConfigurationMonitoringUpdatedHandler } from 'app/companies/observables/handlers/company-workstation-configuration-monitoring-updated.handler';
import { ConnectionEstablishedOnboardingHandler } from 'app/companies/observables/handlers/connection-established-onboarding.handler';
import { DrataSupportAccessGrantedHandler } from 'app/companies/observables/handlers/drata-support-access-granted.handler';
import { DrataSupportAccessRemovedHandler } from 'app/companies/observables/handlers/drata-support-access-removed.handler';
import { PolicyVersionPublishedOnboardingHandler } from 'app/companies/observables/handlers/policy-version-published-onboarding.handler';
import { ProductsController } from 'app/companies/products/controllers/product.controller';
import { UserRoleProductsController } from 'app/companies/products/controllers/user-role-products.controller';
import { WorkspacesController } from 'app/companies/products/controllers/workspaces.controller';
import { WorkspacesPublicController } from 'app/companies/products/controllers/workspaces.public.controller';
import { ProductCreatedHandler } from 'app/companies/products/observables/handlers/product-created.handler';
import { ProductFrameworkService } from 'app/companies/products/services/product-framework.service';
import { ProductsService } from 'app/companies/products/services/products.service';
import { ActAsService } from 'app/companies/services/act-as.service';
import { AdminOnboardingService } from 'app/companies/services/admin-onboarding.service';
import { CompaniesAppService } from 'app/companies/services/companies-app.service';
import { CompaniesService } from 'app/companies/services/companies.service';
import { CompanyArchivesService } from 'app/companies/services/company-archives.service';
import { WorkspacesFrameworkCoreModule } from 'app/companies/workspaces-framework-core.module';
import { ControlModule } from 'app/control/control.module';
import { CreateEventCoreModule } from 'app/create-event/create-event-core.module';
import { DocumentLibraryCoreModule } from 'app/document-library/document-library-core.module';
import { DocumentLibraryOrchestrationModule } from 'app/document-library/document-library-orchestration.module';
import { AccountProvisioningOrchestrationModule } from 'app/frameworks/account-provisioning-orchestration.module';
import { FrameworksCoreModule } from 'app/frameworks/frameworks-core.module';
import { ControlsOrchestrationModule } from 'app/grc/controls-orchestration.module';
import { MonitorIndexingCoreModule } from 'app/monitors/monitor-indexing-core.module';
import { MonitorIndexingModule } from 'app/monitors/monitor-indexing.module';
import { MonitorsCoreModule } from 'app/monitors/monitors-core.module';
import { MonitorsOrchestrationModule } from 'app/monitors/monitors-orchestration.module';
import { SettingsCoreModule } from 'app/settings/settings-core.module';
import { StatsModule } from 'app/stats/stats.module';
import { PersonnelCoreModule } from 'app/users/personnel-core.module';
import { PoliciesCoreModule } from 'app/users/policies-core.module';
import { UserIdentitiesCoreModule } from 'app/users/user-identities-core.module';
import { UsersCoreModule } from 'app/users/users-core.module';
import { VendorsCoreModule } from 'app/users/vendors-core.module';
import { ConnectionCheckFactory } from 'app/v2/apis/factories/connection-check/connection-check.factory';
import { V2ConnectionsController } from 'app/v2/companies/connections/connections.controller';
import { AnalyticsStepperWizardStartedHandler } from 'app/v2/companies/connections/observables/handlers/analytics-stepper-wizard-started.handler';
import { ConnectionHealthCheckService } from 'app/v2/companies/connections/services/connection-health-check.service';
import { ConnectionSetupDetailsService } from 'app/v2/companies/connections/services/connection-setup-details.service';
import { V2ConnectionsService } from 'app/v2/companies/connections/services/connections.service';
import { OrganizationalAccountsService } from 'app/v2/companies/connections/services/organization-accounts.service';
import { UpdateConnectionProductsService } from 'app/v2/companies/connections/services/update-connection-products.service';
import { UpdateConnectionProviderTypesService } from 'app/v2/companies/connections/services/update-connection-provider-types.service';
import { VulnerabilityMonitoringCoreModule } from 'app/vulnerability/vulnerability-monitoring-core.module';
import { AuditorsCoreModule } from 'auditors/auditors-core.module';
import { AuditorsOrchestrationModule } from 'auditors/auditors-orchestration.module';
import { AccountsCoreModule } from 'auth/accounts-core-module';
import { Token } from 'auth/entities/token.entity';
import { EntryCoreModule } from 'auth/entry-core.module';
import { AccountRepository } from 'auth/repositories/account.repository';
import { ModuleType, ModuleTypes } from 'commons/decorators/module-type.decorator';
import { TypeOrmExtensionsModule } from 'database/typeorm/typeorm.extensions.module';
import { AccountRequest } from 'dependencies/insurance-provider/coverdash/account.request';
import { IAccountRequest } from 'dependencies/insurance-provider/coverdash/iaccount.request';
import { ExternalClientRouterModule } from 'external-client-router/external-client-router.module';
import { ServiceUsersCoreModule } from 'service-user/service-users-core.module';

@ModuleType(ModuleTypes.COMMERCE)
@Module({
    imports: [
        // eslint-disable-next-line local-rules/restrict-commerce-module-imports
        ApisModule, // to be refactored
        AuditHubValidationModule,
        TypeOrmExtensionsModule.forGlobalCustomRepository([
            AccountRepository,
            ConnectionsRepository,
        ]),
        TypeOrmModule.forFeature([Token]),
        StatsModule,
        ExternalClientRouterModule,
        MonitorIndexingModule,
        MonitorIndexingCoreModule,
        CompanyArchivesCoreModule,
        PoliciesCoreModule,
        ConnectionsCoreModule,
        VendorsCoreModule,
        AssetsCoreModule,
        PersonnelCoreModule,
        FrameworksCoreModule,
        CompaniesOrchestrationModule,
        AccountsCoreModule,
        CompaniesCoreModule,
        UsersCoreModule,
        CreateEventCoreModule,
        WorkspacesFrameworkCoreModule,
        DocumentLibraryCoreModule,
        AuditorsCoreModule,
        ServiceUsersCoreModule,
        UserIdentitiesCoreModule,
        MonitorsCoreModule,
        MonitorsOrchestrationModule,
        AccountProvisioningOrchestrationModule,
        AuditorsOrchestrationModule,
        DocumentLibraryOrchestrationModule,
        AuditHubOrchestrationModule,
        SettingsCoreModule,
        ControlModule,
        ControlsOrchestrationModule,
        VulnerabilityMonitoringCoreModule,
        ConnectionsOrchestrationModule,
        ConnectionsTicketingModule,
        EntryCoreModule,
    ],
    controllers: [
        AdminOnboardingController,
        CompaniesController,
        ConnectionsController,
        IdpConnectionsController,
        ConnectionsCoverdashController,
        ConnectionsPublicController,
        MergedevController,
        PackagesController,
        ProductsController,
        WorkspacesController,
        WorkspacesPublicController,
        UserRoleProductsController,
        CompaniesPublicController,
        V2ConnectionsController,
    ],
    providers: [
        AdminOnboardingService,
        AnalyticsConnectionEstablishedHandler,
        OnboardingAutocompleteConnectionStepHandler,
        AnalyticsConnectionRemovedHandler,
        AnalyticsConnectionUpdatedHandler,
        AnalyticsConnectionViewedHandler,
        AnalyticsAdminOnboardingStepCompletedHandler,
        AnalyticsIdpConnectionDomainSettingSavedHandler,
        AnalyticsIdpConnectionDomainsMismatchedHandler,
        AnalyticsIdpConnectionPersonnelSettingSavedHandler,
        AnalyticsIdpConnectionRankedHandler,
        AnalyticsIdpConnectionUsersMismatchedHandler,
        AnalyticsIdpConnectionUsersMismatchedResolvedHandler,
        AnalyticsStepperWizardStartedHandler,
        CompaniesAppService,
        CompaniesService,
        CompanyArchiveAllDownloadedHandler,
        CompanyArchivesService,
        ConnectionEstablishedOnboardingHandler,
        ConnectionsAnalyticsHandler,
        ConnectionsService,
        IdpConnectionsService,
        // eslint-disable-next-line local-rules/restrict-commerce-module-imports
        ConnectionCheckFactory, // to be refactored
        ConnectionHealthCheckService,
        ConnectionSetupDetailsService,
        MergedevService,
        PackagesService,
        PolicyVersionPublishedOnboardingHandler,
        ProductCreatedHandler,
        ProductFrameworkService,
        ProductsService,
        ConnectionRegionsRunnerService,
        ConnectionSettingService,
        ConnectionSettingRegionService,
        AiDataShareEntitlementDisabledHandler,
        CoverdashAccountService,
        CoverdashPolicyService,
        {
            provide: IAccountRequest,
            useClass: AccountRequest,
        },
        ProviderConnectionErrorHandler,
        ActAsService,
        V2ConnectionsService,
        UpdateConnectionProductsService,
        UpdateConnectionProviderTypesService,
        OrganizationalAccountsService,
        ConnectionHealthCheckCompletedHandler,
        ConnectionIdpRankingUpdatedHandler,
        ConnectionEnableInfrastructureCustomTestHandler,
        // migrated from events module:
        AnalyticsCompanyDataUpdatedHandler,
        CompanyDataUpdatedEventHandler,
        CompanyDataUpdatedHandler,
        CompanyDocumentDeletedHandler,
        CompanyDocumentDownloadedHandler,
        CompanyDocumentUploadedHandler,
        CompanyHipaaDataUpdatedHandler,
        CompanyHumanResourcesDataUpdatedHandler,
        CompanyKeyPersonnelUpdatedHandler,
        CompanyLogoUpdatedEventHandler,
        CompanyLogoUpdatedHandler,
        CompanyNistAiDataUpdatedHandler,
        CompanySecurityDataUpdatedHandler,
        CompanySecurityReportSettingUpdatedHandler,
        CompanySecurityReportShareableUpdatedHandler,
        CompanySecurityReportShareTokenUpdatedHandler,
        CompanyUserRolesUpdatedHandler,
        CompanyWorkstationConfigurationMonitoringUpdatedHandler,
        DrataSupportAccessGrantedHandler,
        DrataSupportAccessRemovedHandler,
        ConnectionEstablishedLogHandler,
        ConnectionRemovedLogHandler,
        ConnectionUpdatedLogHandler,
        ConnectionWorkspaceAddedLogHandler,
        ConnectionWorkspaceRemovedLogHandler,
    ],
})
export class CompaniesModule extends AppModule {}
