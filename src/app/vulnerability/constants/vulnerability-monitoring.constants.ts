import { SLASeverity, VulnerabilitySeverity } from '@drata/enums';
import { ConnectionEntity } from 'app/companies/connections/entities/connection.entity';
import { ClientType } from 'commons/enums/auth/client-type.enum';
import { VulnerabilityMonitoringTest as TestEnum } from 'commons/enums/vulnerability/monitoring/vulnerability-monitoring-test.enum';
import config from 'config';

export class VulnerabilityMonitoringConstants {
    static CRITICAL_TEST_ID = 212;
    static HIGH_TEST_ID = 213;

    static vulnerabilityProviders = [ClientType.AWS_INSPECTOR];

    static vulnerabilityReportsProviders = [ClientType.RAPID7];

    static leenVulnerabilityProviders = [
        ClientType.LEEN_TENABLE,
        ClientType.LEEN_QUALYS,
        ClientType.LEEN_SEMGREP,
        ClientType.LEEN_SNYK,
        ClientType.LEEN_SENTINELONE_VMS,
        ClientType.LEEN_CROWDSTRIKE_VMS,
        ClientType.LEEN_MS_DEFENDER_VMS,
        ClientType.LEEN_RAPID7_VMS,
        ClientType.LEEN_ARNICA,
        ClientType.LEEN_AIKIDO,
        ClientType.LEEN_WIZ_VMS,
        ClientType.LEEN_WIZ_CODE,
    ];

    static vulnerabilityMonitoringProviders = [
        ...this.vulnerabilityProviders,
        ...this.leenVulnerabilityProviders,
    ];

    static allVulnerabilityProviders = [
        ...this.vulnerabilityProviders,
        ...this.vulnerabilityReportsProviders,
        ...this.leenVulnerabilityProviders,
    ];

    static vulnerabilitySlaConfigObject: Record<SLASeverity, number> = {
        [SLASeverity.LOW]: 0,
        [SLASeverity.MEDIUM]: 0,
        [SLASeverity.HIGH]: 0,
        [SLASeverity.CRITICAL]: 0,
    };

    static vulnerabilityTestIdBySeverity: Record<number, VulnerabilitySeverity> = {
        212: VulnerabilitySeverity.CRITICAL,
        213: VulnerabilitySeverity.HIGH,
    };

    static unSupportedSeverities = [
        VulnerabilitySeverity.INFORMATIONAL,
        VulnerabilitySeverity.UNTRIAGED,
    ];

    static defaultWarningPeriod = config.get('vulnerability.warningPeriod');

    static SNYK_URL_PATTERN = /^(https:\/\/app\.snyk\.io\/org\/[^\/]+\/project\/)/;

    static AWS_INSPECTOR_ARN_PATTERN = /arn:aws:[^:]+:([^:]+):/;

    static issueUrlStatuses = ['Resolved', 'Open', 'Ignored'];

    // [ProviderName: [TestCritical, TestHight]]
    static TEST_IDS = {
        [ClientType.AWS_INSPECTOR]: [
            TestEnum.VULNERABILITY_CRITICAL_ADDRESSED,
            TestEnum.VULNERABILITY_HIGH_ADDRESSED,
        ],
        [ClientType.LEEN_TENABLE]: [
            TestEnum.VULNERABILITY_CRITICAL_ADDRESSED_TENABLE,
            TestEnum.VULNERABILITY_HIGH_ADDRESSED_TENABLE,
        ],
        [ClientType.LEEN_SNYK]: [
            TestEnum.VULNERABILITY_CRITICAL_ADDRESSED_SNYK,
            TestEnum.VULNERABILITY_HIGH_ADDRESSED_SNYK,
        ],
        [ClientType.LEEN_SEMGREP]: [
            TestEnum.VULNERABILITY_CRITICAL_ADDRESSED_SEMGREP,
            TestEnum.VULNERABILITY_HIGH_ADDRESSED_SEMGREP,
        ],
        [ClientType.LEEN_QUALYS]: [
            TestEnum.VULNERABILITY_CRITICAL_ADDRESSED_QUALYS,
            TestEnum.VULNERABILITY_HIGH_ADDRESSED_QUALYS,
        ],
        [ClientType.LEEN_CROWDSTRIKE_VMS]: [
            TestEnum.VULNERABILITY_CRITICAL_ADDRESSED_CROWDSTRIKE_SPOTLIGHT,
            TestEnum.VULNERABILITY_HIGH_ADDRESSED_CROWDSTRIKE_SPOTLIGHT,
        ],
        [ClientType.LEEN_SENTINELONE_VMS]: [
            TestEnum.VULNERABILITY_CRITICAL_ADDRESSED_SENTINELONE_VMS,
            TestEnum.VULNERABILITY_HIGH_ADDRESSED_SENTINELONE_VMS,
        ],
        [ClientType.LEEN_MS_DEFENDER_VMS]: [
            TestEnum.VULNERABILITY_CRITICAL_ADDRESSED_MS_DEFENDER_VMS,
            TestEnum.VULNERABILITY_HIGH_ADDRESSED_MS_DEFENDER_VMS,
        ],
        [ClientType.LEEN_RAPID7_VMS]: [
            TestEnum.VULNERABILITY_CRITICAL_ADDRESSED_RAPID7_VMS,
            TestEnum.VULNERABILITY_HIGH_ADDRESSED_RAPID7_VMS,
        ],
        [ClientType.LEEN_ARNICA]: [
            TestEnum.VULNERABILITY_CRITICAL_ADDRESSED_ARNICA,
            TestEnum.VULNERABILITY_HIGH_ADDRESSED_ARNICA,
        ],
        [ClientType.LEEN_AIKIDO]: [
            TestEnum.VULNERABILITY_CRITICAL_ADDRESSED_AIKIDO,
            TestEnum.VULNERABILITY_HIGH_ADDRESSED_AIKIDO,
        ],
        [ClientType.LEEN_WIZ_VMS]: [
            TestEnum.VULNERABILITY_CRITICAL_ADDRESSED_WIZ_VMS,
            TestEnum.VULNERABILITY_HIGH_ADDRESSED_WIZ_VMS,
        ],
        [ClientType.LEEN_WIZ_CODE]: [
            TestEnum.VULNERABILITY_CRITICAL_ADDRESSED_WIZ_CODE,
            TestEnum.VULNERABILITY_HIGH_ADDRESSED_WIZ_CODE,
        ],
    };

    static vulnerabilityTestToSetUnused = [
        TestEnum.VULNERABILITY_CRITICAL_ADDRESSED_ARNICA,
        TestEnum.VULNERABILITY_HIGH_ADDRESSED_ARNICA,
    ];

    static getTestIdsByConnection(connection: ConnectionEntity, toEnable: boolean): number[] {
        const testIds: number[] = [];
        const severities = connection?.getMetadata().vulnerability?.severities ?? [];

        const clientTypeTestIds = this.TEST_IDS[connection.clientType];

        if (toEnable) {
            if (severities.includes(VulnerabilitySeverity.CRITICAL)) {
                testIds.push(clientTypeTestIds[0]);
            }
            if (severities.includes(VulnerabilitySeverity.HIGH)) {
                testIds.push(clientTypeTestIds[1]);
            }
        } else {
            if (!severities.includes(VulnerabilitySeverity.CRITICAL)) {
                testIds.push(clientTypeTestIds[0]);
            }
            if (!severities.includes(VulnerabilitySeverity.HIGH)) {
                testIds.push(clientTypeTestIds[1]);
            }
        }

        return testIds;
    }

    static vulnerabilityIdOrder = {
        SNYK: 1,
        CVE: 2,
        CWE: 4,
        UUID: 3,
        OTHER: 5,
    };
}

export const HIGHLIGHTS_SEVERITY_FILTER = [
    VulnerabilitySeverity.CRITICAL,
    VulnerabilitySeverity.HIGH,
];

export const VULNERABILITY_CONTAINED_ERRORS = [
    'ENOTFOUND',
    'ECONNRESET',
    'EPIPE',
    'ETIMEDOUT',
    'aborted',
    'socket hang up',
    'connection reset by peer',
];
