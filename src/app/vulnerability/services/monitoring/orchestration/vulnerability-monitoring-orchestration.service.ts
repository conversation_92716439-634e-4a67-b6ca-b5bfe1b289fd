import { format } from 'util';

import { VulnerabilitySeverity, VulnerabilityStatus } from '@drata/enums';
import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { ApiClientService } from 'app/api-client/api-client.service';
import { IVulnerabilityFindingsServices } from 'app/apis/interfaces/vulnerability-finding-services.interface';
import { ConnectionEntity } from 'app/companies/connections/entities/connection.entity';
import { ConnectionsRepository } from 'app/companies/connections/repositories/connections.repository';
import { ConnectionsCoreService } from 'app/companies/connections/services/connections-core.service';
import { WorkspacesCoreService } from 'app/companies/products/services/workspaces-core.service';
import { UserFeature } from 'app/feature-toggling/entities/user-feature.entity';
import { SettingTypeSlugString } from 'app/settings/helper/setting-type-slugs.map';
import { SettingsCoreService } from 'app/settings/services/settings-core.service';
import { VulnerabilityConfig } from 'app/settings/types/vulnerability-config.type';
import { User } from 'app/users/entities/user.entity';
import { UsersCoreService } from 'app/users/services/users-core.service';
import {
    HIGHLIGHTS_SEVERITY_FILTER,
    VULNERABILITY_CONTAINED_ERRORS,
    VulnerabilityMonitoringConstants,
} from 'app/vulnerability/constants/vulnerability-monitoring.constants';
import { VulnerabilityMonitoringHighlightsRequestDto } from 'app/vulnerability/dtos/monitoring/vulnerability-monitoring-highlights-request.dto';
import { VulnerabilityMonitoringQueryRequestDto } from 'app/vulnerability/dtos/monitoring/vulnerability-monitoring-query-request.dto';
import { VulnerabilityResourcesPaginatedRequestDto } from 'app/vulnerability/dtos/monitoring/vulnerability-resources-paginated-request.dto';
import { VulnerabilityFindingDetailEntity } from 'app/vulnerability/entities/monitoring/vulnerability-finding-detail.entity';
import { VulnerabilityFindingMetadataEntity } from 'app/vulnerability/entities/monitoring/vulnerability-finding-metadata.entity';
import { VulnerabilityFindingEntity } from 'app/vulnerability/entities/monitoring/vulnerability-finding.entity';
import { VulnerabilityResourceEntity } from 'app/vulnerability/entities/monitoring/vulnerability-resource.entity';
import { VulnerabilityEntity } from 'app/vulnerability/entities/monitoring/vulnerability.entity';
import {
    getAllowedSeverities,
    getIsBestEffort,
    getReadableClientType,
    getSlaSeverity,
} from 'app/vulnerability/helpers/monitoring/vulnerability-monitoring.helper';
import {
    getDailyRemainingFindingsAmountToSync,
    hasSurpassedFindingsDailyLimit,
    hasSurpassedFindingsLimit,
    hasSurpassedTableSizeLimit,
} from 'app/vulnerability/helpers/vulnerability.helper';
import { IVulnerabilityData } from 'app/vulnerability/interfaces/monitoring/vulnerability-data.interface';
import { IVulnerabilityEntryData } from 'app/vulnerability/interfaces/monitoring/vulnerability-entry-data.interface';
import { IVulnerabilityFindingData } from 'app/vulnerability/interfaces/monitoring/vulnerability-finding-data.interface';
import { IVulnerabilityFindingMetadata } from 'app/vulnerability/interfaces/monitoring/vulnerability-finding-metadata.interface';
import { IVulnerabilityResourceData } from 'app/vulnerability/interfaces/monitoring/vulnerability-resource-data.interface';
import { IVulnerabilitySyncService } from 'app/vulnerability/interfaces/monitoring/vulnerability-sync-service.interface';
import { VulnerabilityMonitoringDownloadEvent } from 'app/vulnerability/observables/events/monitoring/vulnerability-monitoring-download.event';
import { VulnerabilityReportsDownloadEvent } from 'app/vulnerability/observables/events/report/vulnerability-reports-download.event';
import { VulnerabilityFindingRepository } from 'app/vulnerability/repositories/monitoring/vulnerability-finding.repository';
import { VulnerabilityResourceRepository } from 'app/vulnerability/repositories/monitoring/vulnerability-resource.repository';
import { VulnerabilityRepository } from 'app/vulnerability/repositories/monitoring/vulnerability.respository';
import { VulnerabilityMonitoringCoreService } from 'app/vulnerability/services/monitoring/vulnerability-monitoring-core.service';
import { VulnerabilityHighlightData } from 'app/vulnerability/types/monitoring/vulnerability-highlights-data.type';
import { VulnerabilityInfrastructureInfoType } from 'app/vulnerability/types/monitoring/vulnerability-infrastructure-info.type';
import { VulnerabilitySyncCheckType } from 'app/vulnerability/types/monitoring/vulnerability-sync-check.type';
import { vulnerabilitiesMonitoringReportDownloadWorkflowV1 } from 'app/worker/workflows';
import { Account } from 'auth/entities/account.entity';
import { PolloAdapter } from 'commons/adapters/pollo.adapter';
import { EmailConfig } from 'commons/configs/email.config';
import { Retry } from 'commons/decorators/retry.decorator';
import { FeatureType } from 'commons/enums/feature-toggling/feature.enum';
import { SettledStatus } from 'commons/enums/notifications/settled-status.enum';
import { SettingType } from 'commons/enums/settings/settings-type.enum';
import { UploadType } from 'commons/enums/upload-type.enum';
import { Role } from 'commons/enums/users/role.enum';
import { fqtn } from 'commons/helpers/database.helper';
import { isTimeToSendEmailDayCheck } from 'commons/helpers/date.helper';
import { getProductId } from 'commons/helpers/products.helper';
import { getTemporalClient } from 'commons/helpers/temporal/client';
import { AppService } from 'commons/services/app.service';
import { EmailService } from 'commons/services/email.service';
import { EmailOptionsType } from 'commons/types/email-options.type';
import { FileBufferType } from 'commons/types/file-buffer.type';
import { PaginationType } from 'commons/types/pagination.type';
import config from 'config';
import { Downloader } from 'dependencies/downloader/downloader';
import { DownloaderPayloadType } from 'dependencies/downloader/types/downloader-payload.interface';
import { UploaderPayloadType } from 'dependencies/uploader/types/uploader-payload.type';
import { Uploader } from 'dependencies/uploader/uploader';
import { chunk, isEmpty, isNil, noop } from 'lodash';
import moment from 'moment';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { tenantWrapper } from 'tenancy/contexts/tenant-wrapper';
import { FindManyOptions, In, InsertResult, IsNull, Not, Raw, Repository } from 'typeorm';

@Injectable()
export class VulnerabilityMonitoringOrchestrationService
    extends AppService
    implements IVulnerabilitySyncService
{
    protected VULNERABILITY_MAX_RECORDS = config.get('vulnerability.maxFindingsRecordsNumber');
    protected VULNERABILITY_MAX_DAILY_RECORDS = config.get(
        'vulnerability.maxFindingsRecordsDailySync',
    );
    protected DEFAULT_PAGE_INDEX = config.get('pagination.page');

    constructor(
        private readonly connectionsCoreService: ConnectionsCoreService,
        private readonly emailService: EmailService,
        private readonly emailConfig: EmailConfig,
        private readonly usersCoreService: UsersCoreService,
        private readonly apiClientService: ApiClientService,
        private readonly settingsCoreService: SettingsCoreService,
        private readonly workspacesCoreService: WorkspacesCoreService,
        private readonly uploader: Uploader,
        private readonly downloader: Downloader,
        private readonly vmCoreService: VulnerabilityMonitoringCoreService,
    ) {
        super();
    }

    async createAllVulnerabilitiesReportCSVFile(
        filtering: VulnerabilityMonitoringQueryRequestDto,
        onProgress: (details?: unknown) => void = noop,
    ): Promise<FileBufferType> {
        const allFindings = await this.vmCoreService.getAllVulnerabilitiesFindings(
            filtering,
            onProgress,
        );
        const reportCSV = this.vmCoreService.buildVulnerabilitiesReportCsvFile(allFindings);
        this.logger.log(PolloMessage.msg('report files retrieved'));
        onProgress();

        return reportCSV;
    }

    async getDownloadURLForReportUpload(
        reportUpload: UploaderPayloadType,
        onProgress: (details?: unknown) => void = noop,
    ): Promise<DownloaderPayloadType> {
        const downloadOptions = this.vmCoreService.getFindingsReportDownloadOptions();
        const downloadInfo = await this.downloader.getDownloadUrl(
            reportUpload.key,
            downloadOptions,
        );
        this.logger.log(PolloMessage.msg('download Url created'));
        onProgress();

        return downloadInfo;
    }

    async uploadVulnerabilitiesReportCSVZip(
        account: Account,
        zipFile: Buffer,
        onProgress: (details?: unknown) => void = noop,
    ): Promise<UploaderPayloadType> {
        const productId = getProductId(account);
        if (isNil(productId)) {
            throw new NotFoundException(`Product not found`);
        }
        const { name: productName } = await this.workspacesCoreService.getProductById(productId);
        const uploadFileName = this.vmCoreService.buildVulnerabilitiesFindingsReportZipFileName(
            account,
            productName,
        );
        const uploadInfo = await this.uploader.uploadPrivateFileFromBuffer(
            account.id,
            UploadType.REPORT_CSV,
            zipFile,
            uploadFileName,
            config.get('archive.contentType'),
        );
        this.logger.log(PolloMessage.msg('uploaded private file from buffer'));
        onProgress();

        return uploadInfo;
    }

    async getVulnerabilityHighlights(
        account: Account,
        dto: VulnerabilityMonitoringHighlightsRequestDto,
    ): Promise<VulnerabilityHighlightData> {
        const { connectionIds } = dto;
        const vulnerabilityConfig = await this.getVulnerabilitySlaConfig(account);
        const warningPeriod = vulnerabilityConfig?.warningPeriod;
        const [startDate, endDate] = this.getWarningPeriodRange(warningPeriod);

        const [expired, nextToExpire] = await Promise.all([
            this.vulnerabilityFindingRepository.getHighlightsCountBySeverity(
                HIGHLIGHTS_SEVERITY_FILTER,
                connectionIds,
                { missedSla: true, statuses: [VulnerabilityStatus.OPEN] },
            ),
            this.vulnerabilityFindingRepository.getHighlightsCountBySeverity(
                HIGHLIGHTS_SEVERITY_FILTER,
                connectionIds,
                { startDate, endDate, statuses: [VulnerabilityStatus.OPEN] },
            ),
        ]);

        return { expired, nextToExpire };
    }

    async sendVulnerabilitySlaEmail(account: Account): Promise<any> {
        const validUserFeatures = await this.getUsersToSendEmailWithFeature();

        if (isEmpty(validUserFeatures)) {
            this.logger.log(
                PolloAdapter.acct('No users to whom to send vulnerability sla emails to', account),
            );
            return;
        }
        const vulnerabilityConfig = await this.getVulnerabilitySlaConfig(account);
        const warningPeriod = vulnerabilityConfig.warningPeriod;
        const [startDate, endDate] = this.getWarningPeriodRange(warningPeriod);

        const [expired, nextToExpire] = await Promise.all([
            this.vulnerabilityFindingRepository.getMissedSlaVulnerabilitiesCount([
                VulnerabilitySeverity.CRITICAL,
                VulnerabilitySeverity.HIGH,
            ]),
            this.vulnerabilityFindingRepository.getVulnerabilityNextToExpireWithinARangeCount(
                endDate,
                startDate,
                [VulnerabilitySeverity.CRITICAL, VulnerabilitySeverity.HIGH],
            ),
        ]);

        const joinedData: Record<
            string,
            { name: string; clientType: string; totalExpired: number; totalNextToExpire: number }
        > = {};
        expired.forEach(expiredData => {
            joinedData[expiredData.clientAlias + expiredData.connectionId] = {
                name: expiredData.clientAlias,
                clientType: getReadableClientType(expiredData.clientType),
                totalExpired: expiredData.total,
                totalNextToExpire: 0,
            };
        });
        nextToExpire.forEach(nextToExpireData => {
            const key = nextToExpireData.clientAlias + nextToExpireData.connectionId;
            if (isNil(joinedData[key])) {
                joinedData[key] = {
                    name: nextToExpireData.clientAlias,
                    clientType: getReadableClientType(nextToExpireData.clientType),
                    totalExpired: 0,
                    totalNextToExpire: nextToExpireData.total,
                };
            } else {
                joinedData[key].totalNextToExpire = nextToExpireData.total;
            }
        });

        if (isEmpty(joinedData)) {
            this.logger.log(
                PolloAdapter.acct(
                    'No vulnerability email data found, email will not be sent',
                    account,
                ),
            );

            return;
        }

        await this.sendSlaEmailToUsers(
            account,
            Object.values(joinedData),
            validUserFeatures,
            warningPeriod,
        );
    }

    async getInfrastructureInfo(
        account: Account,
        connectionId: number,
    ): Promise<VulnerabilityInfrastructureInfoType> {
        const connection = await this.getConnectionById(connectionId);

        const vulnerabilityApi = await this.apiClientService.api<IVulnerabilityFindingsServices>(
            connection,
            account,
        );

        const { key } = connection.getMetadata();

        return vulnerabilityApi.getInfrastructureInfo(key);
    }

    async downloadAllVulnerabilities(
        account: Account,
        user: User,
        dto: VulnerabilityMonitoringQueryRequestDto,
        requestMeta?: { url?: string; method?: string; requestId?: string },
        sendSnackNotificationOnWorkflow = false,
    ): Promise<DownloaderPayloadType> {
        try {
            const { url = '', method = '', requestId = '' } = requestMeta ?? {};
            this.logger.log(
                PolloAdapter.acct('Downloading Vulnerabilities report in Temporal', account)
                    .setIdentifier({ filters: dto, url, method, requestId })
                    .setSubContext('downloadAllVulnerabilities'),
            );
            const temporalClient = await getTemporalClient(account.domain);
            return await temporalClient.executeWorkflow(
                vulnerabilitiesMonitoringReportDownloadWorkflowV1,
                {
                    taskQueue: config.get('temporal.taskQueues.temporal-default'),
                    args: [
                        {
                            account,
                            user,
                            dto,
                            sendSnackNotification: sendSnackNotificationOnWorkflow,
                            requestMetadata: { url, method, requestId },
                        },
                    ],
                    memo: { accountId: account.id, domain: account.domain },
                },
            );
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct('error:', account, this.constructor.name)
                    .setDomain(account.domain)
                    .setIdentifier({ error: JSON.stringify(error) })
                    .setError(error),
            );

            throw error;
        }
    }

    getVulnerabilities(
        account: Account,
        dto: VulnerabilityMonitoringQueryRequestDto,
    ): Promise<PaginationType<VulnerabilityFindingEntity>> {
        return this.vulnerabilityFindingRepository.getPaginatedVulnerabilitiesFindings(dto);
    }

    async getVulnerabilitiesBySeverity(
        account: Account,
        connections: ConnectionEntity[],
        severity: VulnerabilitySeverity,
        limit?: number,
    ): Promise<VulnerabilityFindingEntity[]> {
        const dto = {
            severity: [severity],
            limit,
            status: [VulnerabilityStatus.OPEN],
            connectionIds: connections.map(connection => connection.id),
        } as VulnerabilityMonitoringQueryRequestDto;

        const { data } = await this.getVulnerabilities(account, dto);

        return data;
    }

    async getVulnerabilitiesWithinLastDays(
        connection: ConnectionEntity,
        days: number,
        limit?: number,
    ): Promise<VulnerabilityFindingEntity[]> {
        return this.vulnerabilityFindingRepository.getVulnerabilitiesWithinLastDays(
            connection,
            days,
            limit,
        );
    }

    /**
     *
     * @param id
     * @param connectionId
     * @returns {Promise<VulnerabilityEntryMapEntity>}
     */

    async getVulnerabilityDetailsById(
        id: string,
        connectionId: number,
    ): Promise<VulnerabilityFindingEntity> {
        const whereOptions = {
            where: { id },
        };

        if (isNil(connectionId)) {
            throw new BadRequestException(`ConnectionId should not be empty`);
        }

        const connection = await this.connectionsCoreService.getConnectionById(connectionId);
        Object.assign(whereOptions.where, {
            connection: { id: connection.id },
        });

        return this.vulnerabilityFindingRepository.findOneOrFail({
            relations: {
                resource: true,
                vulnerabilities: true,
                detail: true,
                connection: { products: true },
                metadata: true,
            },
            ...whereOptions,
        });
    }

    getVulnerabilityResources(
        dto: VulnerabilityResourcesPaginatedRequestDto,
    ): Promise<PaginationType<VulnerabilityResourceEntity>> {
        return this.vulnerabilityResourceRepository.getPaginatedVulnerabilityResources(dto);
    }

    async canSyncVulnerabilityRecordsInAccount(
        account: Account,
    ): Promise<VulnerabilitySyncCheckType> {
        try {
            const [{ count }] = await this.vulnerabilityFindingRepository.query(
                `SELECT COUNT(*) AS count FROM (SELECT id FROM ${fqtn(
                    account.databaseName,
                    'vulnerability_finding',
                )} GROUP BY id LIMIT ${this.VULNERABILITY_MAX_RECORDS}) AS subquery;`,
            );
            const [tableSize] =
                await this.vulnerabilityFindingRepository.getVulnerabilityEntryMapTableSize(
                    account,
                );
            const canSync =
                !hasSurpassedTableSizeLimit(tableSize.size) && !hasSurpassedFindingsLimit(count);
            return { sync: canSync, records: count, size: tableSize.size };
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(
                    `Error trying to calculate vulnerability findings: ${error}`,
                    account,
                    this.constructor.name,
                ).setError(error),
            );
            return { sync: false, records: 0, size: 0 };
        }
    }

    async updateFindings(
        account: Account,
        connection: ConnectionEntity,
        region?: string,
    ): Promise<number> {
        const configObject = await this.getVulnerabilitySlaConfig(account);
        const slaMatrix = configObject.vulnerabilitySla;
        const latestConnection = await this.connectionsCoreService.getConnectionById(connection.id);
        const vulnerabilityApi = await this.apiClientService.api<IVulnerabilityFindingsServices>(
            latestConnection,
            account,
        );
        const maxRecordsToUpdate = vulnerabilityApi.getMaxResultsUpdate();
        const maxParallelizedResults = vulnerabilityApi.getMaxParallelizedResultsToUpdate();

        // Initial check to see if there are any findings to update
        const initialFindingsCheck = await this.getFindingsToUpdate(connection, region, 1);

        if (isEmpty(initialFindingsCheck)) {
            this.logger.log(
                PolloAdapter.acct(
                    `No vulnerability finding records to update for region ${region ?? 'all'}, skipping...`,
                    account,
                ),
            );
            return 0;
        }

        let totalUpdated = 0;
        let hasMoreWork = true;

        this.logger.log(
            PolloAdapter.acct(`Starting vulnerability finding records update process`, account),
        );

        while (hasMoreWork) {
            const findingsToUpdate =
                // eslint-disable-next-line no-await-in-loop
                (await this.getFindingsToUpdate(connection, region, maxRecordsToUpdate)) ?? [];

            if (isEmpty(findingsToUpdate)) {
                break;
            }

            const ids = vulnerabilityApi.getFindingIdsToUpdate(findingsToUpdate);

            let chunks: string[][] = [];

            chunks = chunk(ids, maxParallelizedResults);

            // eslint-disable-next-line no-await-in-loop
            const responses = await Promise.allSettled(
                chunks.map(c =>
                    this.listFindingsByIdWithRetry(vulnerabilityApi, c, slaMatrix, region),
                ),
            );

            // Log rejected chunks and track failures
            const failedIndices: number[] = [];
            responses.forEach((res, idx) => {
                if (res.status === 'rejected') {
                    failedIndices.push(idx);
                    const reason = res.reason;
                    this.logger.warn(
                        PolloAdapter.acct(
                            `Chunk ${idx + 1}/${chunks.length} failed while listing findings`,
                            account,
                        ).setError(reason),
                    );
                }
            });

            const response: IVulnerabilityData[] = [];
            for (const result of responses) {
                if (result.status === 'fulfilled') {
                    response.push(...result.value);
                }
            }

            // eslint-disable-next-line no-await-in-loop
            const updated = await this.processFindings(account, connection, response, true);

            const findings = response.map(data => data.vulnerabilityFindingData.findingId);

            let findingsToRemove: string[] = [];

            if (failedIndices.length === 0) {
                // Only perform deletions when all chunks succeeded
                findingsToRemove = ids.filter(id => !findings.includes(id));

                if (!isEmpty(findingsToRemove)) {
                    // eslint-disable-next-line no-await-in-loop
                    await this.vulnerabilityFindingRepository.delete({
                        findingId: In(findingsToRemove),
                    });
                }
            } else {
                this.logger.warn(
                    PolloAdapter.acct(
                        `Skipping deletions for this iteration due to ${failedIndices.length} failed chunk(s)`,
                        account,
                    ).setMetadata({ failedChunks: failedIndices }),
                );
            }

            totalUpdated = totalUpdated + updated;

            // If we got fewer results than requested, we're likely done
            if (findingsToUpdate.length < maxRecordsToUpdate) {
                hasMoreWork = false;
            }

            // Safety check: if no updates were made in this iteration, stop to prevent infinite loop
            if (updated === 0 && findingsToRemove.length === 0) {
                this.logger.warn(
                    PolloAdapter.acct(
                        `No findings were updated or removed in this iteration, skipping...`,
                        account,
                    ),
                );
                hasMoreWork = false;
            }
        }

        this.logger.log(
            PolloAdapter.acct(
                `Updated ${totalUpdated} vulnerability finding records`,
                account,
            ).setMetadata({
                updatedFindings: totalUpdated,
            }),
        );

        return totalUpdated;
    }

    @Retry({ maxRetries: 5, retryWait: 5000, exponential: true }, ...VULNERABILITY_CONTAINED_ERRORS)
    private async listFindingsByIdWithRetry(
        api: IVulnerabilityFindingsServices,
        ids: string[],
        slaMatrix: any,
        region?: string,
    ): Promise<IVulnerabilityData[]> {
        return api.listFindingsById(ids, slaMatrix, region);
    }

    async updateFindingsSla(account: Account, connection: ConnectionEntity): Promise<void> {
        this.logger.log(PolloAdapter.acct('Running SLA update...', account, this.constructor.name));

        const vulnerabilitySlaConfig = await tenantWrapper(account, () =>
            this.settingsCoreService.getSettingConfig(
                SettingTypeSlugString.get(SettingType.VULNERABILITY_SLA_CONFIG) ?? '',
            ),
        );

        const configObject = vulnerabilitySlaConfig.configObject as VulnerabilityConfig;

        const slaMatrix = configObject.vulnerabilitySla;

        const vulnerabilitySlaConfigObject =
            VulnerabilityMonitoringConstants.vulnerabilitySlaConfigObject;

        for (const entry of slaMatrix) {
            vulnerabilitySlaConfigObject[entry.severity] = entry.timeFrame;
        }

        const { vulnerability } = connection.getMetadata();

        const severities = vulnerability?.severities;

        const allowedSeverities = getAllowedSeverities(severities ?? []);

        for await (const allowedSeverity of allowedSeverities) {
            let isBestEffort = false;

            const slaSeverity = getSlaSeverity(allowedSeverity);

            if (!isNil(slaSeverity)) {
                isBestEffort = getIsBestEffort(slaMatrix, slaSeverity);
            }
            if (!isBestEffort && !isNil(slaSeverity)) {
                await this.vulnerabilityFindingRepository.setVulnerabilityFindingsDueAt(
                    vulnerabilitySlaConfigObject[slaSeverity],
                    allowedSeverity,
                );
                await this.vulnerabilityFindingRepository.setVulnerabilityFindingsMissedSla(
                    allowedSeverity,
                );
            } else {
                await this.vulnerabilityFindingRepository.update(
                    {
                        severity: allowedSeverity,
                    },
                    { dueAt: null, missedSla: false },
                );
            }
        }

        this.logger.log(
            PolloAdapter.acct('Finished running SLA update...', account, this.constructor.name),
        );
    }

    async canSyncVulnerabilitiesRecordsInConnection(
        connection: ConnectionEntity,
    ): Promise<VulnerabilitySyncCheckType> {
        // 24 hrs limitation
        const today = moment().startOf('day').toDate().toISOString();
        const tomorrow = moment().startOf('day').add(1, 'days').toDate().toISOString();
        const findingsCount =
            await this.vulnerabilityFindingRepository.countByCreatedAtDateRangeAndConnectionId(
                connection,
                today,
                tomorrow,
                this.VULNERABILITY_MAX_DAILY_RECORDS,
            );
        const canSync = !hasSurpassedFindingsDailyLimit(findingsCount);
        return { sync: canSync, records: findingsCount, size: 0 };
    }

    allowedSeverities(severities: VulnerabilitySeverity[]): VulnerabilitySeverity[] {
        return getAllowedSeverities(severities);
    }

    allowedRegions(regions: string[]): string[] {
        return regions;
    }
    allowedFirstObserved(date: Date): Date {
        return date;
    }

    async sendAllVulnerabilitiesByEmail(
        account: Account,
        user: User,
        dto: VulnerabilityMonitoringQueryRequestDto,
    ): Promise<void> {
        let connection: ConnectionEntity | null = null;
        if (!isNil(dto.connectionIds)) {
            connection = await this.connectionsCoreService.getConnectionById(dto.connectionIds[0]);
        }
        this._eventBus.publish(new VulnerabilityReportsDownloadEvent(account, user, dto));
        this._eventBus.publish(new VulnerabilityMonitoringDownloadEvent(account, user, connection));
    }

    async deleteFindingsByConnectionId(account: Account, connectionId: number): Promise<void> {
        await this.vulnerabilityFindingRepository.delete({
            connection: { id: connectionId },
        });

        const hasRemainingFindings = await this.vulnerabilityFindingRepository.findOne({
            where: { connection: { id: Not(connectionId) } },
            relations: ['connection'],
            select: ['id'],
        });

        // deletes all info if no vulnerability findings remains in the db
        if (isNil(hasRemainingFindings)) {
            await this.vulnerabilityFindingDetailRepository.delete({
                id: Not(IsNull()),
            });
            await this.vulnerabilityRepository.delete({ id: Not(IsNull()) });
            await this.vulnerabilityResourceRepository.delete({
                id: Not(IsNull()),
            });
        }

        this.logger.log(
            PolloAdapter.acct(`Vulnerability findings deleted...`, account, this.constructor.name),
        );
    }

    async processFindings(
        account: Account,
        connection: ConnectionEntity,
        vulnerabilities: IVulnerabilityData[],
        isUpdate = false,
        totalProcessed = 0,
    ): Promise<number> {
        try {
            const entries = vulnerabilities.flatMap(
                vulnerability => vulnerability.vulnerabilityEntryData,
            );

            await this.processVulnerabilityEntries(entries);

            const details = vulnerabilities.map(
                vulnerability => vulnerability.vulnerabilityFindingData,
            );

            await this.processVulnerabilityFindingDetails(details);

            const resources = vulnerabilities.flatMap(
                vulnerability => vulnerability.vulnerabilityResourceData,
            );

            const uniqueResources = [
                ...new Map(resources.map(resource => [resource.resourceId, resource])).values(),
            ];

            await this.processVulnerabilityResources(uniqueResources);

            return await this.processVulnerabilityFindings(
                account,
                vulnerabilities,
                connection,
                isUpdate,
                totalProcessed,
            );
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(
                    `Error trying to process vulnerability finding: ${error}`,
                    account,
                    this.constructor.name,
                ).setError(error),
            );
        }
        return 0;
    }

    private async processVulnerabilityFindings(
        account: Account,
        vulnerabilitiesData: IVulnerabilityData[],
        connection: ConnectionEntity,
        isUpdate = false,
        totalProcessed = 0,
    ): Promise<number> {
        let createdRecordsCount = 0;
        let updatedRecordsCount = 0;

        const remainingRecordsAmount = getDailyRemainingFindingsAmountToSync(totalProcessed);

        for await (const vulnerabilityData of vulnerabilitiesData) {
            if (createdRecordsCount >= remainingRecordsAmount && !isUpdate) {
                break;
            }

            try {
                const {
                    vulnerabilityFindingData,
                    vulnerabilityFindingMetadata,
                    vulnerabilityResourceData,
                    vulnerabilityEntryData,
                } = vulnerabilityData;

                const detail = await this.vulnerabilityFindingDetailRepository.findOneOrFail({
                    where: {
                        name: vulnerabilityFindingData.name,
                    },
                });

                const vulnerabilities = await this.vulnerabilityRepository.find({
                    where: {
                        vulnerabilityId: In(
                            vulnerabilityEntryData.map(
                                vulnerability => vulnerability.vulnerabilityId,
                            ),
                        ),
                    },
                });

                const resources = await this.vulnerabilityResourceRepository.find({
                    where: {
                        resourceId: In(
                            vulnerabilityResourceData.map(resourceData => resourceData.resourceId),
                        ),
                    },
                });

                const findingsProms = resources.map(async resource => {
                    const finding = await this.vulnerabilityFindingRepository.findOne({
                        where: {
                            findingId: vulnerabilityFindingData.findingId,
                            resource: { resourceId: resource.resourceId },
                            connection: { id: connection.id },
                        },
                        relations: ['resource', 'connection'],
                        select: ['id'],
                    });

                    if (isNil(finding)) {
                        createdRecordsCount++;
                    } else {
                        updatedRecordsCount++;
                    }

                    return this.processVulnerabilityFindingItem(
                        vulnerabilityFindingData,
                        vulnerabilities,
                        resource,
                        detail,
                        connection,
                        finding,
                    );
                });

                const findingResults = await Promise.allSettled(findingsProms);

                const metadataProms = findingResults.map(async findingResult => {
                    if (findingResult.status === SettledStatus.FULFILLED) {
                        return this.processVulnerabilityFindingMetadata(
                            vulnerabilityFindingMetadata,
                            findingResult.value,
                        );
                    } else {
                        this.logger.warn(
                            PolloAdapter.acct(
                                `Finding promise failed with ${findingResult.reason}`,
                                account,
                                this.constructor.name,
                            ),
                        );
                    }
                });

                await Promise.allSettled(metadataProms);
            } catch (error) {
                this.logger.warn(
                    PolloAdapter.acct(
                        `Error while processing vulnerability finding: ${error}`,
                        account,
                        this.constructor.name,
                    ),
                );
            }
        }
        return isUpdate ? updatedRecordsCount : createdRecordsCount;
    }

    async getFindingsToUpdate(
        connection: ConnectionEntity,
        region?: string,
        limit?: number,
    ): Promise<VulnerabilityFindingEntity[]> {
        const today = moment().startOf('day').toDate().toISOString();
        const options: FindManyOptions<VulnerabilityFindingEntity> = {
            where: {
                updatedAt: Raw(alias => `${alias} < :today`, {
                    today,
                }),
                connection: { id: connection.id },
                status: In([VulnerabilityStatus.OPEN, VulnerabilityStatus.IN_PROGRESS]),
            },
            select: ['id', 'findingId', 'externalId', 'scanId'],
            relations: ['connection'],
        };
        if (!isNil(region)) {
            options.where = {
                ...options.where,
                region,
            };
        }
        if (!isNil(limit)) {
            options.take = limit;
        }
        return this.vulnerabilityFindingRepository.find(options);
    }

    private async processVulnerabilityFindingItem(
        vulnerabilityFindingData: IVulnerabilityFindingData,
        vulnerabilities: VulnerabilityEntity[],
        resource: VulnerabilityResourceEntity,
        detail: VulnerabilityFindingDetailEntity,
        connection: ConnectionEntity,
        finding?: VulnerabilityFindingEntity | null,
    ): Promise<VulnerabilityFindingEntity> {
        if (isNil(finding)) {
            finding = new VulnerabilityFindingEntity();
        }

        finding.findingId = vulnerabilityFindingData.findingId;
        finding.externalId = vulnerabilityFindingData.externalId;
        finding.scanId = vulnerabilityFindingData.scanId;
        finding.status = vulnerabilityFindingData.status;
        finding.firstSeenAt = vulnerabilityFindingData.firstSeenAt;
        finding.lastSeenAt = vulnerabilityFindingData.lastSeenAt;
        finding.statusUpdatedAt = vulnerabilityFindingData.statusUpdatedAt;
        finding.dueAt = vulnerabilityFindingData.dueAt;
        finding.missedSla = vulnerabilityFindingData.missedSla;
        finding.score = vulnerabilityFindingData.score;
        finding.severity = vulnerabilityFindingData.severity;
        finding.type = vulnerabilityFindingData.type;
        finding.patchable = vulnerabilityFindingData.patchable;
        finding.vulnerabilities = vulnerabilities;
        finding.detail = detail;
        finding.resource = resource;
        finding.connection = connection;
        finding.findingUrl = vulnerabilityFindingData.findingUrl;
        finding.region = vulnerabilityFindingData.region;
        return this.vulnerabilityFindingRepository.save(finding);
    }

    private async processVulnerabilityResources(
        vulnerabilityResourcesData: IVulnerabilityResourceData[],
    ): Promise<InsertResult> {
        return this.vulnerabilityResourceRepository.upsert(vulnerabilityResourcesData, {
            conflictPaths: ['resourceId'],
            skipUpdateIfNoValuesChanged: true,
        });
    }

    private processVulnerabilityFindingDetails(
        vulnerabilityFindingsData: IVulnerabilityFindingData[],
    ): Promise<InsertResult> {
        const data = vulnerabilityFindingsData.map(finding => {
            const detail = new VulnerabilityFindingDetailEntity();
            detail.name = finding.name;
            detail.description = finding.description;
            detail.remediation = finding.remediation;

            return detail;
        });
        return this.vulnerabilityFindingDetailRepository.upsert(data, {
            conflictPaths: ['name'],
            skipUpdateIfNoValuesChanged: true,
        });
    }

    private async processVulnerabilityFindingMetadata(
        vulnerabilityFindingsMetadata: IVulnerabilityFindingMetadata[],
        finding: VulnerabilityFindingEntity,
    ): Promise<InsertResult> {
        const data: IVulnerabilityFindingMetadata[] = vulnerabilityFindingsMetadata.map(
            metadata => ({
                name: metadata.name,
                externalId: metadata.externalId,
                value: metadata.value,
                finding,
            }),
        );
        return this.vulnerabilityFindingMetadataRepository.upsert(data, {
            conflictPaths: ['externalId'],
            skipUpdateIfNoValuesChanged: true,
        });
    }

    private processVulnerabilityEntries(
        vulnerabilities: IVulnerabilityEntryData[],
    ): Promise<InsertResult> {
        /**
         * Make the upsert operation with query builder to avoid issues with long records
         * since upsert does a select after creating or updating the records
         *  */
        return this.vulnerabilityRepository.upsertVulnerabilities(vulnerabilities);
    }

    protected get vulnerabilityFindingDetailRepository(): Repository<VulnerabilityFindingDetailEntity> {
        return this.getTenantRepository(VulnerabilityFindingDetailEntity);
    }
    protected get vulnerabilityFindingMetadataRepository(): Repository<VulnerabilityFindingMetadataEntity> {
        return this.getTenantRepository(VulnerabilityFindingMetadataEntity);
    }
    protected get vulnerabilityRepository() {
        return VulnerabilityRepository(this._tenancyContext.getConnection());
    }
    protected get vulnerabilityResourceRepository() {
        return VulnerabilityResourceRepository(this._tenancyContext.getConnection());
    }
    protected get vulnerabilityFindingRepository(): VulnerabilityFindingRepository {
        return this.getCustomTenantRepository(VulnerabilityFindingRepository);
    }
    protected get userFeatureRepository(): Repository<UserFeature> {
        return this.getTenantRepository(UserFeature);
    }
    protected get connectionRepository(): ConnectionsRepository {
        return this.getCustomTenantRepository(ConnectionsRepository);
    }

    private async getConnectionById(connectionId: number): Promise<ConnectionEntity> {
        const connection = this.connectionRepository.getConnectionById(connectionId);

        if (isNil(connection)) {
            throw new NotFoundException(`Connection not found`);
        }

        return connection;
    }

    private async getUsersToSendEmailWithFeature() {
        const users = await this.usersCoreService.getUsersByRoles([
            Role.ADMIN,
            Role.WORKSPACE_ADMINISTRATOR,
        ]);

        if (isEmpty(users)) {
            return [];
        }

        const userFeatures = await this.userFeatureRepository.find({
            where: {
                user: In(users.map(u => u.id)),
                enabledAt: Not(IsNull()),
                feature: {
                    featureType: FeatureType.NOTIFY_SLA_VULNERABILITIES,
                },
            },
        });

        const validUserFeatures: UserFeature[] = [];
        const today = new Date();
        for (const feature of userFeatures) {
            const shouldSendEmail = isTimeToSendEmailDayCheck(
                feature,
                today,
                config.get('email.dailyHours'),
            );

            if (shouldSendEmail) {
                validUserFeatures.push(feature);
            }
        }

        return validUserFeatures;
    }

    private async sendSlaEmailToUsers(
        account: Account,
        rowsData: {
            clientType: string;
            name: string;
            totalExpired: number;
            totalNextToExpire: number;
        }[],
        users: UserFeature[],
        warningPeriod: number,
    ): Promise<void> {
        const vulnerabilitySlaEmail = await this.emailConfig.vulnerabilitySlaEmail(
            account.language,
        );
        let findingsTable = '';
        const rows = rowsData.map(
            data =>
                `${format(
                    vulnerabilitySlaEmail.listItem,
                    data.clientType,
                    data.name,
                    data.totalExpired,
                    data.totalNextToExpire,
                    warningPeriod,
                )}`,
        );

        if (!isEmpty(rows)) {
            findingsTable = format(vulnerabilitySlaEmail.list, rows.join(''));
        }

        await Promise.all(
            users.map(userFeature => {
                const templateVariables = {
                    title: format(vulnerabilitySlaEmail.title, userFeature.user.firstName),
                    message: `${vulnerabilitySlaEmail.message}${findingsTable}`,
                    subMessage: vulnerabilitySlaEmail.subMessage,
                    ctaUrl: encodeURI(
                        `${config.get(
                            'url.webApp',
                        )}/risk/vulnerability-monitoring/?page=1&severity[0]=CRITICAL&severity[1]=HIGH&status[]=OPEN`,
                    ),
                    ctaText: vulnerabilitySlaEmail.ctaText,
                    subject: vulnerabilitySlaEmail.subject,
                    ...vulnerabilitySlaEmail.templateCommon,
                };
                const emailOptions: EmailOptionsType = {
                    ...vulnerabilitySlaEmail,
                    toEmail: userFeature.user.email,
                    templateVariables,
                    snippet: '',
                    category: 'vulnerabilitySlaEmail',
                };
                return this.emailService.sendEmail(emailOptions, account);
            }),
        );

        users.forEach(feature => (feature.lastSent = new Date()));
        await this.userFeatureRepository.save(users);
    }

    private async getVulnerabilitySlaConfig(account: Account): Promise<VulnerabilityConfig> {
        const vulnerabilitySlaConfig = await tenantWrapper(account, () =>
            this.settingsCoreService.getSettingConfig(
                SettingTypeSlugString.get(SettingType.VULNERABILITY_SLA_CONFIG) ?? '',
            ),
        );

        return vulnerabilitySlaConfig?.configObject as VulnerabilityConfig;
    }

    private getWarningPeriodRange(warningPeriod: number): [string, string] {
        const startDate = moment().startOf('day').toDate().toISOString();
        const endDate = moment().add(warningPeriod, 'days').endOf('day').toDate().toISOString();

        return [startDate, endDate];
    }
}
