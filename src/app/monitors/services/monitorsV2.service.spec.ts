import { CheckResultStatus, CheckStatus } from '@drata/enums';
import { EventBus } from '@nestjs/cqrs';
import { ConnectionsCoreService } from 'app/companies/connections/services/connections-core.service';
import { CompaniesCoreService } from 'app/companies/services/companies-core.service';
import FeatureFlagServiceMock from 'app/custom-fields/mocks/service/feature-flag.service.mock';
import { ControlTestInstance } from 'app/monitors/entities/control-test-instance.entity';
import { MonitorV2TestTypeOptions } from 'app/monitors/enums/monitor-v2-test-type-options.enum';
import { MonitorV2ResultsData } from 'app/monitors/repositories/monitor-v2-results.data';
import { MonitoringSummaryIndexingService } from 'app/monitors/services/monitoring-summary-indexing.service';
import { MonitorsV2Service } from 'app/monitors/services/monitorsV2.service';
import { User } from 'app/users/entities/user.entity';
import { Account } from 'auth/entities/account.entity';
import { AutopilotTaskType } from 'commons/enums/autopilot/autopilot-task-type.enum';
import { CheckFrequency } from 'commons/enums/check-frequency.enum';
import { createAppTestingModule } from 'commons/helpers/app-testing-module.helper';
import { Socket } from 'dependencies/socket/socket';
import { FeatureFlagService } from 'feature-flags/feature-flag.service';
import { TenancyContext } from 'tenancy/contexts/tenancy.context';

jest.mock('database/typeorm/typeorm.extensions.helper', () => ({
    getCustomRepository: jest.fn().mockImplementation(() => controlTestInstanceRepository),
}));
jest.mock('commons/helpers/control-test-instance.helper', () => ({
    injectCompanyNameForControlTestInstances: jest.fn().mockImplementation(instances => instances),
    shouldEmitEvent: jest.fn().mockReturnValue(false),
}));
jest.mock('commons/helpers/event.helper', () => ({
    shouldEmitEvent: jest.fn().mockReturnValue(false),
}));

const controlTestInstanceRepository = {
    metadata: {
        connection: {
            options: {},
            getMetadata: jest.fn().mockReturnValue({
                columns: [],
                relations: [],
                target: ControlTestInstance,
            }),
        },
        columns: [],
        relations: [],
        target: ControlTestInstance,
        getMetadata: jest.fn(),
        get: jest.fn(),
    },
    createQueryBuilder: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    andWhere: jest.fn().mockReturnThis(),
    getMany: jest.fn().mockResolvedValue([]),
    getControlTestInstancesByTestIds: jest.fn().mockResolvedValue([]),
};

describe('downloadMonitorTests', () => {
    let monitorsV2Service: MonitorsV2Service;
    let account: Account;
    let user: User;

    beforeEach(async () => {
        account = Object.assign(new Account(), { id: 1 });
        user = Object.assign(new User(), { id: 1 });

        const moduleRef = await createAppTestingModule({
            providers: [
                MonitorsV2Service,
                {
                    provide: TenancyContext,
                    useValue: {
                        getRepository: jest.fn().mockReturnValue({
                            target: ControlTestInstance,
                            manager: {},
                            queryRunner: {},
                        }),
                        getCustomRepository: jest
                            .fn()
                            .mockReturnValue(controlTestInstanceRepository),
                        getMetadata: jest.fn().mockReturnValue({
                            columns: [],
                            relations: [],
                            target: ControlTestInstance,
                        }),
                    },
                },
                {
                    provide: CompaniesCoreService,
                    useValue: {
                        getCompanyByAccountId: jest.fn().mockResolvedValue({
                            id: 1,
                            name: 'Company1',
                            settings: [],
                        }),
                    },
                },
                {
                    provide: EventBus,
                    useValue: { publish: jest.fn() },
                },
                {
                    provide: MonitorV2ResultsData,
                    useValue: {},
                },
                {
                    provide: MonitoringSummaryIndexingService,
                    useValue: {},
                },
                {
                    provide: FeatureFlagService,
                    useValue: FeatureFlagServiceMock,
                },
                { provide: ConnectionsCoreService, useValue: {} },
                {
                    provide: Socket,
                    useValue: {
                        sendMessage: jest.fn().mockResolvedValue({}),
                    },
                },
            ],
        }).compile();

        monitorsV2Service = moduleRef.get<MonitorsV2Service>(MonitorsV2Service);
    });

    it('should return CSV data with control test instances', async () => {
        const request = {
            testIds: [1, 2],
        };
        const mockInstances = [
            {
                id: 1,
                name: 'Test 1',
                checkStatus: CheckStatus.ENABLED,
                checkResultStatus: CheckResultStatus.READY,
                monitorInstances: [
                    {
                        checkResultStatus: CheckResultStatus.READY,
                        checkFrequency: CheckFrequency.DAILY,
                        autopilotTaskType: AutopilotTaskType.CUSTOM_TEST,
                    },
                ],
            },
            {
                id: 2,
                name: 'Test 2',
                checkStatus: CheckStatus.ENABLED,
                checkResultStatus: CheckResultStatus.READY,
                monitorInstances: [
                    {
                        checkResultStatus: CheckResultStatus.READY,
                        checkFrequency: CheckFrequency.DAILY,
                        autopilotTaskType: AutopilotTaskType.CUSTOM_TEST,
                    },
                ],
            },
        ];

        controlTestInstanceRepository.getControlTestInstancesByTestIds.mockResolvedValue(
            mockInstances,
        );

        const result = await monitorsV2Service.downloadMonitorTests(request, 1, account, user);

        expect(result).toHaveProperty('data');
        expect(result).toHaveProperty('filename');
        expect(controlTestInstanceRepository.getControlTestInstancesByTestIds).toHaveBeenCalledWith(
            1,
            [1, 2],
        );
    });
});

describe('getProdTestSourceFilter', () => {
    describe('when userProvidedFilter is undefined', () => {
        it('should return default test source values', () => {
            const result = MonitorsV2Service.getProdTestSourceFilter(undefined);

            expect(result).toEqual([
                MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.DRATA],
                MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.CUSTOM_DRAFT],
                MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.CUSTOM_PUBLISHED],
                MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.DRATA_CUSTOM_PUBLISHED],
                MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.DRATA_CUSTOM_DRAFT],
            ]);
        });
    });

    describe('when userProvidedFilter is empty array', () => {
        it('should return default test source values', () => {
            const result = MonitorsV2Service.getProdTestSourceFilter([]);

            expect(result).toEqual([
                MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.DRATA],
                MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.CUSTOM_DRAFT],
                MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.CUSTOM_PUBLISHED],
                MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.DRATA_CUSTOM_PUBLISHED],
                MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.DRATA_CUSTOM_DRAFT],
            ]);
        });
    });

    describe('when userProvidedFilter contains DRATA', () => {
        it('should expand DRATA to include DRATA_CUSTOM_PUBLISHED and DRATA_CUSTOM_DRAFT', () => {
            const input = [MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.DRATA]];
            const result = MonitorsV2Service.getProdTestSourceFilter(input);

            expect(result).toEqual([
                MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.DRATA],
                MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.DRATA_CUSTOM_PUBLISHED],
                MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.DRATA_CUSTOM_DRAFT],
            ]);
        });
    });

    describe('when userProvidedFilter contains CUSTOM_PUBLISHED', () => {
        it('should expand CUSTOM_PUBLISHED to include DRATA_CUSTOM_PUBLISHED', () => {
            const input = [MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.CUSTOM_PUBLISHED]];
            const result = MonitorsV2Service.getProdTestSourceFilter(input);

            expect(result).toEqual([
                MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.CUSTOM_PUBLISHED],
                MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.DRATA_CUSTOM_PUBLISHED],
            ]);
        });
    });

    describe('when userProvidedFilter contains CUSTOM_DRAFT', () => {
        it('should expand CUSTOM_DRAFT to include DRATA_CUSTOM_DRAFT', () => {
            const input = [MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.CUSTOM_DRAFT]];
            const result = MonitorsV2Service.getProdTestSourceFilter(input);

            expect(result).toEqual([
                MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.CUSTOM_DRAFT],
                MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.DRATA_CUSTOM_DRAFT],
            ]);
        });
    });

    describe('when userProvidedFilter contains multiple expandable types', () => {
        it('should expand all types and deduplicate overlapping expansions', () => {
            const input = [
                MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.DRATA],
                MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.CUSTOM_PUBLISHED],
                MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.CUSTOM_DRAFT],
            ];
            const result = MonitorsV2Service.getProdTestSourceFilter(input);

            expect(result).toEqual([
                MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.DRATA],
                MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.CUSTOM_PUBLISHED],
                MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.CUSTOM_DRAFT],
                MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.DRATA_CUSTOM_PUBLISHED],
                MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.DRATA_CUSTOM_DRAFT],
            ]);
        });

        it('should not duplicate DRATA_CUSTOM_PUBLISHED when both DRATA and CUSTOM_PUBLISHED are present', () => {
            const input = [
                MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.DRATA],
                MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.CUSTOM_PUBLISHED],
            ];
            const result = MonitorsV2Service.getProdTestSourceFilter(input);

            expect(result).not.toBeNull();

            // Verify DRATA_CUSTOM_PUBLISHED appears only once
            const drataCustomPublishedCount = result.filter(
                item =>
                    item ===
                    MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.DRATA_CUSTOM_PUBLISHED],
            ).length;

            expect(drataCustomPublishedCount).toBe(1);
            expect(result).toEqual([
                MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.DRATA],
                MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.CUSTOM_PUBLISHED],
                MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.DRATA_CUSTOM_PUBLISHED],
                MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.DRATA_CUSTOM_DRAFT],
            ]);
        });
    });

    describe('when userProvidedFilter contains non-expandable types', () => {
        it('should return default values when no expandable types are present', () => {
            const input = [MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.COMPLIANCE_AS_CODE]];
            const result = MonitorsV2Service.getProdTestSourceFilter(input);

            expect(result).toEqual([
                MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.COMPLIANCE_AS_CODE],
            ]);
        });

        it('should expand only expandable types when mixed with non-expandable types', () => {
            const input = [
                MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.DRATA],
                MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.COMPLIANCE_AS_CODE],
            ];
            const result = MonitorsV2Service.getProdTestSourceFilter(input);

            expect(result).toEqual([
                MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.DRATA],
                MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.COMPLIANCE_AS_CODE],
                MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.DRATA_CUSTOM_PUBLISHED],
                MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.DRATA_CUSTOM_DRAFT],
            ]);
        });
    });

    describe('when userProvidedFilter already contains expanded types', () => {
        it('should not duplicate already present expanded types', () => {
            const input = [
                MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.DRATA],
                MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.DRATA_CUSTOM_PUBLISHED], // Already present
            ];
            const result = MonitorsV2Service.getProdTestSourceFilter(input);

            expect(result).not.toBeNull();

            // Verify DRATA_CUSTOM_PUBLISHED appears only once
            const drataCustomPublishedCount = result.filter(
                item =>
                    item ===
                    MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.DRATA_CUSTOM_PUBLISHED],
            ).length;

            expect(drataCustomPublishedCount).toBe(1);
            expect(result).toEqual([
                MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.DRATA],
                MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.DRATA_CUSTOM_PUBLISHED],
                MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.DRATA_CUSTOM_DRAFT],
            ]);
        });
    });
});
