/* eslint-disable no-await-in-loop */
import {
    CheckResultStatus,
    CheckStatus,
    Error<PERSON>ode,
    EventCategory,
    EventType,
    SocketEvent,
    TestSource,
} from '@drata/enums';
import {
    BadRequestException,
    ConflictException,
    Injectable,
    InternalServerErrorException,
    UnprocessableEntityException,
} from '@nestjs/common';
import { AiExecutionGroupRequestDto } from 'app/ai/dtos/ai-execution-group-request.dto';
import { AiExecutionGroupEvent } from 'app/ai/observables/events/ai-execution-group.event';
import { AiExecutionGroupService } from 'app/ai/services/ai-execution-group.service';
import { TestFailureSummaryMetadata } from 'app/ai/types/test-failure-summary.type';
import { MonitorData } from 'app/autopilot/classes/monitor-data.class';
import { MonitorResult } from 'app/autopilot/classes/monitor-result.class';
import { getFindingTargetId } from 'app/autopilot/helper/fuzzy-matching.helper';
import { AutopilotScheduleRepository } from 'app/autopilot/repositories/autopilot-schedule.repository';
import { IacScanValidationTask } from 'app/autopilot/tasks/iac-scan/iac-scan-validation.task';
import { Task } from 'app/autopilot/tasks/task';
import { MonitorInstanceMetadataItem } from 'app/autopilot/types/monitor-instance-metadata-item.type';
import { ControlsTestsRequestDto } from 'app/companies/connections/dtos/controls-tests.request.dto';
import { ConnectionEntity } from 'app/companies/connections/entities/connection.entity';
import { ConnectionsCoreService } from 'app/companies/connections/services/connections-core.service';
import { Company } from 'app/companies/entities/company.entity';
import { Product } from 'app/companies/products/entities/product.entity';
import { WorkspacesCoreService } from 'app/companies/products/services/workspaces-core.service';
import { CompaniesCoreService } from 'app/companies/services/companies-core.service';
import { ComplianceCheckExclusion } from 'app/compliance-check-exclusions/entities/compliance-check-exclusion.entity';
import { createExtendedComplianceCheckExclusion } from 'app/compliance-check-exclusions/helpers/compliance-check-exclusion.helper';
import { ControlTestInstanceTestIdComplianceCheckTypes } from 'app/compliance-check-exclusions/maps/control-test-instance-test-id-compliance-check-types.map';
import { ComplianceCheckExclusionRepository } from 'app/compliance-check-exclusions/repositories/compliance-check-exclusion.repository';
import { ExtendedComplianceCheckExclusion } from 'app/compliance-check-exclusions/types/extended-compliance-check-exclusion.type';
import { CreateEventCoreService } from 'app/create-event/create-event-core.service';
import { CreateEvent } from 'app/create-event/decorators/create-event.decorator';
import { ControlMappedToTestFailingEvent } from 'app/custom-workflows/custom-workflows-common/triggers/events/control-mapped-to-test-failing-trigger.workflow.event';
import { ControlMappedToTestPassingEvent } from 'app/custom-workflows/custom-workflows-common/triggers/events/control-mapped-to-test-passing-trigger.workflow.event';
import { IListTestResult } from 'app/document-library/evidence-library/interfaces/list-test-result.interface';
import { Event } from 'app/events/entities/event.entity';
import { EventsService } from 'app/events/events.service';
import { ControlTestInstanceEventSummaryCsvData } from 'app/events/types/control-test-instance-event-summary-csv-data.type';
import { Control } from 'app/grc/entities/control.entity';
import { ControlRepository } from 'app/grc/repositories/control.repository';
import { ConnectionTestsType } from 'app/grc/types/connection-tests.type';
import { TestGraphsType } from 'app/grc/types/test-graphs.type';
import { CodeRepositorySettingsRepository } from 'app/iac-scanning/repositories/code-repository-settings.repository';
import { FindingExclusionRepository } from 'app/iac-scanning/repositories/finding-exclusion.repository';
import { MonitoringTestTrendsConstants } from 'app/monitors/constants/monitoring-test-trends.constants';
import { ControlTestInstanceHistoryRequestDto } from 'app/monitors/dtos/control-test-instance-history-request.dto';
import { MonitorControlTesRunModeRequestDto } from 'app/monitors/dtos/monitor-control-test-instance-runmode-request.dto';
import { MonitorInstanceDeleteExclusionsDto } from 'app/monitors/dtos/monitor-instance-delete-exclusions-request.dto';
import { MonitorInstanceExclusionRequestDto } from 'app/monitors/dtos/monitor-instance-exclusion-request.dto';
import { MonitorInstanceExclusionsRequestDto } from 'app/monitors/dtos/monitor-instance-exclusions-request.dto';
import { MonitorStatsRequestDto } from 'app/monitors/dtos/monitor-stats-request.dto';
import { MonitorTestResultsPaginatedRequestDto } from 'app/monitors/dtos/monitor-test-results-paginated.dto';
import { MonitorsDetailsRequestDto } from 'app/monitors/dtos/monitors-details-request.dto';
import { MonitorsRequestDto } from 'app/monitors/dtos/monitors-request.dto';
import { ControlTestInstance } from 'app/monitors/entities/control-test-instance.entity';
import { MonitorConstants } from 'app/monitors/entities/monitor-contants.class';
import { MonitorInstanceExclusion } from 'app/monitors/entities/monitor-instance-exclusion.entity';
import { MonitorInstance } from 'app/monitors/entities/monitor-instance.entity';
import { MonitorStat } from 'app/monitors/entities/monitor-stat.entity';
import { getControlTestHistoryByWeekLabels } from 'app/monitors/helpers/control-test.sql.helper';
import { hasActiveDependantConnections } from 'app/monitors/helpers/has-active-dependant-connections.helper';
import {
    updateMonitoringSummaryResultsIndexForTest,
    updateMonitoringSummaryResultsIndexSetToTestingState,
} from 'app/monitors/helpers/monitoring-summary-indexing.helper';
import { getNewAP2OnlyTests } from 'app/monitors/helpers/new-only-ap2-tests.helper';
import { IFailedTest } from 'app/monitors/interfaces/failed-test.interface';
import {
    clearMDMMetadataWhenEDRIsConnected,
    findAutopilotTaskTypesForConnections,
    gcMetadata,
    getCleanMetadata,
    getRequiredPolicies,
    isConnectionAllowedToAutoRunTests,
    isControlTestInstanceLabeledAsNew,
    resolveMonitorInstanceCheckResultStatus,
    resolveMonitorInstanceMetadataKey,
} from 'app/monitors/monitor-service.helper';
import { ControlTestInstanceNameAndDescriptionUpdatedEvent } from 'app/monitors/observables/events/control-test-instance-name-and-description-updated.events';
import { ExclusionUpdatedEvent } from 'app/monitors/observables/events/exclusion-updated.event';
import { IndexMonitorResultEvent } from 'app/monitors/observables/events/index-monitor-result.event';
import { ControlTestInstanceHistoryRepository } from 'app/monitors/repositories/control-test-instance-history.repository';
import { ControlTestInstanceRepository } from 'app/monitors/repositories/control-test-instance.repository';
import { MonitorInstanceCheckTypeRepository } from 'app/monitors/repositories/monitor-instance-check-type.repository';
import { MonitorInstanceExclusionRepository } from 'app/monitors/repositories/monitor-instance-exclusion.repository';
import { MonitorInstanceRepository } from 'app/monitors/repositories/monitor-instance.repository';
import { MonitoringSummaryIndexingService } from 'app/monitors/services/monitoring-summary-indexing.service';
import { MonitorsCoreService } from 'app/monitors/services/monitors-core.service';
import {
    AvailableConnection,
    ControlTestInstanceDetails,
} from 'app/monitors/types/control-test-instance-details.type';
import { ControlTestInstanceHistoryType } from 'app/monitors/types/control-test-instance-history.type';
import { MonitoringInstanceAdditionalProperty } from 'app/monitors/types/monitoring-instance-additional-property.type';
import { ControlsTests } from 'app/monitors/types/stats/controls-tests.type';
import { TestTrendsOverview } from 'app/monitors/types/stats/test-trends-overview-stats.type';
import { TestTrends } from 'app/monitors/types/stats/test-trends-stats.type';
import { ProviderCatalogService } from 'app/provider-catalog/provider-catalog.service';
import { UserDocument } from 'app/users/entities/user-document.entity';
import { User } from 'app/users/entities/user.entity';
import { UserDocumentDownloadedEvent } from 'app/users/observables/events/user-document-downloaded.event';
import { PersonnelRepository } from 'app/users/personnel/repositories/personnel.repository';
import { Policy } from 'app/users/policies/entities/policy.entity';
import { getIndirectResponsibilities } from 'app/users/policies/helpers/policy-status.helper';
import { PolicyResponsibilityRepository } from 'app/users/policies/repositories/policy-responsibility.repository';
import { PolicyRepository } from 'app/users/policies/repositories/policy.repository';
import { UserRepository } from 'app/users/repositories/user.repository';
import { Account } from 'auth/entities/account.entity';
import {
    getProviderTypeByClientType,
    getProviderTypesByClientType,
} from 'auth/helpers/provider-type.helper';
import { PaginationExclusionRequestDto } from 'autopilot2/dtos/pagination-exclusion-request.dto';
import { CacheBusterWithPrefix } from 'cache/cache.decorator';
import { CACHE_IS_UP } from 'cache/cache.module';
import { PolloAdapter } from 'commons/adapters/pollo.adapter';
import { Benchmark } from 'commons/benchmark/benchmark';
import { deprecatedIndirectControlTestIds } from 'commons/configs/deprecated-control-tests.config';
import { AnalyticsEvent } from 'commons/enums/analytics-event.enum';
import { ClientType } from 'commons/enums/auth/client-type.enum';
import { ProviderType } from 'commons/enums/auth/provider-type.enum';
import { AutopilotTaskResponseStatus } from 'commons/enums/autopilot/autopilot-task-response-status.enum';
import { AutopilotTaskType } from 'commons/enums/autopilot/autopilot-task-type.enum';
import { Caches } from 'commons/enums/cache.enum';
import { CheckType } from 'commons/enums/check-type.enum';
import { ComplianceCheckExclusionTargetType } from 'commons/enums/compliance-check-exclusions/compliance-check-exclusion-target-type.enum';
import { ExecutionStatus } from 'commons/enums/execution-status.enum';
import { LogIdentifierEvent } from 'commons/enums/log-identifier-event.enum';
import { SettledStatus } from 'commons/enums/notifications/settled-status.enum';
import { EmploymentStatusOptionsFilter } from 'commons/enums/personnel/employment-status-options-filter.enum';
import { ProcessFeature } from 'commons/enums/process-feature.enum';
import { ProcessType } from 'commons/enums/process-type.enum';
import { ReportInterval } from 'commons/enums/report-interval.enum';
import { RequestDescriptionType } from 'commons/enums/request-description-type.enum';
import { RunMode } from 'commons/enums/run-mode.enum';
import { BadRequestException as CommonBadRequestException } from 'commons/exceptions/bad-request.exception';
import { ConflictException as OurConflictException } from 'commons/exceptions/conflict.exception';
import { NotFoundException } from 'commons/exceptions/not-found.exception';
import { checkFrequencyNextExpiration } from 'commons/helpers/check-frequency.helper';
import {
    injectCompanyNameForControlTestInstance,
    injectCompanyNameForControlTestInstances,
    isNotUpdateable,
    replaceTarget,
} from 'commons/helpers/control-test-instance.helper';
import { getDateDiffAsHours } from 'commons/helpers/date.helper';
import { getEventStatus, shouldEmitEvent } from 'commons/helpers/event.helper';
import {
    autopilotTaskTypeSharedProvidersMap,
    clientTypeAutopilotTaskDependencyMap,
    extractResourceArnFromFailItem,
    getAutopilotDependencies,
    getAutopilotTaskTypesByProviderType,
    getImplementedAutopilotTaskTypesByClientType,
    getProviderTypeByAutopilotTaskType,
    getRequestDescriptionTypeByAutopilotTaskType,
} from 'commons/helpers/monitor.helper';
import { EmploymentStatusOptionsFilterMap } from 'commons/helpers/personnel.helper';
import { isPolicyControlTestInstance } from 'commons/helpers/policy.helper';
import {
    filterControlTests,
    getProductId,
    hasAssociatedProduct,
    isOrganizationLevelConnection,
} from 'commons/helpers/products.helper';
import {
    DEFAULT_PROMISE_BATCH_SIZE,
    promiseAllInBatches,
    promiseAllSettledInBatches,
} from 'commons/helpers/promise.helper';
import { getTemporalClient } from 'commons/helpers/temporal/client';
import { AppService } from 'commons/services/app.service';
import { WorkspacesBaseService } from 'commons/services/workspaces-base.service';
import { CsvDataSetType } from 'commons/types/csv-data-set.type';
import { PaginationType } from 'commons/types/pagination.type';
import config from 'config';
import { Analytics } from 'dependencies/analytics/analytics';
import { Socket } from 'dependencies/socket/socket';
import { FeatureFlagCategory } from 'feature-flags/enums/feature-flag-category.enum';
import { FeatureFlag } from 'feature-flags/feature-flag.enum';
import { FeatureFlagService } from 'feature-flags/feature-flag.service';
import {
    chain,
    cloneDeep,
    fill,
    filter,
    find,
    first,
    flatMap,
    get,
    indexOf,
    isEmpty,
    isNil,
    isUndefined,
    keys,
    size,
    some,
} from 'lodash';
import moment from 'moment';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { ControlTestTemplateRepository } from 'site-admin/repositories/control-test-template.repository';
import { MonitorTemplateRepository } from 'site-admin/repositories/monitor-template.repository';
import { EntityNotFoundError, FindManyOptions, In, Repository } from 'typeorm';
import { format } from 'util';

@Injectable()
export class MonitorsService extends AppService {
    /**
     * @deprecated Use MonitorCoreService.ALL_COMPANY_TEST_IDS
     *
     * The methods that use this weird array are deprecated in this service and
     * will be removed - remove this private array when the methods are gone ...
     */
    private readonly ALL_COMPANY_TEST_IDS = [37, 38, 39, 190];

    private readonly EXCLUDED_PROVIDERS_FROM_ENABLE_CONTROL_TESTS_INSTANCES = [
        ProviderType.MDM,
        ProviderType.COMMUNICATION,
        ProviderType.VULNERABILITY,
        ProviderType.CSPM,
    ];

    constructor(
        private readonly createEventService: CreateEventCoreService, // needed by the @CreateEvent decorator
        private readonly connectionsCoreService: ConnectionsCoreService,
        private readonly socket: Socket,
        private readonly companiesCoreService: CompaniesCoreService,
        private workspacesCoreService: WorkspacesCoreService,
        private readonly controlTestTemplateRepository: ControlTestTemplateRepository,
        private readonly monitorTemplateRepository: MonitorTemplateRepository,
        private readonly featureFlagService: FeatureFlagService,
        protected readonly providerCatalogService: ProviderCatalogService,
        private readonly aiExecutionGroupService: AiExecutionGroupService,
        private readonly monitoringSummaryIndexingService: MonitoringSummaryIndexingService,
        private readonly analytics: Analytics,
        private readonly eventsService: EventsService,
        private readonly monitorsCoreService: MonitorsCoreService,
        private readonly workspacesBaseService: WorkspacesBaseService,
    ) {
        super();
    }

    /**
     * @deprecated Use MonitorsCoreService.getMonitorInstanceByIdWithControlTest
     *
     * @param monitorInstanceId
     * @returns
     */
    async getMonitorInstanceByIdWithControlTest(
        monitorInstanceId: number,
    ): Promise<MonitorInstance> {
        return this.monitorInstanceRepository.getMonitorInstanceByIdWithControlTest(
            monitorInstanceId,
        );
    }

    /**
     * @deprecated Use MonitorsCoreService.listControlTestInstances
     */
    async listControlTestInstances(
        account: Account,
        dto: MonitorsRequestDto,
        user?: User,
    ): Promise<PaginationType<TestGraphsType>> {
        const controlTestInstances =
            await this.controlTestInstanceRepository.listControlTestInstances(account, dto, user);

        const company = await this.getCompany(account);

        controlTestInstances.data = injectCompanyNameForControlTestInstances(
            controlTestInstances.data,
            company.name,
        );

        const graphs: PaginationType<TestGraphsType> = {
            data: [],
            limit: controlTestInstances.limit,
            page: controlTestInstances.page,
            total: controlTestInstances.total,
        };

        for (const test of controlTestInstances.data) {
            graphs.data.push({
                test,
                graphs: !isNil(dto.reportInterval)
                    ? await this.getControlTestHistory(account, {
                          testId: test.testId,
                          reportInterval: dto.reportInterval,
                      })
                    : null,
            });
        }

        return graphs;
    }

    /**
     * @deprecated Use MonitorsOrchestrationService.listControlTestInstancesByConnection
     *
     * Query the tests linked to a connection.
     *
     * @param account
     * @param connectionId
     * @returns
     */
    async listControlTestInstancesByConnection(
        account: Account,
        connectionId: number,
    ): Promise<ConnectionTestsType> {
        try {
            const connection = await this.connectionsCoreService.getConnectionById(connectionId);
            const workspaces = await this.workspacesCoreService.resolveProductsForConnection(
                account,
                connection,
            );

            if (isNil(workspaces)) {
                return { connection, testInstances: [] };
            }

            const promises = workspaces.map(async workspace => {
                const accountWithWorkspace = account.cloneWithProduct(workspace);

                const testInstancesForWorkspace =
                    await this.listControlTestInstancesByConnectionForWorkspace(
                        accountWithWorkspace,
                        connection,
                    );

                this.log(`Tests instances for workspace`, account, {
                    testInstancesForWorkspaceCount: testInstancesForWorkspace.length,
                });

                return testInstancesForWorkspace;
            });

            const testInstances = (await Promise.all(promises)).flat();

            return {
                connection,
                testInstances,
            };
        } catch (error) {
            this.error(error, account, {
                connectionId,
            });
            throw error;
        }
    }

    /**
     * @deprecated Use MonitorsOrchestrationService.listControlTestInstancesByConnectionForWorkspace
     *
     * Query the tests linked to a connection and a workspace
     *
     * @param account
     * @param providerTypeTasks
     * @param connection
     * @returns
     */
    async listControlTestInstancesByConnectionForWorkspace(
        account: Account,
        connection: ConnectionEntity,
    ): Promise<ControlTestInstance[]> {
        try {
            const activeAutopilotTaskTypes = findAutopilotTaskTypesForConnections([connection]);

            if (isEmpty(activeAutopilotTaskTypes)) {
                this.warn(
                    'Resolved no autopilot task types to query for connection',
                    account,
                    connection,
                );

                return [];
            }

            const controlTestInstances =
                await this.controlTestInstanceRepository.getControlTestInstancesByAutopilotTaskTypesAndCheckStatus(
                    activeAutopilotTaskTypes,
                    [CheckStatus.ENABLED, CheckStatus.TESTING],
                    getProductId(account),
                );

            if (isEmpty(controlTestInstances)) {
                this.log(
                    `No control test instances found for autopilot task types`,
                    account,
                    activeAutopilotTaskTypes.map(att => AutopilotTaskType[att]).join(','),
                );

                return [];
            }

            return controlTestInstances;
        } catch (error) {
            this.error(error, account, {
                connection,
            });
            throw error;
        }
    }

    /**
     * @deprecated Use MonitorsOrchestrationService.listControlTestInstancesByFrameworks
     *
     * @param account
     * @param dto
     * @returns
     */
    listControlTestInstancesByFrameworks(
        account: Account,
        dto: MonitorsRequestDto,
    ): Promise<PaginationType<ControlTestInstance>> {
        return this.controlTestInstanceRepository.listControlTestInstances(account, dto);
    }

    /**
     *
     * @param dto
     * @param account
     * @param user
     * @returns
     */
    async downloadMonitorTests(
        dto: MonitorsRequestDto,
        account: Account,
        user: User,
    ): Promise<CsvDataSetType> {
        const allControlTestInstances =
            await this.controlTestInstanceRepository.listControlTestInstances(account, dto, user);

        const company = await this.getCompany(account);

        allControlTestInstances.data = injectCompanyNameForControlTestInstances(
            allControlTestInstances.data,
            company.name,
        );

        const data = cloneDeep<any[]>(allControlTestInstances.data);
        const filename = `Monitor-${moment().format('MMDDYYYY')}`;

        if (shouldEmitEvent(account, user)) {
            this._eventBus.publish(
                new UserDocumentDownloadedEvent(account, user, filename, {
                    name: filename,
                } as UserDocument),
            );
        }

        return {
            data,
            filename,
        };
    }

    /**
     * @deprecated Use MonitorsOrchestrationService.getAllEnabledControlTestInstances
     *
     * @param account
     * @param checkResultStatuses
     * @param company
     * @param injectCompanyName
     * @returns
     */
    async getAllEnabledControlTestInstances(
        account: Account,
        checkResultStatuses: CheckResultStatus[] = null,
        company?: Company,
        injectCompanyName = false,
        includeDrafts = false,
    ): Promise<ControlTestInstance[]> {
        let controlTestInstances =
            await this.getControlTestInstancesByCheckStatusAndCheckResultStatus(
                account,
                CheckStatus.ENABLED,
                checkResultStatuses,
                includeDrafts,
            );

        if (!injectCompanyName) {
            return controlTestInstances;
        }

        /**
         * https://app.shortcut.com/drata/story/10247/api-security-report-link-shows-a-500-error#activity-10249
         */
        if (isNil(company)) {
            company = await this.getCompany(account);
        }

        controlTestInstances = injectCompanyNameForControlTestInstances(
            controlTestInstances,
            company.name,
        );

        return controlTestInstances;
    }

    /**
     * @deprecated Use MonitorsOrchestrationService.getAllTestingControlTestInstances
     *
     * @param account
     * @param checkResultStatuses
     * @returns
     */
    async getAllTestingControlTestInstances(
        account: Account,
        checkResultStatuses: CheckResultStatus[] = null,
    ): Promise<ControlTestInstance[]> {
        return this.getControlTestInstancesByCheckStatusAndCheckResultStatus(
            account,
            CheckStatus.TESTING,
            checkResultStatuses,
        );
    }

    /**
     * @deprecated Use MonitorsCoreService.getAllStuckInTestingControlTestInstance
     *
     * @param {number} days
     * @returns {Promise<ControlTestInstance[]>}
     */
    async getAllStuckInTestingControlTestInstance(days: number): Promise<ControlTestInstance[]> {
        return this.controlTestInstanceRepository.getStuckInTestingControlTestInstances(days);
    }

    /**
     *
     * @param account
     * @param id
     */
    async getControlTestInstance(account: Account, id: number): Promise<ControlTestInstance> {
        let controlTestInstance =
            await this.controlTestInstanceRepository.getControlTestInstanceWithAllMonitorInstances(
                id,
            );

        const company = await this.getCompany(account);

        controlTestInstance = injectCompanyNameForControlTestInstance(
            controlTestInstance,
            company.name,
        );

        return controlTestInstance;
    }

    /**
     * @deprecated Use MonitorsOrchestrationService.getControlTestInstanceByTestId
     *
     * @param account
     * @param testId
     */
    async getControlTestInstanceByTestId(
        account: Account,
        testId: number,
    ): Promise<ControlTestInstance> {
        let controlTestInstance =
            await this.controlTestInstanceRepository.getControlTestInstanceByTestIdWithAllMonitorInstances(
                testId,
                getProductId(account),
                account,
            );

        const company = await this.getCompany(account);

        controlTestInstance = injectCompanyNameForControlTestInstance(
            controlTestInstance,
            company.name,
        );

        return controlTestInstance;
    }

    /**
     *
     * @param {Account} account
     * @param {ControlTestInstance} controlTestInstance
     * @returns {Promise<boolean>}
     */
    async shouldRevertToControlTestInstance(
        account: Account,
        controlTestInstance: ControlTestInstance,
    ): Promise<boolean> {
        let canRevert = false;

        if (isEmpty(controlTestInstance.monitorInstances)) {
            return canRevert;
        }

        const controlTestTemplate = await this.controlTestTemplateRepository.findOneBy({
            id: controlTestInstance.controlTestTemplateId,
        });
        if (isNil(controlTestTemplate)) {
            return false;
        }
        const monitorInstance = controlTestInstance.monitorInstances[0];
        const monitorTemplate = await this.monitorTemplateRepository.findOneBy({
            id: monitorInstance.monitorTemplateId,
        });
        const company = await this.getCompany(account);

        // custom tests do not have a monitor template
        if (controlTestInstance.source === TestSource.DRATA) {
            const monitorTemplateDescription = replaceTarget(
                monitorTemplate.evidenceCollectionDescription,
                company.name,
            );
            canRevert =
                controlTestInstance.name !== controlTestTemplate.name ||
                monitorInstance.evidenceCollectionDescription !== monitorTemplateDescription;
        }

        return canRevert;
    }

    /**
     *
     * @param account
     * @param controlTestInstance
     */
    private async getControlTestAvailableConnections(
        account: Account,
        controlTestInstance: ControlTestInstance,
    ): Promise<AvailableConnection[]> {
        const monitorInstances =
            await this.monitorInstanceRepository.getMonitorTestInstanceByControlTestInstanceId(
                controlTestInstance.id,
            );

        const ap2Connections = flatMap(
            controlTestInstance.recipes,
            recipe => recipe.recipe.providers,
        ).map(provider => ClientType[provider.provider]);

        if (controlTestInstance.isCustom()) {
            return ap2Connections.map(clientType => ({ clientType: ClientType[clientType] }));
        }

        const ap1Connections = flatMap(monitorInstances, ({ requestDescriptions }) =>
            JSON.parse(requestDescriptions ?? '{}'),
        )
            .filter(
                ({ requestDescriptionType, clientType }) =>
                    requestDescriptionType === RequestDescriptionType.CONNECTION &&
                    !isNil(ClientType[clientType]),
            )
            .map(request => request.clientType);

        if ([RunMode.AP1, RunMode.DUAL].includes(controlTestInstance.runMode)) {
            return ap1Connections.map(clientType => ({
                clientType: ClientType[clientType],
            }));
        }

        const activeConnections =
            await this.connectionsCoreService.getActiveConnectionsByProductId(account);

        const activeConnectionsClientType = activeConnections.map(
            connection => connection.clientType,
        );

        const ap1ActiveConnections = ap1Connections.filter(connection =>
            some(activeConnectionsClientType, clientType => clientType === connection),
        ).length;

        const ap2ActiveConnections = ap2Connections.filter(connection =>
            some(activeConnectionsClientType, clientType => clientType === connection),
        ).length;

        const availableConnections =
            ap1ActiveConnections > ap2ActiveConnections ? ap1Connections : ap2Connections;

        return availableConnections.map(clientType => ({ clientType: ClientType[clientType] }));
    }

    /**
     *
     * @param {Account} account
     * @param {number} testId
     * @returns {Promise<ControlTestInstanceDetails>}
     */
    async getControlTestInstanceForUser(
        account: Account,
        testId: number,
    ): Promise<ControlTestInstanceDetails> {
        const controlTestInstance = await this.getControlTestInstanceByTestId(account, testId);

        if (controlTestInstance.runMode === RunMode.AP2_SHADOW_TEST) {
            throw new NotFoundException(ErrorCode.ENTITY_NOT_FOUND);
        }

        const workspaceId = getProductId(account);

        const results = await Promise.allSettled([
            this.getComplianceCheckExclusionsPersonnel(testId),
            this.getControlTestAvailableConnections(account, controlTestInstance),
            this.getRequiredPoliciesForMonitorDetails(account, workspaceId, controlTestInstance),
            this.getMonitorInstanceAdditionalProperties(account, controlTestInstance),
            this.shouldRevertToControlTestInstance(account, controlTestInstance),
        ]);

        const [
            complianceCheckExclusionPersonnel,
            availableConnections,
            requiredPolicies,
            additionalProperties,
            canRevert,
        ] = results.map((result, index) =>
            result.status === SettledStatus.FULFILLED
                ? result.value
                : this.handleRejectedPromiseForMonitorDetails(account, result.reason, index),
        );

        return Object.assign(controlTestInstance, {
            canManageExclusions: isNil(complianceCheckExclusionPersonnel),
            additionalProperties,
            complianceCheckExclusions: complianceCheckExclusionPersonnel,
            canRevert,
            parentTestId: controlTestInstance.parent?.testId ?? null,
            availableConnections,
            requiredPolicies,
        }) as ControlTestInstanceDetails;
    }

    /**
     *
     * @param {number} testId
     * @returns {Promise<ExtendedComplianceCheckExclusion[] | null>}
     */
    async getComplianceCheckExclusionsPersonnel(
        testId: number,
    ): Promise<ExtendedComplianceCheckExclusion[] | null> {
        const { complianceCheckType, employmentStatuses: testEmploymentStatuses } =
            ControlTestInstanceTestIdComplianceCheckTypes.get(testId) ?? {};

        if (isNil(complianceCheckType)) {
            return null;
        }

        const complianceCheckExclusions =
            await this.complianceCheckExclusionRepository.findByComplianceCheckType(
                complianceCheckType,
            );

        let exclusionsWithPersonnel: ExtendedComplianceCheckExclusion[] = [];
        const complianceCheckExclusionForPersonnel: ComplianceCheckExclusion[] = [];

        for (const complianceCheckExclusion of complianceCheckExclusions) {
            const { targetType, targetId } = complianceCheckExclusion;
            switch (targetType) {
                case ComplianceCheckExclusionTargetType.PERSONNEL:
                    complianceCheckExclusionForPersonnel.push(complianceCheckExclusion);
                    break;
                case ComplianceCheckExclusionTargetType.GROUP:
                    const personnelIds = await this.personnelRepository.getPersonnelIdsByGroupIds([
                        Number(targetId),
                    ]);
                    const personnelByGroupIds = await this.personnelRepository.findByIdsWithOptions(
                        personnelIds,
                        { setDevices: true, setGroups: false },
                    );
                    exclusionsWithPersonnel.push(
                        createExtendedComplianceCheckExclusion(
                            complianceCheckExclusion,
                            personnelByGroupIds,
                        ),
                    );
                    break;
                case ComplianceCheckExclusionTargetType.EMPLOYMENT_STATUS:
                    const personnelByEmploymentStatus =
                        await this.personnelRepository.findByEmploymentStatuses([Number(targetId)]);
                    exclusionsWithPersonnel.push(
                        createExtendedComplianceCheckExclusion(
                            complianceCheckExclusion,
                            personnelByEmploymentStatus,
                        ),
                    );
                    break;
                case ComplianceCheckExclusionTargetType.COMPANY:
                    const personnelByCompany =
                        await this.personnelRepository.findByEmploymentStatuses(
                            EmploymentStatusOptionsFilterMap.get(
                                EmploymentStatusOptionsFilter.ALL_PERSONNEL,
                            ),
                        );
                    exclusionsWithPersonnel.push(
                        createExtendedComplianceCheckExclusion(
                            complianceCheckExclusion,
                            personnelByCompany,
                        ),
                    );
                    break;
            }
        }

        if (!isEmpty(complianceCheckExclusionForPersonnel)) {
            const personnelIds = complianceCheckExclusionForPersonnel.map(({ targetId }) =>
                Number(targetId),
            );
            const personnel = await this.personnelRepository.find({
                where: {
                    id: In(personnelIds),
                },
                select: {
                    id: true,
                    employmentStatus: true,
                    user: {
                        firstName: true,
                        lastName: true,
                        email: true,
                        id: true,
                    },
                    devices: {
                        id: true,
                        alias: true,
                        asset: {
                            id: true,
                            name: true,
                        },
                        agentAssignation: {
                            id: true,
                            agent: {
                                id: true,
                                serialNumber: true,
                                operationalState: true,
                            },
                        },
                    },
                },
                relations:
                    testId === 64
                        ? [
                              'devices',
                              'user',
                              'devices.agentAssignation',
                              'devices.agentAssignation.agent',
                              'devices.asset',
                          ]
                        : ['user'],
                loadEagerRelations: false,
            });

            if (!isEmpty(personnel)) {
                for (const p of personnel) {
                    const personnelCompliance = find(
                        complianceCheckExclusions,
                        ({ targetType, targetId }) =>
                            targetType === ComplianceCheckExclusionTargetType.PERSONNEL &&
                            Number(targetId) === p.id,
                    );

                    if (isNil(personnelCompliance)) {
                        continue;
                    }
                    exclusionsWithPersonnel.push(
                        createExtendedComplianceCheckExclusion(personnelCompliance, [p]),
                    );
                }
            }
        }

        // filter by employmentStatus if test is restricted by one
        if (!isEmpty(testEmploymentStatuses)) {
            exclusionsWithPersonnel = exclusionsWithPersonnel.filter(exclusion => {
                exclusion.personnel = exclusion.personnel.filter(pers =>
                    testEmploymentStatuses.includes(pers.employmentStatus),
                );
                return !isEmpty(exclusion.personnel);
            });
        }

        return exclusionsWithPersonnel;
    }

    /**
     *
     * @param account
     * @returns
     */
    getMonitorsStats(account: Account, dto: MonitorStatsRequestDto): Promise<MonitorStat> {
        if (!hasAssociatedProduct(account)) {
            return;
        }

        const options: FindManyOptions<MonitorStat> = {};
        options.where = {
            product: { id: account.getCurrentProduct().id },
            statType: dto.type,
        };
        options.relations = ['product'];

        return this.monitorStatRepository.findOneOrFail(options);
    }

    /**
     * @deprecated Use MonitorsCoreService.getTestsCount
     */
    getTestsCount(account: Account): Promise<number> {
        return this.controlTestInstanceHistoryRepository.getTestsCount(account);
    }

    /**
     * @deprecated Use MonitorsCoreService.getControlTestHistory
     */
    async getControlTestHistory(
        account: Account,
        dto: ControlTestInstanceHistoryRequestDto,
    ): Promise<ControlTestInstanceHistoryType> {
        const { testId, reportInterval } = dto;
        let data = null;

        switch (reportInterval) {
            case ReportInterval.WEEKLY:
                data = this.getControlTestHistoryByWeek(account, testId);
                break;
            case ReportInterval.MONTHLY:
                data = this.getControlTestHistoryByMonth(account, testId);
                break;
            default:
                throw new InternalServerErrorException(
                    `ReportInterval ${ReportInterval[reportInterval]} is not supported`,
                );
        }

        return data;
    }

    public async getMonitorInstanceById(id: number): Promise<MonitorInstance> {
        return this.monitorInstanceRepository.findOneByOrFail({ id });
    }

    /**
     *
     * @param id
     */
    public async getMonitorExclusionById(id: number): Promise<MonitorInstanceExclusion> {
        // return the exclusion here by id - do not fail
        const exclusion = await this.exclusionRepository.findOneOrFail({
            where: { id },
            relations: ['controlTestInstance', 'exclusionDesignator', 'connection'],
        });
        // return here
        return exclusion;
    }

    /**
     * @deprecated Use MonitorsCoreService.getAllMonitorExclusions
     */
    public getAllMonitorExclusions(
        dto: PaginationExclusionRequestDto,
    ): Promise<PaginationType<MonitorInstanceExclusion>> {
        return this.exclusionRepository.getAllMonitorInstanceExclusion(dto);
    }

    /**
     * @deprecated Use MonitorsCoreService.getMonitorExclusionsByDeletedConnection
     */
    public getMonitorExclusionsByDeletedConnection(
        deletedConnection: ConnectionEntity,
    ): Promise<MonitorInstanceExclusion[]> {
        return this.exclusionRepository.find({
            where: {
                deletedAt: deletedConnection.deletedAt,
                connection: {
                    id: deletedConnection.id,
                },
            },
            withDeleted: true,
        });
    }

    /**
     * @deprecated Use MonitorsCoreService.deleteMonitorExclusionsByConnection
     *
     * @param connectionId
     * @param deletedAtTimestamp
     * @returns void
     */
    public async deleteMonitorExclusionsByConnection(
        connectionId: number,
        deletedAtTimestamp: Date,
    ): Promise<void> {
        const exclusions = await this.exclusionRepository.find({
            where: { connection: { id: connectionId } },
        });

        if (!isEmpty(exclusions)) {
            for (const exclusion of exclusions) {
                exclusion.deletedAt = deletedAtTimestamp;
            }

            await this.exclusionRepository.save(exclusions);
        }
    }

    /**
     *
     * @param exclusion
     * @returns
     */
    public saveExclusion(exclusion: MonitorInstanceExclusion): Promise<MonitorInstanceExclusion> {
        return this.exclusionRepository.save(exclusion);
    }

    /**
     * @deprecated Use MonitorsCoreService.saveExclusions
     */
    public saveExclusions(
        exclusions: MonitorInstanceExclusion[],
    ): Promise<MonitorInstanceExclusion[]> {
        return this.exclusionRepository.save(exclusions);
    }

    /**
     * @deprecated Use MonitorsCoreService.getMonitorExclusionsByTestIdAndConnectionId
     *
     * @param account
     * @param testId
     * @param connectionId
     * @returns
     */
    public getMonitorExclusionsByTestIdAndConnectionId(
        account: Account,
        testId: number,
        connectionId: number | null,
    ): Promise<MonitorInstanceExclusion[]> {
        return this.exclusionRepository.getMonitorExclusionsByTestIdAndConnectionId(
            account,
            testId,
            connectionId,
        );
    }

    /**
     *
     * @param account
     * @param targetId
     * @param testId
     * @param connectionId
     * @returns
     */
    private getMonitorExclusionByTargetIdAndTestId(
        account: Account,
        targetId: string,
        testId: number,
        connectionId: number | null,
    ): Promise<MonitorInstanceExclusion> {
        return this.exclusionRepository.getMonitorExclusionByTargetIdAndTestId(
            account,
            targetId,
            testId,
            connectionId,
        );
    }

    /**
     * @deprecated Use MonitorsCoreService.getControlTestHistoryByMonth
     */
    private async getControlTestHistoryByMonth(
        account: Account,
        testId: number,
    ): Promise<ControlTestInstanceHistoryType> {
        const months = this.getTwelveMonthTrail();

        const data: ControlTestInstanceHistoryType = {
            labels: months.map(el => 'MONTH_' + el),
            passed: [],
            failed: [],
            errored: [],
        };

        const passedResults =
            await this.controlTestInstanceHistoryRepository.getControlTestHistoryByMonth(
                account,
                CheckResultStatus.PASSED,
                testId,
            );

        const failedResults =
            await this.controlTestInstanceHistoryRepository.getControlTestHistoryByMonth(
                account,
                CheckResultStatus.FAILED,
                testId,
            );

        const errorResults =
            await this.controlTestInstanceHistoryRepository.getControlTestHistoryByMonth(
                account,
                CheckResultStatus.ERROR,
                testId,
            );

        if (!isNil(passedResults[0])) {
            const results = passedResults[0];

            for (const month of months) {
                const count = Number(results[month]) ?? 0;
                data.passed.push(count);
            }
        } else {
            data.passed = fill(Array(months.length), 0);
        }

        if (!isNil(failedResults[0])) {
            const results = failedResults[0];

            for (const month of months) {
                const count = Number(results[month]) ?? 0;
                data.failed.push(count);
            }
        } else {
            data.failed = fill(Array(months.length), 0);
        }

        if (!isNil(errorResults[0])) {
            const results = errorResults[0];

            for (const month of months) {
                const count = Number(results[month]) ?? 0;
                data.errored.push(count);
            }
        } else {
            data.errored = fill(Array(months.length), 0);
        }

        return data;
    }

    /**
     * @deprecated Use MonitorsCoreService.getControlTestHistoryByWeek
     */
    private async getControlTestHistoryByWeek(
        account: Account,
        testId: number,
    ): Promise<ControlTestInstanceHistoryType> {
        const weeksPerYear = 52;

        const data: ControlTestInstanceHistoryType = {
            labels: [],
            passed: fill(Array(weeksPerYear), 0),
            failed: fill(Array(weeksPerYear), 0),
            errored: fill(Array(weeksPerYear), 0),
        };

        const labels = getControlTestHistoryByWeekLabels(weeksPerYear);

        const passedResults =
            await this.controlTestInstanceHistoryRepository.getControlTestHistoryByWeek(
                account,
                weeksPerYear,
                CheckResultStatus.PASSED,
                testId,
            );

        const failedResults =
            await this.controlTestInstanceHistoryRepository.getControlTestHistoryByWeek(
                account,
                weeksPerYear,
                CheckResultStatus.FAILED,
                testId,
            );
        const errroResults =
            await this.controlTestInstanceHistoryRepository.getControlTestHistoryByWeek(
                account,
                weeksPerYear,
                CheckResultStatus.ERROR,
                testId,
            );

        for (let index = 1; index <= weeksPerYear; index++) {
            data.labels.push(labels[0][index]);
        }

        if (!isNil(passedResults[0])) {
            for (const result of passedResults) {
                const index = indexOf(data.labels, result.week_of);
                data.passed[index] = Number(result.total) ?? 0;
            }
        }

        if (!isNil(failedResults[0])) {
            for (const result of failedResults) {
                const index = indexOf(data.labels, result.week_of);
                data.failed[index] = Number(result.total) ?? 0;
            }
        }

        if (!isNil(errroResults[0])) {
            for (const result of errroResults) {
                const index = indexOf(data.labels, result.week_of);
                data.errored[index] = Number(result.total) ?? 0;
            }
        }

        return data;
    }

    /**
     * @deprecated Use MonitorsOrchestrationService.passMonitorInstanceTest
     *
     * @param task
     * @param result
     * @param account
     */
    async passMonitorInstanceTest<T extends MonitorData>(
        task: Task<any>,
        result: MonitorResult<T>,
        account: Account,
    ): Promise<any> {
        this.logger.log(
            PolloAdapter.acct(
                `Test ${task.getTestId()} passed for ${account.companyName}`,
                account,
            ).setIdentifier({
                event: LogIdentifierEvent.autopilotTestPass,
                clientType: ClientType[task.getConnectionClientType()],
                providerType: ProviderType[task.getConnectionProviderType()],
                providerTypes: task.getConnectionProviderTypes().map(type => ProviderType[type]),
                connectionId: task.getConnectionId(),
                connectionAlias: task.getConnectionClientAlias(),
                testId: task.getTestId(),
            }),
        );

        await this.updateMonitorInstanceTestCheckResultStatus(
            task,
            CheckResultStatus.PASSED,
            result,
        );
    }

    /**
     * @deprecated Use MonitorsOrchestrationService.failMonitorInstanceTest
     *
     * @param task
     * @param result
     * @param account
     */
    async failMonitorInstanceTest<T extends MonitorData>(
        task: Task<any>,
        result: MonitorResult<T>,
        account: Account,
    ): Promise<any> {
        this.logger.log(
            PolloAdapter.acct(
                `Test ${task.getTestId()} failed for ${account.companyName}`,
                account,
            ).setIdentifier({
                event: LogIdentifierEvent.autopilotTestFail,
                clientType: ClientType[task.getConnectionClientType()],
                providerType: ProviderType[task.getConnectionProviderType()],
                providerTypes: task.getConnectionProviderTypes().map(type => ProviderType[type]),
                connectionId: task.getConnectionId(),
                connectionAlias: task.getConnectionClientAlias(),
                testId: task.getTestId(),
            }),
        );

        await this.updateMonitorInstanceTestCheckResultStatus(
            task,
            CheckResultStatus.FAILED,
            result,
        );
    }

    /**
     * @deprecated Use MonitorsCoreService.setControlTestInstancesTestStatus
     *
     * @param instances
     */
    async setControlTestInstancesTestStatus(
        instances: ControlTestInstance[],
        checkStatus: CheckStatus,
    ): Promise<void> {
        instances.forEach((instance, index) => {
            instances[index].checkStatus = checkStatus;
            delete instances[index].controls; // delete this else typeorm will delete the association on disabled controls
        });

        await this.controlTestInstanceRepository.save(instances, {
            reload: false,
        });
    }

    /**
     * @deprecated Use MonitorsOrchestrationService.startTestingControlTestInstanceForRetest
     *
     * @param account
     * @param instance
     */
    async startTestingControlTestInstanceForRetest(
        account: Account,
        instance: ControlTestInstance,
    ): Promise<void> {
        instance.checkStatus = CheckStatus.TESTING;

        await this.controlTestInstanceRepository.save(instance, {
            reload: false,
        });

        // Update the monitoring results OpenSearch index when the test begins running
        // this needs to block before we notify the client via socket
        await updateMonitoringSummaryResultsIndexSetToTestingState(
            this.monitoringSummaryIndexingService,
            account,
            null,
            [instance.testId],
        );

        await this.notifyTestStateChange(account, instance.testId);
    }

    /**
     * @deprecated Use AccountProvisioningOrchestration.processControlTestInstancesForConnection
     */
    async processControlTestInstancesForConnection(account: Account, connection: ConnectionEntity) {
        try {
            await this.enableControlTestInstancesByClientType(account, connection);

            await this.resolveControlTestConnectionDependencyOnConnection(account, connection);
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(
                    `Could not update Control Test Instances for connection: ${error.message}`,
                    account,
                ).setError(error),
            );
        }
    }

    /**
     * @deprecated Use MonitorsOrchestrationService.changeRelatedTestStatus
     */
    async changeRelatedTestStatus(
        account: Account,
        testIds: number[],
        updatedStatus: CheckStatus,
        productIds: number[],
    ): Promise<void> {
        for await (const productId of productIds) {
            for (const testId of testIds) {
                try {
                    const controlTestInstance =
                        await this.controlTestInstanceRepository.getControlTestInstanceByTestIdWithAllMonitorInstances(
                            testId,
                            productId,
                            account,
                        );
                    controlTestInstance.checkStatus = updatedStatus;

                    if (updatedStatus === CheckStatus.ENABLED) {
                        this.logger.log(
                            PolloMessage.msg(
                                `Enabling test id ${testId} for product: ${productId}`,
                            ),
                        );
                        await this.enableMonitorTestsOnControlTestInstance(
                            account,
                            controlTestInstance,
                        );
                    } else {
                        await this.controlTestInstanceRepository.save(controlTestInstance, {
                            reload: false,
                        });
                    }
                } catch (error) {
                    this.logger.warn(
                        PolloMessage.msg(
                            `Unable to change test ${testId} status: ${JSON.stringify(error)}`,
                        ).setAccountId(account.id),
                    );
                }
            }
        }

        /**
         * - Note: we are updating the entire account since this functionality has a iterator over workspace/tests,
         *   and we don't want to spam the indexer.
         * - Parent scopes are event handlers, which are already a secondary thread, we can move to async.
         */
        // Update the monitoring results OpenSearch since there is an indicator on it for exclusions
        this._eventBus.publish(new IndexMonitorResultEvent(account, null, null));
    }

    /**
     * @deprecated Use AccountProvisioningOrchestration.enableControlTestInstancesByClientType
     */
    async enableControlTestInstancesByClientType(
        account: Account,
        connection: ConnectionEntity,
    ): Promise<void> {
        const shouldEnableControlTestInstances = !connection.connectionProviderTypes.every(
            ({ providerType }) =>
                this.EXCLUDED_PROVIDERS_FROM_ENABLE_CONTROL_TESTS_INSTANCES.includes(providerType),
        );

        if (!shouldEnableControlTestInstances) {
            return;
        }

        for (const provider of connection.connectionProviderTypes) {
            // will run per provider for each connection. this is to avoid pulling task types
            // when a connection is connected as global and will pull connections (wrongly) for workspaces
            // example is AWS when is connected as INFRASTRUCTURE and USER_ACCESS_REVIEW

            const singleProviderConnection = cloneDeep(connection);
            singleProviderConnection.connectionProviderTypes = [provider];
            const products = await this.workspacesCoreService.resolveProductsForConnection(
                account,
                singleProviderConnection,
            );

            const providerTypeTasks: AutopilotTaskType[] = [];

            singleProviderConnection.connectionProviderTypes.forEach(({ providerType }) => {
                providerTypeTasks.push(...getAutopilotTaskTypesByProviderType(providerType));
            });

            const providerTypeTasksWithoutDuplicates = Array.from(new Set(providerTypeTasks));

            if (!isNil(products)) {
                for (const product of products) {
                    const accountWithProduct = account.cloneWithProduct(product);

                    await this.enableControlTestInstancesByClientTypeForProduct(
                        accountWithProduct,
                        providerTypeTasksWithoutDuplicates,
                        singleProviderConnection,
                    );
                }
            }
        }

        this._eventBus.publish(
            new IndexMonitorResultEvent(account, null, null, {
                calculateFindingsAndExclusionsCounts: true,
            }),
        );
    }

    /**
     * @deprecated Use MonitorsOrchestrationService.enableControlTestInstancesByClientTypeForProduct or AccountProvisioningOrchestration.enableControlTestInstancesByClientTypeForProduct
     */
    async enableControlTestInstancesByClientTypeForProduct(
        account: Account,
        providerTypeTasks: AutopilotTaskType[],
        connection: ConnectionEntity,
    ): Promise<void> {
        const otherConnections: ConnectionEntity[] = [];

        for (const { providerType } of connection.connectionProviderTypes) {
            otherConnections.push(
                ...(
                    await this.connectionsCoreService.getConnectionsByProviderType(
                        providerType,
                        account,
                    )
                ).filter(otherConnection => otherConnection.id !== connection.id),
            );
        }

        const activeConnections = otherConnections.concat(connection);

        const activeAutopilotTaskTypes = findAutopilotTaskTypesForConnections(activeConnections);

        if (isEmpty(activeAutopilotTaskTypes)) {
            this.warn(
                'Resolved no autopilot task types to enable for connection',
                account,
                connection,
            );

            return;
        }

        const controlTestInstances =
            await this.controlTestInstanceRepository.getControlTestInstancesByAutopilotTaskTypesAndCheckStatus(
                providerTypeTasks,
                null, // could be UNUSED or NEW
                getProductId(account),
                false,
                false,
            );

        if (isEmpty(controlTestInstances)) {
            this.log(
                `No control test instances found to set to ${
                    CheckStatus[CheckStatus.ENABLED]
                } for autopilot task types`,
                account,
                providerTypeTasks.map(att => AutopilotTaskType[att]).join(','),
            );

            return;
        }

        const updatedControlTestInstances = [];
        const updatedMonitorInstances = [];

        for (const controlTestInstance of controlTestInstances) {
            const dependencySatisfied = await this.controlTestConnectionDependencySatisfied(
                account,
                controlTestInstance,
                connection.clientType,
            );

            const isNotUnusedCheckStatus = ![CheckStatus.UNUSED].includes(
                controlTestInstance.checkStatus,
            );

            const monitorInstances = get(controlTestInstance, 'monitorInstances', []);

            if (isNotUnusedCheckStatus || !dependencySatisfied || isEmpty(monitorInstances)) {
                continue;
            }

            const monitorInstance: MonitorInstance = monitorInstances.find(Boolean);

            if (activeAutopilotTaskTypes.includes(monitorInstance.autopilotTaskType)) {
                if (isNil(controlTestInstance.disabledMessage)) {
                    controlTestInstance.checkStatus = CheckStatus.ENABLED;
                    monitorInstance.enabled = true;
                } else {
                    controlTestInstance.checkStatus = CheckStatus.DISABLED;
                    monitorInstance.enabled = false;
                }
            } else {
                // test not implemented
                controlTestInstance.checkStatus = CheckStatus.UNUSED;
                monitorInstance.enabled = false;
            }

            updatedControlTestInstances.push(controlTestInstance);
            updatedMonitorInstances.push(monitorInstance);
        }

        if (!isEmpty(updatedControlTestInstances)) {
            await this.controlTestInstanceRepository.save(updatedControlTestInstances, {
                reload: false,
            });
        }

        if (!isEmpty(updatedMonitorInstances)) {
            await this.monitorInstanceRepository.save(updatedMonitorInstances, {
                reload: false,
            });
        }
    }

    /**
     * @deprecated Use MonitorsServiceOrchestrationService.getControlTestInstancesByClientTypeForProduct
     *
     * @param account
     * @param clientType
     * @returns
     */
    async getControlTestInstancesByClientTypeForProduct(
        account: Account,
        clientType: ClientType,
    ): Promise<ControlTestInstance[]> {
        const providerTypeTasks = getProviderTypesByClientType(clientType).flatMap(providerType =>
            getAutopilotTaskTypesByProviderType(providerType),
        );

        const controlTestInstances =
            await this.controlTestInstanceRepository.getControlTestInstancesByAutopilotTaskTypesAndCheckStatus(
                providerTypeTasks,
                null, // could be UNUSED or NEW
                getProductId(account),
            );

        // MDM connections aren't in the clientTypeAutopilotTaskTypeMap that is checked below so we just return the list.
        // Both MDMs and the Drata Agent can submit device data, so we can't rely on the presence of a connection to drive the AP Tests.
        if (getProviderTypeByClientType(clientType) === ProviderType.MDM) {
            return controlTestInstances;
        }

        const implementedAutopilotTaskTypes =
            getImplementedAutopilotTaskTypesByClientType(clientType);

        const filteredControlTestInstances = [];

        for (const controlTestInstance of controlTestInstances) {
            const dependencySatisfied = await this.controlTestConnectionDependencySatisfied(
                account,
                controlTestInstance,
                clientType,
            );

            const monitorInstances = get(controlTestInstance, 'monitorInstances', []);

            if (!dependencySatisfied || isEmpty(monitorInstances)) {
                continue;
            }

            const monitorInstance: MonitorInstance = monitorInstances.find(Boolean);

            if (
                implementedAutopilotTaskTypes.includes(monitorInstance.autopilotTaskType) &&
                isNil(controlTestInstance.disabledMessage)
            ) {
                filteredControlTestInstances.push(controlTestInstance);
            }
        }
        return filteredControlTestInstances;
    }

    /**
     * @deprecated Use MonitorsOrchestrationService.getControlTestInstancesByClientType
     *
     * @param account
     * @param requestDto
     * @returns
     */
    async getControlTestInstancesByClientType(
        account: Account,
        requestDto: ControlsTestsRequestDto,
    ): Promise<ControlsTests> {
        try {
            const { type } = requestDto;
            let controlTestInstances = await this.getControlTestInstancesByClientTypeForProduct(
                account.cloneWithProduct(await this.workspacesCoreService.getPrimaryProduct()),
                type,
            );

            let controlCodes = controlTestInstances.reduce(
                (codes: { code: string; controlNumber: number }[], test) => {
                    test.controls.forEach(control => {
                        if (!codes.some(code => code.code === control.code)) {
                            codes.push({
                                code: control.code,
                                controlNumber: control.controlNumber || 0,
                            });
                        }
                    });
                    return codes;
                },
                [],
            );

            // Sort Controls and Tests ASC
            controlTestInstances = controlTestInstances.sort((a, b) => a.testId - b.testId);
            controlCodes = controlCodes.sort((a, b) => a.controlNumber - b.controlNumber);

            return {
                tests: controlTestInstances.map(test => ({
                    id: test.testId,
                    name: test.name,
                })),
                controlCodes: controlCodes.map(code => code.code),
            };
        } catch (error) {
            this.error(error, account, 'Error while getting controls and tests for client type');
            throw error;
        }
    }

    /**
     * @deprecated use MonitorsOrchestrationService.controlTestConnectionDependencySatisfied or AccountProvisioningOrchestration.controlTestConnectionDependencySatisfied
     */
    async controlTestConnectionDependencySatisfied(
        account: Account,
        controlTestInstance: ControlTestInstance,
        clientType: ClientType,
    ): Promise<boolean> {
        const autopilotTaskType = get(controlTestInstance, 'monitorInstances[0].autopilotTaskType');
        const dependency = get(clientTypeAutopilotTaskDependencyMap, autopilotTaskType, []).find(
            d => d.source === clientType,
        );

        if (isNil(dependency)) {
            return true;
        }

        const connections = await this.connectionsCoreService.getConnectionsByClientTypes(
            dependency.dependencies,
            getProductId(account),
        );

        if (isEmpty(connections)) {
            return false;
        }
        return true;
    }

    /**
     * @deprecated Use AccountProvisioningOrchestration.resolveControlTestConnectionDependencyOnConnection
     */
    async resolveControlTestConnectionDependencyOnConnection(
        account: Account,
        connection: ConnectionEntity,
    ): Promise<void> {
        const dependency = getAutopilotDependencies(connection);
        if (isEmpty(dependency)) {
            return;
        }
        const clientTypes = dependency.map(d => d.dependency);

        const products = await this.workspacesCoreService.resolveProductsForConnection(
            account,
            connection,
        );

        for (const product of products) {
            /**
             * Get the connections that are dependent on the connection that was just established
             * that are also under the same product. Also pull in the associated products
             * for these connections; note that these connections will have at least
             * one product associated, namely the current product in question
             */
            const dependentConnections =
                await this.connectionsCoreService.getConnectionsByClientTypes(
                    clientTypes,
                    product.id,
                );

            for (const dependentConnection of dependentConnections) {
                const dependentConnectionProducts =
                    await this.workspacesCoreService.getProductsByConnection(dependentConnection);

                dependentConnection.products = dependentConnectionProducts;

                await this.enableControlTestInstancesByClientType(account, dependentConnection);
            }
        }
    }

    /**
     * @deprecated Use MonitorsOrchestrationService.shouldDeactivateControlTestInstancesForDisconnectedProvider
     **/
    private async shouldDeactivateControlTestInstancesForDisconnectedProvider({
        connectionProviderTypes,
    }: ConnectionEntity): Promise<boolean> {
        if (
            connectionProviderTypes.every(
                provider => provider.providerType === ProviderType.CODEBASE,
            )
        ) {
            const allActiveConnections =
                await this.connectionsCoreService.getActiveAndHealthyConnections();

            const activeConnectionsWithSameProviderType = allActiveConnections.filter(
                cnn => cnn.providerType === ProviderType.CODEBASE,
            );

            if (activeConnectionsWithSameProviderType.length > 0) {
                return false;
            }
        }

        return !connectionProviderTypes.every(
            ({ providerType }) => providerType === ProviderType.MDM,
        );
    }

    /**
     * @deprecated Use MonitorsOrchestrationService.setControlTestInstancesCheckStatusByActiveConnections
     *
     * @param account
     * @param removedConnection
     * @param productId
     * @returns
     */
    async setControlTestInstancesCheckStatusByActiveConnections(
        account: Account,
        removedConnection: ConnectionEntity,
        productId?: number,
    ): Promise<void> {
        const shouldDeactivate =
            await this.shouldDeactivateControlTestInstancesForDisconnectedProvider(
                removedConnection,
            );

        if (!shouldDeactivate) {
            return;
        }

        const products = await this.getProductsForConnection(removedConnection, productId);

        for (const product of products) {
            await this.setControlTestInstancesCheckStatusByActiveConnectionsForProduct(
                account,
                removedConnection,
                product,
            );
        }
    }

    /**
     * @deprecated Use MonitorsOrchestrationService.setControlTestInstancesCheckStatusByActiveConnectionsForProduct
     **/
    async setControlTestInstancesCheckStatusByActiveConnectionsForProduct(
        account: Account,
        removedConnection: ConnectionEntity,
        product: Product,
    ): Promise<void> {
        const productId = isNil(product)
            ? await this.workspacesCoreService.getPrimaryProductId()
            : product.id;

        if (isNil(removedConnection) || isNil(productId)) {
            return;
        }

        const activeTestsForRemovedConnectionProviderType =
            await this.getActiveTestsByProviderTypes(
                removedConnection.connectionProviderTypes.map(({ providerType }) => providerType),
                productId,
            );

        if (isEmpty(activeTestsForRemovedConnectionProviderType.controlTestInstances)) {
            this.log(
                `No control test instances found to set to ${
                    CheckStatus[CheckStatus.UNUSED]
                } for autopilot task types`,
                account,
                activeTestsForRemovedConnectionProviderType.autopilotTaskTypes
                    .map(att => AutopilotTaskType[att])
                    .join(','),
            );

            return;
        }

        const updatedControlTestInstances = [];
        const updatedMonitorInstances = [];

        for (const controlTestInstance of activeTestsForRemovedConnectionProviderType.controlTestInstances) {
            const autopilotTaskType = get(
                controlTestInstance,
                'monitorInstances[0].autopilotTaskType',
            );

            let activeAutopilotTaskTypesForOtherProviderTypes = [];

            const autopilotTaskSharedProviders =
                autopilotTaskTypeSharedProvidersMap[autopilotTaskType] ?? [];

            if (!isEmpty(autopilotTaskSharedProviders)) {
                activeAutopilotTaskTypesForOtherProviderTypes =
                    await this.getActiveAutopilotTaskTypesByProviderTypes(
                        autopilotTaskSharedProviders,
                        productId,
                    );
            }

            const newTests = getNewAP2OnlyTests();
            if (
                newTests.includes(controlTestInstance.testId) &&
                isControlTestInstanceLabeledAsNew(controlTestInstance.releasedAt)
            ) {
                continue;
            }

            const monitorInstances = get(controlTestInstance, 'monitorInstances', []);

            const monitorInstance: MonitorInstance = monitorInstances.find(Boolean);

            const disableTest =
                !activeTestsForRemovedConnectionProviderType.autopilotTaskTypes.includes(
                    autopilotTaskType,
                ) && !activeAutopilotTaskTypesForOtherProviderTypes.includes(autopilotTaskType);

            if (disableTest) {
                controlTestInstance.checkStatus = CheckStatus.UNUSED;
                updatedControlTestInstances.push(controlTestInstance);

                if (isUndefined(monitorInstance)) {
                    continue;
                }

                monitorInstance.enabled = false;
                updatedMonitorInstances.push(monitorInstance);
            } else if (!(isUndefined(monitorInstance) || !disableTest)) {
                controlTestInstance.checkStatus = CheckStatus.DISABLED;
                updatedControlTestInstances.push(controlTestInstance);

                if (isEmpty(monitorInstance)) {
                    continue;
                }

                monitorInstance.enabled = false;
                updatedMonitorInstances.push(monitorInstance);
            }
        }

        if (!isEmpty(updatedControlTestInstances)) {
            await this.deleteAutopilotSchedulesFromControls(updatedControlTestInstances, account);
            await this.controlTestInstanceRepository.save(updatedControlTestInstances);
        }

        if (!isEmpty(updatedMonitorInstances)) {
            await this.monitorInstanceRepository.save(updatedMonitorInstances);
        }
    }

    /**
     * @deprecated Use MonitorsOrchestrationService.deleteAutopilotSchedulesFromControls
     */
    private async deleteAutopilotSchedulesFromControls(
        controlTestInstances: ControlTestInstance[],
        account: Account,
    ): Promise<void> {
        if (!config.get<boolean>('temporal.enabled')) {
            this.logger.warn(
                PolloAdapter.acct(
                    `Temporal is not enabled for this API service, unable to delete schedules`,
                    account,
                ).setIdentifier({}),
            );
            return;
        }

        const temporalClient = await getTemporalClient(account.domain);

        this.logger.log(
            PolloAdapter.acct(
                `Deleting Autopilot schedule instances from temporal for control tests`,
                account,
            ),
        );

        const controlsInstancesWithSchedules = controlTestInstances.filter(
            controlTestInstance => !isNil(controlTestInstance.schedule),
        );
        const results = await Promise.allSettled(
            controlsInstancesWithSchedules.map(async controlTestInstance => {
                const [currentSchedule] = controlTestInstance.schedule;
                try {
                    await temporalClient.deleteSchedule(currentSchedule.scheduleHandler);
                    return currentSchedule.id;
                } catch (e) {
                    this.logger.error(
                        PolloMessage.msg(
                            `Could not delete schedule from temporal for controlTestInstance`,
                        ).setIdentifier({
                            controlTestId: controlTestInstance.id,
                            scheduleHandler: currentSchedule.scheduleHandler,
                        }),
                    );
                    throw e;
                }
            }),
        );
        const schedulesToDelete = results
            .filter(result => result.status === 'fulfilled')
            .map((fulfilledPromise: PromiseFulfilledResult<number>) => fulfilledPromise.value);
        if (!isEmpty(schedulesToDelete)) {
            await this.autopilotScheduleRepository.softDelete(schedulesToDelete);
        }
        this.logger.log(
            PolloAdapter.acct(
                `Autopilot schedule instances deleted from temporal for control tests`,
                account,
            ),
        );
    }

    /**
     *
     * @deprecated Use MonitorsOrchestrationService.getActiveTestsByProviderTypes
     *
     * @param providerType
     * @returns
     */
    private async getActiveTestsByProviderTypes(
        providerTypes: ProviderType[],
        productId: number,
    ): Promise<{
        controlTestInstances: ControlTestInstance[];
        autopilotTaskTypes: AutopilotTaskType[];
    }> {
        const providerTypesToLeaveEnabled = [ProviderType.MDM, ProviderType.VULNERABILITY];
        const connectionProviderTypesToDisable = providerTypes.filter(
            providerType => !providerTypesToLeaveEnabled.includes(providerType),
        );

        const providerTypeAutopilotTasks: AutopilotTaskType[] = [];

        connectionProviderTypesToDisable.forEach(providerType =>
            providerTypeAutopilotTasks.push(...getAutopilotTaskTypesByProviderType(providerType)),
        );

        const controlTestInstances =
            await this.controlTestInstanceRepository.getControlTestInstancesByAutopilotTaskTypesAndCheckStatus(
                providerTypeAutopilotTasks,
                null,
                productId,
                true,
            );
        const autopilotTaskTypes = await this.getActiveAutopilotTaskTypesByProviderTypes(
            providerTypes,
            productId,
        );

        return {
            controlTestInstances,
            autopilotTaskTypes,
        };
    }

    /**
     * @deprecated Use MonitorsOrchestrationService.getActiveAutopilotTaskTypesByProviderTypes
     *
     * @param providerTypes
     * @returns
     */
    private async getActiveAutopilotTaskTypesByProviderTypes(
        providerTypes: ProviderType[],
        productId: number,
    ): Promise<AutopilotTaskType[]> {
        let autopilotTaskTypes = [];

        const activeConnectionsByProviderType =
            await this.connectionsCoreService.getConnectionsByProviderTypes(
                providerTypes,
                productId,
            );

        if (isEmpty(activeConnectionsByProviderType)) {
            return autopilotTaskTypes;
        }

        autopilotTaskTypes = findAutopilotTaskTypesForConnections(activeConnectionsByProviderType);

        return autopilotTaskTypes;
    }

    /**
     * @deprecated Use MonitorsOrchestrationService.resolveControlTestConnectionDependencyOnDisconnection
     *
     * @param account
     * @param connection
     * @returns
     */
    async resolveControlTestConnectionDependencyOnDisconnection(
        account: Account,
        connection: ConnectionEntity,
        productId?: number,
    ): Promise<void> {
        const dependencies = getAutopilotDependencies(connection);

        if (isEmpty(dependencies)) {
            return;
        }

        const clientTypes = [...new Set(dependencies.map(d => d.dependency))];

        const products = await this.getProductsForConnection(connection, productId);

        for (const product of products) {
            const accountWithProduct = account.cloneWithProduct(product);
            await this.resolveControlTestConnectionDependencyOnDisconnectionForProduct(
                accountWithProduct,
                clientTypes,
                dependencies,
                product,
            );
        }
    }

    /**
     * @deprecated Use MonitorsOrchestrationService.getProductsForConnection
     **/
    private async getProductsForConnection(
        connection: ConnectionEntity,
        productId?: number,
    ): Promise<Product[]> {
        if (isOrganizationLevelConnection(connection)) {
            return this.workspacesCoreService.getAllProducts();
        } else {
            return [await this.workspacesCoreService.getProductById(productId)];
        }
    }

    /**
     * @deprecated Use MonitorsOrchestrationService.resolveControlTestConnectionDependencyOnDisconnectionForProduct
     **/
    async resolveControlTestConnectionDependencyOnDisconnectionForProduct(
        account: Account,
        clientTypes: ClientType[],
        dependencies: {
            taskType: AutopilotTaskType;
            dependency: ClientType;
        }[],
        product: Product,
    ): Promise<void> {
        const productId = isNil(product)
            ? await this.workspacesCoreService.getPrimaryProductId()
            : product.id;

        const connections = await this.connectionsCoreService.getConnectionsByClientTypes(
            clientTypes,
            productId,
        );

        if (isEmpty(connections)) {
            return;
        }

        const updatedControlTestInstances = [];
        const updatedMonitorInstances = [];

        for (const dependency of dependencies) {
            const dependentConnection = connections.find(
                c => c.clientType === dependency.dependency,
            );

            if (dependentConnection) {
                const activeProviderTypeConnections = [];
                for (const { providerType } of dependentConnection.connectionProviderTypes) {
                    activeProviderTypeConnections.push(
                        ...(
                            await this.connectionsCoreService.getConnectionsByProviderType(
                                providerType,
                                account,
                            )
                        ).filter(
                            connection =>
                                connection.id !== dependentConnection.id &&
                                !clientTypes.includes(connection.clientType),
                        ),
                    );
                }
                const enabledAutopilotTaskTypesForDependentConnection =
                    findAutopilotTaskTypesForConnections([dependentConnection]);

                if (
                    isEmpty(activeProviderTypeConnections) &&
                    enabledAutopilotTaskTypesForDependentConnection.includes(dependency.taskType)
                ) {
                    const controlTestInstances =
                        await this.controlTestInstanceRepository.getControlTestInstancesByAutopilotTaskTypesAndCheckStatus(
                            [AutopilotTaskType[AutopilotTaskType[dependency.taskType]]],
                            null,
                        );

                    const controlTestInstance = controlTestInstances[0];

                    // skip new AP2 tests with NEW status
                    // New can run if shadow
                    const newTests = getNewAP2OnlyTests();
                    if (
                        newTests.includes(controlTestInstance.testId) &&
                        isControlTestInstanceLabeledAsNew(controlTestInstance.releasedAt)
                    ) {
                        continue;
                    }

                    const hasDependantConnections = await hasActiveDependantConnections({
                        connectionService: this.connectionsCoreService,
                        dependentConnection,
                        taskType: dependency.taskType,
                        productId,
                        logger: this.logger,
                    });
                    if (hasDependantConnections) {
                        continue;
                    }

                    const monitorInstances = get(controlTestInstance, 'monitorInstances', []);

                    const monitorInstance: MonitorInstance = monitorInstances.find(Boolean);

                    controlTestInstance.checkStatus = CheckStatus.UNUSED;

                    updatedControlTestInstances.push(controlTestInstance);

                    if (isUndefined(monitorInstance)) {
                        continue;
                    }

                    monitorInstance.enabled = false;
                    updatedMonitorInstances.push(monitorInstance);
                }
            }

            if (!isEmpty(updatedControlTestInstances)) {
                await this.controlTestInstanceRepository.save(updatedControlTestInstances);
            }

            if (!isEmpty(updatedMonitorInstances)) {
                await this.monitorInstanceRepository.save(updatedMonitorInstances);
            }
        }
    }

    /**
     *
     * @param account
     * @param user
     * @param testId
     * @param dto
     */
    async createMonitorExclusions(
        account: Account,
        user: User,
        dto: MonitorInstanceExclusionsRequestDto,
        testId: number,
    ): Promise<MonitorInstanceExclusion[]> {
        const { exclusionReason, monitorInstanceExclusionsByConnection } = dto;

        const complianceCheckOptions = ControlTestInstanceTestIdComplianceCheckTypes.get(testId);

        if (!isNil(complianceCheckOptions)) {
            throw new UnprocessableEntityException({
                message: 'Test ID is not applicable for a monitor exclusion',
            });
        }

        const controlTestInstance =
            await this.controlTestInstanceRepository.getControlTestInstanceByTestIdWithAllMonitorInstances(
                testId,
                getProductId(account),
                account,
            );

        const testResources: string[] = [];

        for (const exclusions of monitorInstanceExclusionsByConnection) {
            const { connectionId = null, monitorInstanceExclusions } = exclusions;

            let connection: ConnectionEntity | null = null;

            if (!isNil(connectionId)) {
                connection = await this.connectionsCoreService.getConnectionById(connectionId);
            }

            for (const monitorInstanceExclusion of monitorInstanceExclusions) {
                monitorInstanceExclusion.targetId = getFindingTargetId(
                    controlTestInstance.monitorInstances,
                    connection,
                    monitorInstanceExclusion.targetId,
                );

                const existingExclusion = await this.getMonitorExclusionByTargetIdAndTestId(
                    account,
                    monitorInstanceExclusion.targetId,
                    testId,
                    connectionId,
                );

                if (!isNil(existingExclusion)) {
                    throw new ConflictException();
                }

                await this.createMonitorInstanceExclusion(
                    account,
                    user,
                    monitorInstanceExclusion,
                    controlTestInstance,
                    connection,
                    exclusionReason,
                );
                testResources.push(monitorInstanceExclusion.targetId);
            }
        }

        if (testResources.length > 0) {
            const productId = getProductId(account);
            const product = await this.workspacesCoreService.getProductById(productId ?? 0);

            this.analytics.track({
                event: AnalyticsEvent.MONITORING_EXCLUSION_CREATED,
                userId: user.entryId,

                properties: {
                    companyName: account.companyName,
                    testResources,
                    exclusionReason,
                    workspaceName: product?.name,
                    createdAt: new Date().toISOString(),
                    testId,
                },
            });
        }

        this._eventBus.publish(new ExclusionUpdatedEvent(account, user, controlTestInstance));

        await updateMonitoringSummaryResultsIndexForTest(
            this.monitoringSummaryIndexingService,
            account,
            null,
            controlTestInstance.testId,
            { calculateFindingsAndExclusionsCounts: true },
        );

        return this.exclusionRepository.getExclusionsByControlTestInstance(controlTestInstance.id);
    }

    @CreateEvent<MonitorInstanceExclusion>(
        EventType.MONITOR_EXCLUSION_CREATED,
        EventCategory.MONITOR,
        '${user.firstName} ${user.lastName} created exclusion ${monitorExclusionDto.targetName}',
    )
    async createMonitorInstanceExclusion(
        account: Account,
        user: User,
        monitorExclusionDto: MonitorInstanceExclusionRequestDto,
        controlTestInstance: ControlTestInstance,
        connection: ConnectionEntity | null,
        exclusionReason: string,
    ): Promise<MonitorInstanceExclusion> {
        const monitorInstanceExclusion = new MonitorInstanceExclusion();
        monitorInstanceExclusion.targetId = monitorExclusionDto.targetId;
        monitorInstanceExclusion.targetName = monitorExclusionDto.targetName;
        monitorInstanceExclusion.exclusionReason = exclusionReason;
        monitorInstanceExclusion.controlTestInstance = controlTestInstance;
        monitorInstanceExclusion.exclusionDesignator = user;
        monitorInstanceExclusion.connection = connection;

        return this.exclusionRepository.save(monitorInstanceExclusion);
    }

    /**
     *
     * @param account
     * @param user
     * @param dto
     * @param testId
     */
    async updateMonitorExclusions(
        account: Account,
        user: User,
        dto: MonitorInstanceExclusionsRequestDto,
        testId: number,
    ): Promise<MonitorInstanceExclusion[]> {
        const { exclusionReason, monitorInstanceExclusionsByConnection } = dto;

        let monitorInstanceId = null;

        for (const monitorExclusions of monitorInstanceExclusionsByConnection) {
            const { connectionId = null, monitorInstanceExclusions } = monitorExclusions;

            if (!isNil(connectionId)) {
                await this.connectionsCoreService.getConnectionById(connectionId);
            }

            for (const monitorExclusion of monitorInstanceExclusions) {
                let monitorInstanceExclusion = await this.getMonitorExclusionByTargetIdAndTestId(
                    account,
                    monitorExclusion.targetId,
                    testId,
                    connectionId,
                );

                if (isNil(monitorInstanceExclusion)) {
                    throw new NotFoundException(ErrorCode.ENTITY_NOT_FOUND);
                }

                monitorInstanceExclusion = await this.updateMonitorInstanceExclusion(
                    account,
                    user,
                    monitorInstanceExclusion,
                    monitorExclusion,
                    exclusionReason,
                );

                monitorInstanceId = get(monitorInstanceExclusion, 'controlTestInstance.id', null);
            }
        }

        // Update the monitoring results OpenSearch since there is an indicator on it for exclusions
        this._eventBus.publish(
            new IndexMonitorResultEvent(account, null, [testId], {
                calculateFindingsAndExclusionsCounts: true,
            }),
        );

        return this.exclusionRepository.getExclusionsByControlTestInstance(monitorInstanceId);
    }

    @CreateEvent<MonitorInstanceExclusion>(
        EventType.MONITOR_EXCLUSION_UPDATED,
        EventCategory.MONITOR,
        '${user.firstName} ${user.lastName} updated exclusion ${monitorExclusion.targetName}',
    )
    private async updateMonitorInstanceExclusion(
        account: Account,
        user: User,
        monitorInstanceExclusion: MonitorInstanceExclusion,
        monitorExclusion: MonitorInstanceExclusionRequestDto,
        exclusionReason: string,
    ) {
        monitorInstanceExclusion.targetName = monitorExclusion.targetName;
        monitorInstanceExclusion.exclusionReason = exclusionReason;
        monitorInstanceExclusion.exclusionDesignator = user;

        return this.exclusionRepository.save(monitorInstanceExclusion);
    }

    /**
     *
     * @param {Account} account
     * @param {User} user
     * @param {MonitorInstanceDeleteExclusionsDto} dto
     */
    async deleteMonitorExclusions(
        account: Account,
        user: User,
        dto: MonitorInstanceDeleteExclusionsDto,
    ): Promise<MonitorInstanceExclusion[]> {
        let controlTestInstances = await this.controlTestInstanceRepository.find({
            where: {
                testId: dto.monitorInstanceTestId,
            },
            relations: ['products'],
        });

        let controlTestInstance: ControlTestInstance | undefined;
        if (!isEmpty(controlTestInstances)) {
            controlTestInstances = filterControlTests(account, controlTestInstances);

            if (controlTestInstances.length === 1) {
                controlTestInstance = controlTestInstances.shift();
            }
        }

        if (isNil(controlTestInstance)) {
            throw new BadRequestException();
        }

        // NOTE: these would be the exclusions just in the active workspace that the user is in
        const exclusions = await this.exclusionRepository.find({
            where: {
                id: In(dto.exclusionIds),
                controlTestInstance: {
                    id: controlTestInstance.id,
                },
            },
            relations: ['controlTestInstance'],
        });

        if (!isEmpty(exclusions)) {
            for (const exclusion of exclusions) {
                await this.deleteMonitorExclusion(account, user, exclusion);
            }
        }

        this._eventBus.publish(new ExclusionUpdatedEvent(account, user, controlTestInstance));

        // Update the monitoring results OpenSearch since there is an indicator on it for exclusions
        this._eventBus.publish(
            new IndexMonitorResultEvent(account, null, [controlTestInstance.testId], {
                calculateFindingsAndExclusionsCounts: true,
            }),
        );

        return this.exclusionRepository.getExclusionsByControlTestInstance(controlTestInstance.id);
    }

    /**
     * @deprecated use MonitorsOrchestrationService.listAllTestsWithConnectionIssues
     * Get the whole list of tests with connection issues (ERROR status)
     */
    async listAllTestsWithConnectionIssues(
        limit: number,
    ): Promise<[ControlTestInstance[], number]> {
        const productId = await this.workspacesCoreService.getPrimaryProductId();
        const totalTestsWithError =
            await this.controlTestInstanceRepository.getControlTestInstanceCountByResultStatus(
                productId,
                CheckResultStatus.ERROR,
            );
        const testsWithError =
            await this.controlTestInstanceRepository.getControlTestInstanceWithErrorStatusForNotifications(
                limit,
            );
        return [testsWithError, totalTestsWithError];
    }

    /**
     *
     * @param {Account} account
     * @param {User} user
     * @param {MonitorInstanceExclusion} exclusion
     * @returns {Promise<MonitorInstanceExclusion>}
     */
    @CreateEvent<MonitorInstanceExclusion>(
        EventType.MONITOR_EXCLUSION_DELETED,
        EventCategory.MONITOR,
        '${user.firstName} ${user.lastName} deleted exclusion ${result.targetName}',
    )
    private async deleteMonitorExclusion(
        account: Account,
        user: User,
        exclusion: MonitorInstanceExclusion,
    ): Promise<MonitorInstanceExclusion> {
        const deletedAtTimestamp = new Date();

        exclusion.deletedAt = deletedAtTimestamp;

        return this.exclusionRepository.save(exclusion);
    }

    /**
     *
     * @param account
     * @returns
     */
    private async getOtherProducts(account: Account): Promise<Product[]> {
        let products = await this.workspacesCoreService.getAllProducts();

        if (!isNil(products)) {
            products = products.filter(product => product.id != getProductId(account));
        }

        return products;
    }

    /**
     * @deprecated Use MonitorsOrchestrationService.getCompany
     *
     * @param {Account} account
     * @return {Promise<Company>}
     */
    private getCompany(account: Account): Promise<Company> {
        return this.companiesCoreService.getCompanyByAccountId(account.id);
    }

    /**
     * @deprecated Use MonitorsOrchestrationService.getControlTestInstancesByCheckStatusAndCheckResultStatus
     *
     * @param checkStatus
     * @param checkResultStatuses
     * @returns
     */
    private async getControlTestInstancesByCheckStatusAndCheckResultStatus(
        account: Account,
        checkStatus: CheckStatus | null,
        checkResultStatuses: CheckResultStatus[] | null,
        includeDrafts = false,
    ): Promise<ControlTestInstance[]> {
        let controlTestInstances = [];
        let miniRequest = null;

        let page = 1;
        const limit = config.get('pagination.limit');

        do {
            miniRequest = await this.listControlTestInstancesByFrameworks(account, {
                q: null,
                checkResultStatus: null,
                checkResultStatuses,
                checkStatus: checkStatus,
                type: null,
                page: page,
                limit: limit,
                controlOwner: null,
                includeDrafts,
            });

            if (!isNil(miniRequest)) {
                controlTestInstances = controlTestInstances.concat(miniRequest.data);
            }

            page++;
        } while (miniRequest.data.length === limit);

        return controlTestInstances;
    }

    /**
     * @deprecated Use MonitorsCoreService.getControlTestInstancesByCheckTypeAndCheckStatus
     * @param account
     * @param checkType
     * @param checkStatus
     * @returns
     */
    private async getControlTestInstancesByCheckTypeAndCheckStatus(
        account: Account,
        checkType: CheckType,
        checkStatus: CheckStatus,
    ): Promise<ControlTestInstance[]> {
        let controlTestInstances = [];
        let miniRequest = null;

        let page = 1;
        const limit = config.get('pagination.limit');

        do {
            const dto = new MonitorsRequestDto();
            dto.q = null;
            dto.checkResultStatus = null;
            dto.checkResultStatuses = null;
            dto.checkStatus = checkStatus;
            dto.type = checkType;
            dto.page = page;
            dto.limit = limit;

            miniRequest = await this.controlTestInstanceRepository.listControlTestInstances(
                account,
                dto,
            );

            if (!isNil(miniRequest)) {
                controlTestInstances = controlTestInstances.concat(miniRequest.data);
            }

            page++;
        } while (miniRequest.data.length === limit);

        return controlTestInstances;
    }

    /**
     * @deprecated Use MonitorsCoreService.enablePolicyBasedControlTestInstances
     * @returns
     */
    async enablePolicyBasedControlTestInstances(account: Account): Promise<ControlTestInstance[]> {
        /**
         * We are not required to filter by products so pass an empty account
         */
        const controlTestInstances = await this.getControlTestInstancesByCheckTypeAndCheckStatus(
            account,
            CheckType.POLICY,
            CheckStatus.UNUSED,
        );

        if (controlTestInstances.length === 0) {
            return controlTestInstances;
        }

        for (const controlTestInstance of controlTestInstances) {
            controlTestInstance.checkStatus = CheckStatus.ENABLED;
        }

        return this.controlTestInstanceRepository.save(controlTestInstances);
    }

    /**
     * @deprecated Use MonitorsOrchestrationService.updateMonitorInstanceTestCheckResultStatus
     *
     * @param task
     * @param checkResultStatus
     * @param monitorResult
     */
    private async updateMonitorInstanceTestCheckResultStatus<T extends MonitorData>(
        task: Task<any>,
        checkResultStatus: CheckResultStatus,
        monitorResult?: MonitorResult<T>,
    ): Promise<void> {
        const autopilotTaskType = task.getType();

        // returns null if autopilotTaskType is not found on providerTypeAutopilotTaskTypeMap
        let providerType = getProviderTypeByAutopilotTaskType(autopilotTaskType);

        // Overwrite providerType if the task is for EDR otherwise it will be MDM
        if (task?.getConnection()?.providerType === ProviderType.EDR) {
            providerType = task.getConnection().providerType;
        }

        let monitorInstance: MonitorInstance;
        let recipeClientType: string;

        // TODO: look into removing if/else and just use findOneByMonitorTestIdOrFail
        if (autopilotTaskType === AutopilotTaskType.CUSTOM_TEST) {
            monitorInstance = await this.monitorInstanceRepository.findOneByMonitorTestIdOrFail(
                task.getAccount(),
                task.getTestId(),
            );

            /**
             * We need to get all connections later on the process, for CUSTOM TEST we need to get the
             * ProviderType base on the "ClientType" that is defined on the recipe, there will be just one
             * Provider per recipe
             */
            // TODO: Custom Providers, verify if we need to update this or if there is any ripple effect
            recipeClientType =
                monitorInstance?.controlTestInstance?.recipes[0]?.recipe?.providers[0]?.provider?.toUpperCase();

            providerType = ClientType[recipeClientType]
                ? getProviderTypeByClientType(ClientType[recipeClientType])
                : ProviderType.CUSTOM;
        } else {
            monitorInstance = await this.monitorInstanceRepository.findOneByAutopilotTaskTypeOrFail(
                task.getAccount(),
                autopilotTaskType,
            );
        }

        const metadata = getCleanMetadata(monitorInstance);
        let activeConnections: ConnectionEntity[] = [];
        let hasAllConnections = false;

        if (!isNil(providerType)) {
            if (autopilotTaskType === AutopilotTaskType.CUSTOM_TEST) {
                activeConnections = chain(monitorInstance.controlTestInstance.recipes)
                    .flatMap('connections')
                    .value();
                hasAllConnections = some(
                    monitorInstance.controlTestInstance.recipes,
                    'allConnections',
                );
            }

            if (isEmpty(activeConnections) || hasAllConnections) {
                activeConnections = await this.connectionsCoreService.getConnectionsByProviderType(
                    providerType,
                    task.getAccount(),
                );
                if (!isNil(recipeClientType)) {
                    activeConnections = filter(
                        activeConnections,
                        connection => connection.clientType === ClientType[recipeClientType],
                    );
                }
            }

            const company = await this.companiesCoreService.getCompanyByAccountId(
                get(task.getAccount(), 'id', null),
            );

            gcMetadata(metadata, activeConnections, company);
        }

        let finalCheckResultStatus = checkResultStatus;
        const taskConnection = task.getConnection();
        const requestDescriptionType =
            getRequestDescriptionTypeByAutopilotTaskType(autopilotTaskType);

        // If the task requires a connection to be completed,
        // check if the task actually has a connection.
        // If not, skip the metadata step
        const shouldHaveConnection = requestDescriptionType === RequestDescriptionType.CONNECTION;

        const isConnectionFailing = isNil(taskConnection) || !isNil(taskConnection.failedAt);

        const shouldUpdateMetadata = !shouldHaveConnection || !isConnectionFailing;

        if (!isNil(monitorResult) && shouldUpdateMetadata) {
            /**
             * If the requestDescriptionType is not CONNECTION it will overwrite the results even
             * that there are multiples connections
             */
            const key = resolveMonitorInstanceMetadataKey(taskConnection, requestDescriptionType);

            /**
             * Monitor Metadata gets overloaded for this customer
             * This is a hotfix that should be removed after metadata gets fixed
             */
            if (task.getAccount().domain === 'on24.com' && task.getTestId() === 104) {
                this.logger.log(
                    PolloAdapter.acct('Removing pass results', task.getAccount())
                        .setContext(this.constructor.name)
                        .setSubContext(this.updateMonitorInstanceTestCheckResultStatus.name)
                        .setIdentifier(task.getTestId()),
                );
                monitorResult.pass = [];
            }
            metadata.set(key, { checkResultStatus, monitorResult });

            const connections = await this.connectionsCoreService.getActiveAndHealthyConnections();
            clearMDMMetadataWhenEDRIsConnected(metadata, connections, autopilotTaskType);

            monitorInstance.setMetadata(metadata);

            finalCheckResultStatus = resolveMonitorInstanceCheckResultStatus(
                monitorInstance,
                activeConnections,
                task, // remove when logging is completed
            );
        }

        monitorInstance.checkResultStatus = finalCheckResultStatus;

        if (finalCheckResultStatus === CheckResultStatus.PASSED) {
            monitorInstance.expiresAt = checkFrequencyNextExpiration(
                monitorInstance.checkFrequency,
            );
            await this.emitTestPassedWorkflowTrigger(task, monitorInstance);
        } else {
            monitorInstance.expiresAt = new Date();
            if (finalCheckResultStatus === CheckResultStatus.FAILED) {
                await this.emitTestFailedWorkflowTrigger(task, monitorInstance);
            }
        }

        await this.monitorInstanceRepository.save(monitorInstance, {
            reload: false,
        });

        if (task instanceof IacScanValidationTask) {
            // Only the final connection of a task set should update the check result status,
            // otherwise subsequent tasks will continue to run on the same control test instance
            // and indicate a false status
            if (task.getIsFinalTaskInSet()) {
                await this.updateControlTestInstanceCheckResultStatus(
                    monitorInstance.controlTestInstance.id,
                    task,
                );
            }
        } else {
            await this.updateControlTestInstanceCheckResultStatus(
                monitorInstance.controlTestInstance.id,
                task,
            );
        }
    }

    /**
     *
     * @param controlTestInstanceId
     * @param task
     */
    private async updateControlTestInstanceCheckResultStatus(
        controlTestInstanceId: number,
        task: Task<any>,
    ): Promise<void> {
        const controlTestInstance = await this.controlTestInstanceRepository.findOneByOrFail({
            id: controlTestInstanceId,
        });

        const monitorInstance = controlTestInstance.monitorInstances[0];
        const resolvedCheckResultStatus = monitorInstance.checkResultStatus;
        controlTestInstance.checkResultStatus = resolvedCheckResultStatus;
        controlTestInstance.checkStatus = CheckStatus.ENABLED;
        controlTestInstance.lastCheck = new Date();

        let controlTestInstanceHistory = task.getControlTestInstanceHistory();
        controlTestInstanceHistory.checkResultStatus = resolvedCheckResultStatus;
        controlTestInstanceHistory.draft = controlTestInstance.draft;
        controlTestInstanceHistory.controlTestInstance = controlTestInstance;

        await this.controlTestInstanceRepository.save(controlTestInstance, {
            reload: false,
        });

        controlTestInstanceHistory = await this.controlTestInstanceHistoryRepository.save(
            controlTestInstanceHistory,
        );

        task.setControlTestInstanceHistory(controlTestInstanceHistory);
        await this.monitorsCoreService.updateMonitorStats(
            task.getAccount(),
            controlTestInstance.source,
        );

        if (controlTestInstance.runMode === RunMode.AP2) {
            await this.socket.sendMessage(
                task.getAccount().id,
                SocketEvent.MONITOR_TEST_STATUS_UPDATED,
                {
                    testId: controlTestInstance.testId,
                    workspaceId: getProductId(task.getAccount()),
                    checkStatus: CheckStatus[controlTestInstance.checkStatus],
                    checkResultStatus: CheckResultStatus[resolvedCheckResultStatus],
                },
            );
        }
    }

    /**
     * @deprecated Use MonitorsOrchestrationService.notifyTestStateChange
     *
     * Send a message via socket to update a monitor state
     *
     * @param account
     * @param testId
     */
    public async notifyTestStateChange(account: Account, testId: number): Promise<void> {
        const controlTestInstance = await this.getControlTestInstanceByTestId(account, testId);

        const productId = getProductId(account);

        if (!isNil(controlTestInstance)) {
            const { checkStatus, checkResultStatus, updatedAt } = controlTestInstance;
            const responseDto = {
                testId,
                productId,
                checkStatus: CheckStatus[checkStatus],
                checkResultStatus: CheckResultStatus[checkResultStatus],
                updatedAt,
            };

            try {
                await this.socket.sendMessage(
                    account.id,
                    SocketEvent.MONITOR_TEST_RUN,
                    responseDto,
                );
            } catch (error) {}
        }
    }

    /**
     * @deprecated Use MonitorsOrchestrationService.enableMonitorTestInstanceByTestId
     */
    async enableMonitorTestInstanceByTestId(account: Account, testId: number): Promise<void> {
        const controlTestInstance =
            await this.controlTestInstanceRepository.getControlTestInstanceByTestIdWithAllMonitorInstances(
                testId,
                getProductId(account),
                account,
            );

        controlTestInstance.disablingUser = null;
        controlTestInstance.disabledMessage = null;
        controlTestInstance.checkStatus = CheckStatus.ENABLED;
        controlTestInstance.disabledAt = null;

        await this.enableMonitorTestsOnControlTestInstance(account, controlTestInstance);

        // updating the index as it has an indicator if test is enabled/disabled.
        this._eventBus.publish(
            new IndexMonitorResultEvent(account, getProductId(account), [testId]),
        );
    }

    /**
     * @deprecated Use MonitorsCoreService.enableMonitorTestsOnControlTestInstance
     *
     * @param account
     * @param controlTestInstance
     */
    private async enableMonitorTestsOnControlTestInstance(
        account: Account,
        controlTestInstance: ControlTestInstance,
    ): Promise<void> {
        /**
         * This has to be saved FIRST or we get inaccurate monitor stats
         */
        await this.controlTestInstanceRepository.save(controlTestInstance, {
            reload: false,
        });

        if (!isEmpty(controlTestInstance.monitorInstances)) {
            await this.monitorsCoreService.enableMonitorInstances(account, controlTestInstance);
        }
    }

    /**
     * @deprecated Use MonitorsCoreService.enableControlTestInstance
     *
     * @param account
     * @param controlTestInstance
     */
    async enableControlTestInstance(
        account: Account,
        controlTestInstance: ControlTestInstance,
    ): Promise<void> {
        controlTestInstance.disablingUser = null;
        controlTestInstance.disabledMessage = null;
        controlTestInstance.checkStatus = CheckStatus.ENABLED;
        controlTestInstance.disabledAt = null;

        await this.enableMonitorTestsOnControlTestInstance(account, controlTestInstance);
    }

    /**
     * @deprecated Use MonitorsOrchestrationService.enablePolicyControlTests
     */
    async enablePolicyControlTests(
        account: Account,
        controls: Control[],
        policy: Policy,
        replacedPolicies?: Policy[],
    ): Promise<void> {
        const enableMonitorPromises: Promise<void>[] = [];
        for (const control of controls) {
            if (!isEmpty(control.controlTestInstances)) {
                for (const controlTestInstance of control.controlTestInstances) {
                    if (
                        isPolicyControlTestInstance(
                            controlTestInstance.testId,
                            controlTestInstance,
                            policy,
                            replacedPolicies,
                        )
                    ) {
                        controlTestInstance.disablingUser = null;
                        controlTestInstance.disabledMessage = null;
                        controlTestInstance.checkStatus = CheckStatus.ENABLED;
                        controlTestInstance.disabledAt = null;

                        enableMonitorPromises.push(
                            this.enableMonitorTestsOnControlTestInstance(
                                account,
                                controlTestInstance,
                            ),
                        );
                    }
                }
            }
        }

        await Promise.allSettled(enableMonitorPromises);

        await this.enableIndirectPolicyTests(account, policy, replacedPolicies);

        if (await this.policyRepository.areAllPoliciesInactive()) {
            await this.enableCompanyLevelControlTestInstances(account);
        }
    }

    /**
     * @deprecated Use MonitorsOrchestrationService.enableIndirectPolicyTests
     */
    private async enableIndirectPolicyTests(
        account: Account,
        policy: Policy,
        replacedPolicies: Policy[],
    ): Promise<void> {
        /**
         * if a test looks for multiple policies we need to ensure that
         * all those policies are active before enabling such test
         * */
        const indirectTestsData = getIndirectResponsibilities(policy, replacedPolicies);

        for (const indirectTestData of indirectTestsData) {
            const { testId, responsibilities } = indirectTestData;

            if (deprecatedIndirectControlTestIds.includes(testId)) {
                const deprecatedTestExists = await this.checkForDeprecatedTestExistence(testId);
                if (!deprecatedTestExists) {
                    continue;
                }
            }

            const activePolicyResponsibilities = await this.policyResponsibilityRepository.find({
                where: {
                    policyResponsibilityToken: In(responsibilities),
                },
            });

            if (activePolicyResponsibilities.length === responsibilities.length) {
                const products = await this.workspacesCoreService.getAllProducts();

                /**
                 * The complete control/monitor activation/deactivation flow is based on the
                 * assumption that there is only one monitor-per-testId. The MPS project has
                 * broken that assumption. When a policy status is updated we are required to
                 * activate/deactivate ALL control tests and monitors for that policy. In order
                 * to have the smallest impact on the code loop through the products here and
                 * clone the account to make all the existing ByTestId methods data-safe.
                 */

                const { rejected } = await promiseAllSettledInBatches(
                    products,
                    DEFAULT_PROMISE_BATCH_SIZE,
                    async product =>
                        this.enableMonitorTestInstanceByTestId(
                            account.cloneWithProduct(product),
                            testId,
                        ),
                );

                for (const errorReason of rejected.reasons) {
                    this.logger.error(
                        PolloAdapter.acct(
                            'Error in enabling monitor test instance by test id.',
                            account,
                        ).setError(errorReason),
                    );
                }
            }
        }
    }

    /**
     * @deprecated Use MonitorsCoreService.enableCompanyLevelControlTestInstances
     *
     * @param account
     */
    async enableCompanyLevelControlTestInstances(account: Account): Promise<void> {
        const controlTests = await this.controlTestInstanceRepository.find({
            where: {
                testId: In(this.ALL_COMPANY_TEST_IDS),
            },
        });

        for (const controlTest of controlTests) {
            await this.enableControlTestInstance(account, controlTest);
        }
    }

    /**
     * @deprecated Use MonitorsCoreService.findControlTestInstances
     *
     * @param {FindManyOptions} options
     * @returns {Promise<ControlTestInstance[]>}
     */
    async findControlTestInstances(
        options?: FindManyOptions<ControlTestInstance>,
    ): Promise<ControlTestInstance[]> {
        return this.controlTestInstanceRepository.find(options);
    }

    /**
     * @deprecated Use MonitorsCoreService.getTwelveMonthTrail
     * @returns
     */
    private getTwelveMonthTrail(): string[] {
        const now = new Date();

        const currentMonth = now.getMonth() + 1;
        const monthsInAYear = 12;

        const months = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'];

        for (let index = 0; index < monthsInAYear - currentMonth; index++) {
            months.unshift(months.pop());
        }

        return months;
    }

    /**
     *
     * @param account
     * @param testId
     */
    async controlTestInstanceAutopilotStop(
        account: Account,
        testId: number,
    ): Promise<ControlTestInstance> {
        const maxHours = 6;
        const originalControlTestInstance =
            await this.controlTestInstanceRepository.getControlTestInstanceByTestIdWithAllMonitorInstances(
                testId,
                getProductId(account),
                account,
            );
        if (!originalControlTestInstance) {
            throw new NotFoundException(ErrorCode.MONITOR_TEST_NOT_FOUND);
        }
        try {
            if (
                isNotUpdateable(originalControlTestInstance) ||
                originalControlTestInstance.checkStatus !== CheckStatus.TESTING ||
                getDateDiffAsHours(originalControlTestInstance.updatedAt, new Date()) < maxHours
            ) {
                throw new OurConflictException(
                    'This monitor cannot be updated',
                    ErrorCode.CONFLICT_MONITOR_CHECK_STATUS,
                );
            }

            originalControlTestInstance.checkStatus = CheckStatus.ENABLED;
            originalControlTestInstance.updatedAt = new Date();

            return await this.controlTestInstanceRepository.save(originalControlTestInstance);
        } catch (error) {
            const message = `Control test instance autopilot stop error for test id ${testId} and product id ${getProductId(
                account,
            )} ${error}`;
            const polloMessage = PolloMessage.msg(message).setError(error);
            this.logger.error(polloMessage);
            throw error;
        }
    }

    /**
     * @deprecated Use MonitorsCoreService.getMonitorInstanceByIdOrFail
     *
     * @param id
     * @returns
     */
    public getMonitorInstanceByIdOrFail(id: number): Promise<ControlTestInstance> {
        return this.controlTestInstanceRepository.findOneOrFail({
            where: { id },
        });
    }

    /**
     * @deprecated Use MonitorCoreService.getMonitorControlWithRecipeByControlIdOrFail
     *
     * @param id
     * @returns
     */
    public getMonitorControlWithRecipeByControlIdOrFail(id: number): Promise<ControlTestInstance> {
        return this.controlTestInstanceRepository
            .createQueryBuilder('ControlTestInstance')
            .leftJoinAndSelect('ControlTestInstance.recipes', 'AutopilotRecipeInstance')
            .where('ControlTestInstance.id = :id', { id })
            .andWhere('AutopilotRecipeInstance.current = :current', {
                current: 1,
            })
            .getOneOrFail();
    }

    async mapControlTestInstanceWithMostRecentFailure(
        currentFailedTests: ControlTestInstance[],
        account: Account,
    ): Promise<IFailedTest[]> {
        const controlTestInstances = cloneDeep(currentFailedTests ?? []);
        const failedTests: IFailedTest[] = [];
        if (!Array.isArray(controlTestInstances) || isEmpty(controlTestInstances)) {
            return failedTests;
        }

        await promiseAllInBatches(controlTestInstances, 20, async test => {
            const [packet] = await this.controlTestInstanceHistoryRepository.getMostRecentFailure(
                test?.id,
                account,
            );

            failedTests.push({ ...test, firstMostRecentFailure: packet } as IFailedTest);
        });
        return failedTests;
    }

    async getTestTrends(account: Account): Promise<TestTrends> {
        const product = account.getCurrentProduct();
        const nDaysAgoDate = moment().subtract(7, 'days').startOf('day').toDate();

        const [currentFailedTests, daysAgoFailedTestsHistory] = await Promise.all([
            this.controlTestInstanceRepository.getFailedControlTestInstances(product),
            this.controlTestInstanceHistoryRepository.getFailedControlTestDaysAgo(
                product,
                nDaysAgoDate,
            ),
        ]);

        const failedTestsWithFailure: IFailedTest[] =
            await this.mapControlTestInstanceWithMostRecentFailure(currentFailedTests, account);

        const calculateTrend = (current, past) =>
            past === 0 ? (current > 0 ? 100 : 0) : (current / past - 1) * 100;

        const daysAgoFailedTests = chain(daysAgoFailedTestsHistory)
            .flatMap(history =>
                chain(history.controlTestInstance.monitorInstances)
                    .flatMap('monitorInstanceCheckTypes')
                    .map('checkType')
                    .map((category: CheckType) => ({
                        ...history.controlTestInstance,
                        failedTest: history,
                        category,
                    }))
                    .value(),
            )
            .groupBy('category')
            .value();

        const failsByCategory = chain(failedTestsWithFailure)
            .flatMap(test =>
                chain(test.monitorInstances)
                    .flatMap('monitorInstanceCheckTypes')
                    .map('checkType')
                    .map((category: CheckType) => ({ ...test, category }))
                    .value(),
            )
            .groupBy('category')
            .tap(existingGroups => {
                chain(CheckType)
                    .filter(Number.isInteger)
                    .map(String)
                    .difference(keys(existingGroups))
                    .each(key => (existingGroups[key] = []))
                    .value();
            })
            .omit(CheckType.HRIS)
            .map((failedTests, category) => {
                const tests = chain(failedTests)
                    .map(({ testId, name, lastCheck, firstMostRecentFailure }) => ({
                        id: testId,
                        name,
                        firstFailure: firstMostRecentFailure?.created_at ?? lastCheck,
                        withinTimeFrame: moment(
                            firstMostRecentFailure?.created_at ?? lastCheck,
                        ).isSameOrAfter(nDaysAgoDate),
                    }))
                    .orderBy(['firstFailure', 'name'], ['desc', 'asc'])
                    .value();

                return {
                    category: parseInt(category) as CheckType,
                    failing: tests.length,
                    trend: calculateTrend(tests.length, size(daysAgoFailedTests[category])),
                    withinTimeFrame: some(tests, 'withinTimeFrame'),
                    tests,
                };
            })
            .orderBy(
                ['withinTimeFrame', 'trend', 'failing', ({ category }) => CheckType[category]],
                ['desc', 'desc', 'desc', 'asc'],
            )
            .value();

        return {
            total: {
                failing: currentFailedTests.length,
                trend: calculateTrend(currentFailedTests.length, daysAgoFailedTestsHistory.length),
                withinTimeFrame: some(failsByCategory, 'withinTimeFrame'),
            },
            failsByCategory,
        };
    }

    async getTestTrendsOverview(
        account: Account,
        workspaceId: number,
        dayRange = MonitoringTestTrendsConstants.DEFAULT_TEST_TRENDS_TIMEFRAME_DAYS,
    ): Promise<TestTrendsOverview> {
        let acctForWorkspace = account;
        let product = account.getCurrentProduct();

        if (product?.id !== workspaceId) {
            const productForId = await this.workspacesBaseService.getProductById(workspaceId);

            if (isNil(productForId)) {
                throw new NotFoundException(ErrorCode.PRODUCT_NOT_FOUND);
            }

            acctForWorkspace = account.cloneWithProduct(productForId);
            product = acctForWorkspace.getCurrentProduct();
        }

        const fromDate = moment().subtract(dayRange, 'days').startOf('day').toDate();
        return this.calculateTestTrendsOverview(acctForWorkspace, product, fromDate);
    }

    private async calculateTestTrendsOverview(
        account: Account,
        product: Product,
        fromDate: Date,
    ): Promise<TestTrendsOverview> {
        this.log('Will calculate TestTrendsOverview', account, {
            workspaceId: product.id,
            fromDate,
        });
        const testTrendsOverviewBenchmark = new Benchmark();

        const [failedTests, passedCount, errorCount, avgTimeToRemediation] = await Promise.all([
            this.controlTestInstanceRepository.getFailedControlTestInstances(product),
            this.controlTestInstanceRepository.getPassedControlTestInstancesCount(product),
            this.controlTestInstanceRepository.getErrorControlTestInstancesCount(product),
            this.controlTestInstanceHistoryRepository.getAverageTimeToRemediationMs(
                account,
                product.id,
                fromDate,
                MonitoringTestTrendsConstants.DEFAULT_TEST_TRENDS_MAX_LOOK_BEHIND_DAYS,
                [TestSource.ACORN], // Exclude ACORN tests from the calculation
            ),
        ]);

        const result: TestTrendsOverview = {
            window: {
                from: fromDate,
                to: new Date(),
            },
            counts: {
                passing: passedCount,
                failed: failedTests.length,
                error: errorCount,
            },
            avgTimeToRemediationMs: avgTimeToRemediation.avgTimeToRemediationMs,
            includedRecoveries: avgTimeToRemediation.failuresResolved,
        };

        this.log(
            'Calculated TestTrendsOverview',
            account,
            { workspaceId: product.id, fromDate },
            testTrendsOverviewBenchmark,
        );

        return result;
    }

    async getMonitorsForCsvExport(
        account: Account,
        testId: number,
        providerType: ProviderType,
    ): Promise<CsvDataSetType> {
        const activeConnection =
            await this.connectionsCoreService.getConnectionByProviderType(providerType);

        if (isNil(activeConnection)) {
            throw new NotFoundException(ErrorCode.MONITOR_TEST_NOT_FOUND);
        }

        const monitorsInstance = await this.getControlTestInstanceByTestId(account, testId);

        const metadata = monitorsInstance.monitorInstances[0].getMetadata();

        let failedMonitors = metadata.entries().next().value[1].monitorResult.fail;

        if (
            !isNil(activeConnection) &&
            providerType == ProviderType.CSPM &&
            !isEmpty(monitorsInstance.monitorInstanceExclusions)
        ) {
            const exclusions = monitorsInstance.monitorInstanceExclusions;
            failedMonitors = failedMonitors.filter(
                failItem => !exclusions.some(x => x.targetId == failItem.id),
            );
        }

        return {
            data: failedMonitors,
            filename: config.get('reports.failedTestFile'),
        };
    }

    /**
     * @deprecated Use MonitorsCoreService.getInfrastructureMonitorHeadersForCsv
     */
    private getInfrastructureMonitorHeadersForCsv(metadata: MonitorInstanceMetadataItem[]): {
        organizationalUnitId: boolean;
        accountId: boolean;
        accountName: boolean;
        region: boolean;
        arn?: boolean;
    } {
        const headers: {
            organizationalUnitId: boolean;
            accountId: boolean;
            accountName: boolean;
            region: boolean;
            arn?: boolean;
        } = {
            organizationalUnitId: false,
            accountId: false,
            accountName: false,
            region: false,
        };

        for (const { monitorResult } of metadata) {
            if (!monitorResult || !Array.isArray(monitorResult.fail)) break;

            for (const item of monitorResult.fail) {
                if (item.organizationalUnitId && !headers.organizationalUnitId) {
                    headers.organizationalUnitId = true;
                }

                if (item.accountId && !headers.accountId) {
                    headers.accountId = true;
                }

                if (item.accountName && !headers.accountName) {
                    headers.accountName = true;
                }

                if (item.region && !headers.region) {
                    headers.region = true;
                }

                if (Object.values(headers).every(Boolean)) break;
            }

            if (Object.values(headers).every(Boolean)) break;
        }

        return headers;
    }

    /**
     * @deprecated Use MonitorsCoreService.isValidCheckType
     */
    private isValidCheckType(checkType: string): boolean {
        const isValidKey = Object.keys(CheckType).includes(checkType);
        const isValidValue = Object.values(CheckType).includes(checkType as any);
        return isValidKey || isValidValue;
    }

    /**
     * @deprecated Use MonitorsCoreService.getInfrastructureMonitorsForCsvExport
     * Get failing or excluded results of a monitorin test for a
     * infrastructure csv report.
     * @param account
     * @param testId
     * @param type
     * @returns CsvDataSetType
     */
    async getInfrastructureMonitorsForCsvExport(
        account: Account,
        testId: number,
        type: 'included' | 'excluded',
        checkType?: string | null,
    ): Promise<CsvDataSetType> {
        if (!isNil(checkType) && !this.isValidCheckType(checkType)) {
            throw new CommonBadRequestException(
                `Invalid checkType: ${checkType}`,
                ErrorCode.CONTROL_TEST_INSTANCE_CHECK_TYPE_INVALID,
            );
        }

        const controlTestInstanceExclusions =
            await this.controlTestInstanceRepository.getControlTestInstanceExclusionsByTestId(
                testId,
                getProductId(account),
            );

        const monitorExclusions = get(
            controlTestInstanceExclusions,
            'monitorInstanceExclusions',
            [],
        );

        const exclusions = monitorExclusions.map(({ connection, targetName, targetId }) => ({
            clientType: connection.clientType,
            clientId: connection.clientId,
            name: targetName,
            targetId,
            headers: {},
            checkType,
        }));

        if (type === 'excluded') {
            if (controlTestInstanceExclusions.source === TestSource.ACORN) {
                const findingExclusions =
                    await this.findingExclusionRepository.getFindingExclusionsByTestId(testId);

                return {
                    data: findingExclusions.map(exclusion => ({
                        ...exclusion,
                        isComplianceAsCodeTest: true,
                        headers: {},
                    })),
                    filename: '',
                };
            }

            return {
                data: exclusions,
                filename: '',
            };
        }

        /**
         * we need to extract the error message from the event history only for
         * failing results
         */
        const controlTestinstanceHistory = await this.getMostRecenControlTestHistorytByTestId(
            getProductId(account),
            testId,
        );

        const errorMessage =
            controlTestinstanceHistory?.events?.[0]?.getMetadata()?.response?.message ?? '';

        /**
         * we need to filter failing results removing all the exclusions from
         * the array.
         */
        const controlTestInstance =
            await this.controlTestInstanceRepository.getControlTestInstanceByTestIdWithAllMonitorInstances(
                testId,
                getProductId(account),
                account,
            );

        const monitorInstance = controlTestInstance.monitorInstances[0];

        const monitorInstanceMetadata = monitorInstance.getMetadata().values();
        const metadataArray = Array.from(monitorInstanceMetadata);

        /**
         * get failing results from metadata
         */
        const failingItems: any[] = [];

        const headers = this.getInfrastructureMonitorHeadersForCsv(metadataArray);

        /**
         * collect all fail items for each item inside of metadata
         */
        for (const { monitorResult } of metadataArray) {
            headers.arn ||= monitorResult.fail.some(failItem => failItem.resourceArn);

            for (const failItem of monitorResult.fail) {
                failingItems.push({
                    clientType: monitorResult.clientType,
                    clientId: monitorResult.clientId,
                    clientAlias: monitorResult.clientAlias,
                    ...failItem,
                    error: errorMessage,
                    url: monitorInstance.url,
                    resourceArn: failItem.resourceArn,

                    // conditional rendering columns
                    headers,
                    checkType,
                });
            }
        }

        /**
         * remove items in failinResults that match exclusions and assign to data
         */
        const results = failingItems.filter(failingItem => {
            return !exclusions.some(exclusionItem => {
                /**
                 * if the failing item has a findingTargetId, we want to use that as the targetId
                 */
                if (!isEmpty(failingItem.findingTargetId)) {
                    return (
                        exclusionItem.targetId === failingItem.findingTargetId ||
                        exclusionItem.targetId === failingItem.id
                    );
                }
                /**
                 * we want only these items that are not present in exclusions
                 */
                return exclusionItem.targetId === failingItem.id;
            });
        });

        /**
         * filename is empty since we create the name dynamically in the frontend
         */
        return {
            data: results,
            filename: '',
        };
    }

    /**
     * @deprecated Use MonitorsCoreService.getMostRecenControlTestHistorytByTestId
     */
    private async getMostRecenControlTestHistorytByTestId(workspaceId: number, testId: number) {
        return this.controlTestInstanceHistoryRepository.getMostRecentByTestId(workspaceId, testId);
    }

    @CacheBusterWithPrefix<ControlTestInstanceDetails>({
        stores: [Caches.FIND_CONTROL_DETAILS_BY_ID],
    })
    async putMonitorDetails(
        account: Account,
        user: User,
        testId: number,
        requestDto: MonitorsDetailsRequestDto,
    ): Promise<ControlTestInstanceDetails> {
        const controlTestInstance =
            await this.controlTestInstanceRepository.getControlTestInstanceByTestIdWithAllMonitorInstances(
                testId,
                getProductId(account),
                account,
            );
        const monitorInstance = controlTestInstance.monitorInstances[0];

        const controlTestTemplate = await this.controlTestTemplateRepository.findOneBy({
            id: controlTestInstance.controlTestTemplateId,
        });
        const monitorTemplate = await this.monitorTemplateRepository.findOneBy({
            id: monitorInstance.monitorTemplateId,
        });

        const canRevert =
            controlTestInstance.isCustom() ||
            controlTestTemplate.name !== requestDto.name ||
            monitorTemplate.evidenceCollectionDescription !== requestDto.description;

        controlTestInstance.name = requestDto.name;
        monitorInstance.evidenceCollectionDescription = requestDto.description;

        await this.monitorInstanceRepository.save(monitorInstance);
        const controlTestInstanceUpdated =
            await this.controlTestInstanceRepository.save(controlTestInstance);

        if (controlTestInstance.isCustom()) {
            const workspace = await this.workspacesCoreService.getProductById(
                getProductId(account),
            );

            this._eventBus.publish(
                new ControlTestInstanceNameAndDescriptionUpdatedEvent(
                    account,
                    user,
                    controlTestInstance,
                    getProductId(account),
                    workspace?.name,
                ),
            );
        }

        this._eventBus.publish(
            new IndexMonitorResultEvent(
                account,
                getProductId(account),
                [controlTestInstance.testId],
                {
                    calculateFindingsAndExclusionsCounts: false,
                },
            ),
        );

        return {
            ...controlTestInstanceUpdated,
            canRevert,
        } as ControlTestInstanceDetails;
    }

    async getAllDraftFromTestId(testId: number, productId: number): Promise<ControlTestInstance[]> {
        return this.controlTestInstanceRepository.getDraftControlTestInstances(testId, productId);
    }

    async updateNameAndDescription(controlTestInstance: ControlTestInstance): Promise<void> {
        await this.controlTestInstanceRepository.save(controlTestInstance);
        await this.monitorInstanceRepository.save(first(controlTestInstance.monitorInstances));
    }

    @CacheBusterWithPrefix<ControlTestInstanceDetails>({
        stores: [Caches.FIND_CONTROL_DETAILS_BY_ID],
    })
    async putRevertMonitorDetails(
        account: Account,
        testId: number,
    ): Promise<ControlTestInstanceDetails> {
        const controlTestInstance =
            await this.controlTestInstanceRepository.getControlTestInstanceByTestIdWithAllMonitorInstances(
                testId,
                getProductId(account),
                account,
            );
        const controlTestTemplate = await this.controlTestTemplateRepository.findOneBy({
            id: controlTestInstance.controlTestTemplateId,
        });
        const monitorInstance = controlTestInstance.monitorInstances[0];
        const monitorTemplate = await this.monitorTemplateRepository.findOneBy({
            id: monitorInstance.monitorTemplateId,
        });
        controlTestInstance.name = controlTestTemplate.name;
        monitorInstance.evidenceCollectionDescription =
            monitorTemplate.evidenceCollectionDescription;

        await this.monitorInstanceRepository.save(monitorInstance);
        const controlTestInstanceUpdated =
            await this.controlTestInstanceRepository.save(controlTestInstance);

        return {
            ...controlTestInstanceUpdated,
            canRevert: false,
        } as ControlTestInstanceDetails;
    }

    /**
     * @deprecated Use MonitorsCoreService.checkForDeprecatedTestExistence
     *
     * @param testId
     */
    async checkForDeprecatedTestExistence(testId: number): Promise<boolean> {
        const controlTestInstance = await this.controlTestInstanceRepository.findOne({
            where: {
                testId,
            },
        });

        return !isNil(controlTestInstance);
    }

    /**
     * JUST USED FOR POSTMAN LOCAL TEST
     * DON'T USE OUTSIDE OF THAT CONTEXT
     * @deprecated()
     */
    async setMonitorControlRunMode(
        account: Account,
        updateMonitorControlRequestDTO: MonitorControlTesRunModeRequestDto,
        testId: number,
    ): Promise<ControlTestInstance> {
        const controlTestInstance = await this.getControlTestInstanceByTestId(account, testId);
        if (isNil(controlTestInstance)) {
            throw new NotFoundException(ErrorCode.ENTITY_NOT_FOUND);
        }
        await this.controlTestInstanceRepository.update(controlTestInstance.id, {
            runMode: updateMonitorControlRequestDTO.runMode,
        });

        return this.getControlTestInstanceByTestId(account, testId);
    }

    async generateTestSummaryCsv(account: Account, testId: number) {
        const { events, controlTestInstance } =
            await this.controlTestInstanceHistoryRepository.findOneOrFail({
                where: {
                    controlTestInstance: {
                        testId,
                    },
                    checkResultStatus: CheckResultStatus.FAILED,
                },
                relations: {
                    events: true,
                    controlTestInstance: true,
                },
                order: {
                    createdAt: 'DESC',
                },
            });

        if (await this.eventsService.isReadingFromS3(account)) {
            for (const event of events) {
                if (!isNil(event.fileKey)) {
                    await this.eventsService.setEventMetadataFromRemote(account, event);
                }
            }
        }

        const eventIds = events
            .filter(
                event =>
                    JSON.parse(event.metadata)?.response?.status ===
                        AutopilotTaskResponseStatus.FAILED &&
                    !isEmpty(JSON.parse(event.metadata).response?.data?.results?.fail),
            )
            .map(e => e.id);

        const aiExecutionGroups = await this.aiExecutionGroupService.getByFeatureIds(eventIds, {
            processFeature: ProcessFeature.EVENT_TEST_FAILURE,
            processType: ProcessType.SUMMARY,
        });

        const errorEventIds = aiExecutionGroups
            .filter(aeg =>
                aeg.executions.some(execution => execution.status === ExecutionStatus.ERROR),
            )
            .map(aeg => aeg.featureId);

        if (eventIds.length !== aiExecutionGroups.length || !isEmpty(errorEventIds)) {
            let missingEventIds = eventIds;

            if (!isEmpty(aiExecutionGroups)) {
                const featureIds = aiExecutionGroups.map(aeg => aeg.featureId);
                missingEventIds = eventIds.filter(
                    eventId => !featureIds.some(featureId => featureId === eventId),
                );
            }

            const eventIdsToExecute = [...missingEventIds, ...errorEventIds];

            for (const eventId of eventIdsToExecute) {
                const aiExecutionEventRequestDto = new AiExecutionGroupRequestDto();
                aiExecutionEventRequestDto.featureId = eventId;
                aiExecutionEventRequestDto.processFeature = ProcessFeature.EVENT_TEST_FAILURE;
                aiExecutionEventRequestDto.processType = ProcessType.SUMMARY;

                this._eventBus.publish(
                    new AiExecutionGroupEvent(account, aiExecutionEventRequestDto),
                );
            }

            return {};
        }

        const hasOnGoingAiExecution = aiExecutionGroups.some(aeg =>
            aeg.executions.some(
                e =>
                    e.status === ExecutionStatus.INPROGRESS || e.status === ExecutionStatus.PENDING,
            ),
        );

        if (hasOnGoingAiExecution) {
            throw new UnprocessableEntityException(
                'Error while generating summary CSV. There is at least one AI execution pending or in-progress',
            );
        }

        const csvData: ControlTestInstanceEventSummaryCsvData[] = [];

        for (const aiExecutionGroup of aiExecutionGroups) {
            const completedExecutions = aiExecutionGroup.executions.filter(
                execution => execution.status === ExecutionStatus.COMPLETED,
            );
            for (const execution of completedExecutions) {
                const { connection } = events.find(e => e.id === aiExecutionGroup.featureId) ?? {};
                const summary: TestFailureSummaryMetadata = JSON.parse(execution.response.data);
                const { resource, resourceIds, cause } = summary;

                for (const resourceId of resourceIds) {
                    const monitorInstance = controlTestInstance.monitorInstances[0];
                    const resourceArn = extractResourceArnFromFailItem(resourceId, monitorInstance);
                    csvData.push({
                        provider: isNil(connection)
                            ? 'Autopilot'
                            : ClientType[connection.clientType],
                        accountId: connection?.clientAlias ?? connection?.clientId ?? '-',
                        resourceId,
                        resourceName: resource,
                        summary: cause.explanation,
                        resourceArn,
                    });
                }
            }
        }

        return {
            data: csvData,
            filename: format(
                config.get('reports.controlTestInstanceEventSummaryFileName'),
                controlTestInstance.name,
            ),
        };
    }

    /**
     * @deprecated Use MonitorsOrchestrationService.isConnectionAllowedToAutoRunTests
     *
     * Check if the connection is allowed to run tests. If not throws a BadRequestException.
     *
     * @param account
     * @param connectionId
     * @returns
     */
    async isConnectionAllowedToAutoRunTests(
        account: Account,
        connectionId: number,
    ): Promise<boolean> {
        let connectionToCheck;

        try {
            connectionToCheck = await this.connectionsCoreService.getConnectionById(connectionId);
        } catch (error) {
            this.log('Error querying connection data', account, {
                connectionId,
            });
            this.error(error, account, {
                connectionId,
            });

            return false;
        }

        if (!isConnectionAllowedToAutoRunTests(connectionToCheck)) {
            this.log(`Connection NOT allowed to run tests`, account, {
                connectionId,
            });
            return false;
        }

        this.log(`Connection allowed to run tests`, account, {
            connectionId,
        });
        return true;
    }

    getConnectionAutoRunTestsFeatureFlag(
        account: Account,
        providerType: ProviderType,
    ): Promise<boolean> {
        const flagConfig = {
            category: FeatureFlagCategory.NONE,
            name: FeatureFlag.RELEASE_AWS_AUTO_RUN_TESTS,
            defaultValue: false,
        };
        switch (providerType) {
            case ProviderType.INFRASTRUCTURE: {
                flagConfig.name = FeatureFlag.RELEASE_INFRA_AUTO_RUN_TESTS;
                break;
            }
            case ProviderType.IDENTITY: {
                flagConfig.name = FeatureFlag.RELEASE_IDENTITY_AUTO_RUN_TESTS;
                break;
            }
            case ProviderType.VERSION_CONTROL: {
                flagConfig.name = FeatureFlag.RELEASE_VERSIONING_AUTO_RUN_TESTS;
                break;
            }
            case ProviderType.TICKETING: {
                flagConfig.name = FeatureFlag.RELEASE_TICKETING_AUTO_RUN_TESTS;
                break;
            }
            default:
                return Promise.resolve(false);
        }

        this.log('Evaluating auto run tests FF', account, {
            providerType,
            flagConfig,
        });

        return this.featureFlagService.evaluateAs(flagConfig, account);
    }

    /**
     * @deprecated Use MonitorsOrchestrationService.validateConnectionRunTestsFeatureFlag
     *
     * Query FF related to auto run tests feature and resolves if the connection provider type has a FF that allows the execution.
     *
     * @param account
     * @param connectionId
     * @returns
     */
    async validateConnectionRunTestsFeatureFlag(
        account: Account,
        connectionId: number,
    ): Promise<boolean> {
        try {
            const connection = await this.connectionsCoreService.getConnectionById(connectionId);

            const { providerType, clientType } = connection;

            if (clientType === ClientType.AWS) {
                // Handle AWS FF case independently
                const flagConfig = {
                    category: FeatureFlagCategory.NONE,
                    name: FeatureFlag.RELEASE_AWS_AUTO_RUN_TESTS,
                    defaultValue: false,
                };

                const isAwsAutoRunEnabled = await this.featureFlagService.evaluateAs(
                    flagConfig,
                    account,
                );

                if (!isAwsAutoRunEnabled) {
                    this.log('AWS auto run FF is NOT ENABLED', account, {
                        connectionId,
                    });

                    return false;
                }

                return true;
            }

            const providerAutoRunTestFF = await this.getConnectionAutoRunTestsFeatureFlag(
                account,
                providerType,
            );

            if (!providerAutoRunTestFF) {
                this.log('Release Connection Test Run Flag is NOT enabled', account, {
                    connectionId,
                });
                return providerAutoRunTestFF;
            }

            return providerAutoRunTestFF;
        } catch (error) {
            this.error(error, account, {
                connectionId,
            });

            if (error instanceof EntityNotFoundError) {
                throw new NotFoundException(ErrorCode.ENTITY_NOT_FOUND);
            }

            return false;
        }
    }

    async emitTestPassedWorkflowTrigger(
        task: Task<any>,
        monitorInstance: MonitorInstance,
    ): Promise<void> {
        const account = task.getAccount();
        try {
            const productId = account.getCurrentProduct().id;
            if (!isNil(productId)) {
                const { testId, id } = monitorInstance?.controlTestInstance;
                const controls = await this.controlRepository.getControlsByTestId(
                    productId,
                    testId,
                );
                const user = await this.getSupportUser();
                if (!isNil(testId) && !isNil(controls) && !isNil(user)) {
                    controls.forEach(control => {
                        // Trigger for Custom Workflow

                        this._eventBus.publish(
                            new ControlMappedToTestPassingEvent(
                                account,
                                user,
                                [id],
                                control.id,
                                productId,
                            ),
                        );
                    });
                }
            } else {
                this.logger.error(
                    PolloAdapter.acct('Unable to get workspace Id ', account)
                        .setContext(this.constructor.name)
                        .setSubContext(this.emitTestPassedWorkflowTrigger.name),
                );
            }
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(
                    `Something went wrong emitting passing test workflow: ${error.message}`,
                    account,
                )
                    .setContext(this.constructor.name)
                    .setSubContext(this.emitTestPassedWorkflowTrigger.name)
                    .setError(error),
            );
        }
    }

    async emitTestFailedWorkflowTrigger(
        task: Task<any>,
        monitorInstance: MonitorInstance,
    ): Promise<void> {
        const account = task.getAccount();
        try {
            const productId = account.getCurrentProduct().id;
            if (!isNil(productId)) {
                const { testId, id } = monitorInstance?.controlTestInstance;
                const controls = await this.controlRepository.getControlsByTestId(
                    productId,
                    testId,
                );
                const user = await this.getSupportUser();
                if (!isNil(testId) && !isNil(controls) && !isNil(user)) {
                    controls.forEach(control => {
                        // Trigger for Custom Workflow

                        this._eventBus.publish(
                            new ControlMappedToTestFailingEvent(
                                account,
                                user,
                                [id],
                                control.id,
                                productId,
                            ),
                        );
                    });
                }
            } else {
                this.logger.error(
                    PolloAdapter.acct('Unable to get workspace Id ', account)
                        .setContext(this.constructor.name)
                        .setSubContext(this.emitTestFailedWorkflowTrigger.name),
                );
            }
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(
                    `Something went wrong emitting failing test workflow: ${error.message}`,
                    account,
                )
                    .setContext(this.constructor.name)
                    .setSubContext(this.emitTestFailedWorkflowTrigger.name)
                    .setError(error),
            );
        }
    }

    /**
     * @deprecated Use MonitorsCoreService.getSupportUser
     *
     * @returns
     */
    async getSupportUser(): Promise<User | null> {
        return this.userRepository.findOneBy({
            email: config.get('api.supportUser.email'),
        });
    }

    private async getMonitorInstanceAdditionalProperties(
        account: Account,
        controlTestInstance: ControlTestInstance,
    ): Promise<MonitoringInstanceAdditionalProperty[]> {
        const activeConnections =
            await this.connectionsCoreService.getActiveConnectionsByProductId(account);
        const { monitorInstances, testId } = controlTestInstance;
        const monitorInstanceCheckTypes = this.getUniqueActiveCheckTypes(monitorInstances);
        let additionalProperties: MonitoringInstanceAdditionalProperty[] = [];

        if (controlTestInstance.source !== TestSource.ACORN) {
            additionalProperties = this.getAdditionalPropertiesByCheckTypes(
                activeConnections,
                testId,
                monitorInstanceCheckTypes,
            );
        } else {
            additionalProperties = this.getAdditionalPropertiesForCodebase();
        }
        return additionalProperties;
    }

    private getUniqueActiveCheckTypes(monitorInstances: MonitorInstance[]) {
        const checkTypeSet = new Set<number>();

        for (const instance of monitorInstances) {
            for (const type of instance.monitorInstanceCheckTypes) {
                if (type.deletedAt === null) {
                    checkTypeSet.add(type.checkType);
                }
            }
        }

        return Array.from(checkTypeSet);
    }

    private getAdditionalPropertiesByCheckType(
        connections: ConnectionEntity[],
        testId: number,
        checkType: CheckType,
    ): MonitoringInstanceAdditionalProperty[] {
        switch (checkType) {
            case CheckType.AGENT:
                return this.getAdditionalPropertiesForAgent(connections, testId);
            case CheckType.IDENTITY:
                return [
                    {
                        id: 'TARGET_NAME',
                        name: 'Applicable personnel',
                        accessorKey: 'displayName',
                        enableSorting: true,
                        enableFiltering: true,
                        hide: false,
                    },
                    {
                        id: 'EMAIL',
                        name: 'Email',
                        accessorKey: 'email',
                        enableSorting: false,
                        enableFiltering: false,
                        hide: false,
                    },
                    {
                        id: 'CONNECTION',
                        name: 'Connection',
                        accessorKey: 'connection',
                        enableSorting: false,
                        enableFiltering: false,
                        hide: false,
                    },
                    {
                        id: 'CONNECTION_CLIENT_ALIAS',
                        name: 'Connection ID / alias',
                        accessorKey: 'connectionClientAlias',
                        enableSorting: false,
                        enableFiltering: false,
                        hide: false,
                    },
                ];
            case CheckType.IN_DRATA:
                return [
                    {
                        id: 'TARGET_NAME',
                        name: 'Applicable personnel',
                        accessorKey: 'displayName',
                        enableSorting: true,
                        enableFiltering: true,
                        hide: false,
                    },
                    {
                        id: 'EMAIL',
                        name: 'Email',
                        accessorKey: 'email',
                        enableSorting: false,
                        enableFiltering: false,
                        hide: false,
                    },
                    {
                        id: 'CONNECTION',
                        name: 'Connection',
                        accessorKey: 'connection',
                        enableSorting: false,
                        enableFiltering: false,
                        hide: true,
                    },
                    {
                        id: 'CONNECTION_CLIENT_ALIAS',
                        name: 'Connection ID / alias',
                        accessorKey: 'connectionClientAlias',
                        enableSorting: false,
                        enableFiltering: false,
                        hide: true,
                    },
                ];
            case CheckType.INFRASTRUCTURE:
                return [
                    {
                        id: 'REGION',
                        name: 'Region',
                        accessorKey: 'region',
                        enableSorting: false,
                        enableFiltering: false,
                        hide: false,
                    },
                    {
                        id: 'CONNECTION',
                        name: 'Connection',
                        accessorKey: 'connection',
                        enableSorting: false,
                        enableFiltering: false,
                        hide: false,
                    },
                    {
                        id: 'CONNECTION_CLIENT_ALIAS',
                        name: 'Connection ID / alias',
                        accessorKey: 'connectionClientAlias',
                        enableSorting: true,
                        enableFiltering: true,
                        hide: false,
                    },
                    {
                        id: 'CONNECTION_PROVIDER_ACCOUNT_NAME_ID',
                        name: 'Account Name / ID',
                        accessorKey: 'connectionProviderAccountName',
                        enableSorting: false,
                        enableFiltering: false,
                        hide: false,
                    },
                    {
                        id: 'CONNECTION_PROVIDER_GROUP',
                        name: 'Group',
                        accessorKey: 'connectionProviderGroup',
                        enableSorting: false,
                        enableFiltering: false,
                        hide: false,
                    },
                ];
            case CheckType.POLICY:
                return [
                    {
                        id: 'EMAIL',
                        name: 'Email',
                        accessorKey: 'email',
                        enableSorting: false,
                        enableFiltering: false,
                        hide: false,
                    },
                    {
                        id: 'CONNECTION',
                        name: 'Connection',
                        accessorKey: 'connection',
                        enableSorting: false,
                        enableFiltering: false,
                        hide: true,
                    },
                    {
                        id: 'CONNECTION_CLIENT_ALIAS',
                        name: 'Connection ID / alias',
                        accessorKey: 'connectionClientAlias',
                        enableSorting: false,
                        enableFiltering: false,
                        hide: true,
                    },
                ];
            case CheckType.TICKETING:
                return [
                    {
                        id: 'TARGET_NAME',
                        name: 'Ticket title',
                        accessorKey: 'ticketTitle',
                        enableSorting: true,
                        enableFiltering: true,
                        hide: false,
                    },
                    {
                        id: 'TICKET_URL',
                        name: 'Url',
                        accessorKey: 'ticketUrl',
                        enableSorting: false,
                        enableFiltering: false,
                        hide: false,
                    },
                    {
                        id: 'CONNECTION',
                        name: 'Connection',
                        accessorKey: 'connection',
                        enableSorting: false,
                        enableFiltering: false,
                        hide: false,
                    },
                    {
                        id: 'CONNECTION_CLIENT_ALIAS',
                        name: 'Connection ID / alias',
                        accessorKey: 'connectionClientAlias',
                        enableSorting: true,
                        enableFiltering: true,
                        hide: false,
                    },
                ];
            case CheckType.VERSION_CONTROL:
                return [
                    {
                        id: 'TARGET_NAME',
                        name: 'Name',
                        accessorKey: 'displayName',
                        enableSorting: true,
                        enableFiltering: true,
                        hide: false,
                    },
                    {
                        id: 'CONNECTION',
                        name: 'Connection',
                        accessorKey: 'connection',
                        enableSorting: false,
                        enableFiltering: false,
                        hide: false,
                    },
                    {
                        id: 'CONNECTION_CLIENT_ALIAS',
                        name: 'Connection ID / alias',
                        accessorKey: 'connectionClientAlias',
                        enableSorting: true,
                        enableFiltering: true,
                        hide: false,
                    },
                ];
            case CheckType.OBSERVABILITY:
                return this.getAdditionalPropertiesForObservability(connections, testId);
            default:
                return [];
        }
    }

    private getAdditionalPropertiesByCheckTypes(
        connections: ConnectionEntity[],
        testId: number,
        checkTypes: CheckType[],
    ): MonitoringInstanceAdditionalProperty[] {
        const propertyMap = new Map<string, MonitoringInstanceAdditionalProperty>();

        for (const checkType of checkTypes) {
            const properties = this.getAdditionalPropertiesByCheckType(
                connections,
                testId,
                checkType,
            );

            for (const property of properties) {
                propertyMap.set(property.id, property); // Use `id` as the unique key
            }
        }

        return Array.from(propertyMap.values());
    }

    private getAdditionalPropertiesForCodebase(): MonitoringInstanceAdditionalProperty[] {
        return [
            {
                id: 'CODEBASE_REPOSITORY',
                name: 'Repository',
                accessorKey: 'repositoryName',
                enableSorting: true,
                enableFiltering: true,
                hide: false,
            },
            {
                id: 'CODEBASE_RESOURCE',
                name: 'Resource',
                accessorKey: 'resourceName',
                enableSorting: true,
                enableFiltering: true,
                hide: false,
            },
            {
                id: 'CODEBASE_PROPERTY',
                name: 'Property',
                accessorKey: 'propertyName',
                enableSorting: true,
                enableFiltering: true,
                hide: false,
            },
        ];
    }

    private getAdditionalPropertiesForAgent(connections: ConnectionEntity[], testId: number) {
        const additionalProperties: MonitoringInstanceAdditionalProperty[] = [];
        const edrConnection = connections.find(connection =>
            connection.connectionProviderTypes.some(
                connectionProviderType => connectionProviderType.providerType === ProviderType.EDR,
            ),
        );
        if (!isNil(edrConnection) && MonitorConstants.EDR_TEST_IDS.includes(testId)) {
            additionalProperties.push({
                id: 'DRATA_DEVICE',
                name: 'Drata device',
                accessorKey: 'drataDevice',
                enableSorting: false,
                enableFiltering: false,
                hide: false,
            });
            if (edrConnection.clientType === ClientType.SENTINEL_ONE) {
                additionalProperties.push({
                    id: 'PROVIDER_SERIAL_NUMBER',
                    name: 'SentinelOne serial number',
                    accessorKey: 'providerSerial',
                    enableSorting: false,
                    enableFiltering: false,
                    hide: false,
                });
                additionalProperties.push({
                    id: 'PROVIDER_OPERATIONAL_STATE',
                    name: 'Operational state',
                    accessorKey: 'operationalState',
                    enableSorting: false,
                    enableFiltering: false,
                    hide: false,
                });
            } else if (edrConnection.clientType === ClientType.CROWDSTRIKE) {
                additionalProperties.push({
                    id: 'PROVIDER_SERIAL_NUMBER',
                    name: 'CrowdStrike serial number',
                    accessorKey: 'providerSerial',
                    enableSorting: false,
                    enableFiltering: false,
                    hide: false,
                });
                additionalProperties.push({
                    id: 'PROVIDER_PREVENTION_POLICY',
                    name: 'Prevention policy',
                    accessorKey: 'operationalState',
                    enableSorting: false,
                    enableFiltering: false,
                    hide: false,
                });
            }
        } else {
            additionalProperties.push({
                id: 'EMAIL',
                name: 'Email',
                accessorKey: 'email',
                enableSorting: false,
                enableFiltering: false,
                hide: false,
            });
        }
        additionalProperties.push({
            id: 'CONNECTION',
            name: 'Connection',
            accessorKey: 'connection',
            enableSorting: false,
            enableFiltering: false,
            hide: true,
        });
        additionalProperties.push({
            id: 'CONNECTION_CLIENT_ALIAS',
            name: 'Connection ID / alias',
            accessorKey: 'connectionClientAlias',
            enableSorting: false,
            enableFiltering: false,
            hide: true,
        });
        return additionalProperties;
    }

    private getAdditionalPropertiesForObservability(
        connections: ConnectionEntity[],
        testId: number,
    ) {
        const cspmConnection = connections.find(connection =>
            connection.connectionProviderTypes.some(
                connectionProviderType => connectionProviderType.providerType === ProviderType.CSPM,
            ),
        );

        const vulnerabilityConnection = connections.find(connection =>
            connection.connectionProviderTypes.some(
                connectionProviderType =>
                    connectionProviderType.providerType === ProviderType.VULNERABILITY,
            ),
        );

        if (!isNil(cspmConnection) && MonitorConstants.CSPM_TEST_IDS.includes(testId)) {
            return [
                {
                    id: 'CSPM_ISSUE_ID',
                    name: 'Issue ID',
                    accessorKey: 'cspmIssueId',
                    enableSorting: false,
                    enableFiltering: false,
                    hide: false,
                },
                {
                    id: 'CSPM_ISSUE_TITLE',
                    name: 'Issue title',
                    accessorKey: 'cspmIssueTitle',
                    enableSorting: false,
                    enableFiltering: false,
                    hide: false,
                },
                {
                    id: 'TARGET_NAME',
                    name: 'Entity name',
                    accessorKey: 'cspmEntityName',
                    enableSorting: true,
                    enableFiltering: true,
                    hide: false,
                },
                {
                    id: 'CSPM_SEVERITY',
                    name: 'Severity',
                    accessorKey: 'cspmSeverity',
                    enableSorting: false,
                    enableFiltering: false,
                    hide: false,
                },
                {
                    id: 'CONNECTION',
                    name: 'Connection',
                    accessorKey: 'connection',
                    enableSorting: false,
                    enableFiltering: false,
                    hide: false,
                },
                {
                    id: 'CONNECTION_CLIENT_ALIAS',
                    name: 'Connection ID / alias',
                    accessorKey: 'connectionClientAlias',
                    enableSorting: true,
                    enableFiltering: true,
                    hide: false,
                },
            ];
        }

        if (
            !isNil(vulnerabilityConnection) &&
            MonitorConstants.VULNERABILITY_TEST_IDS.includes(testId)
        ) {
            return [
                {
                    id: 'TARGET_NAME',
                    name: 'Vulnerability title',
                    accessorKey: 'vulnerabilityTitle',
                    enableSorting: true,
                    enableFiltering: true,
                    hide: false,
                },
                {
                    id: 'VULNERABILITY_RESOURCE_ID',
                    name: 'Resource ID',
                    accessorKey: 'vulnerabilityResourceId',
                    enableSorting: false,
                    enableFiltering: false,
                    hide: false,
                },
                {
                    id: 'VULNERABILITY_DUE_DATE',
                    name: 'Due date',
                    accessorKey: 'vulnerabilityDueDate',
                    enableSorting: false,
                    enableFiltering: false,
                    hide: false,
                },
                {
                    id: 'CONNECTION',
                    name: 'Connection',
                    accessorKey: 'connection',
                    enableSorting: false,
                    enableFiltering: false,
                    hide: true,
                },
                {
                    id: 'CONNECTION_CLIENT_ALIAS',
                    name: 'Connection ID / alias',
                    accessorKey: 'connectionClientAlias',
                    enableSorting: false,
                    enableFiltering: false,
                    hide: true,
                },
            ];
        }
        return [
            {
                id: 'REGION',
                name: 'Region',
                accessorKey: 'region',
                enableSorting: false,
                enableFiltering: false,
                hide: false,
            },
            {
                id: 'CONNECTION',
                name: 'Connection',
                accessorKey: 'connection',
                enableSorting: false,
                enableFiltering: false,
                hide: false,
            },
            {
                id: 'CONNECTION_CLIENT_ALIAS',
                name: 'Connection ID / alias',
                accessorKey: 'connectionClientAlias',
                enableSorting: true,
                enableFiltering: true,
                hide: false,
            },
        ];
    }

    private async getRequiredPoliciesForMonitorDetails(
        account: Account,
        workspaceId: number,
        controlTestInstance: ControlTestInstance,
    ): Promise<string[]> {
        if ([CheckStatus.UNUSED, CheckStatus.DISABLED].includes(controlTestInstance.checkStatus)) {
            return getRequiredPolicies(
                String(workspaceId),
                account,
                controlTestInstance.recipes,
                this.providerCatalogService,
            );
        }

        return [];
    }

    private handleRejectedPromiseForMonitorDetails(
        account: Account,
        reason: any,
        index: number,
    ): any {
        this.logger.warn(
            PolloMessage.msg(
                `Promise at index ${index} rejected with reason: ${reason}`,
            ).setAccountId(account.id),
        );

        switch (index) {
            case 1:
            case 2:
            case 3:
                return []; // availableConnections, requiredPolicies or additionalProperties
            case 4:
                return false; // canRevert
            case 0:
            default:
                return null; // complianceCheckExclusionPersonnel
        }
    }

    async listTestResultsFromEvents(
        account: Account,
        user: User,
        controlTestInstanceId: number,
        evidenceLibraryTestResultsDto: MonitorTestResultsPaginatedRequestDto,
    ): Promise<PaginationType<IListTestResult>> {
        try {
            const checkType =
                await this.monitorInstanceCheckTypeRepository.getCheckTypeByControlTestInstanceId(
                    controlTestInstanceId,
                );
            const controlTestInstance = await this.controlTestInstanceRepository.findOneBy({
                id: controlTestInstanceId,
            });
            const response = await this.monitorInstanceRepository.listTestResults(
                account,
                controlTestInstanceId,
                evidenceLibraryTestResultsDto,
                controlTestInstance?.draft ?? false,
                checkType,
            );

            /**
             * This process helps to build the check_result_status from legacy monitoring test
             * Just to recall: before new column in event.check_result_status, Drata was taking the test statuses from
             * the metadata, so to keep backward compatibility we guarantees either ways, from the the new column, but also
             * from the metadata in case the first one is null
             */

            if (!isNil(response) && !isEmpty(response?.data)) {
                const testResultsFiltered = response?.data?.filter(result =>
                    isNil(result.check_result_status),
                );
                testResultsFiltered.forEach(result => {
                    const buildEventToChechResult = {
                        autopilotTaskResponseStatus: result['autopilot_task_response_status'],
                        checkResultStatus: result['check_result_status'],
                        controlTestInstanceHistory: {
                            checkResultStatus: result['ctih_check_result_status'],
                        },
                        metadata: result['metadata'],
                    } as Event;
                    result.check_result_status =
                        CheckResultStatus[getEventStatus(buildEventToChechResult) ?? ''] ?? null;
                });

                /**
                 * Remove unnecesary fields from the query response after using them to build the check_result_status
                 * and also to have a consistent response with type expected on the method <IListTestResult>
                 *  */
                response.data = response?.data?.map(result => {
                    delete result['autopilot_task_response_status'];
                    delete result['ctih_check_result_status'];
                    delete result['metadata'];
                    return result;
                });
            }

            this.logger.log(
                PolloAdapter.acct(
                    `${response?.data?.length} test results pulled from events`,
                    account,
                )
                    .setIdentifier({
                        controlTestInstanceId,
                    })
                    .setDomain(account.domain)
                    .setContext(this.constructor.name)
                    .setSubContext('listTestResultsFromEvents'),
            );

            return response;
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct('Unable to list test results:', account)
                    .setIdentifier({
                        controlTestInstanceId,
                    })
                    .setDomain(account.domain)
                    .setContext(this.constructor.name)
                    .setSubContext('listTestResultsFromEvents')
                    .setError(error),
            );
            throw error;
        }
    }

    private getCacheValueForKey<T>(cacheKey: string): Promise<T | null> {
        return config.get<boolean>('cache.enabled') && CACHE_IS_UP
            ? this._cacheService.get<T>(cacheKey)
            : Promise.resolve(null);
    }

    private async setCacheValueForKey<T>(cacheKey: string, value: T, ttl?: number): Promise<T> {
        if (config.get<boolean>('cache.enabled') && CACHE_IS_UP) {
            return this._cacheService.set<T>(cacheKey, value, {
                ttl,
            });
        }
        return Promise.resolve(value);
    }

    private get controlTestInstanceRepository(): ControlTestInstanceRepository {
        return this.getCustomTenantRepository(ControlTestInstanceRepository);
    }
    private get controlTestInstanceHistoryRepository(): ControlTestInstanceHistoryRepository {
        return this.getCustomTenantRepository(ControlTestInstanceHistoryRepository);
    }
    private get monitorStatRepository(): Repository<MonitorStat> {
        return this.getTenantRepository(MonitorStat);
    }
    private get monitorInstanceRepository(): MonitorInstanceRepository {
        return this.getCustomTenantRepository(MonitorInstanceRepository);
    }
    private get exclusionRepository(): MonitorInstanceExclusionRepository {
        return this.getCustomTenantRepository(MonitorInstanceExclusionRepository);
    }
    private get policyRepository(): PolicyRepository {
        return this.getCustomTenantRepository(PolicyRepository);
    }
    private get policyResponsibilityRepository(): PolicyResponsibilityRepository {
        return this.getCustomTenantRepository(PolicyResponsibilityRepository);
    }
    private get complianceCheckExclusionRepository(): ComplianceCheckExclusionRepository {
        return this.getCustomTenantRepository(ComplianceCheckExclusionRepository);
    }
    private get personnelRepository(): PersonnelRepository {
        return this.getCustomTenantRepository(PersonnelRepository);
    }
    private get autopilotScheduleRepository(): AutopilotScheduleRepository {
        return this.getCustomTenantRepository(AutopilotScheduleRepository);
    }
    private get codeRepositorySettingsRepository(): CodeRepositorySettingsRepository {
        return this.getCustomTenantRepository(CodeRepositorySettingsRepository);
    }
    private get findingExclusionRepository(): FindingExclusionRepository {
        return this.getCustomTenantRepository(FindingExclusionRepository);
    }
    private get controlRepository(): ControlRepository {
        return this.getCustomTenantRepository(ControlRepository);
    }
    private get userRepository(): UserRepository {
        return this.getCustomTenantRepository(UserRepository);
    }
    private get monitorInstanceCheckTypeRepository(): MonitorInstanceCheckTypeRepository {
        return this.getCustomTenantRepository(MonitorInstanceCheckTypeRepository);
    }
}
