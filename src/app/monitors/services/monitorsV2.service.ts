import { CheckStatus, ErrorCode, SocketEvent, TestSource } from '@drata/enums';
import { Injectable, NotFoundException } from '@nestjs/common';
import { ScheduleOptions, ScheduleOptionsAction } from '@temporalio/client';
import { ConnectionsRepository } from 'app/companies/connections/repositories/connections.repository';
import { ConnectionsCoreService } from 'app/companies/connections/services/connections-core.service';
import { Company } from 'app/companies/entities/company.entity';
import { ProductRepository } from 'app/companies/products/repositories/product.repository';
import { CompaniesCoreService } from 'app/companies/services/companies-core.service';
import { LibraryDocument } from 'app/document-library/entities/library-document.entity';
import { EvidenceLibraryVersionTestResultRepository } from 'app/document-library/evidence-library/repositories/evidence-library-version-test-result.repository';
import { FrameworkRepository } from 'app/frameworks/repositories/framework.repository';
import { ControlRepository } from 'app/grc/repositories/control.repository';
import { ControlTestInstanceAutopilotBulkRequestDto } from 'app/monitors/dtos/monitor-v2-control-test-instance-autopilot-bulk-request.dto';
import { MonitorsRequestDto } from 'app/monitors/dtos/monitors-request.dto';
import { MonitorsV2CodeListRequestDto } from 'app/monitors/dtos/monitors-v2-code-list-request.dto';
import { MonitorsV2DownloadTestsRequestDto } from 'app/monitors/dtos/monitors-v2-download-tests-request.dto';
import { MonitorsV2ListRequestDto } from 'app/monitors/dtos/monitors-v2-list-request.dto';
import { ControlTestInstance } from 'app/monitors/entities/control-test-instance.entity';
import { MonitorInstance } from 'app/monitors/entities/monitor-instance.entity';
import { MonitorResultSummary } from 'app/monitors/entities/monitor-result-summary.opensearch.entity';
import { MonitorStat } from 'app/monitors/entities/monitor-stat.entity';
import { MonitorV2TestTypeOptions } from 'app/monitors/enums/monitor-v2-test-type-options.enum';
import { deleteMonitoringSummaryResultsIndexForMultipleTests } from 'app/monitors/helpers/monitoring-summary-indexing.helper';
import {
    MonitorV2CodeTestsFilterOptions,
    MonitorV2ProductionTestsFilterOptions,
} from 'app/monitors/interfaces/monitor-v2-filter-options.interface';
import { wasCustomTestManuallyDisabled } from 'app/monitors/monitor-service.helper';
import { IndexMonitorResultEvent } from 'app/monitors/observables/events/index-monitor-result.event';
import { ControlTestInstanceHistoryRepository } from 'app/monitors/repositories/control-test-instance-history.repository';
import { ControlTestInstanceRepository } from 'app/monitors/repositories/control-test-instance.repository';
import { MonitorInstanceRepository } from 'app/monitors/repositories/monitor-instance.repository';
import { MonitorStatRepository } from 'app/monitors/repositories/monitor-stat.repository';
import { MonitorV2ResultsData } from 'app/monitors/repositories/monitor-v2-results.data';
import { MonitoringSummaryIndexingService } from 'app/monitors/services/monitoring-summary-indexing.service';
import { BulkActionResponseType } from 'app/monitors/types/bulk/bulk-action-response.type';
import { AvailableConnection } from 'app/monitors/types/control-test-instance-details.type';
import { ControlWithFramework } from 'app/monitors/types/control-with-frameworks.type';
import { MonitorFinding } from 'app/monitors/types/monitor-finding.type';
import { MonitorFindings } from 'app/monitors/types/monitor-findings.type';
import { MonitorTrack } from 'app/monitors/types/monitor-track.type';
import { UserDocument } from 'app/users/entities/user-document.entity';
import { User } from 'app/users/entities/user.entity';
import { UserDocumentDownloadedEvent } from 'app/users/observables/events/user-document-downloaded.event';
import { VulnerabilityMonitoringConstants } from 'app/vulnerability/constants/vulnerability-monitoring.constants';
import { publishDraftTestsBulkWorkflowV1 } from 'app/worker/workflows';
import { Account } from 'auth/entities/account.entity';
import { PolloAdapter } from 'commons/adapters/pollo.adapter';
import { INT32_MAX } from 'commons/constants/numeric-limits.const';
import { ExtractedRequestProps } from 'commons/decorators/get-request-notifier-props.decorator';
import { PaginationRequestDto } from 'commons/dtos/pagination-request.dto';
import { ClientType } from 'commons/enums/auth/client-type.enum';
import { MonitorStatType } from 'commons/enums/monitors/monitor-stat-type.enum';
import { RunMode } from 'commons/enums/run-mode.enum';
import { Action } from 'commons/enums/users/action.enum';
import { Subject } from 'commons/enums/users/subject.enum';
import { ConflictException } from 'commons/exceptions/conflict.exception';
import {
    customMonitorNotUpdatable,
    injectCompanyNameForControlTestInstance,
    injectCompanyNameForControlTestInstances,
    isNotUpdateable,
} from 'commons/helpers/control-test-instance.helper';
import { getDateDiffAsDays } from 'commons/helpers/date.helper';
import { shouldEmitEvent } from 'commons/helpers/event.helper';
import { getEnabledClientTypesByAutopilotTaskType } from 'commons/helpers/monitor.helper';
import { is32BitInteger } from 'commons/helpers/number.helper';
import { getProductId, hasAssociatedProduct } from 'commons/helpers/products.helper';
import { PartialNested, getTemporalClient } from 'commons/helpers/temporal/client';
import { hasUserPermission } from 'commons/helpers/user.helper';
import { AppService } from 'commons/services/app.service';
import { CsvDataSetType } from 'commons/types/csv-data-set.type';
import { PaginationType } from 'commons/types/pagination.type';
import { SearchFilterOptions } from 'commons/types/search-filter-options.type';
import config from 'config';
import { Socket } from 'dependencies/socket/socket';
import { FeatureFlagCategory } from 'feature-flags/enums/feature-flag-category.enum';
import { FeatureFlag } from 'feature-flags/feature-flag.enum';
import { FeatureFlagService } from 'feature-flags/feature-flag.service';
import { chain, cloneDeep, first, flatMap, get, isEmpty, isNil, some, uniq } from 'lodash';
import moment from 'moment';
import { In } from 'typeorm';
@Injectable()
export class MonitorsV2Service extends AppService {
    constructor(
        private readonly companiesService: CompaniesCoreService,
        private readonly monitorV2ResultsRepository: MonitorV2ResultsData,
        private readonly monitoringSummaryIndexingService: MonitoringSummaryIndexingService,
        private readonly featureFlagService: FeatureFlagService,
        private readonly connectionsCoreService: ConnectionsCoreService,
        private readonly socket: Socket,
    ) {
        super();
    }

    private get controlTestInstanceRepository(): ControlTestInstanceRepository {
        return this.getCustomTenantRepository(ControlTestInstanceRepository);
    }

    private get monitorInstanceRepository(): MonitorInstanceRepository {
        return this.getCustomTenantRepository(MonitorInstanceRepository);
    }

    private get getProductRepository(): ProductRepository {
        return this.getCustomTenantRepository(ProductRepository);
    }

    private get getControlRepository(): ControlRepository {
        return this.getCustomTenantRepository(ControlRepository);
    }

    private get getFrameworkRepository(): FrameworkRepository {
        return this.getCustomTenantRepository(FrameworkRepository);
    }

    private get controlRepository(): ControlRepository {
        return this.getCustomTenantRepository(ControlRepository);
    }

    private get controlTestInstanceHistoryRepository(): ControlTestInstanceHistoryRepository {
        return this.getCustomTenantRepository(ControlTestInstanceHistoryRepository);
    }

    private get monitorStatRepository(): MonitorStatRepository {
        return this.getCustomTenantRepository(MonitorStatRepository);
    }

    private get evidenceLibraryVersionTestResultRepository(): EvidenceLibraryVersionTestResultRepository {
        return this.getCustomTenantRepository(EvidenceLibraryVersionTestResultRepository);
    }

    private get connectionsRepository(): ConnectionsRepository {
        return this.getCustomTenantRepository(ConnectionsRepository);
    }

    private readonly INT32_MAX = INT32_MAX;

    async getControlTestInstanceDetailsData(
        account: Account,
        testId: number,
    ): Promise<ControlTestInstance & { libraryDocument: LibraryDocument | null }> {
        const controlTestInstance =
            await this.controlTestInstanceRepository.getControlTestInstanceMonitorData(
                testId,
                account,
            );

        // Add Evidence reference to control test instance
        // TODO: Remove feature flag validation with this ticket:https://drata.atlassian.net/browse/ENG-58940
        const hideTestEvidences = await this.featureFlagService.evaluateAsTenant(
            {
                name: FeatureFlag.RELEASE_HIDE_TEST_EVIDENCES,
                category: FeatureFlagCategory.NONE,
                defaultValue: true,
            },
            account,
        );
        let libraryDocument = null;
        if (!hideTestEvidences) {
            const testResultMap =
                await this.evidenceLibraryVersionTestResultRepository.getTestResultByControlTestInstance(
                    controlTestInstance.id,
                    getProductId(account),
                );
            libraryDocument = get(testResultMap, 'libraryDocumentVersion.libraryDocument', null);
        }

        return Object.assign(controlTestInstance, {
            libraryDocument,
        });
    }

    async listControlTestInstances(
        account: Account,
        user: User,
        dto: MonitorsV2ListRequestDto,
        workspaceId: number,
    ): Promise<PaginationType<MonitorResultSummary>> {
        // Create a copy of the DTO to avoid mutating the input
        const processedDto = { ...dto };

        // Apply user permission-based control filtering
        const userAllowedControls = await this.getUserAllowedControls(user, workspaceId);
        if (!isNil(userAllowedControls)) {
            const effectiveControls = this.getEffectiveControlsFilter(
                processedDto.allowedControls,
                userAllowedControls,
            );

            // If no controls are allowed after filtering, return empty result
            if (isEmpty(effectiveControls)) {
                return {
                    data: [],
                    page: processedDto.page,
                    limit: processedDto.limit || null,
                    total: 0,
                };
            }

            processedDto.allowedControls = effectiveControls;
        }

        const options = await this.mapProductionMonitorDTOToSearchOptions(processedDto);
        return this.monitorV2ResultsRepository.filterMonitorResultsDocuments(
            account.id,
            workspaceId,
            options,
        );
    }

    async listCodeControlTestInstances(
        account: Account,
        dto: MonitorsV2CodeListRequestDto,
        workspaceId: number,
    ): Promise<PaginationType<MonitorResultSummary>> {
        const options = await this.mapCodeMonitorDTOToSearchOptions(dto);
        return this.monitorV2ResultsRepository.filterMonitorResultsDocuments(
            account.id,
            workspaceId,
            options,
        );
    }

    async getControlTestInstanceByTestId(
        account: Account,
        testId: number,
        workspaceId: number,
    ): Promise<MonitorResultSummary> {
        return this.monitorV2ResultsRepository.getMonitorSummaryById(
            account.id,
            workspaceId,
            testId,
        );
    }

    async getMonitorControls(
        account: Account,
        testId: number,
        workspaceId: number,
        dto: PaginationRequestDto,
    ): Promise<PaginationType<ControlWithFramework>> {
        const response = await this.controlRepository.getMonitorControls(testId, workspaceId, dto);

        response.data.forEach(control => {
            control.description = control.description.replace(/%s/g, account.companyName);
        });

        const controls = response.data.map(control => {
            const frameworks = chain(control?.requirements)
                .map('requirementIndex')
                .map('framework')
                .value();

            return { ...control, frameworks };
        });

        return {
            ...response,
            data: controls as ControlWithFramework[],
        };
    }

    async getControlTestInstanceOverview(
        account: Account,
        workspaceId: number,
        testId: number,
    ): Promise<
        ControlTestInstance & { availableConnections; libraryDocument: LibraryDocument | null }
    > {
        let controlTestInstance =
            await this.controlTestInstanceRepository.getControlTestInstanceMonitorOverviewData(
                testId,
                workspaceId,
            );

        const company = await this.getCompany(account);

        controlTestInstance.controls = controlTestInstance.controls ?? [];

        controlTestInstance = injectCompanyNameForControlTestInstance(
            controlTestInstance,
            company.name,
        );

        if (controlTestInstance.runMode === RunMode.AP2_SHADOW_TEST) {
            throw new NotFoundException(ErrorCode.ENTITY_NOT_FOUND);
        }

        const availableConnections: AvailableConnection[] =
            await this.getControlTestAvailableConnections(
                account,
                testId,
                workspaceId,
                controlTestInstance,
            );

        // Add Evidence reference to control test instance
        // TODO: Remove feature flag validation with this ticket:https://drata.atlassian.net/browse/ENG-58940
        const hideTestEvidences = await this.featureFlagService.evaluateAsTenant(
            {
                name: FeatureFlag.RELEASE_HIDE_TEST_EVIDENCES,
                category: FeatureFlagCategory.NONE,
                defaultValue: true,
            },
            account,
        );
        let libraryDocument = null;
        if (!hideTestEvidences) {
            const testResultMap =
                await this.evidenceLibraryVersionTestResultRepository.getTestResultByControlTestInstance(
                    controlTestInstance.id,
                    workspaceId,
                );
            libraryDocument = get(testResultMap, 'libraryDocumentVersion.libraryDocument', null);
        }

        return Object.assign(controlTestInstance, {
            availableConnections,
            libraryDocument,
        });
    }

    private getAP1AvailableConnections(
        controlTestInstance: ControlTestInstance,
    ): AvailableConnection[] {
        const taskType = first(controlTestInstance?.monitorInstances)?.autopilotTaskType;

        if (isNil(taskType)) {
            return [];
        }
        // this is a case were info does not exists in requestDescriptions list on control-tests.yml
        const clients = getEnabledClientTypesByAutopilotTaskType(taskType);

        return !isEmpty(clients)
            ? clients.map(e => {
                  return { clientType: ClientType[e] };
              })
            : Object.keys(VulnerabilityMonitoringConstants.TEST_IDS)
                  .filter(t =>
                      // this is a fallback because vulnerability does not exists in clientTypeAutopilotTaskTypeMap
                      VulnerabilityMonitoringConstants.TEST_IDS[t]?.includes(
                          controlTestInstance.testId,
                      ),
                  )
                  .map(e => ({
                      clientType: ClientType[e],
                  }));
    }

    async getFindings(
        account: Account,
        workspaceId: number,
        testId: number,
    ): Promise<MonitorFindings> {
        const controlTestInstance = await this.getControlTestInstanceByTestId(
            account,
            testId,
            workspaceId,
        );

        if (isNil(controlTestInstance)) {
            throw new NotFoundException(ErrorCode.ENTITY_NOT_FOUND);
        }

        return Promise.resolve({
            failingResourcesCount: controlTestInstance.findingsCount ?? 0,
        });
    }

    async getTrack(workspaceId: number, testId: number): Promise<MonitorTrack> {
        const latestResult =
            await this.controlTestInstanceHistoryRepository.getLatestHistoryByTestId(
                workspaceId,
                testId,
            );

        // this handle the case were it haven't run yet
        if (isNil(latestResult) || isNil(latestResult.checkResultStatus)) {
            return { checkResultStatus: null, consecutiveDays: 0, startDateWithStatus: null };
        }

        const latestDifferentResult =
            await this.controlTestInstanceHistoryRepository.getLatestHistoryByTestIdWithDifferentResult(
                workspaceId,
                testId,
                latestResult.checkResultStatus,
            );

        // this handle the case when all history have the same status
        if (isEmpty(latestDifferentResult)) {
            const oldestResult =
                await this.controlTestInstanceHistoryRepository.getOldestHistoryByTestId(
                    workspaceId,
                    testId,
                    latestResult.checkResultStatus,
                );

            return {
                checkResultStatus: oldestResult.checkResultStatus,
                consecutiveDays:
                    getDateDiffAsDays(new Date(oldestResult.createdAt), new Date(Date.now())) + 1,
                startDateWithStatus: oldestResult.createdAt,
            };
        }

        const oldestResultWithSameStatus =
            await this.controlTestInstanceHistoryRepository.getOldestHistoryByTestId(
                workspaceId,
                testId,
                latestResult.checkResultStatus,
                latestDifferentResult?.createdAt,
            );

        return {
            checkResultStatus: oldestResultWithSameStatus.checkResultStatus,
            consecutiveDays:
                getDateDiffAsDays(
                    new Date(oldestResultWithSameStatus.createdAt),
                    new Date(Date.now()),
                ) + 1,
            startDateWithStatus: oldestResultWithSameStatus.createdAt,
        };
    }

    /**
     * @param {Account} account
     * @return {Promise<Company>}
     */
    private getCompany(account: Account): Promise<Company> {
        return this.companiesService.getCompanyByAccountId(account.id);
    }

    /**
     *
     * @param workspaceId
     * @param testIds
     * @param account
     * @param user
     * @returns
     */
    async downloadMonitorTests(
        request: MonitorsV2DownloadTestsRequestDto,
        workspaceId: number,
        account: Account,
        user: User,
    ): Promise<CsvDataSetType> {
        let allControlTestInstances =
            await this.controlTestInstanceRepository.getControlTestInstancesByTestIds(
                workspaceId,
                request.testIds,
            );

        const company = await this.getCompany(account);

        allControlTestInstances = injectCompanyNameForControlTestInstances(
            allControlTestInstances,
            company.name,
        );

        const data = cloneDeep<any[]>(allControlTestInstances);
        const filename = `Monitor-${moment().format('MMDDYYYY')}`;

        if (shouldEmitEvent(account, user)) {
            this._eventBus.publish(
                new UserDocumentDownloadedEvent(account, user, filename, {
                    name: filename,
                } as UserDocument),
            );
        }

        return {
            data,
            filename,
        };
    }

    async updateControlTestInstanceStatus(
        account: Account,
        user: User,
        dto: ControlTestInstanceAutopilotBulkRequestDto,
        workspaceId: number,
    ): Promise<void> {
        const controlTestInstances =
            await this.controlTestInstanceRepository.getControlTestInstanceByTestIds(
                dto.controlTestInstanceTestIds,
                workspaceId,
            );

        const monitorInstancesToUpdate: MonitorInstance[] = [];
        const autopilotSchedulersToUpdate: [
            string,
            PartialNested<ScheduleOptions<ScheduleOptionsAction>>,
        ][] = [];
        const controlTestInstancesToUpdate: ControlTestInstance[] = [];
        const monitorStatTestSourcesToUpdate: TestSource[] = [];

        for (const originalControlTestInstance of controlTestInstances) {
            try {
                /**
                 * Make sure the control test instance can be enabled. Control
                 * test instances that are in a needs attention check status
                 * OR in an upcoming check status but have no auto-enabled
                 * date cannot be manipulated in terms of check status
                 */
                if (isNotUpdateable(originalControlTestInstance)) {
                    throw new ConflictException(
                        'This monitor cannot be updated',
                        ErrorCode.CONFLICT_MONITOR_CHECK_STATUS,
                    );
                }

                if (customMonitorNotUpdatable(originalControlTestInstance)) {
                    throw new ConflictException(
                        'Disabled monitor has no available connections',
                        ErrorCode.CONFLICT_MONITOR_CUSTOM_CONNECTION_DELETED,
                    );
                }

                const controlTestInstance = {
                    ...originalControlTestInstance,
                } as ControlTestInstance;

                if (dto.checkStatus !== originalControlTestInstance.checkStatus) {
                    let monitorInstancesStatus = true;

                    if (wasCustomTestManuallyDisabled(originalControlTestInstance, false)) {
                        this.logger.log(
                            PolloAdapter.acct(
                                `Setting checkStatus for custom test as DISABLED`,
                                account,
                            )
                                .setContext(this.constructor.name)
                                .setSubContext('MonitorsV2Service')
                                .setIdentifier(
                                    `${originalControlTestInstance.name} - ${originalControlTestInstance.testId}`,
                                ),
                        );
                    }

                    controlTestInstance.checkStatus = dto.checkStatus;

                    if (dto.checkStatus === CheckStatus.ENABLED) {
                        controlTestInstance.disabledMessage = null;
                        controlTestInstance.disablingUser = null;
                    } else {
                        controlTestInstance.disabledMessage = dto.disabledMessage;
                        controlTestInstance.disablingUser = user;
                        monitorInstancesStatus = false;
                    }

                    if (!isEmpty(originalControlTestInstance.monitorInstances)) {
                        for (const originalMonitorInstance of originalControlTestInstance.monitorInstances) {
                            const monitorInstance = {
                                ...originalMonitorInstance,
                            } as MonitorInstance;

                            monitorInstance.enabled = monitorInstancesStatus;
                            monitorInstancesToUpdate.push(monitorInstance);
                        }
                    }

                    if (!isEmpty(originalControlTestInstance.schedule)) {
                        const [schedule] = originalControlTestInstance.schedule;

                        if (config.get<boolean>('temporal.enabled')) {
                            autopilotSchedulersToUpdate.push([
                                schedule.scheduleHandler,
                                {
                                    state: {
                                        paused: !monitorInstancesStatus,
                                        note: !monitorInstancesStatus
                                            ? dto.disabledMessage
                                            : 'Test re-enabled',
                                    },
                                },
                            ]);
                        } else {
                            this.logger.warn(
                                PolloAdapter.acct(
                                    `Temporal is not enabled for this API service, unable to update monitor schedule.
                                    Occurred while bulk updating entities`,
                                    account,
                                ).setIdentifier({
                                    schedule,
                                    paused: !monitorInstancesStatus,
                                    note: !monitorInstancesStatus
                                        ? dto.disabledMessage
                                        : 'Test re-enabled',
                                }),
                            );
                        }
                    }

                    controlTestInstancesToUpdate.push(controlTestInstance);
                    monitorStatTestSourcesToUpdate.push(controlTestInstance.source);
                }
            } catch (error) {
                this.logger.error(
                    PolloAdapter.acct(
                        `Error occurred while bulk updating control test instances`,
                        account,
                    ).setError(error),
                );
                continue;
            }
        }

        try {
            await this.controlTestInstanceRepository.save(controlTestInstancesToUpdate);
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(
                    `Failure to update control test instances`,
                    account,
                ).setIdentifier({
                    testIds: controlTestInstancesToUpdate.map(ci => ci.testId),
                }),
            );
        }

        try {
            await this.monitorInstanceRepository.save(monitorInstancesToUpdate);
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(`Failure to update monitor instances`, account).setIdentifier({
                    ids: monitorInstancesToUpdate.map(mi => mi.id),
                }),
            );
        }

        try {
            const client = await getTemporalClient(account.domain);

            const scheduleUpdates = autopilotSchedulersToUpdate.map(([schedule, options]) =>
                client.updateSchedule(schedule, options),
            );
            const results = await Promise.allSettled(scheduleUpdates);

            results
                .filter(({ status }) => status === 'rejected')
                .forEach(({ reason }: PromiseRejectedResult) => {
                    this.logger.error(
                        PolloAdapter.acct(
                            `Failure to update temporal schedule`,
                            account,
                        ).setIdentifier({
                            reason,
                        }),
                    );
                });
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(`Failure to initialize temporal client`, account).setIdentifier(
                    {},
                ),
            );
        }

        this._eventBus.publish(
            new IndexMonitorResultEvent(
                account,
                getProductId(account),
                dto.controlTestInstanceTestIds,
            ),
        );

        await this.updateMonitorStats(account, uniq(monitorStatTestSourcesToUpdate));
    }

    /**
     *
     * @param account
     */
    async updateMonitorStat(account: Account, statType: MonitorStatType): Promise<MonitorStat> {
        const product = account.getCurrentProduct();
        if (isNil(product)) {
            throw new NotFoundException(ErrorCode.PRODUCT_NOT_FOUND);
        }
        const monitorStat = await this.monitorStatRepository.findOneOrFail({
            where: {
                product: { id: product.id },
                statType,
            },
            relations: ['product'],
        });

        const { passed, failed, preAudit, passingPercent } =
            await this.controlTestInstanceRepository.aggregateStats(
                account,
                statType === MonitorStatType.CODE ? TestSource.ACORN : TestSource.DRATA,
            );

        monitorStat.passingPercent = passingPercent;
        monitorStat.passed = passed;
        monitorStat.failed = failed;
        monitorStat.preAudit = preAudit;

        /**
         * To research: Can we find a smarter way to determine when to update number of repositories monitored?
         * We don't need to update number of repos monitored on every stat update call
         * Disabling number of repos monitored updates for now
         */
        // if (testSource === TestSourceEnum.ACORN) {
        // const { numRepositoriesMonitored, totalRepositories } =
        //     await this.codeRepositorySettingsRepository.getMonitoredRepositoriesStats();
        // monitorStat.numberOfRepositoriesMonitored = numRepositoriesMonitored;
        // monitorStat.totalRepositories = totalRepositories;
        // }

        return this.monitorStatRepository.save(monitorStat, { reload: false });
    }

    /**
     *
     * @param account
     */
    private async updateMonitorStats(account: Account, testSources: TestSource[]): Promise<void> {
        /**
         * If there is no associated product we are updating a monitor stat
         * that is account-scoped; ie, Policies - get all the monitor stats
         * and run and update to get all the stats up to date for the account.
         */
        if (!hasAssociatedProduct(account)) {
            const monitorStats = await this.monitorStatRepository.find({
                where: {
                    statType: In(testSources),
                },
                relations: ['product'],
            });

            await Promise.allSettled(
                monitorStats.map(monitorStat =>
                    this.updateMonitorStat(
                        account.cloneWithProduct(monitorStat.product),
                        monitorStat.statType,
                    ),
                ),
            );
        } else {
            await Promise.allSettled(
                testSources.map(testSource =>
                    this.updateMonitorStat(
                        account.cloneWithProduct(account.getCurrentProduct()),
                        testSource === TestSource.ACORN
                            ? MonitorStatType.CODE
                            : MonitorStatType.DEPLOYED,
                    ),
                ),
            );
        }
    }

    async getTestIdsFromSelectedFilters(
        account: Account,
        workspaceId: number,
        requestDto: MonitorsV2ListRequestDto,
    ): Promise<number[]> {
        /**
         * Assumption: 1 document exists for each monitor result, so 10k page size and returning all results in 1 page
         * should always result in a fully saturated search with 1 round trip
         */
        let filters: Record<string, any>;
        if (
            requestDto.allowedTestSources?.includes(
                MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.COMPLIANCE_AS_CODE],
            )
        ) {
            const { filters: codeFilters } =
                await this.mapCodeMonitorDTOToSearchOptions(requestDto);
            filters = codeFilters ?? {};
        } else {
            const { filters: productionFilters } =
                await this.mapProductionMonitorDTOToSearchOptions(requestDto);
            filters = productionFilters ?? {};
        }
        const options: SearchFilterOptions = {
            filters: filters,
            page: 1,
            size: 10000,
            sort: MonitorV2ResultsData.defaultSortClause,
        };

        const response = await this.monitorV2ResultsRepository.filterMonitorResultsDocuments(
            account.id,
            workspaceId,
            options,
        );

        return response.data.map((d: MonitorResultSummary) => d.testId);
    }

    /**
     * @deprecated Use MonitorsCoreService.listControlTestInstancesByTestIds
     *
     * @param testIds
     * @param productId
     * @returns
     */
    async listControlTestInstancesByTestIds(
        testIds: number[],
        productId: number,
    ): Promise<ControlTestInstance[]> {
        return this.controlTestInstanceRepository.getControlTestInstanceByTestIds(
            testIds,
            productId,
        );
    }

    async getProductionMonitorFilters(
        account: Account,
        workspaceId: number,
        user: User,
    ): Promise<MonitorV2ProductionTestsFilterOptions> {
        const userAllowedControls = await this.getUserAllowedControls(user, workspaceId);
        const controlCodes = userAllowedControls ?? [];

        return this.monitorV2ResultsRepository.getProdMonitoringFacets(
            account.id,
            workspaceId,
            controlCodes,
        );
    }

    async getCodeMonitorFilters(
        account: Account,
        workspaceId: number,
    ): Promise<MonitorV2CodeTestsFilterOptions> {
        return this.monitorV2ResultsRepository.getCodeMonitoringFacets(account.id, workspaceId);
    }

    private async mapProductionMonitorDTOToSearchOptions(
        dto: MonitorsV2ListRequestDto,
    ): Promise<SearchFilterOptions> {
        const searchOptions: SearchFilterOptions = {
            page: dto.page ?? config.get('pagination.page'),
            size: dto.limit ?? config.get('pagination.limit'),
            sort:
                dto.sort && dto.sortDir
                    ? [
                          {
                              field: dto.sort,
                              order: dto.sortDir.toUpperCase() === 'ASC' ? 'asc' : 'desc',
                          },
                      ]
                    : MonitorV2ResultsData.defaultSortClause,
        };
        const filters: Record<string, any> = {};

        filters.testType = MonitorsV2Service.getProdTestSourceFilter(dto.allowedTestSources);

        if (!isNil(dto.q) && dto.q != '') {
            const searchTestId = Number(dto.q);
            if (is32BitInteger(searchTestId) && isNil(dto.testIds)) {
                filters.testId = searchTestId;
            } else {
                searchOptions.searchFields = ['testName'];
                searchOptions.searchQuery = dto.q;
            }
        }

        if (!isEmpty(dto.testIds)) {
            filters.testId = dto.testIds;
        }

        if (!isNil(dto.hasExclusions)) {
            filters.hasExclusions = dto.hasExclusions;
        }

        if (!isNil(dto.allowedCheckResultStatuses) && dto.allowedCheckResultStatuses.length > 0) {
            filters.checkResultStatus = dto.allowedCheckResultStatuses;
        }

        if (!isNil(dto.allowedStatuses) && dto.allowedStatuses.length > 0) {
            filters.checkStatus = dto.allowedStatuses;
        }

        if (!isNil(dto.allowedCategories) && dto.allowedCategories.length > 0) {
            filters.category = dto.allowedCategories;
        }

        if (!isNil(dto.allowedConnections) && dto.allowedConnections.length > 0) {
            filters.possibleConnections = dto.allowedConnections;
        }

        if (!isNil(dto.allowedControls) && dto.allowedControls.length > 0) {
            filters.controls = dto.allowedControls;
        }

        if (!isNil(dto.allowedFrameworks) && dto.allowedFrameworks.length > 0) {
            filters.frameworks = dto.allowedFrameworks;
        }

        if (!isNil(dto.allowedTicketStatuses) && dto.allowedTicketStatuses.length > 0) {
            filters.ticketStatus = dto.allowedTicketStatuses;
        }

        if (dto.isNew) {
            filters.isNew = true;
        }

        searchOptions.filters = filters;
        return searchOptions;
    }

    static getProdTestSourceFilter(userProvidedFilter?: string[]): string[] {
        if (isEmpty(userProvidedFilter) || isNil(userProvidedFilter)) {
            return this.getDefaultTestSourceValues();
        }

        // Define filter expansion mapping
        const filterExpansions = new Map<string, string[]>([
            [
                MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.DRATA],
                [
                    MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.DRATA_CUSTOM_PUBLISHED],
                    MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.DRATA_CUSTOM_DRAFT],
                ],
            ],
            [
                MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.CUSTOM_PUBLISHED],
                [MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.DRATA_CUSTOM_PUBLISHED]],
            ],
            [
                MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.CUSTOM_DRAFT],
                [MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.DRATA_CUSTOM_DRAFT]],
            ],
        ]);

        // Check if any filter needs expansion
        const additionalFilters = new Set<string>();
        for (const [filterType, expansions] of filterExpansions) {
            if (userProvidedFilter.includes(filterType)) {
                expansions.forEach(expansion => {
                    // Only add if not already present in the original filter
                    if (!userProvidedFilter.includes(expansion)) {
                        additionalFilters.add(expansion);
                    }
                });
            }
        }

        // Return expanded filter if any expansions were found, otherwise return original filter
        return additionalFilters.size > 0
            ? [...userProvidedFilter, ...Array.from(additionalFilters)]
            : userProvidedFilter;
    }

    private static getDefaultTestSourceValues(): string[] {
        return [
            MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.DRATA],
            MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.CUSTOM_DRAFT],
            MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.CUSTOM_PUBLISHED],
            MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.DRATA_CUSTOM_PUBLISHED],
            MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.DRATA_CUSTOM_DRAFT],
        ];
    }

    private async mapCodeMonitorDTOToSearchOptions(
        dto: MonitorsV2ListRequestDto,
    ): Promise<SearchFilterOptions> {
        const searchOptions: SearchFilterOptions = {
            page: dto.page ?? config.get('pagination.page'),
            size: dto.limit ?? config.get('pagination.limit'),
            sort:
                dto.sort && dto.sortDir
                    ? [
                          {
                              field: dto.sort,
                              order: dto.sortDir.toUpperCase() === 'ASC' ? 'asc' : 'desc',
                          },
                      ]
                    : MonitorV2ResultsData.defaultSortClause,
        };

        const filters: Record<string, any> = {};

        filters.testType = [MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.COMPLIANCE_AS_CODE]];

        if (!isNil(dto.q) && dto.q != '') {
            const searchTestId = Number(dto.q);
            if (
                !isNaN(searchTestId) &&
                Number.isInteger(searchTestId) &&
                searchTestId <= this.INT32_MAX
            ) {
            } else {
                searchOptions.searchFields = ['testName'];
                searchOptions.searchQuery = dto.q;
            }
        }

        if (!isNil(dto.hasExclusions)) {
            filters.hasExclusions = dto.hasExclusions;
        }

        if (!isNil(dto.allowedCheckResultStatuses) && dto.allowedCheckResultStatuses.length > 0) {
            filters.checkResultStatus = dto.allowedCheckResultStatuses;
        }

        if (!isNil(dto.allowedStatuses) && dto.allowedStatuses.length > 0) {
            filters.checkStatus = dto.allowedStatuses;
        }

        if (!isNil(dto.allowedCategories) && dto.allowedCategories.length > 0) {
            filters.category = dto.allowedCategories;
        }

        if (!isNil(dto.allowedControls) && dto.allowedControls.length > 0) {
            filters.controls = dto.allowedControls;
        }

        if (!isNil(dto.allowedFrameworks) && dto.allowedFrameworks.length > 0) {
            filters.frameworks = dto.allowedFrameworks;
        }

        if (dto.isNew) {
            filters.isNew = true;
        }

        searchOptions.filters = filters;

        return searchOptions;
    }

    /**
     *
     * @param account
     * @param testId
     */
    async getControlTestAvailableConnections(
        account: Account,
        testId: number,
        workspaceId: number,
        test?: ControlTestInstance,
    ): Promise<AvailableConnection[]> {
        const controlTestInstance = !isNil(test)
            ? test
            : await this.controlTestInstanceRepository.getControlTestInstanceWithActiveMonitorInstancesByTestId(
                  workspaceId,
                  testId,
              );

        const ap2Connections: AvailableConnection[] = flatMap(
            controlTestInstance.recipes,
            recipe => recipe.recipe.providers,
        ).map(provider => ({ clientType: provider.provider }));

        if (controlTestInstance.isCustom()) {
            return ap2Connections;
        }

        const ap1Connections = this.getAP1AvailableConnections(controlTestInstance);

        if ([RunMode.AP1, RunMode.DUAL].includes(controlTestInstance.runMode)) {
            return ap1Connections;
        }

        const activeConnections =
            await this.connectionsCoreService.getActiveConnectionsByProductId(account);

        const activeConnectionsClientType = activeConnections.map(
            connection => connection.clientType,
        );

        const ap1ActiveConnections = ap1Connections.filter(({ clientType }) =>
            some(
                activeConnectionsClientType,
                activeClientType => activeClientType === ClientType[clientType],
            ),
        ).length;

        const ap2ActiveConnections = ap2Connections.filter(({ clientType }) =>
            some(
                activeConnectionsClientType,
                activeClientType => activeClientType === ClientType[clientType],
            ),
        ).length;

        const availableConnections =
            ap1ActiveConnections > ap2ActiveConnections ? ap1Connections : ap2Connections;

        return availableConnections;
    }

    async getMonitorFindingById(
        account: Account,
        workspaceId: number,
        testId: number,
        connectionId: number,
        findingId: string,
        drataDevice?: string,
        serialNumber?: string,
    ): Promise<MonitorFinding> {
        try {
            const controlTestInstance =
                await this.controlTestInstanceRepository.getControlTestInstanceMonitorData(
                    testId,
                    account,
                );

            const monitorInstance = controlTestInstance.monitorInstances[0];
            if (!monitorInstance) {
                throw new NotFoundException(`Unable to find monitor instance. Test ID: ${testId}`);
            }

            const metadata = monitorInstance.getMetadata();
            const connectionMetadata = metadata.get(connectionId);

            if (!connectionMetadata) {
                throw new NotFoundException(
                    `No metadata for connection ID ${connectionId} was found on monitor with test ID ${testId}`,
                );
            }

            connectionMetadata.monitorResult.fail = connectionMetadata.monitorResult.fail.map(
                failResult => {
                    if (failResult.raw) {
                        try {
                            failResult.raw = JSON.parse(failResult.raw);
                            return failResult;
                        } catch (error) {}
                    }

                    return failResult;
                },
            );

            drataDevice = drataDevice ? decodeURIComponent(drataDevice) : undefined;
            serialNumber = serialNumber ? decodeURIComponent(serialNumber) : undefined;

            const metadataFinding = connectionMetadata.monitorResult.fail.find(
                finding =>
                    finding.id === findingId &&
                    (!isNil(drataDevice) && !isEmpty(drataDevice)
                        ? finding.raw?.drataDevice?.deviceName === drataDevice
                        : true) &&
                    (!isNil(serialNumber) && !isEmpty(serialNumber)
                        ? finding.raw?.drataDevice?.serialNumber === serialNumber
                        : true),
            );

            if (!metadataFinding) {
                throw new NotFoundException(
                    `No metadata was found for finding with ID ${findingId} on connection ID ${connectionId} on monitor with test ID ${testId}`,
                );
            }

            let connectionAlias: string | null | undefined;
            if (connectionId) {
                try {
                    const connection =
                        await this.connectionsRepository.getConnectionById(connectionId);
                    connectionAlias = connection.clientAlias;
                } catch (error) {
                    this.logger.error(
                        PolloAdapter.acct(
                            `Error occurred while retrieving connection info`,
                            account,
                        ).setError(error),
                    );
                    connectionAlias = '';
                }
            }

            const monitorFinding: MonitorFinding = {
                findingId: metadataFinding.id,
                testId: testId,
                testDescription:
                    monitorInstance.evidenceCollectionDescription?.replace(
                        /%s/g,
                        account.companyName,
                    ) ?? '',
                testSource: controlTestInstance.source,
                checkType: monitorInstance.monitorInstanceCheckTypes[0].checkType,
                metadata: metadataFinding,
                clientType: connectionMetadata?.monitorResult.clientType,
                resourceName: metadataFinding.name,
                createdAt: controlTestInstance.lastCheck ?? new Date(),
                remedy: monitorInstance.remedyDescription?.replace(/%s/g, account.companyName),
                connectionId: connectionId,
                connectionAlias: connectionAlias,
                connectionMonitorResult: connectionMetadata.monitorResult,
            };
            return monitorFinding;
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(
                    `Error occurred while bulk updating control test instances`,
                    account,
                ).setError(error),
            );

            throw error;
        }
    }

    // KR / PP / BN TODO - Delete/replace this method  with the real "list findings" method
    async listFindings(account: Account, testId: number) {
        const controlTestInstance =
            await this.controlTestInstanceRepository.getControlTestInstanceMonitorData(
                testId,
                account,
            );

        const monitorInstance = controlTestInstance.monitorInstances[0];
        if (!monitorInstance) {
            throw new NotFoundException(`Unable to find monitor instance. Test ID: ${testId}`);
        }

        return monitorInstance.getMetadata();
    }

    async publishDraftTestsBulkWorkflow(
        account: Account,
        user: User,
        requestDto: MonitorsRequestDto,
        requestMetadata: ExtractedRequestProps,
        workspaceId: number,
        sendSnackNotification = false,
    ): Promise<BulkActionResponseType> {
        const taskQueue = config.get('temporal.taskQueues.temporal-default');
        const temporalClient = await getTemporalClient(account.domain);

        try {
            const temporalBulkActionResponse = await temporalClient.executeWorkflow(
                publishDraftTestsBulkWorkflowV1,
                {
                    taskQueue: taskQueue,
                    args: [
                        {
                            account,
                            user,
                            requestDto,
                            requestMetadata,
                            workspaceId,
                            sendSnackNotification,
                        },
                    ],
                    memo: { accountId: account.id, domain: account.domain },
                },
            );

            const testIdsToUpdateIndex = temporalBulkActionResponse.result.map(testIds =>
                Number(testIds.targetId),
            );

            if (!isEmpty(testIdsToUpdateIndex)) {
                this._eventBus.publish(
                    new IndexMonitorResultEvent(account, workspaceId, testIdsToUpdateIndex, {
                        calculateFindingsAndExclusionsCounts: true,
                    }),
                );
            }

            const mergedDraftTestIds = temporalBulkActionResponse.mergedDraftTestIds;
            if (mergedDraftTestIds && !isEmpty(mergedDraftTestIds)) {
                await deleteMonitoringSummaryResultsIndexForMultipleTests(
                    this.monitoringSummaryIndexingService,
                    account,
                    workspaceId,
                    mergedDraftTestIds,
                );

                await this.notifyMonitorTestDeletedChange(account, workspaceId, mergedDraftTestIds);
            }

            return temporalBulkActionResponse;
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(
                    `Error occurred while bulk updating control test instances`,
                    account,
                ).setError(error),
            );

            throw error;
        }
    }

    /**
     * Send a message via socket to indicate monitor tests were deleted
     *
     * @param account
     * @param workspaceId
     * @param testIds
     */
    private async notifyMonitorTestDeletedChange(
        account: Account,
        workspaceId: number,
        testIds: number[],
    ): Promise<void> {
        const responseDto = {
            testIds,
            workspaceId,
        };

        try {
            await this.socket.sendMessage(
                account.id,
                SocketEvent.MONITOR_TEST_DELETED,
                responseDto,
            );
        } catch (error) {}
    }

    /**
     * Get the controls that a user is allowed to access based on their permissions.
     * Returns null if the user has ViewAllControls permission (no filtering needed).
     * Returns an array of control codes if the user has restricted access.
     */
    private async getUserAllowedControls(
        user: User,
        workspaceId: number,
    ): Promise<string[] | null> {
        // Users with ViewAllControls permission can see all controls
        if (hasUserPermission(user, Action.READ, Subject.ViewAllControls)) {
            return null;
        }

        // Users with Control permission but not ViewAllControls have restricted access
        if (hasUserPermission(user, Action.READ, Subject.Control)) {
            return this.getControlCodesForControlManager(workspaceId, user);
        }

        // Users without Control permission cannot see any controls
        return [];
    }

    /**
     * Determines the effective controls filter by intersecting requested controls
     * with user-allowed controls.
     */
    private getEffectiveControlsFilter(
        requestedControls: string[] | undefined,
        userAllowedControls: string[],
    ): string[] {
        // If no controls are requested, use all user-allowed controls
        if (isNil(requestedControls) || isEmpty(requestedControls)) {
            return userAllowedControls;
        }

        // Return intersection of requested and allowed controls
        return requestedControls.filter(control => userAllowedControls.includes(control));
    }

    private async getControlCodesForControlManager(
        workspaceId: number,
        user: User,
    ): Promise<string[]> {
        const controlIds = await this.controlRepository.getControlIdsByUserId(user.id, workspaceId);

        const controlsWithApprovalIds = await this.controlRepository.filterControlsByApprovals(
            workspaceId,
            [user.id],
        );

        // remove duplicates
        const mergeControlIds = [...new Set([...controlIds, ...controlsWithApprovalIds])];

        const controlCodes = await this.controlRepository.find({
            loadEagerRelations: false,
            where: {
                id: In(mergeControlIds),
            },
            select: ['code'],
        });

        return controlCodes.map(control => control.code);
    }
}
