import { CheckResultStatus, CheckStatus } from '@drata/enums';
import { ApiProperty } from '@nestjs/swagger';
import { MonitorV2TestTypeOptions } from 'app/monitors/enums/monitor-v2-test-type-options.enum';
import { Transform } from 'class-transformer';
import { ArrayMaxSize, IsEnum, IsOptional, IsPositive, IsString } from 'class-validator';
import { TransformBoolean } from 'commons/decorators/transform-boolean.decorator';
import { TransformToArrayOfUniqueNumbers } from 'commons/decorators/transform-to-array-of-uniques.decorator';
import { PaginationRequestDto } from 'commons/dtos/pagination-request.dto';
import { TaskManagementStatus } from 'commons/enums/tickets/task-management-status-enum';
import { ISearch } from 'commons/interfaces/search.interface';

export class MonitorsV2ListRequestDto extends PaginationRequestDto implements ISearch {
    @ApiProperty({
        type: 'string',
        example: 'search term',
        description: 'A free-text query string to filter results by multiple fields.',
        required: false,
    })
    @IsOptional()
    @IsString()
    q: string;

    @ApiProperty({
        type: 'string',
        example: 'testName',
        description: 'The field by which to sort results.',
        required: false,
    })
    @IsOptional()
    @IsString()
    sort?: string;

    @ApiProperty({
        type: 'string',
        example: 'ASC',
        description:
            'Sort direction for results. Use "ASC" for ascending or "DESC" for descending order.',
        required: false,
    })
    @IsOptional()
    @IsString()
    sortDir?: string;

    @IsOptional()
    @ApiProperty({
        required: false,
        type: 'boolean',
        example: true,
        description: 'Return only Tests that have exclusions related to them',
    })
    @TransformBoolean()
    hasExclusions?: boolean;

    @ApiProperty({
        enum: CheckResultStatus,
        isArray: true,
        example: [
            CheckResultStatus[CheckResultStatus.PASSED],
            CheckResultStatus[CheckResultStatus.FAILED],
        ],
        description: 'List of allowed Check Result Statuses',
        required: false,
    })
    @IsOptional()
    @IsEnum(CheckResultStatus, { each: true })
    allowedCheckResultStatuses?: CheckResultStatus[];

    @ApiProperty({
        enum: CheckStatus,
        isArray: true,
        example: [CheckStatus[CheckStatus.ENABLED], CheckStatus[CheckStatus.DISABLED]],
        description: 'List of allowed Check Result Statuses',
        required: false,
    })
    @IsOptional()
    allowedStatuses?: CheckStatus[];

    @ApiProperty({
        type: 'string',
        enum: MonitorV2TestTypeOptions,
        isArray: true,
        example: [
            MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.DRATA],
            MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.CUSTOM_PUBLISHED],
        ],
        description: 'List of allowed Test types',
        required: false,
    })
    @IsOptional()
    @Transform(({ value }) => (typeof value === 'string' ? [value] : value))
    allowedTestSources?: string[];

    @ApiProperty({
        enum: TaskManagementStatus,
        isArray: true,
        example: [TaskManagementStatus[TaskManagementStatus.IN_PROGRESS]],
        description: 'List of allowed Ticket Status',
        required: false,
    })
    @IsOptional()
    allowedTicketStatuses?: TaskManagementStatus[];

    @ApiProperty({
        type: 'string',
        isArray: true,
        example: ['AWS-PROD'],
        description: 'List of Connections',
        required: false,
    })
    @IsOptional()
    allowedConnections?: string[];

    @ApiProperty({
        type: 'string',
        isArray: true,
        example: ['DCF-1', 'DCF-9'],
        description: 'List of Control Codes',
        required: false,
    })
    @IsOptional()
    allowedControls?: string[];

    @ApiProperty({
        type: 'string',
        isArray: true,
        example: ['hipaa', 'cobit'],
        description: 'List of Frameworks',
        required: false,
    })
    @IsOptional()
    allowedFrameworks?: string[];

    @ApiProperty({
        type: 'string',
        isArray: true,
        example: ['IN_DRATA', 'INFRASTRUCTURE'],
        description: 'List of Categories',
        required: false,
    })
    @IsOptional()
    allowedCategories?: string[];

    @ApiProperty({
        type: 'boolean',
        example: false,
        description: 'Is Draft',
        required: false,
    })
    @TransformBoolean()
    @IsOptional()
    isDraft?: boolean;

    @ApiProperty({
        type: 'boolean',
        example: false,
        description: 'Is New',
        required: false,
    })
    @TransformBoolean()
    @IsOptional()
    isNew?: boolean;

    @ApiProperty({
        type: 'number',
        isArray: true,
        required: false,
        example: [221, 104, 108],
        description: 'Array of test IDs to query',
    })
    @IsOptional()
    @ArrayMaxSize(50)
    @IsPositive({ each: true, message: 'Each element in the array must be a positive number.' })
    @TransformToArrayOfUniqueNumbers()
    testIds?: number[];
}
