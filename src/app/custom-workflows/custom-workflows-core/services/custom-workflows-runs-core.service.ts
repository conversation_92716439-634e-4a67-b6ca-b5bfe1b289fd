import { CustomWorkflowErrorType, CustomWorkflowRunStatus, SortDir, SortType } from '@drata/enums';
import { Injectable } from '@nestjs/common';
import { EvidenceType } from 'app/custom-workflows/custom-workflows-common/enums/evidence-type.enum';
import { CustomWorkflowEvidenceScope } from 'app/custom-workflows/custom-workflows-common/types/custom-workflow-evidence-scope.type';
import { CustomWorkflowRiskScope } from 'app/custom-workflows/custom-workflows-common/types/custom-workflow-risk-scope.type';
import {
    ControlIdRunStateType,
    CustomWorkflowRunStateType,
    EvidenceIdRunStateType,
    RiskIdRunStateType,
} from 'app/custom-workflows/custom-workflows-common/types/custom-workflow-run-state.type';
import { CustomWorkflowRunStepRunData } from 'app/custom-workflows/custom-workflows-common/types/custom-workflow-run-step-run-data.type';
import { CustomWorkflowError } from 'app/custom-workflows/custom-workflows-common/utilities/custom-workflow-error';
import { buildCustomWorkflowRunStepFromEntity } from 'app/custom-workflows/custom-workflows-core/adapters/custom-workflow-run-step.adapter';
import { buildCustomWorkflowRunFromEntity } from 'app/custom-workflows/custom-workflows-core/adapters/custom-workflow-runs.adapter';
import { CustomWorkflowsControlRepository } from 'app/custom-workflows/custom-workflows-core/custom-repositories/custom-workflows-control.repository';
import { CustomWorkflowsEvidenceRepository } from 'app/custom-workflows/custom-workflows-core/custom-repositories/custom-workflows-evidence.repository';
import { CustomWorkflowsExternalEvidenceRepository } from 'app/custom-workflows/custom-workflows-core/custom-repositories/custom-workflows-external-evidence.repository';
import { CustomWorkflowsRiskRepository } from 'app/custom-workflows/custom-workflows-core/custom-repositories/custom-workflows-risk.repository';
import { CustomWorkflowsRunRepository } from 'app/custom-workflows/custom-workflows-core/custom-repositories/custom-workflows-run.repository';
import { CustomWorkflowRunStateEntity } from 'app/custom-workflows/custom-workflows-core/entities/custom-workflow-run-state.entity';
import { CustomWorkflowRunStepEntity } from 'app/custom-workflows/custom-workflows-core/entities/custom-workflow-run-step.entity';
import { CustomWorkflowRunEntity } from 'app/custom-workflows/custom-workflows-core/entities/custom-workflow-run.entity';
import { CustomWorkflowControlScope } from 'app/custom-workflows/custom-workflows-core/types/custom-workflow-control-scope.type';
import { CustomWorkflowEvidenceRunStateObject } from 'app/custom-workflows/custom-workflows-core/types/custom-workflow-evidence-run-state-object.type';
import { CustomWorkflowRunListAndCount } from 'app/custom-workflows/custom-workflows-core/types/custom-workflow-run-list-and-count.type';
import { CustomWorkflowRunStep } from 'app/custom-workflows/custom-workflows-core/types/custom-workflow-run-step.type';
import { CustomWorkflowRun } from 'app/custom-workflows/custom-workflows-core/types/custom-workflow-run.type';
import { User } from 'app/users/entities/user.entity';
import { TenancyTransaction } from 'commons/decorators/transaction';
import { AppService } from 'commons/services/app.service';
import config from 'config';
import { get, isEmpty, isNil } from 'lodash';
import { Span } from 'nestjs-ddtrace';
import { In, Repository } from 'typeorm';

@Injectable()
export class CustomWorkflowsRunsCoreService extends AppService {
    @Span()
    public async listCustomWorkflowRuns(
        workflowId: number,
        status?: CustomWorkflowRunStatus,
        page?: number,
        limit?: number,
        sort?: SortType,
        sortDir?: SortDir,
    ): Promise<CustomWorkflowRunListAndCount> {
        const { workflowRuns, count } =
            await this.customWorkflowsRunRepository.getCustomWorkflowRuns(
                page ?? config.get('pagination.page'),
                limit ?? config.get('pagination.limit'),
                workflowId,
                status,
                sort,
                sortDir,
            );

        return {
            workflowRuns: await Promise.all(
                workflowRuns.map(async run => this.buildCustomWorkflowRun(run)),
            ),
            count,
        };
    }

    @Span()
    public async getCustomWorkflowRun(
        workflowId: number,
        runId: number,
    ): Promise<CustomWorkflowRun> {
        return this.buildCustomWorkflowRun(
            await this.customWorkflowsRunRepository.getCustomWorkflowRun(workflowId, runId),
        );
    }

    @Span()
    public async createCustomWorkflowRun(
        customWorkflowDefinitionId: number,
        customWorkflowDefinitionVersionId: number,
        initialObjectState: CustomWorkflowRunStateType,
        waasRunId?: string,
    ): Promise<CustomWorkflowRun> {
        const run = new CustomWorkflowRunEntity();
        run.customWorkflowDefinitionVersionId = customWorkflowDefinitionVersionId;
        run.status = CustomWorkflowRunStatus.STARTED;
        run.waasRunId = waasRunId;

        const initialRunState = new CustomWorkflowRunStateEntity();
        initialRunState.customWorkflowRunId = run.id;
        initialRunState.state = initialObjectState;

        run.runStates = [initialRunState];

        await this.customWorkflowsRunRepository.save(run);

        const hydratedRun = await this.customWorkflowsRunRepository.getCustomWorkflowRun(
            customWorkflowDefinitionId,
            run.id,
        );
        return this.buildCustomWorkflowRun(hydratedRun);
    }

    @Span()
    public async startWorkflowRunStep(
        stepData: CustomWorkflowRunStepRunData,
        customWorkflowRunId: number,
        childWorkflowId: string,
    ): Promise<CustomWorkflowRunStep> {
        const runStep = new CustomWorkflowRunStepEntity();
        runStep.customWorkflowStepDefinitionId = stepData.id;
        runStep.customWorkflowRunId = customWorkflowRunId;
        runStep.status = CustomWorkflowRunStatus.STARTED;
        runStep.waasRunId = childWorkflowId;

        await this.customWorkflowRunStepEntityRepository.save(runStep);

        return buildCustomWorkflowRunStepFromEntity(
            await this.customWorkflowRunStepEntityRepository.findOneByOrFail({
                id: runStep.id,
            }),
        );
    }

    @TenancyTransaction()
    @Span()
    public async finishWorkflowRunStep(
        customWorkflowRunStepId: number,
        childOutputData: CustomWorkflowRunStateType,
    ): Promise<CustomWorkflowRunStep> {
        const runStep = await this.customWorkflowRunStepEntityRepository.findOneBy({
            id: customWorkflowRunStepId,
        });

        if (isNil(runStep)) {
            throw new CustomWorkflowError(
                CustomWorkflowErrorType.NO_RUN_STEP_FOUND,
                'Run step that was supposed to be running was not found',
                false,
            );
        }

        runStep.status = CustomWorkflowRunStatus.COMPLETED;
        await this.customWorkflowRunStepEntityRepository.save(runStep);

        // We should record any state from the output if it exists
        const state = new CustomWorkflowRunStateEntity();
        state.customWorkflowRunId = runStep.customWorkflowRunId;
        state.customWorkflowRunStepId = runStep.id;
        state.state = childOutputData ?? undefined;

        await this.customWorkflowRunStateEntityRepository.save(state);

        return buildCustomWorkflowRunStepFromEntity(
            await this.customWorkflowRunStepEntityRepository.findOneByOrFail({
                id: runStep.id,
            }),
        );
    }

    @Span()
    public async markWorkflowRunStepErrored(
        customWorkflowRunStepId: number,
        error: Error,
    ): Promise<CustomWorkflowRunStep> {
        const runStep = await this.customWorkflowRunStepEntityRepository.findOneBy({
            id: customWorkflowRunStepId,
        });

        if (isNil(runStep)) {
            throw new CustomWorkflowError(
                CustomWorkflowErrorType.NO_RUN_STEP_FOUND,
                'Run step that was supposed to be running was not found',
                false,
            );
        }

        runStep.status = CustomWorkflowRunStatus.ERRORED;
        const firstCause = get(error, 'cause.type', null);
        const secondCause = get(error, 'cause.cause.type', null);
        runStep.message = firstCause || secondCause || error.message;

        await this.customWorkflowRunStepEntityRepository.save(runStep);

        return this.buildCustomWorkflowRunStep(runStep);
    }

    @Span()
    public async finishWorkflowRun(customWorkflowRunId: number): Promise<CustomWorkflowRun> {
        const run = await this.customWorkflowsRunRepository.findOneBy({
            id: customWorkflowRunId,
        });

        if (isNil(run)) {
            throw new CustomWorkflowError(
                CustomWorkflowErrorType.NO_RUN_FOUND,
                'Run that was supposed to be running was not found',
                false,
            );
        }

        run.status = CustomWorkflowRunStatus.COMPLETED;
        await this.customWorkflowsRunRepository.save(run);

        return this.buildCustomWorkflowRun(run);
    }

    @Span()
    public async markWorkflowRunErrored(
        customWorkflowRunId: number,
        error: CustomWorkflowError,
    ): Promise<CustomWorkflowRun> {
        const run = await this.customWorkflowsRunRepository.findOneBy({
            id: customWorkflowRunId,
        });

        if (isNil(run)) {
            throw new CustomWorkflowError(
                CustomWorkflowErrorType.NO_RUN_FOUND,
                'Run step that was supposed to be running was not found',
                false,
            );
        }

        run.status = CustomWorkflowRunStatus.ERRORED;
        run.message = error.message;

        await this.customWorkflowsRunRepository.save(run);

        return this.buildCustomWorkflowRun(run);
    }

    private async buildCustomWorkflowRunStep(
        runStep: CustomWorkflowRunStepEntity,
    ): Promise<CustomWorkflowRunStep> {
        const { scopeObjects, users } = await this.buildCustomWorkflowRunData(runStep.runStates);
        return buildCustomWorkflowRunStepFromEntity(
            runStep,
            runStep.runStates,
            scopeObjects,
            users,
        );
    }

    private async buildCustomWorkflowRun(run: CustomWorkflowRunEntity): Promise<CustomWorkflowRun> {
        const { scopeObjects, users } = await this.buildCustomWorkflowRunData(run.runStates);
        return buildCustomWorkflowRunFromEntity(run, scopeObjects, users);
    }

    private async buildCustomWorkflowRunData(runStates: CustomWorkflowRunStateEntity[] = []) {
        // Extract all IDs in a single pass
        const { controlIds, riskIds, ownerIds, evidenceData } =
            this.extractIdsFromRunStates(runStates);

        // Fetch all required data in parallel
        const [users, controls, risks, evidence] = await Promise.all([
            !isEmpty(ownerIds) ? this.getUsersByIds(ownerIds) : Promise.resolve([]),
            !isEmpty(controlIds) ? this.getControlsByIds(controlIds) : Promise.resolve([]),
            !isEmpty(riskIds) ? this.getRisksByIds(riskIds) : Promise.resolve([]),
            !isEmpty(evidenceData) ? this.getEvidenceByIds(evidenceData) : Promise.resolve([]),
        ]);

        // Combine controls and risks
        return { scopeObjects: [...controls, ...risks, ...evidence], users };
    }

    private async getControlsByIds(controlIds: number[]): Promise<CustomWorkflowControlScope[]> {
        return this.customWorkflowsControlRepository.findWorkflowScopedControlsByIds(controlIds);
    }

    private async getRisksByIds(riskIds: number[]): Promise<CustomWorkflowRiskScope[]> {
        return this.customWorkflowsRiskRepository.findWorkflowScopeRisksByIds(riskIds);
    }

    private async getEvidenceByIds(
        evidenceData: CustomWorkflowEvidenceRunStateObject[],
    ): Promise<CustomWorkflowEvidenceScope[]> {
        const externalEvidenceIds = evidenceData
            .filter(e => e.type === EvidenceType.EXTERNAL_EVIDENCE)
            .map(e => e.id);
        const libraryDocumentIds = evidenceData
            .filter(e => e.type === EvidenceType.LIBRARY_DOCUMENT)
            .map(e => e.id);

        if (isEmpty(externalEvidenceIds) && isEmpty(libraryDocumentIds)) {
            return [];
        }

        const [externalEvidence, libraryEvidence] = await Promise.all([
            !isEmpty(externalEvidenceIds)
                ? this.customWorkflowsExternalEvidenceRepository.findEvidenceByIds(
                      externalEvidenceIds,
                  )
                : Promise.resolve([]),
            !isEmpty(libraryDocumentIds)
                ? this.customWorkflowsEvidenceRepository.findEvidenceByIds(libraryDocumentIds, true)
                : Promise.resolve([]),
        ]);

        return [...externalEvidence, ...libraryEvidence];
    }

    private async getUsersByIds(userIds: number[]): Promise<User[]> {
        if (isEmpty(userIds)) {
            return [];
        }
        return this.userRepository.find({ where: { id: In(userIds) } });
    }

    private extractIdsFromRunStates(runStates: CustomWorkflowRunStateEntity[]): {
        controlIds: number[];
        riskIds: number[];
        ownerIds: number[];
        evidenceData: CustomWorkflowEvidenceRunStateObject[];
    } {
        const controlIds = new Set<number>();
        const riskIds = new Set<number>();
        const ownerIds = new Set<number>();
        const evidenceData = new Map<number, EvidenceType>();

        for (const runState of runStates) {
            const state = runState.state;
            if (isNil(state)) continue;

            if (state.ownerId) {
                ownerIds.add(state.ownerId);
            }

            // Type-safe extraction of control and risk IDs
            if (this.isControlIdRunState(state)) {
                controlIds.add(state.controlId);
            } else if (this.isRiskIdRunState(state)) {
                riskIds.add(state.riskId);
            } else if (this.isEvidenceIdRunState(state)) {
                if (
                    evidenceData.has(state.evidenceId) &&
                    evidenceData.get(state.evidenceId) === state.evidenceType
                ) {
                    // If we already have this evidence ID and it's the same type, skip
                    continue;
                }
                evidenceData.set(state.evidenceId, state.evidenceType);
            }
        }

        return {
            controlIds: Array.from(controlIds),
            riskIds: Array.from(riskIds),
            ownerIds: Array.from(ownerIds),
            evidenceData: Array.from(evidenceData, ([key, value]) => ({
                id: key,
                type: value,
            })),
        };
    }

    private isControlIdRunState(state: CustomWorkflowRunStateType): state is ControlIdRunStateType {
        return 'controlId' in state && typeof state.controlId === 'number';
    }

    private isRiskIdRunState(state: CustomWorkflowRunStateType): state is RiskIdRunStateType {
        return 'riskId' in state && typeof state.riskId === 'number';
    }

    private isEvidenceIdRunState(
        state: CustomWorkflowRunStateType,
    ): state is EvidenceIdRunStateType {
        return 'evidenceId' in state && typeof state.evidenceId === 'number';
    }

    private get customWorkflowsRunRepository(): CustomWorkflowsRunRepository {
        return this.getCustomTenantRepository(CustomWorkflowsRunRepository);
    }
    private get customWorkflowRunStepEntityRepository(): Repository<CustomWorkflowRunStepEntity> {
        return this.getTenantRepository(CustomWorkflowRunStepEntity);
    }
    private get customWorkflowRunStateEntityRepository(): Repository<CustomWorkflowRunStateEntity> {
        return this.getTenantRepository(CustomWorkflowRunStateEntity);
    }
    private get userRepository(): Repository<User> {
        return this.getTenantRepository(User);
    }
    private get customWorkflowsControlRepository(): CustomWorkflowsControlRepository {
        return this.getCustomTenantRepository(CustomWorkflowsControlRepository);
    }
    private get customWorkflowsRiskRepository(): CustomWorkflowsRiskRepository {
        return this.getCustomTenantRepository(CustomWorkflowsRiskRepository);
    }
    private get customWorkflowsEvidenceRepository(): CustomWorkflowsEvidenceRepository {
        return this.getCustomTenantRepository(CustomWorkflowsEvidenceRepository);
    }
    private get customWorkflowsExternalEvidenceRepository(): CustomWorkflowsExternalEvidenceRepository {
        return this.getCustomTenantRepository(CustomWorkflowsExternalEvidenceRepository);
    }
}
