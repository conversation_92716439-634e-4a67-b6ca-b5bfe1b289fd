import { Injectable } from '@nestjs/common';
import { ApplyFormulasToRisksService } from 'app/custom-fields/workflow-services/apply-formulas-to-risks.service';
import { UpdateAdvancedFormulasService } from 'app/custom-fields/workflow-services/update-advanced-formulas.service';
import { DynamicContentActivitiesService } from 'app/dynamic-content/workflow/activities/dynamic-content-activities.service';
import { EventsAppService } from 'app/events/events-app.service';
import { ControlSendNotificationWorkflowService } from 'app/grc/workflow-services/control-send-notification-workflow.service';
import { CustomTasksWorkflowsService } from 'app/grc/workflow-services/custom-tasks.workflow.service';
import { SendNotificationWorkflowsService } from 'app/notifications/workflow-services/send-notification.workflow.service';
import { SchedulerActivityService } from 'app/scheduler/services/scheduler-activity.service';
import { BaseWorkersService } from 'app/worker/base-workers.service';
import { default as accountMigrationActivities } from 'app/worker/workflows/administration/account-migration/activities/account-migration.activities';
import { AccountBackupActivityController } from 'app/worker/workflows/administration/account-snapshots/activities/account-backup-activity.controller';
import { AccountLookupActivityController } from 'app/worker/workflows/administration/account-snapshots/activities/account-lookup-activity.controller';
import { AccountNotificationActivityController } from 'app/worker/workflows/administration/account-snapshots/activities/account-notification-activity.controller';
import { AccountProvisioningActivityController } from 'app/worker/workflows/administration/account-snapshots/activities/account-provisioning-activity.controller';
import { AccountRestoreActivityController } from 'app/worker/workflows/administration/account-snapshots/activities/account-restore-activity.controller';
import { AccountValidationActivityController } from 'app/worker/workflows/administration/account-snapshots/activities/account-validation-activity.controller';
import { default as adminSharedActivities } from 'app/worker/workflows/administration/shared/activities/admin-shared.activities';
import { RunUntilActivityController } from 'app/worker/workflows/administration/support/run-until-activities.controller';
import { ApiActivitiesService } from 'app/worker/workflows/apis/activities/api-activities.service';
import { AssetsDownloadService } from 'app/worker/workflows/assets/activities/assets-download-activity.service';
import { Autopilot2EventsActivityService } from 'app/worker/workflows/autopilot-2-events/activities/autopilot-2-events-activity.service';
import { AutopilotActivityService } from 'app/worker/workflows/autopilot/activities/autopilot-activity.service';
import { BackfillsActivityService } from 'app/worker/workflows/backfills/activities/backfills-activity.service';
import { FlatfileProcessingWorkflowController } from 'app/worker/workflows/bulk-import/activities/flatfile-processing-workflow.controller';
import { SocketNotificationActivityService } from 'app/worker/workflows/common/activities/socket-notifications-activities.service';
import { CompanyArchiveDownloadActivityController } from 'app/worker/workflows/companies/activities/company-archive-download-activity.service';
import { CommonActivityService } from 'app/worker/workflows/composable-workflows/common/activities/common-activity.service';
import { SendWebhookWorkflowController } from 'app/worker/workflows/composable-workflows/send-webhook/activities/send-webhook.workflow.controller';
import { StartApprovalWorkflowController } from 'app/worker/workflows/composable-workflows/start-approval/activities/start-approval-workflow.controller';
import { StartReviewGroupReviewCollectionWorkflowController } from 'app/worker/workflows/composable-workflows/start-review-group-review-collection/activities/start-review-group-review-collection-workflow.controller';
import { ConnectionsActivityController } from 'app/worker/workflows/connections/activities/connections-activity.controller';
import { ControlEvidencePackageActivityService } from 'app/worker/workflows/control-evidence-package/activities/control-evidence-package-activity.service';
import { ControlReadinessWorkflowService } from 'app/worker/workflows/control-readiness/control-readiness-workflow.service';
import { GenerateCustomDataEvidenceActivityService } from 'app/worker/workflows/custom-data/activities/generate-custom-data-evidence-activity.service';
import { CustomWorkflowsDSLController } from 'app/worker/workflows/custom-workflow-dsl/activities/custom-workflows-DSL-workflow.controller';
import { CustomWorkflowsRunTriggerService } from 'app/worker/workflows/custom-workflow-dsl/activities/custom-workflows-run-trigger.service';
import { CustomWorkflowsRunService } from 'app/worker/workflows/custom-workflow-dsl/activities/custom-workflows-run.v1.service';
import { DeleteTenantCompoundService } from 'app/worker/workflows/delete-tenant/activities/base/delete-tenant-compound.service';
import { DeleteDbDataActivityService } from 'app/worker/workflows/delete-tenant/activities/delete-db-data.activity.service';
import { DeleteFormActivityService } from 'app/worker/workflows/delete-tenant/activities/delete-form-activity.service';
import { DeleteS3ObjectsActivityService } from 'app/worker/workflows/delete-tenant/activities/delete-s3-objects-activity.service';
import { HardDeleteTenantActivitiesService } from 'app/worker/workflows/delete-tenant/activities/hard-delete-tenant-activities.service';
import { EvidenceDownloadAllZipActivityController } from 'app/worker/workflows/document-library/evidence/activities/evidence-download-all-zip-activity.controller';
import { SendAllEvidenceByEmailActivityController } from 'app/worker/workflows/document-library/evidence/activities/send-all-evidence-by-email-activity.controller';
import { EmailTemplateActivityController } from 'app/worker/workflows/email-notifications/activities/email-template-activity.controller';
import { EmailActivityController } from 'app/worker/workflows/email-notifications/activities/send-email-activity.controller';
import { EvidenceLibrarySendNotificationWorkflowService } from 'app/worker/workflows/evidence-library/activities/evidence-library-send-notification-workflow.service';
import { ControlEvidenceDownloadActivityService } from 'app/worker/workflows/fr20x-ksi-validation-report/activities/control-evidence-download-activity.service';
import { MachineReadableReportDownloadActivityService } from 'app/worker/workflows/fr20x-ksi-validation-report/activities/machine-readable-report-download-activity.service';
import { FrameworkDeactivationActivityController } from 'app/worker/workflows/framework-deactivation/activities/framework-deactivation-activity.controller';
import { GrcEvidenceDownloadActivityController } from 'app/worker/workflows/grc/activities/grc-evidence-download-activity.controller';
import { TaskNotificationsActivityController } from 'app/worker/workflows/grc/activities/task-notifications-activity.controller';
import { BulkAddTestTemplatesActivityService } from 'app/worker/workflows/library-test-template/activities/bulk-add-test-templates-activity.service';
import { MonitorsBulkActionActivityService } from 'app/worker/workflows/monitors-bulk-actions/activities/monitors-bulk-actions-activity.service';
import { MonitorFindingsCsvActivityController } from 'app/worker/workflows/monitors/findings/activities/monitor-findings-csv-activity.controller';
import { MonitorFindingsZipActivityController } from 'app/worker/workflows/monitors/findings/activities/monitor-findings-zip-activity.controller';
import { CompanyNotificationsActivityService } from 'app/worker/workflows/notifications/activities/company-notifications-activity.service';
import { PersonnelBulkActionActivityController } from 'app/worker/workflows/personnel/activities/personnel-bulk-action-activity.controller';
import { PersonnelBulkActionAttachEvidenceActivityController } from 'app/worker/workflows/personnel/activities/personnel-bulk-action-attach-evidence-activity.controller';
import { PersonnelBulkActionUpdateStatusActivityController } from 'app/worker/workflows/personnel/activities/personnel-bulk-action-update-status-activity.controller';
import { PersonnelReportsCsvActivityController } from 'app/worker/workflows/personnel/activities/personnel-reports-csv-activity.controller';
import { PersonnelValidateEmploymentStatusActivityController } from 'app/worker/workflows/personnel/activities/personnel-validate-employment-status-activity.controller';
import { DownloadPoliciesActivityController } from 'app/worker/workflows/policies/activities/download-policies-activity-controller.service';
import { PolicyAssignmentActivityController } from 'app/worker/workflows/policy-assignment/activities/policy-assignment-activity.controller';
import { HostRemindersV2ActivityController } from 'app/worker/workflows/reminders/host-reminders-v2/activities/host-reminders-v2-activity.controller';
import { OverdueTasksRemindersActivityController } from 'app/worker/workflows/reminders/overdue-tasks-reminders/activities/overdue-tasks-reminders-activity.controller';
import { RegionalRemindersV2ActivityController } from 'app/worker/workflows/reminders/regional-reminders-v2/activities/regional-reminders-v2-activity.controller';
import { RegionalRemindersActivityController } from 'app/worker/workflows/reminders/regional-reminders/activities/regional-reminders-activity.controller';
import { TenantRemindersV2ActivityController } from 'app/worker/workflows/reminders/tenant-reminders-v2/activities/tenant-reminders-v2-activity.controller';
import { TenantRemindersActivityController } from 'app/worker/workflows/reminders/tenant-reminders/activities/tenant-reminders-activity.controller';
import { UpcomingTasksRemindersActivityController } from 'app/worker/workflows/reminders/upcoming-tasks-reminders/activities/upcoming-tasks-reminders-activity.controller';
import { UserRemindersActivityController } from 'app/worker/workflows/reminders/user-reminders/activities/user-reminders-activity.controller';
import { LongRunningWorkflowsKillerActivityController } from 'app/worker/workflows/reporters/activities/long-running-workflows-killer-activity.controller';
import { LongRunningWorkflowsReporterActivityController } from 'app/worker/workflows/reporters/activities/long-running-workflows-reporter-activity.controller';
import { RiskBulkActionsActivityController } from 'app/worker/workflows/risk/activities/risk-bulk-actions-activity.controller';
import { RiskCustomTasksWorkflowsService } from 'app/worker/workflows/risk/activities/risk-custom-task.workflow.service';
import { RiskSendNotificationWorkflowService } from 'app/worker/workflows/risk/activities/risk-send-notification-workflow.service';
import { SalesforceActivityService } from 'app/worker/workflows/salesforce/activities/salesforce-activity.service';
import { SalesforceCommunicationActivityService } from 'app/worker/workflows/salesforce/activities/salesforce-communication-activity.service';
import { SalesforceDrataAccountsSyncService } from 'app/worker/workflows/salesforce/activities/salesforce-drata-accounts-sync.service';
import { SalesforceDrataTenantSyncActivityService } from 'app/worker/workflows/salesforce/activities/salesforce-drata-tenant-sync.service';
import { SalesforceExternalReport } from 'app/worker/workflows/salesforce/activities/salesforce-external-report.service';
import { SalesforceHealthCheckActivityService } from 'app/worker/workflows/salesforce/activities/salesforce-health-check-activity.service';
import { OptInSharingAccountActivityService } from 'app/worker/workflows/shared-account/activities/opt-in-sharing-account-activity.service';
import { SharedPoolPocActivityController } from 'app/worker/workflows/shared-pool-poc/activities/shared-pool-poc-activity.controller';
import { SupportTestActivityService } from 'app/worker/workflows/support/activities/support-test-activity.service';
import { TimerTestActivityController } from 'app/worker/workflows/support/activities/timer-test-activities.controller';
import { SynchronizationsActivityService } from 'app/worker/workflows/synchronizations/activities/synchronizations-activity.service';
import { TrustCenterReportActivityService } from 'app/worker/workflows/trust-center/activities/trust-center-report-activity.service';
import { TrustCenterSalesforceActivityService } from 'app/worker/workflows/trust-center/activities/trust-center-salesforce-activity.service';
import { VendorDocumentActivityService } from 'app/worker/workflows/vendor/activities/vendor-document-activity.service';
import { VendorHubQuestionnaireActivityService } from 'app/worker/workflows/vendor/activities/vendor-hub-questionnaire-activity.service';
import { VendorHubQuestionnaireFileActivityService } from 'app/worker/workflows/vendor/activities/vendor-hub-questionnaire-file-activity.service';
import { VendorMonthlyDigestActivityService } from 'app/worker/workflows/vendor/activities/vendor-monthly-digest-activity.service';
import { VendorQuestionnaireActivityService } from 'app/worker/workflows/vendor/activities/vendor-questionnaire-activity.service';
import { VendorQuestionnaireSentReassignmentService } from 'app/worker/workflows/vendor/activities/vendor-questionnaire-sent-reassignment.service';
import { VendorQuestionnaireSummaryActivityService } from 'app/worker/workflows/vendor/activities/vendor-questionnaire-summary-activity.service';
import { VendorReportDownloadActivityController } from 'app/worker/workflows/vendor/activities/vendor-report-download-activity.controller';
import { VendorScheduleQuestionnaireActivityService } from 'app/worker/workflows/vendor/activities/vendor-schedule-questionnaire-activity.service';
import { VulnerabilitiesMonitoringActivityService } from 'app/worker/workflows/vulnerabilities/activities/vulnerabilities-monitoring-activity.service';
import { DeleteWorkspaceActivityService } from 'app/worker/workflows/workspace/activities/delete-workspace-activity.service';
import { RestoreWorkspaceActivityService } from 'app/worker/workflows/workspace/activities/restore-workspace-activity.service';
import { AccountsCoreService } from 'auth/services/accounts-core.service';
import { AuthService } from 'auth/services/auth.service';

@Injectable()
export class WorkersService extends BaseWorkersService {
    /**
     * This must name all dependencies as lowercased classname for automatic activity registration to work!
     *
     * e.g.,
     *
     * protected apiActivitiesService: ApiActivitiesService, // works
     *
     * protected activitiesService: ApiActivitiesService, // will not work
     *
     */
    constructor(
        protected accountsCoreService: AccountsCoreService,
        protected accountBackupActivityController: AccountBackupActivityController,
        protected accountLookupActivityController: AccountLookupActivityController,
        protected accountProvisioningActivityController: AccountProvisioningActivityController,
        protected accountRestoreActivityController: AccountRestoreActivityController,
        protected accountNotificationActivityController: AccountNotificationActivityController,
        protected accountValidationActivityController: AccountValidationActivityController,
        protected apiActivitiesService: ApiActivitiesService,
        protected applyFormulasToRisksService: ApplyFormulasToRisksService,
        protected assetsDownloadService: AssetsDownloadService,
        protected authService: AuthService,
        protected autopilot2EventsActivityService: Autopilot2EventsActivityService,
        protected autopilotActivityService: AutopilotActivityService,
        protected backfillsActivityService: BackfillsActivityService,
        protected bulkAddTestTemplatesActivityService: BulkAddTestTemplatesActivityService,
        protected commonActivityService: CommonActivityService,
        protected companyArchiveDownloadActivityController: CompanyArchiveDownloadActivityController,
        protected companyNotificationsActivityService: CompanyNotificationsActivityService,
        protected connectionsActivityController: ConnectionsActivityController,
        protected controlEvidenceDownloadActivityService: ControlEvidenceDownloadActivityService,
        protected controlEvidencePackageActivityService: ControlEvidencePackageActivityService,
        protected controlReadinessWorkflowService: ControlReadinessWorkflowService,
        protected controlSendNotificationWorkflowService: ControlSendNotificationWorkflowService,
        protected customTasksWorkflowsService: CustomTasksWorkflowsService,
        protected customWorkflowsDSLController: CustomWorkflowsDSLController,
        protected customWorkflowsRunService: CustomWorkflowsRunService,
        protected customWorkflowsRunTriggerService: CustomWorkflowsRunTriggerService,
        protected deleteFormActivityService: DeleteFormActivityService,
        protected deleteS3ObjectsActivityService: DeleteS3ObjectsActivityService,
        protected deleteTenantCompoundService: DeleteTenantCompoundService,
        protected deleteWorkspaceActivityService: DeleteWorkspaceActivityService,
        protected downloadPoliciesActivityController: DownloadPoliciesActivityController,
        protected dynamicContentActivitiesService: DynamicContentActivitiesService,
        protected emailActivityController: EmailActivityController,
        protected emailTemplateActivityController: EmailTemplateActivityController,
        protected eventsAppService: EventsAppService,
        protected evidenceDownloadAllZipActivityController: EvidenceDownloadAllZipActivityController,
        protected evidenceLibrarySendNotificationWorkflowService: EvidenceLibrarySendNotificationWorkflowService,
        protected flatfileProcessingWorkflowController: FlatfileProcessingWorkflowController,
        protected frameworkDeactivationActivityController: FrameworkDeactivationActivityController,
        protected generateCustomDataEvidenceActivityService: GenerateCustomDataEvidenceActivityService,
        protected grcEvidenceDownloadActivityController: GrcEvidenceDownloadActivityController,
        protected hostRemindersV2ActivityController: HostRemindersV2ActivityController,
        protected longRunningWorkflowsKillerActivityController: LongRunningWorkflowsKillerActivityController,
        protected longRunningWorkflowsReporterActivityController: LongRunningWorkflowsReporterActivityController,
        protected machineReadableReportDownloadActivityService: MachineReadableReportDownloadActivityService,
        protected monitorsBulkActionActivityService: MonitorsBulkActionActivityService,
        protected monitorFindingsZipActivityController: MonitorFindingsZipActivityController,
        protected monitorFindingsCsvActivityController: MonitorFindingsCsvActivityController,
        protected optInSharingAccountActivityService: OptInSharingAccountActivityService,
        protected overdueTasksRemindersActivityController: OverdueTasksRemindersActivityController,
        protected personnelBulkActionActivityController: PersonnelBulkActionActivityController,
        protected personnelBulkActionAttachEvidenceActivityController: PersonnelBulkActionAttachEvidenceActivityController,
        protected personnelBulkActionUpdateStatusActivityController: PersonnelBulkActionUpdateStatusActivityController,
        protected personnelReportsCsvActivityController: PersonnelReportsCsvActivityController,
        protected personnelValidateEmploymentStatusActivityController: PersonnelValidateEmploymentStatusActivityController,
        protected policyAssignmentActivityController: PolicyAssignmentActivityController,
        protected regionalRemindersActivityController: RegionalRemindersActivityController,
        protected regionalRemindersV2ActivityController: RegionalRemindersV2ActivityController,
        protected restoreWorkspaceActivityService: RestoreWorkspaceActivityService,
        protected riskBulkActionsActivityController: RiskBulkActionsActivityController,
        protected riskCustomTasksWorkflowsService: RiskCustomTasksWorkflowsService,
        protected riskSendNotificationWorkflowService: RiskSendNotificationWorkflowService,
        protected runUntilActivityController: RunUntilActivityController,
        protected salesforceActivityService: SalesforceActivityService,
        protected salesforceCommunicationActivityService: SalesforceCommunicationActivityService,
        protected salesforceDrataAccountsSyncService: SalesforceDrataAccountsSyncService,
        protected salesforceDrataTenantSyncActivityService: SalesforceDrataTenantSyncActivityService,
        protected salesforceExternalReport: SalesforceExternalReport,
        protected salesforceHealthCheckActivityService: SalesforceHealthCheckActivityService,
        protected schedulerActivityService: SchedulerActivityService,
        protected sendAllEvidenceByEmailActivityController: SendAllEvidenceByEmailActivityController,
        protected sendNotificationWorkflowsService: SendNotificationWorkflowsService,
        protected sendWebhookWorkflowController: SendWebhookWorkflowController,
        protected sharedPoolPocActivityController: SharedPoolPocActivityController,
        protected socketNotificationActivityService: SocketNotificationActivityService,
        protected startApprovalWorkflowController: StartApprovalWorkflowController,
        protected startReviewGroupReviewCollectionWorkflowController: StartReviewGroupReviewCollectionWorkflowController,
        protected supportTestActivityService: SupportTestActivityService,
        protected synchronizationsActivityService: SynchronizationsActivityService,
        protected taskNotificationsActivityController: TaskNotificationsActivityController,
        protected tenantRemindersActivityController: TenantRemindersActivityController,
        protected tenantRemindersV2ActivityController: TenantRemindersV2ActivityController,
        protected timerTestActivityController: TimerTestActivityController,
        protected trustCenterReportActivityService: TrustCenterReportActivityService,
        protected trustCenterSalesforceActivityService: TrustCenterSalesforceActivityService,
        protected updateAdvancedFormulasService: UpdateAdvancedFormulasService,
        protected upcomingTasksRemindersActivityController: UpcomingTasksRemindersActivityController,
        protected userRemindersActivityController: UserRemindersActivityController,
        protected vendorDocumentActivityService: VendorDocumentActivityService,
        protected vendorHubQuestionnaireActivityService: VendorHubQuestionnaireActivityService,
        protected vendorHubQuestionnaireFileActivityService: VendorHubQuestionnaireFileActivityService,
        protected vendorMonthlyDigestActivityService: VendorMonthlyDigestActivityService,
        protected vendorQuestionnaireActivityService: VendorQuestionnaireActivityService,
        protected vendorQuestionnaireSentReassignmentService: VendorQuestionnaireSentReassignmentService,
        protected vendorQuestionnaireSummaryActivityService: VendorQuestionnaireSummaryActivityService,
        protected vendorReportDownloadActivityController: VendorReportDownloadActivityController,
        protected vendorScheduleQuestionnaireActivityService: VendorScheduleQuestionnaireActivityService,
        protected vulnerabilitiesMonitoringActivityService: VulnerabilitiesMonitoringActivityService,
        protected deleteDbDataActivityService: DeleteDbDataActivityService,
        protected hardDeleteTenantActivitiesService: HardDeleteTenantActivitiesService,
    ) {
        super(accountsCoreService);
        this.activityFunctions = { ...accountMigrationActivities, ...adminSharedActivities };
    }
}
