import { SalesforceHealthCheckActivityService } from 'app/worker/workflows/salesforce/activities/salesforce-health-check-activity.service';
import { SalesforceCrm } from 'dependencies/crm/salesforce/salesforce-crm';

describe('SalesforceHealthCheckActivityService', () => {
    let service: SalesforceHealthCheckActivityService;
    let mockSalesforceService: jest.Mocked<SalesforceCrm>;

    beforeEach(() => {
        mockSalesforceService = {
            healthCheck: jest.fn(),
        } as any;

        service = new SalesforceHealthCheckActivityService(mockSalesforceService);
    });

    describe('performHealthCheck', () => {
        it('should return success result when health check passes', async () => {
            const mockOpportunity = {
                Id: 'test-opportunity-id',
                Name: 'Test Opportunity',
                StageName: 'Prospecting',
            };

            mockSalesforceService.healthCheck.mockResolvedValue(mockOpportunity);

            const result = await service.performHealthCheck();

            expect(result).toEqual({
                success: true,
                opportunity: mockOpportunity,
                timestamp: expect.any(String),
            });
            expect(mockSalesforceService.healthCheck).toHaveBeenCalledTimes(1);
        });

        it('should throw error when no opportunity is found', async () => {
            mockSalesforceService.healthCheck.mockResolvedValue(null);

            await expect(service.performHealthCheck()).rejects.toThrow('No opportunity found');
            expect(mockSalesforceService.healthCheck).toHaveBeenCalledTimes(1);
        });

        it('should throw error when health check fails', async () => {
            const error = new Error('Salesforce connection failed');
            mockSalesforceService.healthCheck.mockRejectedValue(error);

            await expect(service.performHealthCheck()).rejects.toThrow(
                'Salesforce connection failed',
            );
            expect(mockSalesforceService.healthCheck).toHaveBeenCalledTimes(1);
        });
    });
});
