import { Injectable } from '@nestjs/common';
import { ActivityNs } from 'app/worker/activity-ns.decorator';
import { BaseService } from 'commons/services/base.service';
import { Crm } from 'dependencies/crm/crm';

@Injectable()
export class SalesforceHealthCheckActivityService extends BaseService {
    constructor(private readonly salesforceService: Crm) {
        super();
    }

    @ActivityNs()
    async performHealthCheck(): Promise<any> {
        this.log('Starting Salesforce health check activity');

        try {
            const result = await this.salesforceService.healthCheck();

            if (!result) {
                const error = new Error('No opportunity found');
                this.error(error, undefined, { opportunityFound: false });
                throw error;
            }

            this.log('Salesforce health check activity completed successfully', undefined, {
                opportunityFound: !!result,
                opportunityId: result?.Id || null,
            });

            return {
                success: true,
                opportunity: result,
                timestamp: new Date().toISOString(),
            };
        } catch (error) {
            this.error(error, undefined, {
                error: error.message,
            });
            throw error;
        }
    }
}
