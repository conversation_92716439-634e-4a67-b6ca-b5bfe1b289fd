import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { FrameworksCoreModule } from 'app/frameworks/frameworks-core.module';
import { SalesforceActivityService } from 'app/worker/workflows/salesforce/activities/salesforce-activity.service';
import { SalesforceCommunicationActivityService } from 'app/worker/workflows/salesforce/activities/salesforce-communication-activity.service';
import { SalesforceDrataAccountsSyncService } from 'app/worker/workflows/salesforce/activities/salesforce-drata-accounts-sync.service';
import { SalesforceDrataTenantSyncActivityService } from 'app/worker/workflows/salesforce/activities/salesforce-drata-tenant-sync.service';
import { SalesforceDryRunService } from 'app/worker/workflows/salesforce/activities/salesforce-dry-run.service';
import { SalesforceExternalReport } from 'app/worker/workflows/salesforce/activities/salesforce-external-report.service';
import { SalesforceHealthCheckActivityService } from 'app/worker/workflows/salesforce/activities/salesforce-health-check-activity.service';
import { AccountRepository } from 'auth/repositories/account.repository';
import { EntryRepository } from 'auth/repositories/entry.repository';
import { RefreshTokenRepository } from 'auth/repositories/refresh-token.repository';
import { CommonsModule } from 'commons/commons.module';
import { ModuleType, ModuleTypes } from 'commons/decorators/module-type.decorator';
import { TypeOrmExtensionsModule } from 'database/typeorm/typeorm.extensions.module';
import { AccountEntitlementRepository } from 'entitlements/repositories/entitlements.repository';
import { AccountCurrentContractAddonMap } from 'plan-and-usage/entities/account-current-contract-addon-map.entity';
import { AccountCurrentContract } from 'plan-and-usage/entities/account-current-contract.entity';
import { Plan } from 'plan-and-usage/entities/plan.entity';
import { ProductsBuilderCoreModule } from 'self-service-invite/products-builder-core.module';
import { FrameworkTemplateRepository } from 'site-admin/repositories/framework-template.repository';

@ModuleType(ModuleTypes.COMMERCE)
@Module({
    imports: [
        FrameworksCoreModule,
        CommonsModule,
        TypeOrmExtensionsModule.forGlobalCustomRepository([
            AccountRepository,
            FrameworkTemplateRepository,
            AccountEntitlementRepository,
            AccountCurrentContractAddonMap,
            EntryRepository,
            RefreshTokenRepository,
        ]),
        TypeOrmModule.forFeature([AccountCurrentContract, Plan, AccountCurrentContractAddonMap]),
        ProductsBuilderCoreModule,
    ],
    providers: [
        SalesforceActivityService,
        SalesforceCommunicationActivityService,
        SalesforceDrataTenantSyncActivityService,
        SalesforceDryRunService,
        SalesforceExternalReport,
        SalesforceDrataAccountsSyncService,
        SalesforceHealthCheckActivityService,
    ],
    exports: [
        SalesforceActivityService,
        SalesforceCommunicationActivityService,
        SalesforceDrataTenantSyncActivityService,
        SalesforceDrataAccountsSyncService,
        SalesforceExternalReport,
        SalesforceHealthCheckActivityService,
    ],
})
export class SalesforceActivities {}
