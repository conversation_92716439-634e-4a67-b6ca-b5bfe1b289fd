import { proxyActivitiesForService } from 'app/worker/helpers/worker.helper';
import { SalesforceHealthCheckActivityService } from 'app/worker/workflows/salesforce/activities/salesforce-health-check-activity.service';

const { SalesforceHealthCheckActivityService_performHealthCheck } = proxyActivitiesForService<
    typeof SalesforceHealthCheckActivityService
>({
    startToCloseTimeout: '5 minutes',
    retry: {
        maximumAttempts: 3,
    },
});

export async function salesforceHealthCheckWorkflowV1(): Promise<any> {
    return SalesforceHealthCheckActivityService_performHealthCheck();
}
