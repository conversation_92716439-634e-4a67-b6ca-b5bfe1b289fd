import { CustomWorkflowTrigger, SocketEvent } from '@drata/enums';
import { Injectable } from '@nestjs/common';
import { ApplicationFailure } from '@temporalio/workflow';
import { ApprovalConfigurationCoreService } from 'app/approvals/v2/approval-configuration-core/services/approval-configuration-core.service';
import { ConsensusRule } from 'app/approvals/v2/approval-configuration-core/types/consensus-rule.enum';
import { ReviewGroupCoreService } from 'app/approvals/v2/approval-review-groups-core/services/review-group-core.service';
import { ReviewGroup } from 'app/approvals/v2/approval-review-groups-core/types/review-group.type';
import { ReviewStatus } from 'app/approvals/v2/approval-review-groups-core/types/review-status.enum';
import { UpdateReviewGroupRequest } from 'app/approvals/v2/approval-review-groups-core/types/update-review-group-request.type';
import { ApprovalsCoreService } from 'app/approvals/v2/approvals-core/services/approvals-core.service';
import { ApprovalStatusType } from 'app/approvals/v2/approvals-core/types/approval-status.enum';
import { ApprovalsOrchestrationService } from 'app/approvals/v2/approvals-orchestration/services/approvals-orchestration.service';
import { CustomAssignmentCoreService } from 'app/custom-assignment-core/services/custom-assignment-core.service';
import {
    ObjectContext,
    SupportedObjectTypes,
} from 'app/custom-assignment-core/types/object-context.type';
import { PolicyApprovalsAssociationEntity } from 'app/policy-approvals/policy-approvals-commerce/entities/policy-approvals-associations.entity';
import { PolicyApprovalsCommerceService } from 'app/policy-approvals/policy-approvals-commerce/services/policy-approvals-commerce.service';
import { User } from 'app/users/entities/user.entity';
import { PolicyVersion } from 'app/users/policies/entities/policy-version.entity';
import { Policy } from 'app/users/policies/entities/policy.entity';
import { PolicyEmailsService } from 'app/users/policies/services/policy-emails.service';
import { PolicyVersionService } from 'app/users/policies/services/policy-version.service';
import { UsersCoreService } from 'app/users/services/users-core.service';
import { ActivityNs } from 'app/worker/activity-ns.decorator';
import { getContext } from 'app/worker/helpers/logging-activity-context.helper';
import { ReviewGroupConfigurationData } from 'app/worker/workflows/composable-workflows/start-review-group-review-collection/types';
import {
    ComposableWorkflowTrigger,
    PolicyApprovalWorkflowTrigger,
    ReviewGroupRunState,
} from 'app/worker/workflows/composable-workflows/types';
import { Account } from 'auth/entities/account.entity';
import { AccountsCoreService } from 'auth/services/accounts-core.service';
import { TenancyTransaction } from 'commons/decorators/transaction';
import { WorkflowActivityController } from 'commons/services/workflow-activity-controller.service';
import { Socket } from 'dependencies/socket/socket';
import { FeatureFlagCategory } from 'feature-flags/enums/feature-flag-category.enum';
import { FeatureFlag } from 'feature-flags/feature-flag.enum';
import { FeatureFlagService } from 'feature-flags/feature-flag.service';
import { compact, isEmpty, isNil } from 'lodash';
import { Span } from 'nestjs-ddtrace';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';

@Injectable()
export class StartReviewGroupReviewCollectionWorkflowController extends WorkflowActivityController {
    constructor(
        private readonly accountsCoreService: AccountsCoreService,
        private readonly approvalConfigurationsCoreService: ApprovalConfigurationCoreService,
        private readonly approvalsCoreService: ApprovalsCoreService,
        private readonly approvalsOrchestrationService: ApprovalsOrchestrationService,
        private readonly customAssignmentCoreService: CustomAssignmentCoreService,
        private readonly featureFlagService: FeatureFlagService,
        private readonly policyApprovalsCommerceService: PolicyApprovalsCommerceService,
        private readonly policyEmailsService: PolicyEmailsService,
        private readonly policyVersionService: PolicyVersionService,
        private readonly reviewGroupCoreService: ReviewGroupCoreService,
        private readonly socket: Socket,
        private readonly usersCoreService: UsersCoreService,
    ) {
        super();
    }

    @Span()
    @TenancyTransaction()
    @ActivityNs()
    public async createReviewGroupWithReviews(
        configuration: ReviewGroupConfigurationData,
        trigger: PolicyApprovalWorkflowTrigger,
        workflowId: string,
    ): Promise<[number, number[]]> {
        const { logInfo, logError } = getContext();

        const identifier = `${this.constructor.name}::createReviewGroupWithReviews`;
        logInfo(`${identifier} - Input`, {
            approvalId: configuration.approvalId,
            configurationId: configuration.configurationId,
            flexibleAssignmentId: configuration.flexibleAssignmentId,
            workflowId,
            policyId: trigger.details.policyId,
            policyVersionId: trigger.details.policyVersionId,
            consensusRule: configuration.consensusRule,
            consensusRuleSubsetNumber: configuration.consensusRuleSubsetNumber,
            timeline: configuration.timeline,
        });

        try {
            let objectContext: ObjectContext;
            if (!isNil(trigger.details.policyId) && !isNil(trigger.details.policyVersionId)) {
                objectContext = {
                    type: SupportedObjectTypes.POLICY,
                    policyId: trigger.details.policyId,
                };

                logInfo(`${identifier} - Object context created`, {
                    approvalId: configuration.approvalId,
                    objectContext,
                });
            } else {
                logError(
                    `${identifier} - Trigger type is unsupported`,
                    new Error('Missing policy details'),
                    {
                        approvalId: configuration.approvalId,
                        triggerDetails: trigger.details,
                    },
                );
                throw new ApplicationFailure(
                    'Trigger type is unsupported',
                    'UnsupportedTriggerException',
                    false,
                );
            }

            logInfo(`${identifier} - denormalizeUsersInCustomGroup - Input`, {
                approvalId: configuration.approvalId,
                flexibleAssignmentId: configuration.flexibleAssignmentId,
                objectContext,
            });

            const userIds = await this.customAssignmentCoreService.denormalizeUsersInCustomGroup(
                configuration.flexibleAssignmentId,
                objectContext,
            );

            logInfo(`${identifier} - denormalizeUsersInCustomGroup - Result`, {
                approvalId: configuration.approvalId,
                flexibleAssignmentId: configuration.flexibleAssignmentId,
                userCount: userIds.length,
                userIds,
            });

            logInfo(`${identifier} - createReviewGroup - Input`, {
                approvalId: configuration.approvalId,
                reviewGroupConfigurationId: configuration.configurationId,
                waasRunId: workflowId,
                userCount: userIds.length,
                userIds,
            });

            const { id, reviews } = await this.reviewGroupCoreService.createReviewGroup({
                approvalId: configuration.approvalId,
                reviewGroupConfigurationId: configuration.configurationId,
                waasRunId: workflowId,
                userIds,
            });

            if (trigger.type === CustomWorkflowTrigger.POLICY_VERSION_PUBLISHED) {
                await this.handlePolicyReviewGroupReadyForReview(
                    configuration.approvalId,
                    trigger.details.policyId,
                    trigger.details.policyVersionId,
                    id,
                    trigger.details.triggeringUserId,
                );
            }

            logInfo(`${identifier} - createReviewGroup - Result`, {
                approvalId: configuration.approvalId,
                reviewGroupId: id,
                reviewGroupConfigurationId: configuration.configurationId,
                waasRunId: workflowId,
                reviews: reviews.map(review => ({
                    id: review.id,
                    userId: review.userId,
                    status: review.status,
                    statusUpdatedAt: review.statusUpdatedAt,
                    isOverridden: review.isOverridden,
                })),
            });

            const reviewIds = compact(reviews.map(review => review.id));

            return [id, reviewIds];
        } catch (error) {
            logError(`${identifier} - Error`, error as Error, {
                approvalId: configuration.approvalId,
                configurationId: configuration.configurationId,
                workflowId,
            });
            throw error;
        }
    }

    @Span()
    @TenancyTransaction()
    @ActivityNs()
    public async createReviewNote(reviewId: number, note: string): Promise<void> {
        const { logInfo, logError } = getContext();

        const identifier = `${this.constructor.name}::createReviewNote`;
        logInfo(`${identifier} - Input`, {
            reviewId,
            hasNote: !isEmpty(note),
        });

        if (isEmpty(note)) {
            logInfo(`${identifier} note is empty. Skipping.`, {
                reviewId,
            });
            return;
        }

        try {
            logInfo(`${identifier} - createReviewNote - Input`, {
                reviewId,
            });
            await this.reviewGroupCoreService.createReviewNote(reviewId, note);
            logInfo(`${identifier} - createReviewNote - Result`, {
                reviewId,
                noteCreated: true,
            });
        } catch (error) {
            logError(`${identifier} - Error`, error as Error, {
                reviewId,
                noteLength: note?.length || 0,
            });
            throw error;
        }
    }

    @Span()
    @TenancyTransaction()
    @ActivityNs()
    public async setReviewStatus(reviewId: number, status: ReviewStatus): Promise<void> {
        const { logInfo, logError } = getContext();

        const identifier = `${this.constructor.name}::setReviewStatus`;
        try {
            logInfo(`${identifier} - setReviewStatus - Input`, { reviewId, newStatus: status });
            await this.reviewGroupCoreService.setReviewStatus(reviewId, status);
            logInfo(`${identifier} - setReviewStatus - Result`, {
                reviewId,
                status,
                statusUpdated: true,
            });
        } catch (error) {
            logError(`${identifier} - Error`, error as Error, {
                reviewId,
                attemptedStatus: status,
            });
            throw error;
        }
    }

    @Span()
    @TenancyTransaction()
    @ActivityNs()
    public async createCustomTasksForReviewGroup(
        configuration: ReviewGroupConfigurationData,
        runState: ReviewGroupRunState,
        trigger: ComposableWorkflowTrigger,
    ): Promise<number[]> {
        const { logInfo, logError } = getContext();

        const identifier = `${this.constructor.name}::createCustomTasksForReviewGroup`;
        logInfo(`${identifier} - Input`, {
            approvalId: configuration.approvalId,
            configurationId: configuration.configurationId,
            triggerType: trigger.type,
            triggerDetails: trigger.details,
        });

        try {
            if (trigger.type !== CustomWorkflowTrigger.POLICY_VERSION_PUBLISHED) {
                logError(
                    `${identifier} - Unsupported trigger type`,
                    new Error('Unsupported trigger'),
                    {
                        approvalId: configuration.approvalId,
                        triggerType: trigger.type,
                    },
                );
                throw new ApplicationFailure(
                    'Trigger type is unsupported',
                    'UnsupportedTriggerException',
                    false,
                );
            }

            let objectContext: ObjectContext;
            if (!isNil(trigger.details.policyId) && !isNil(trigger.details.policyVersionId)) {
                objectContext = {
                    type: SupportedObjectTypes.POLICY,
                    policyId: trigger.details.policyId,
                };

                logInfo(`${identifier} - Object context created`, {
                    approvalId: configuration.approvalId,
                    objectContext,
                });
            } else {
                logError(
                    `${identifier} - Missing trigger details`,
                    new Error('Missing policy details'),
                    {
                        approvalId: configuration.approvalId,
                        triggerDetails: trigger.details,
                    },
                );
                throw new ApplicationFailure(
                    'Missing Trigger Details for Policy Version Approval Type',
                    'BadTriggerDataException',
                    false,
                );
            }

            logInfo(`${identifier} - getApprovalById - Input`, {
                approvalId: configuration.approvalId,
            });
            const approval = await this.approvalsCoreService.getApprovalById(
                configuration.approvalId,
            );
            logInfo(`${identifier} - getApprovalById - Result`, { approval });

            logInfo(`${identifier} - getApprovalConfigurationById - Input`, {
                approvalConfigurationId: approval.approvalConfigurationId,
            });
            const approvalConfiguration =
                await this.approvalConfigurationsCoreService.getApprovalConfigurationById(
                    approval.approvalConfigurationId,
                );

            logInfo(`${identifier} - Approval configuration retrieved`, {
                approvalConfigurationId: approvalConfiguration.id,
                ownerId: approvalConfiguration.ownerId,
            });

            logInfo(`${identifier} - createCustomTasksForReviewGroup - Input`, {
                approvalId: configuration.approvalId,
                configurationId: configuration.configurationId,
                ownerId: approvalConfiguration.ownerId,
            });

            const taskIds =
                await this.approvalsOrchestrationService.createCustomTasksForReviewGroup(
                    configuration.configurationId,
                    approvalConfiguration.ownerId,
                    runState,
                    objectContext,
                );

            logInfo(`${identifier} - createCustomTasksForReviewGroup - Result`, {
                approvalId: configuration.approvalId,
                taskCount: taskIds.length,
                taskIds,
            });

            return taskIds;
        } catch (error) {
            logError(`${identifier} - Error`, error as Error, {
                approvalId: configuration.approvalId,
                configurationId: configuration.configurationId,
                triggerType: trigger.type,
            });
            throw error;
        }
    }

    @ActivityNs()
    @Span()
    @TenancyTransaction()
    public async removeCustomTasksInReviewGroup(
        trigger: PolicyApprovalWorkflowTrigger,
    ): Promise<void> {
        const { logInfo, logError } = getContext();

        const identifier = `${this.constructor.name}::removeCustomTasksInReviewGroup`;
        logInfo(`${identifier} - Input`, {
            triggerType: trigger.type,
            policyId: trigger.details.policyId,
            policyVersionId: trigger.details.policyVersionId,
        });

        try {
            let objectContext: ObjectContext;
            if (!isNil(trigger.details.policyId) && !isNil(trigger.details.policyVersionId)) {
                objectContext = {
                    type: SupportedObjectTypes.POLICY,
                    policyId: trigger.details.policyId,
                };

                logInfo(`${identifier} - Object context created`, {
                    objectContext,
                });
            } else {
                logError(
                    `${identifier} - Missing trigger details`,
                    new Error('Missing policy details'),
                    {
                        triggerDetails: trigger.details,
                    },
                );
                throw new ApplicationFailure(
                    'Missing Trigger Details for Policy Version Approval Type',
                    'BadTriggerDataException',
                    false,
                );
            }

            logInfo(`${identifier} - removeCustomTasksInReviewGroup - Input`, {
                objectContext,
            });

            await this.approvalsOrchestrationService.removeCustomTasksInReviewGroup(objectContext);

            logInfo(`${identifier} - removeCustomTasksInReviewGroup - Result`, {
                tasksRemoved: true,
            });
        } catch (error) {
            logError(`${identifier} - Error`, error as Error, {
                triggerType: trigger.type,
            });
            throw error;
        }
    }

    @ActivityNs()
    @Span()
    @TenancyTransaction()
    public async evaluateConsensusDecision(
        reviewGroupId: number,
        consensusRule: ConsensusRule,
        consensusRuleSubsetNumber?: number,
    ): Promise<ReviewStatus> {
        const { logInfo, logError } = getContext();

        const identifier = `${this.constructor.name}::evaluateConsensusDecision`;
        logInfo(`${identifier} - Input`, {
            reviewGroupId,
            consensusRule,
            consensusRuleSubsetNumber,
        });

        try {
            const result = await this.approvalsOrchestrationService.evaluateConsensusDecision(
                reviewGroupId,
                consensusRule,
                consensusRuleSubsetNumber,
            );

            logInfo(`${identifier} - evaluateConsensusDecision - Result`, {
                reviewGroupId,
                consensusRule,
                consensusRuleSubsetNumber,
                consensusDecision: result,
            });

            return result;
        } catch (error) {
            logError(`${identifier} - Error`, error as Error, {
                reviewGroupId,
                consensusRule,
                consensusRuleSubsetNumber,
            });
            throw error;
        }
    }

    @ActivityNs()
    @Span()
    @TenancyTransaction()
    public async markCustomTaskAsCompletedForReview(
        reviewId: number,
        trigger: PolicyApprovalWorkflowTrigger,
    ): Promise<void> {
        const { logInfo, logError } = getContext();
        const identifier = `${this.constructor.name}::markCustomTaskAsCompletedForReview`;
        try {
            logInfo(`${identifier} - Input`, {
                reviewId,
                triggerType: trigger.type,
                triggerDetails: trigger.details,
            });

            logInfo(`${identifier} - getReviewById - Input`, { reviewId });
            const reviewer = await this.reviewGroupCoreService.getReviewById(reviewId);
            logInfo(`${identifier} - getReviewById - Result`, {
                reviewer,
            });
            let objectContext: ObjectContext;
            if (!isNil(trigger.details.policyId) && !isNil(trigger.details.policyVersionId)) {
                objectContext = {
                    type: SupportedObjectTypes.POLICY,
                    policyId: trigger.details.policyId,
                };
            } else {
                throw new ApplicationFailure(
                    'Missing Trigger Details for Policy Version Approval Type',
                    'BadTriggerDataException',
                    false,
                );
            }

            if (!isNil(trigger.details?.policyId)) {
                logInfo(`${identifier} - markCustomTaskAsCompletedForReviewer - Input`, {
                    reviewerId: reviewer.userId,
                    objectContext,
                });
                await this.approvalsOrchestrationService.markCustomTaskAsCompletedForReviewer(
                    reviewer.userId,
                    objectContext,
                );
                logInfo(`${identifier} - markCustomTaskAsCompletedForReviewer - Result`, {
                    reviewerId: reviewer.userId,
                    objectContext,
                });
            }
        } catch (error) {
            logError(`${identifier} - Error`, error as Error, {
                reviewId,
                triggerType: trigger.type,
                triggerDetails: trigger.details,
            });
            throw error;
        }
    }

    @ActivityNs()
    @Span()
    @TenancyTransaction()
    public async evaluateApprovalDecision(approvalId: number): Promise<ApprovalStatusType> {
        const { logInfo, logError } = getContext();

        const identifier = `${this.constructor.name}::evaluateApprovalDecision`;
        logInfo(`${identifier} - Input`, { approvalId });

        try {
            const reviewGroups =
                await this.reviewGroupCoreService.findReviewGroupsFromApprovalId(approvalId);

            logInfo(`${identifier} - findReviewGroupsFromApprovalId - Result`, {
                approvalId,
                reviewGroupCount: reviewGroups.length,
                reviewGroups: reviewGroups.map(rg => ({
                    id: rg.id,
                    consensusDecision: rg.consensusDecision,
                    reviewGroupConfigurationId: rg.reviewGroupConfigurationId,
                })),
            });

            logInfo(`${identifier} - getApprovalById - Input`, { approvalId });
            const approval = await this.approvalsCoreService.getApprovalById(approvalId);
            logInfo(`${identifier} - getApprovalById - Result`, { approval });

            logInfo(`${identifier} - getApprovalConfigurationById - Input`, {
                approvalConfigurationId: approval.approvalConfigurationId,
            });
            const configuration =
                await this.approvalConfigurationsCoreService.getApprovalConfigurationById(
                    approval.approvalConfigurationId,
                );
            logInfo(`${identifier} - getApprovalConfigurationById - Result`, { configuration });

            let result: ApprovalStatusType;
            let decisionReason: string;

            if (reviewGroups.length !== configuration.reviewGroupConfigurations.length) {
                result = approval.status;
                decisionReason = 'Review groups incomplete';
            } else if (
                reviewGroups.every(group => group.consensusDecision === ReviewStatus.APPROVED)
            ) {
                result = ApprovalStatusType.APPROVED;
                decisionReason = 'All review groups approved';
            } else if (
                reviewGroups.some(
                    group => group.consensusDecision === ReviewStatus.CHANGES_REQUESTED,
                )
            ) {
                result = ApprovalStatusType.CHANGES_REQUESTED;
                decisionReason = 'Some review groups requested changes';
            } else {
                result = approval.status;
                decisionReason = 'No consensus reached, keeping current status';
            }

            logInfo(`${identifier} - Consensus decision evaluated`, {
                approvalId,
                decision: result,
                decisionReason,
                currentReviewGroupCount: reviewGroups.length,
                expectedReviewGroupCount: configuration.reviewGroupConfigurations.length,
                reviewGroupDecisions: reviewGroups.map(rg => rg.consensusDecision),
            });

            return result;
        } catch (error) {
            logError('Failed to evaluate approval decision', error as Error, {
                approvalId,
            });
            throw error;
        }
    }

    @ActivityNs()
    @Span()
    @TenancyTransaction()
    public async setOverrideReview(
        reviewGroupId: number,
        userId: number,
        overrideDecision: ReviewStatus,
    ): Promise<number> {
        const { logInfo, logError } = getContext();
        const identifier = `${this.constructor.name}::setOverrideReview`;

        logInfo(`${identifier} - Input`, {
            reviewGroupId,
            userId,
            overrideDecision,
        });

        try {
            const overrideReviewId = await this.reviewGroupCoreService.setOverrideReview(
                reviewGroupId,
                userId,
                overrideDecision,
            );

            await this.sendSocketApprovalWorkflowReviewGroupStatusUpdated({
                reviewGroupId,
                status: ReviewStatus.APPROVED,
            });
            logInfo(`${identifier} - setOverrideReview - Result`, { overrideReviewId });

            return overrideReviewId;
        } catch (error) {
            logError(`${identifier} - Error}`, error as Error, {
                reviewGroupId,
                userId,
                overrideDecision,
            });
            throw error;
        }
    }

    @ActivityNs()
    @Span()
    @TenancyTransaction()
    public async deleteReviewGroup(reviewGroupId: number): Promise<void> {
        const { logInfo, logError } = getContext();

        const identifier = `${this.constructor.name}::deleteReviewGroup`;
        logInfo(`${identifier} - Input`, {
            reviewGroupId,
        });

        try {
            await this.reviewGroupCoreService.deleteReviewGroup(reviewGroupId);

            logInfo(`${identifier} - deleteReviewGroup - Result`, {
                reviewGroupId,
                deleted: true,
            });
        } catch (error) {
            logError(`${identifier} - Error`, error as Error, {
                reviewGroupId,
            });
            throw error;
        }
    }

    @ActivityNs()
    @Span()
    @TenancyTransaction()
    public async updateReviewGroup(
        updateReviewGroupRequest: UpdateReviewGroupRequest,
    ): Promise<ReviewGroup> {
        const { logInfo, logError } = getContext();

        const identifier = `${this.constructor.name}::updateReviewGroup`;
        logInfo(`${identifier} - Input`, { updateReviewGroupRequest });

        try {
            const updatedReviewGroup =
                await this.reviewGroupCoreService.updateReviewGroup(updateReviewGroupRequest);

            logInfo(`${identifier} - updateReviewGroup - Result`, { updateReviewGroupRequest });
            if (updatedReviewGroup?.consensusDecision) {
                await this.sendSocketApprovalWorkflowReviewGroupStatusUpdated({
                    reviewGroupId: updatedReviewGroup.id,
                    status: updatedReviewGroup.consensusDecision,
                });
            }
            return updatedReviewGroup;
        } catch (error) {
            logError(`${identifier} - Error`, error as Error, {
                reviewGroupId: updateReviewGroupRequest.id,
                updateRequest: updateReviewGroupRequest,
            });
            throw error;
        }
    }

    @ActivityNs()
    @Span()
    @TenancyTransaction()
    public async getCurrentAccount(domain: string): Promise<Account> {
        const { logInfo, logError } = getContext();
        const identifier = `${this.constructor.name}::getCurrentAccount`;

        logInfo(`${identifier} - Input`, { domain });

        try {
            const account = await this.accountsCoreService.getAccountByDomain(domain);
            logInfo(`${identifier} - getAccountByDomain - Result`, { account });

            if (!account) {
                throw new Error('No Account found for workflow');
            }

            return account;
        } catch (error) {
            logError(`${identifier} - Error`, error as Error, {
                domain,
            });
            throw error;
        }
    }

    @ActivityNs()
    @Span()
    @TenancyTransaction()
    public async getPolicyVersionFromApproval(
        approvalId: number,
    ): Promise<PolicyApprovalsAssociationEntity> {
        const { logInfo, logError } = getContext();

        const identifier = `${this.constructor.name}::getPolicyVersionFromApproval`;
        logInfo(`${identifier} - Input`, { approvalId });

        try {
            const approvalWithPolicy =
                await this.policyApprovalsCommerceService.getPolicyForApproval(approvalId);

            logInfo(`${identifier} - getPolicyForApproval - Result`, { approvalWithPolicy });

            return approvalWithPolicy;
        } catch (error) {
            logError(`${identifier} - Error`, error as Error, {
                approvalId,
            });
            throw error;
        }
    }

    @ActivityNs()
    @Span()
    @TenancyTransaction()
    public async sendPolicyVersionPastDueEmail(
        account: Account,
        owner: User,
        policy: Policy,
        policyVersion: PolicyVersion,
        deadline: string,
    ): Promise<void> {
        const { logInfo, logError } = getContext();
        const identifier = `${this.constructor.name}::sendPolicyVersionPastDueEmail`;
        try {
            logInfo(`${identifier} - Input`, {
                accountId: account.id,
                ownerId: owner?.id,
                policyId: policy?.id,
                policyVersionId: policyVersion?.id,
                deadline,
            });
            await this.policyEmailsService.sendPolicyVersionPastDueEmail(
                account,
                owner,
                policy,
                policyVersion,
                deadline,
            );
            logInfo(`${identifier} - Result`, { emailSent: true });
        } catch (error) {
            logError(`${identifier} - Error`, error as Error, {
                accountId: account.id,
                ownerId: owner?.id,
                policyId: policy?.id,
                policyVersionId: policyVersion?.id,
                deadline,
            });
            throw error;
        }
    }

    @ActivityNs()
    @Span()
    @TenancyTransaction()
    public async sendPolicyNeedsApprovalEmail(
        account: Account,
        reviewId: number,
        policy: Policy,
        policyVersionId: number,
        timeline: string,
        triggeringUserId: number,
    ): Promise<void> {
        const { logInfo, logError } = getContext();
        const identifier = `${this.constructor.name}::sendPolicyNeedsApprovalEmail`;

        logInfo(`${identifier} - Input`, {
            accountId: account.id,
            policyId: policy.id,
            policyVersionId,
            reviewId,
            timeline,
            triggeringUserId,
        });

        try {
            const policyVersion = await this.policyVersionService.getPolicyVersion(
                account,
                policy.id,
                policyVersionId,
            );
            logInfo(`${identifier} - getPolicyVersion - Result`, { policyVersion });

            logInfo(`${identifier} - getReviewById - Input`, { reviewId });
            const review = await this.reviewGroupCoreService.getReviewById(reviewId);
            logInfo(`${identifier} - getReviewById - Result`, { review: review.id });

            logInfo(`${identifier} - getUserById - Input`, { reviewUserId: review.userId });
            const user = await this.usersCoreService.getUserById(review.userId);
            logInfo(`${identifier} - getUserById - Result`, { user });

            logInfo(`${identifier} - getUserById - Input`, { triggeringUserId });
            const triggeringUser = await this.usersCoreService.getUserById(triggeringUserId);
            logInfo(`${identifier} - getUserById - Result`, {
                triggeringUserId: triggeringUser.id,
            });

            const formattedTimeline = new Date(
                Date.now() + parseInt(timeline) * 24 * 60 * 60 * 1000,
            );

            logInfo(`${identifier} - Checking feature flag`, {
                featureFlag: FeatureFlag.POLICY_MULTI_APPROVERS,
                userId: user.id,
            });

            const isFeatureFlagEnabled = await this.featureFlagService.evaluate(
                {
                    name: FeatureFlag.POLICY_MULTI_APPROVERS,
                    defaultValue: false,
                    category: FeatureFlagCategory.NONE,
                },
                user,
                account,
            );

            logInfo(`${identifier} - Feature flag evaluated`, {
                featureFlag: FeatureFlag.POLICY_MULTI_APPROVERS,
                enabled: isFeatureFlagEnabled,
                userId: user.id,
            });

            if (!isFeatureFlagEnabled) {
                logInfo(`${identifier} - Skipping email`, {
                    emailSent: false,
                    reason: 'Feature flag disabled',
                });
                return;
            }

            const owner = policyVersion.owner ?? policy.currentOwner;
            if (owner) {
                logInfo(`${identifier} - sendEmailAboutPolicyNeedsApproval - Input`, {
                    formattedTimeline: formattedTimeline.toISOString(),
                    ownerId: owner.id,
                    recipientEmail: user.email,
                    recipientId: user.id,
                    reviewId,
                });
                await this.policyEmailsService.sendEmailAboutPolicyNeedsApproval(
                    account,
                    policy,
                    policyVersion,
                    user,
                    formattedTimeline,
                    owner,
                    triggeringUser,
                );
                logInfo(`${identifier} - sendEmailAboutPolicyNeedsApproval - Result`, {
                    reviewId,
                    emailSent: true,
                    recipientId: user.id,
                    recipientEmail: user.email,
                });
            } else {
                this.logger.warn(
                    PolloMessage.msg(
                        `Not possible to send email. Policy version ${policyVersion.id} does not have an owner.`,
                    )
                        .setIdentifier({
                            policyId: policy.id,
                            policyVersionId: policyVersion.id,
                            policyOwner: policy.currentOwnerId,
                        })
                        .setContext(this.constructor.name)
                        .setSubContext(this.sendPolicyNeedsApprovalEmail.name),
                );
                return;
            }
        } catch (error) {
            logError(`${identifier} - Error`, error as Error, {
                reviewId,
                policyId: policy.id,
                policyVersionId,
                triggeringUserId,
            });
            throw error;
        }
    }

    @Span()
    private async handlePolicyReviewGroupReadyForReview(
        approvalId: number,
        policyId: number,
        policyVersionId: number,
        reviewGroupId: number,
        triggeringUserId: number,
    ): Promise<void> {
        const account = this._tenancyContext.getAccount();
        if (!account) {
            this.logger.warn(
                PolloMessage.msg('No account found in tenancy context. Skipping.')
                    .setIdentifier({
                        policyId,
                        policyVersionId,
                    })
                    .setContext(this.constructor.name)
                    .setSubContext(this.handlePolicyReviewGroupReadyForReview.name),
            );
            return;
        }

        const triggerUser = await this.usersCoreService.getUserById(triggeringUserId);

        await this.policyApprovalsCommerceService.handlePolicyApprovalWorkflowStarted(
            account,
            triggerUser,
            policyId,
            policyVersionId,
        );

        await this.sendSocketApprovalWorkflowReviewGroupStatusUpdated({
            approvalId,
            policyVersionId,
            reviewGroupId,
            status: ReviewStatus.READY_FOR_REVIEW,
        });

        await this.sendSocketApprovalWorkflowHandlerStarted({
            approvalId,
            policyVersionId,
            startedByUserId: triggeringUserId,
            isLocked: false,
        });
    }

    @Span()
    private async sendSocketApprovalWorkflowReviewGroupStatusUpdated(data: {
        approvalId?: number;
        policyVersionId?: number;
        reviewGroupId: number;
        status: ReviewStatus;
    }): Promise<void> {
        const { logError, logInfo, logWarn } = getContext();
        const accountId = this._tenancyContext.getAccountId();
        try {
            if (accountId) {
                await this.socket.sendMessage(
                    accountId,
                    SocketEvent.APPROVAL_WORKFLOW_REVIEW_GROUP_STATUS_UPDATED,
                    data,
                );
                logInfo('Sent socket review group status updated', {
                    approvalId: data.approvalId,
                    reviewGroupId: data.reviewGroupId,
                    status: data.status,
                    policyVersionId: data.policyVersionId,
                });
            } else {
                logWarn('No account id found. Skipping.');
            }
        } catch (error) {
            logError('Failed to send socket approval workflow status updated', error);
        }
    }

    private async sendSocketApprovalWorkflowHandlerStarted(data: {
        approvalId: number;
        policyVersionId: number;
        startedByUserId: number;
        isLocked: boolean;
    }): Promise<void> {
        const { logError, logInfo, logWarn } = getContext();
        const accountId = this._tenancyContext.getAccountId();
        try {
            if (accountId) {
                await this.socket.sendMessage(
                    accountId,
                    SocketEvent.APPROVAL_WORKFLOW_HANDLER_STATE_UPDATED,
                    data,
                );
                logInfo('Sent socket approval workflow handler started', data);
            } else {
                logWarn('No account id found. Skipping.');
            }
        } catch (error) {
            logError('Failed to send socket approval workflow handler started', error);
        }
    }
}
