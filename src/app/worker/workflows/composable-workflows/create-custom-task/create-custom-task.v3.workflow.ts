import { EventCategory, EventType } from '@drata/enums';
import { workflowInfo } from '@temporalio/workflow';
import { StateTypeEnum } from 'app/custom-workflows/custom-workflows-common/enums/custom-workflow-run-state-type.enum';
import {
    ControlIdRunStateType,
    CustomWorkflowRunStateType,
    RiskIdRunStateType,
} from 'app/custom-workflows/custom-workflows-common/types/custom-workflow-run-state.type';
import type { EventMetadata } from 'app/events/classes/event-metadata.class';
import { TaskNotificationAction } from 'app/grc/enums/task-notification-action.enum';
import type { CustomTasksWorkflowsService } from 'app/grc/workflow-services/custom-tasks.workflow.service';
import { proxyActivitiesForService } from 'app/worker/helpers/worker.helper';
import type { CommonActivityService } from 'app/worker/workflows/composable-workflows/common/activities/common-activity.service';
import type { CustomTaskConfigurationData } from 'app/worker/workflows/composable-workflows/create-custom-task/types';
import { getEnrichedData } from 'app/worker/workflows/composable-workflows/helpers/template-enrichment.helper';
import { ComposableWorkflowTrigger } from 'app/worker/workflows/composable-workflows/types';
import type { EmailTemplateActivityController } from 'app/worker/workflows/email-notifications/activities/email-template-activity.controller';
import type { EmailActivityController } from 'app/worker/workflows/email-notifications/activities/send-email-activity.controller';
import type { TaskNotificationsActivityController } from 'app/worker/workflows/grc/activities/task-notifications-activity.controller';
import { RiskCustomTasksWorkflowsService } from 'app/worker/workflows/risk/activities/risk-custom-task.workflow.service';
import { AnalyticsEvent } from 'commons/enums/analytics-event.enum';
import { EventSource } from 'commons/enums/events/event-source.enum';
import { UpcomingTaskType } from 'commons/enums/upcoming-task-type-enum';
import { toAccountIdType } from 'commons/types/account-id.type';
import { pick } from 'lodash';

const {
    CustomTasksWorkflowsService_createCustomTaskForControl,
    CustomTasksWorkflowsService_createEventDescriptionForTask,
    CustomTasksWorkflowsService_createCustomAssignmentMapTask,
} = proxyActivitiesForService<typeof CustomTasksWorkflowsService>({
    startToCloseTimeout: '30 minutes',
    retry: {
        maximumAttempts: 3,
    },
});

const {
    RiskCustomTasksWorkflowsService_createCustomTaskForRisk,
    RiskCustomTasksWorkflowsService_createCustomAssignmentMapTaskForRisk,
} = proxyActivitiesForService<typeof RiskCustomTasksWorkflowsService>({
    startToCloseTimeout: '30 minutes',
    retry: {
        maximumAttempts: 3,
    },
});

const { CommonActivityService_publishEvent, CommonActivityService_publishSegmentEvent } =
    proxyActivitiesForService<typeof CommonActivityService>({
        startToCloseTimeout: '30 minutes',
        retry: {
            maximumAttempts: 3,
        },
    });

const { EmailActivityController_sendEmailMany } = proxyActivitiesForService<
    typeof EmailActivityController
>({
    startToCloseTimeout: '90 seconds',
    retry: {
        maximumAttempts: 1,
    },
});

const { EmailTemplateActivityController_getEmailOptionsMany } = proxyActivitiesForService<
    typeof EmailTemplateActivityController
>({
    startToCloseTimeout: '90 seconds',
    retry: {
        maximumAttempts: 1,
    },
});

const { TaskNotificationsActivityController_transform } = proxyActivitiesForService<
    typeof TaskNotificationsActivityController
>({
    startToCloseTimeout: '50 seconds',
    retry: {
        maximumAttempts: 1,
    },
});

const { CommonActivityService_getAccountFromId } = proxyActivitiesForService<
    typeof CommonActivityService
>({
    startToCloseTimeout: '5 seconds',
    retry: {
        maximumAttempts: 10,
    },
});

// v3 was created to support multiple run states with CustomWorkflowRunStateType
export async function createCustomTaskV3(
    configuration: CustomTaskConfigurationData,
    runState: CustomWorkflowRunStateType,
    trigger: ComposableWorkflowTrigger,
) {
    const accountId = toAccountIdType(workflowInfo().memo?.accountId as string);

    const templated = await getEnrichedData(
        pick(configuration, 'title', 'description'),
        runState,
        trigger,
    );

    if (configuration.taskType === UpcomingTaskType.CONTROL) {
        const controlRunState = runState as ControlIdRunStateType;
        let userIds: number[] = [];

        if (configuration.customAssignment) {
            userIds = await CustomTasksWorkflowsService_createCustomAssignmentMapTask(
                configuration.title,
                controlRunState.controlId,
                configuration.customAssignment.userIds,
                configuration.customAssignment.roleNames,
                configuration.customAssignment.responsibilities,
            );
        }
        const customTask = await CustomTasksWorkflowsService_createCustomTaskForControl({
            title: templated.title,
            description: templated.description,
            daysAfterCreationDueDate: configuration.daysAfterCreationDueDate,
            assigneeId: configuration.assigneeId,
            assigneeMany: userIds,
            controlId: controlRunState.controlId,
            triggerUserId: controlRunState.ownerId,
        });

        const account = await CommonActivityService_getAccountFromId(accountId);

        const emailConfigs = await TaskNotificationsActivityController_transform(
            {
                dtoName: 'CustomTaskBaseRequestDto',
                title: configuration.title,
                description: configuration.description,
                dueDate: customTask.dueDate,
                assigneeId: configuration.assigneeId || 0,
                taskType: configuration.taskType,
            },
            customTask,
            account,
            undefined,
            TaskNotificationAction.ASSIGNED,
            undefined,
        );
        const emailOptionsList =
            await EmailTemplateActivityController_getEmailOptionsMany(emailConfigs);
        await EmailActivityController_sendEmailMany(emailOptionsList, account);

        const metadata = {
            task: customTask,
        } as EventMetadata;

        // The string formatter is non-deterministic, so have to use an activity
        const description = await CustomTasksWorkflowsService_createEventDescriptionForTask(
            customTask,
            EventType.CONTROL_TASK_CREATED,
        );

        await CommonActivityService_publishEvent(accountId, {
            type: EventType.CONTROL_TASK_CREATED,
            category: EventCategory.TASK,
            source: EventSource.WORKFLOW,
            description,
            metadata,
            userId: runState.ownerId,
        });

        await CommonActivityService_publishSegmentEvent({
            userId: runState.ownerId,
            event: AnalyticsEvent.CONTROL_TASK_CREATED,
            payload: {
                customTaskName: customTask.title,
            },
        });
    } else if (configuration.taskType === UpcomingTaskType.RISK) {
        // Type guard to ensure we have a risk run state
        if (runState.type !== StateTypeEnum.RISK_ID_RUN_STATE_TYPE) {
            throw new Error('Risk task type requires RiskIdRunStateType');
        }

        // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion
        const riskRunState = runState as RiskIdRunStateType;
        let userIds: number[] = [];

        if (configuration.customAssignment) {
            userIds = await RiskCustomTasksWorkflowsService_createCustomAssignmentMapTaskForRisk(
                configuration.title,
                riskRunState.riskId,
                configuration.customAssignment.userIds,
                configuration.customAssignment.roleNames,
                configuration.customAssignment.responsibilities,
            );
        }

        const customTask = await RiskCustomTasksWorkflowsService_createCustomTaskForRisk({
            title: templated.title,
            description: templated.description,
            daysAfterCreationDueDate: configuration.daysAfterCreationDueDate,
            assigneeId: configuration.assigneeId,
            assigneeMany: userIds,
            riskId: riskRunState.riskId,
            triggerUserId: riskRunState.ownerId,
        });

        const account = await CommonActivityService_getAccountFromId(accountId);

        const emailConfigs = await TaskNotificationsActivityController_transform(
            {
                dtoName: 'CustomTaskBaseRequestDto',
                title: templated.title,
                description: templated.description,
                dueDate: customTask.dueDate,
                assigneeId: configuration.assigneeId || 0,
                taskType: configuration.taskType,
            },
            customTask,
            account,
            undefined,
            TaskNotificationAction.ASSIGNED,
            undefined,
        );
        const emailOptionsList =
            await EmailTemplateActivityController_getEmailOptionsMany(emailConfigs);
        await EmailActivityController_sendEmailMany(emailOptionsList, account);

        const metadata = {
            task: customTask,
        } as EventMetadata;

        await CommonActivityService_publishEvent(accountId, {
            type: EventType.RISK_TASK_CREATED,
            category: EventCategory.TASK,
            source: EventSource.WORKFLOW,
            description: `Risk task "${customTask.title}" created`,
            metadata,
            userId: riskRunState.ownerId,
        });

        await CommonActivityService_publishSegmentEvent({
            userId: riskRunState.ownerId,
            event: AnalyticsEvent.RISK_TASK_CREATED,
            payload: {
                customTaskName: customTask.title,
            },
        });
    }
    return runState;
}
