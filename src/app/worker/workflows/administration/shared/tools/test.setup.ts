// Import library mocks
import 'app/worker/workflows/administration/shared/tools/test-library.mocks';

import {
    mockDrataDataSourceInstance,
    mockDynamoClient,
    mockPassword,
    mockQueryRunner,
    mockS3ClientV3,
    mockTenantTables,
    mockUsername,
} from 'app/worker/workflows/administration/shared/tools/test.helper';

/////////////////
/// App mocks
/////////////////
jest.mock('commons/helpers/environment.helper');

// Mock the enum helper to avoid validation issues during tests
jest.mock('commons/helpers/enum.helper', () => ({
    ...jest.requireActual('commons/helpers/enum.helper'),
    getValues: jest.fn().mockImplementation(enumeration => {
        // Handle common enum patterns used in entities
        if (enumeration && typeof enumeration === 'object') {
            return Object.values(enumeration);
        }
        return [];
    }),
    isEnum: jest.fn().mockReturnValue(true), // Always return true to avoid validation errors
}));

jest.mock('commons/classes/drata-data-source.class', () => ({
    ...jest.requireActual('commons/classes/drata-data-source.class'),
    DrataDataSource: jest.fn().mockImplementation(() => mockDrataDataSourceInstance),
}));
jest.mock('app/worker/workflows/administration/shared/tools/database.helper', () => ({
    ...jest.requireActual('app/worker/workflows/administration/shared/tools/database.helper'),
    exportDataFromRegionalDatabaseTable: jest.fn().mockResolvedValue(undefined),
    runCommandWithCredentials: jest.fn().mockResolvedValue(undefined),
    getDatabaseCredentials: jest.fn().mockResolvedValue({
        username: mockUsername,
        password: mockPassword,
    }),
    prepareDbConnection: jest.fn().mockResolvedValue(mockQueryRunner),
    getTableColumns: jest.fn().mockResolvedValue([]),
    getChecksumWithOffset: jest.fn().mockResolvedValue(null),
    getDbTables: jest.fn().mockResolvedValue(mockTenantTables),
    getDynamoDbClient: jest.fn().mockReturnValue(mockDynamoClient),
    getAvailableTenant: jest.fn().mockResolvedValue({
        id: 'tenant-id',
        friendly_name: 'tenant-friendly',
        dns_name: 'tenant-host',
        port: 3306,
        accepting_new_tenants: true,
        number_of_accounts: 0,
    }),
}));
jest.mock('app/worker/workflows/administration/shared/tools/s3.helper', () => {
    const actual = jest.requireActual('app/worker/workflows/administration/shared/tools/s3.helper');
    // Simple in-memory S3 store to simulate persistence between calls in a single test file
    const __s3Store = new Map<string, Buffer>();
    const keyOf = (bucket?: string, key?: string) => `${bucket ?? ''}/${key ?? ''}`;
    return {
        ...actual,
        deleteS3Objects: jest.fn().mockResolvedValue(undefined),
        getS3Client: jest.fn().mockResolvedValue(mockS3ClientV3),
        getS3ObjectDetails: jest.fn().mockImplementation(async (_client, request) => {
            const buf = __s3Store.get(keyOf(request.Bucket, request.Key)) ?? Buffer.from('{}');
            return { body: buf, metadata: {} };
        }),
        uploadFile: jest.fn().mockImplementation(async ({ s3Bucket, s3Key, body }) => {
            const content = Buffer.isBuffer(body) ? body : Buffer.from(String(body ?? ''), 'utf-8');
            __s3Store.set(keyOf(s3Bucket, s3Key), content);
            return '';
        }),
    };
});
jest.mock('app/worker/workflows/administration/shared/tools/file.helper', () => {
    const actual = jest.requireActual(
        'app/worker/workflows/administration/shared/tools/file.helper',
    );
    return {
        ...actual,
        compress: jest.fn().mockResolvedValue(''),
    };
});
