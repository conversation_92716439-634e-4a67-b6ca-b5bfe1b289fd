const drataSupportEmail = '<EMAIL>';
export const EXPORT_ACCOUNT_QUERIES: Record<string, (accountId: string) => string> = {
    account_entitlement_addon: (accountId: string) =>
        `fk_account_entitlement_setting_id IN (
            SELECT id FROM account_entitlement_setting
            WHERE fk_account_id = '${accountId}'
        )`,
    auditor: (accountId: string) => `fk_entry_id IN (
        SELECT DISTINCT fk_entry_id
        FROM auditor_client
        WHERE fk_account_id = '${accountId}'
    )`,
    audit_firm: (accountId: string) => `id IN (
        SELECT fk_audit_firm_id
        FROM auditor
        WHERE fk_entry_id IN (
            SELECT DISTINCT fk_entry_id
            FROM auditor_client
            WHERE fk_account_id = '${accountId}'
        )
    )`,
    auditor_framework_auditors: (accountId: string) => `fk_auditor_framework_id IN (
        SELECT id FROM auditor_framework
        WHERE fk_account_id = '${accountId}'
    )`,
    entry: (accountId: string) => `id IN (
        SELECT fk_entry_id FROM account_entry_map WHERE fk_account_id = '${accountId}')`,
    external_client_router: (accountId: string) => `fk_account_id = '${accountId}'`,
    refresh_token: (accountId: string) => `fk_entry_id IN (
        SELECT fk_entry_id
        FROM account_entry_map aem
        JOIN entry e ON e.id = aem.fk_entry_id
        WHERE aem.fk_account_id = '${accountId}' AND e.email != '${drataSupportEmail}'
    )`,
    service_user: (accountId: string) => `fk_entry_id IN (
        SELECT fk_entry_id FROM service_user_client WHERE fk_account_id = '${accountId}')`,
    service_group: (accountId: string) => `id IN (
        SELECT fk_service_group_id
        FROM service_user
        WHERE fk_entry_id IN (
            SELECT DISTINCT fk_entry_id
            FROM service_user_client
            WHERE fk_account_id = '${accountId}'
        )
    )`,
    token: (accountId: string) => `fk_entry_id IN (
        SELECT fk_entry_id
        FROM account_entry_map aem
        JOIN entry e ON e.id = aem.fk_entry_id
        WHERE aem.fk_account_id = '${accountId}' AND e.email != '${drataSupportEmail}'
    )`,
    token_public: (accountId: string) =>
        `id IN (
            SELECT fk_token_public_id FROM self_service_account_creation_invite
            WHERE fk_account_id = '${accountId}'
        )`,
    shared_account: (accountId: string) => `fk_account_id = '${accountId}'`,
    tenant_router: (accountId: string) => `fk_account_id = '${accountId}'`,
    trust_center: (accountId: string) => `fk_account_id = '${accountId}'`,
    account_event: (accountId: string) => `fk_account_id = '${accountId}'`,
    account_entitlement_map: (accountId: string) => `fk_account_id = '${accountId}'`,
    account_entitlement_setting: (accountId: string) => `fk_account_id = '${accountId}'`,
    account_service_group_map: (accountId: string) => `fk_account_id = '${accountId}'`,
    account_service_user_map: (accountId: string) => `fk_account_id = '${accountId}'`,
    account_safebase_settings: (accountId: string) => `fk_account_id = '${accountId}'`,
    adaptive_automation_metadata: (accountId: string) => `fk_account_id = '${accountId}'`,
    auditor_client: (accountId: string) => `fk_account_id = '${accountId}'`,
    auditor_framework: (accountId: string) => `fk_account_id = '${accountId}'`,
    public_api_key: (accountId: string) => `fk_account_id = '${accountId}'`,
    service_user_client: (accountId: string) => `fk_account_id = '${accountId}'`,
    self_service_account_creation_invite: (accountId: string) => `fk_account_id = '${accountId}'`,
    self_service_quick_launch_agreement: (accountId: string) => `fk_account_id = '${accountId}'`,
    account_entry_map: (accountId: string) => `fk_account_id = '${accountId}'`,
    account: (accountId: string) => `id = '${accountId}'`,
};

/**
 * DO NOT REORDER THESE. They are executed in the order they appear.
 */
export const DELETE_ACCOUNT_QUERIES: Record<string, (accountId: string) => string> = {
    account_current_contract_addon_map: (accountId: string) =>
        `fk_account_current_contract_id IN (
            SELECT id FROM account_current_contract
            WHERE fk_account_id = '${accountId}'
        )`,
    account_current_contract_framework_map: (accountId: string) =>
        `fk_account_current_contract_id IN (
            SELECT id FROM account_current_contract
            WHERE fk_account_id = '${accountId}'
        )`,
    audit_api_key: (accountId: string) =>
        `fk_auditor_id IN (
            SELECT id FROM auditor
            WHERE fk_entry_id IN (
                SELECT id FROM entry
                WHERE id IN (
                    SELECT r.fk_entry_id
                    FROM (
                        SELECT
                            fk_entry_id,
                            GROUP_CONCAT(fk_account_id) AS related_accounts,
                            LENGTH(GROUP_CONCAT(fk_account_id)) - LENGTH(REPLACE(GROUP_CONCAT(fk_account_id), ',', '')) + 1 AS num_related_accounts
                        FROM account_entry_map
                        GROUP BY fk_entry_id
                    ) r
                    WHERE r.num_related_accounts <= 1
                        AND r.related_accounts = '${accountId}'
                )
                AND email != '${drataSupportEmail}'
            )
        )`,
    auditor_framework_auditors: (accountId: string) =>
        `fk_auditor_framework_id IN (
            SELECT id FROM auditor_framework
            WHERE fk_account_id = '${accountId}'
        )`,
    audit_firm: (accountId: string) => `id IN (
            SELECT fk_audit_firm_id
            FROM auditor
            WHERE fk_entry_id IN (
                SELECT id FROM entry
                WHERE id IN (
                    SELECT r.fk_entry_id
                    FROM (
                        SELECT
                            fk_entry_id,
                            GROUP_CONCAT(fk_account_id) AS related_accounts,
                            LENGTH(GROUP_CONCAT(fk_account_id)) - LENGTH(REPLACE(GROUP_CONCAT(fk_account_id), ',', '')) + 1 AS num_related_accounts
                        FROM account_entry_map
                        GROUP BY fk_entry_id
                    ) r
                    WHERE r.num_related_accounts <= 1
                        AND r.related_accounts = '${accountId}'
                )
                AND email != '${drataSupportEmail}'
            )
        )`,
    service_group: (accountId: string) =>
        `id IN (
            SELECT r.fk_service_group_id
            FROM (
                SELECT
                    fk_service_group_id,
                    GROUP_CONCAT(fk_account_id) AS related_accounts,
                    LENGTH(GROUP_CONCAT(fk_account_id)) - LENGTH(REPLACE(GROUP_CONCAT(fk_account_id), ',', '')) + 1 AS num_related_accounts
                FROM account_service_group_map
                GROUP BY fk_service_group_id
            ) r
            WHERE r.num_related_accounts <= 1
                AND r.related_accounts = '${accountId}'
        )`,
    auditor: (accountId: string) =>
        `fk_entry_id IN (
            SELECT id FROM entry
            WHERE id IN (
                SELECT r.fk_entry_id
                FROM (
                    SELECT
                        fk_entry_id,
                        GROUP_CONCAT(fk_account_id) AS related_accounts,
                        LENGTH(GROUP_CONCAT(fk_account_id)) - LENGTH(REPLACE(GROUP_CONCAT(fk_account_id), ',', '')) + 1 AS num_related_accounts
                    FROM account_entry_map
                    GROUP BY fk_entry_id
                ) r
                WHERE r.num_related_accounts <= 1
                    AND r.related_accounts = '${accountId}'
            )
            AND email != '${drataSupportEmail}'
        )`,
    service_user: (accountId: string) =>
        `fk_entry_id IN (
            SELECT id FROM entry
            WHERE id IN (
                SELECT r.fk_entry_id
                FROM (
                    SELECT
                        fk_entry_id,
                        GROUP_CONCAT(fk_account_id) AS related_accounts,
                        LENGTH(GROUP_CONCAT(fk_account_id)) - LENGTH(REPLACE(GROUP_CONCAT(fk_account_id), ',', '')) + 1 AS num_related_accounts
                    FROM account_entry_map
                    GROUP BY fk_entry_id
                ) r
                WHERE r.num_related_accounts <= 1
                    AND r.related_accounts = '${accountId}'
            )
            AND email != '${drataSupportEmail}'
        )`,
    token: (accountId: string) =>
        `fk_entry_id IN (
            SELECT id FROM entry
            WHERE id IN (
                SELECT r.fk_entry_id
                FROM (
                    SELECT
                        fk_entry_id,
                        GROUP_CONCAT(fk_account_id) AS related_accounts,
                        LENGTH(GROUP_CONCAT(fk_account_id)) - LENGTH(REPLACE(GROUP_CONCAT(fk_account_id), ',', '')) + 1 AS num_related_accounts
                    FROM account_entry_map
                    GROUP BY fk_entry_id
                ) r
                WHERE r.num_related_accounts <= 1
                    AND r.related_accounts = '${accountId}'
            )
            AND email != '${drataSupportEmail}'
        )`,
    refresh_token: (accountId: string) =>
        `fk_entry_id IN (
            SELECT id FROM entry
            WHERE id IN (
                SELECT r.fk_entry_id
                FROM (
                    SELECT
                        fk_entry_id,
                        GROUP_CONCAT(fk_account_id) AS related_accounts,
                        LENGTH(GROUP_CONCAT(fk_account_id)) - LENGTH(REPLACE(GROUP_CONCAT(fk_account_id), ',', '')) + 1 AS num_related_accounts
                    FROM account_entry_map
                    GROUP BY fk_entry_id
                ) r
                WHERE r.num_related_accounts <= 1
                    AND r.related_accounts = '${accountId}'
            )
            AND email != '${drataSupportEmail}'
        )`,
    token_public: (accountId: string) =>
        `id IN (
            SELECT fk_token_public_id
            FROM self_service_account_creation_invite
            WHERE fk_account_id = '${accountId}'
        )`,
    entry: (accountId: string) =>
        `id IN (
	        SELECT r.fk_entry_id
	        FROM (
                SELECT
                    fk_entry_id,
                    GROUP_CONCAT(fk_account_id) AS related_accounts,
                    LENGTH(GROUP_CONCAT(fk_account_id)) - LENGTH(REPLACE(GROUP_CONCAT(fk_account_id), ',', '')) + 1 AS num_related_accounts
                FROM account_entry_map
                GROUP BY fk_entry_id
            ) r
            WHERE r.num_related_accounts <= 1
                AND r.related_accounts = '${accountId}'
        ) AND email != '${drataSupportEmail}'`,
    account_entitlement_addon: (accountId: string) =>
        `fk_account_entitlement_setting_id IN (
            SELECT id FROM account_entitlement_setting
            WHERE fk_account_id = '${accountId}'
        )`,
    external_client_router: (accountId: string) => `fk_account_id = '${accountId}'`,
    account_current_contract: (accountId: string) => `fk_account_id = '${accountId}'`,
    account_entitlement_map: (accountId: string) => `fk_account_id = '${accountId}'`,
    account_entitlement_setting: (accountId: string) => `fk_account_id = '${accountId}'`,
    account_event: (accountId: string) => `fk_account_id = '${accountId}'`,
    account_service_group_map: (accountId: string) => `fk_account_id = '${accountId}'`,
    account_service_user_map: (accountId: string) => `fk_account_id = '${accountId}'`,
    adaptive_automation_metadata: (accountId: string) => `fk_account_id = '${accountId}'`,
    auditor_client: (accountId: string) => `fk_account_id = '${accountId}'`,
    auditor_framework: (accountId: string) => `fk_account_id = '${accountId}'`,
    public_api_key: (accountId: string) => `fk_account_id = '${accountId}'`,
    self_service_account_creation_invite: (accountId: string) => `fk_account_id = '${accountId}'`,
    self_service_quick_launch_agreement: (accountId: string) => `fk_account_id = '${accountId}'`,
    service_user_client: (accountId: string) => `fk_account_id = '${accountId}'`,
    shared_account: (accountId: string) => `fk_account_id = '${accountId}'`,
    tenant_router: (accountId: string) => `fk_account_id = '${accountId}'`,
    trust_center: (accountId: string) => `fk_account_id = '${accountId}'`,
    account_entry_map: (accountId: string) => `fk_account_id = '${accountId}'`,
    account: (accountId: string) => `id = '${accountId}'`,
};

export const IGNORED_COLUMNS: { [key: string]: string[] } = {
    account: ['db_host', 'updated_at', 'fk_tenant_database_host_id'],
};

export const TABLES_FOR_ACCOUNT_MIGRATE = new Set(Object.keys(EXPORT_ACCOUNT_QUERIES));

export const TABLES_FOR_ACCOUNT_DELETE = new Set(Object.keys(DELETE_ACCOUNT_QUERIES));

export const IGNORED_TABLES = new Set([
    'account_current_contract',
    'account_current_contract_addon_map',
    'account_current_contract_framework_map',
    'audit_api_key',
    'auditor_framework_type_template',
    'autopilot_recipe_template',
    'control_template_control_test_template_map',
    'control_template_evidence_template_map',
    'control_template',
    'control_templates_policy_templates_map',
    'control_templates_requirement_templates_map',
    'control_test_template',
    'entitlement',
    'evidence_template',
    'feature_template',
    'framework_template',
    'migration',
    'monitor_template_check_type',
    'monitor_template',
    'plan',
    'plan_entitlement',
    'policy_metadata_template',
    'policy_sla_template',
    'policy_template_content',
    'policy_template',
    'profile_details_template',
    'requirement_index_tag_template',
    'requirement_index_template_tag_map',
    'requirement_index_template',
    'requirement_template',
    'site_admin_comment',
    'site_admin_refresh_token',
    'site_admin_role',
    'site_admin',
    'tenant_database_host',
    'trust_center_monitoring_control_template',
    'typeorm_metadata',
]);
