import 'app/worker/workflows/administration/account-migration/tools/test.setup';
import 'app/worker/workflows/administration/shared/tools/test.setup';

import { MockActivityEnvironment } from '@temporalio/testing';

import * as loggingHelper from 'app/worker/helpers/logging-activity-context.helper';
import { ActivityContext } from 'app/worker/types';
import {
    AccountMigrationRunStatus,
    AccountMigrationSteps,
} from 'app/worker/workflows/administration/account-migration/enums/account-migration-run-statuses.enum';
import {
    updateAccountMigrationRunStatus,
    updateAccountStatus,
    validateAccountMigrationRunStatus,
} from 'app/worker/workflows/administration/shared/activities/shared.activities';
import * as dbHelper from 'app/worker/workflows/administration/shared/tools/database.helper';
import * as sharedS3Helper from 'app/worker/workflows/administration/shared/tools/s3.helper';
import {
    mockQueryRunner,
    mockS3ClientV3,
} from 'app/worker/workflows/administration/shared/tools/test.helper';
import { AccountStatus } from 'commons/enums/auth/account-status.enum';
import config from 'config';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { PolloLogger } from 'pollo-logger/pollo.logger';

describe('Export Account Activities', () => {
    let temporalEnv: MockActivityEnvironment;
    const mockAccountId = 'test-account-123';
    const mockActivityContext = {
        current: {
            account: {
                id: mockAccountId,
            },
            logging: {
                correlationId: 'test-correlation-id',
                traceId: 'test-trace-id',
            },
        },
        logger: {
            debug: jest.fn(),
            error: jest.fn(),
            verbose: jest.fn(),
            log: jest.fn(),
            warn: jest.fn(),
        } as unknown as PolloLogger<PolloMessage>,
        heartbeat: jest.fn(),
        logDebug: jest.fn(),
        logWarn: jest.fn(),
        logInfo: () => jest.fn(),
        logError: () => jest.fn(),
        heartbeatFn: () => jest.fn(),
    } as unknown as ActivityContext;

    beforeAll(() => {
        temporalEnv = new MockActivityEnvironment();
        jest.spyOn(loggingHelper, 'getContext').mockReturnValue(mockActivityContext);
        jest.useFakeTimers();
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    afterAll(() => {
        jest.clearAllMocks();
        temporalEnv?.cancel();
        jest.useRealTimers();
    });

    describe('updateAccountStatus', () => {
        beforeEach(() => {
            jest.clearAllMocks();
            jest.spyOn(mockQueryRunner, 'query').mockResolvedValue([]);
        });

        it('should update account status successfully', async () => {
            await temporalEnv.run(
                updateAccountStatus,
                mockAccountId,
                false,
                AccountStatus.MAINTENANCE,
            );
            expect(dbHelper.prepareDbConnection).toHaveBeenCalled();
            expect(mockQueryRunner.query).toHaveBeenCalledWith(
                `UPDATE account SET status = ? WHERE id = ?`,
                [AccountStatus.MAINTENANCE, mockAccountId],
            );
            expect(mockQueryRunner.release).toHaveBeenCalled();
        });

        it('should not update account status when testRun is true', async () => {
            await temporalEnv.run(
                updateAccountStatus,
                mockAccountId,
                true,
                AccountStatus.MAINTENANCE,
            );
            expect(mockQueryRunner.query).not.toHaveBeenCalled();
        });

        it('should not update account status when newStatus is not provided', async () => {
            await expect(
                // @ts-expect-error: Testing invalid input
                temporalEnv.run(updateAccountStatus, mockAccountId, false),
            ).rejects.toThrow('Account status is required to update account statu');

            expect(mockQueryRunner.query).not.toHaveBeenCalled();
        });
    });

    describe('updateAccountMigrationRunStatus', () => {
        beforeEach(() => {
            jest.clearAllMocks();
        });

        it('should update account migration run status successfully', async () => {
            await temporalEnv.run(
                updateAccountMigrationRunStatus,
                mockAccountId,
                AccountMigrationSteps.EXPORT,
                AccountMigrationRunStatus.IN_PROGRESS,
            );

            expect(sharedS3Helper.getS3ObjectDetails).toHaveBeenCalledWith(
                mockS3ClientV3,
                expect.objectContaining({
                    Bucket: config.get('accountMigration.s3.bucketName'),
                    Key: `${mockAccountId}/migrationStatus.json`,
                }),
            );

            expect(sharedS3Helper.uploadFile).toHaveBeenCalledWith(
                expect.objectContaining({
                    body: JSON.stringify({ EXPORT: AccountMigrationRunStatus.IN_PROGRESS }),
                    s3Bucket: config.get('accountMigration.s3.bucketName'),
                    s3Key: `${mockAccountId}/migrationStatus.json`,
                    s3Client: mockS3ClientV3,
                }),
            );
        });

        it('should throw error when step is not provided', async () => {
            await expect(
                // @ts-expect-error: Testing invalid input
                temporalEnv.run(updateAccountMigrationRunStatus, mockAccountId),
            ).rejects.toThrow(
                'Account migration run status is required to update account migration run status',
            );
        });

        it('should throw error when newStatus is not provided', async () => {
            await expect(
                temporalEnv.run(
                    // @ts-expect-error: Testing invalid input
                    updateAccountMigrationRunStatus,
                    mockAccountId,
                    AccountMigrationSteps.EXPORT,
                ),
            ).rejects.toThrow(
                'Account migration run status is required to update account migration run status',
            );
        });

        it('should update migration status when it exists and has multiple steps', async () => {
            await temporalEnv.run(
                updateAccountMigrationRunStatus,
                mockAccountId,
                AccountMigrationSteps.EXPORT,
                AccountMigrationRunStatus.IN_PROGRESS,
            );
            await temporalEnv.run(
                updateAccountMigrationRunStatus,
                mockAccountId,
                AccountMigrationSteps.IMPORT,
                AccountMigrationRunStatus.IN_PROGRESS,
            );
            await temporalEnv.run(
                updateAccountMigrationRunStatus,
                mockAccountId,
                AccountMigrationSteps.MIGRATION,
                AccountMigrationRunStatus.IN_PROGRESS,
            );

            expect(sharedS3Helper.uploadFile).toHaveBeenCalledWith(
                expect.objectContaining({
                    body: JSON.stringify({
                        EXPORT: AccountMigrationRunStatus.IN_PROGRESS,
                        IMPORT: AccountMigrationRunStatus.IN_PROGRESS,
                        MIGRATION: AccountMigrationRunStatus.IN_PROGRESS,
                    }),
                    s3Bucket: config.get('accountMigration.s3.bucketName'),
                    s3Key: `${mockAccountId}/migrationStatus.json`,
                    s3Client: mockS3ClientV3,
                }),
            );
        });

        it('should use source region when provided', async () => {
            await temporalEnv.run(
                updateAccountMigrationRunStatus,
                mockAccountId,
                AccountMigrationSteps.EXPORT,
                AccountMigrationRunStatus.IN_PROGRESS,
                'us-east-1',
            );
            expect(sharedS3Helper.getS3Client).toHaveBeenCalledWith('us-east-1');
        });

        it('should use default region when source region is not provided', async () => {
            await temporalEnv.run(
                updateAccountMigrationRunStatus,
                mockAccountId,
                AccountMigrationSteps.EXPORT,
                AccountMigrationRunStatus.IN_PROGRESS,
            );
            expect(sharedS3Helper.getS3Client).toHaveBeenCalledWith(
                config.get('accountMigration.region'),
            );
        });
    });

    describe('validateAccountMigrationRunStatus', () => {
        beforeEach(() => {
            jest.clearAllMocks();
        });

        it('should validate account migration run status successfully', async () => {
            (sharedS3Helper.getS3ObjectDetails as jest.Mock).mockResolvedValue({
                body: JSON.stringify({
                    IMPORT: AccountMigrationRunStatus.COMPLETED,
                }),
            });

            await temporalEnv.run(
                validateAccountMigrationRunStatus,
                mockAccountId,
                AccountMigrationSteps.IMPORT,
                AccountMigrationRunStatus.COMPLETED,
            );
        });

        it('should throw error when expected status does not match', async () => {
            (sharedS3Helper.getS3ObjectDetails as jest.Mock).mockResolvedValue({
                body: JSON.stringify({
                    IMPORT: AccountMigrationRunStatus.FAILED,
                }),
            });

            await expect(
                temporalEnv.run(
                    validateAccountMigrationRunStatus,
                    mockAccountId,
                    AccountMigrationSteps.IMPORT,
                    AccountMigrationRunStatus.COMPLETED,
                ),
            ).rejects.toThrow(
                `Failed to validate account migration run status for step ${AccountMigrationSteps.IMPORT}`,
            );
        });

        it('should throw error when migration status does not exist', async () => {
            (sharedS3Helper.getS3ObjectDetails as jest.Mock).mockResolvedValue({
                body: undefined,
            });

            await expect(
                temporalEnv.run(
                    validateAccountMigrationRunStatus,
                    mockAccountId,
                    AccountMigrationSteps.IMPORT,
                    AccountMigrationRunStatus.COMPLETED,
                ),
            ).rejects.toThrow(
                `Failed to validate account migration run status for step ${AccountMigrationSteps.IMPORT}`,
            );
        });

        it('should get migration status from source region when provided', async () => {
            (sharedS3Helper.getS3ObjectDetails as jest.Mock).mockResolvedValue({
                body: JSON.stringify({
                    IMPORT: AccountMigrationRunStatus.COMPLETED,
                }),
            });

            await temporalEnv.run(
                validateAccountMigrationRunStatus,
                mockAccountId,
                AccountMigrationSteps.IMPORT,
                AccountMigrationRunStatus.COMPLETED,
                'us-east-1',
            );

            expect(sharedS3Helper.getS3Client).toHaveBeenCalledWith('us-east-1');
        });
    });
});
