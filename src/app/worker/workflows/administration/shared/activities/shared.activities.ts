import { ApplicationFailure } from '@temporalio/workflow';
import config from 'config';
import { isNil } from 'lodash';
import { z } from 'zod';

import { getContext } from 'app/worker/helpers/logging-activity-context.helper';
import {
    AccountMigrationRunStatus,
    AccountMigrationStatusObject,
    AccountMigrationSteps,
} from 'app/worker/workflows/administration/account-migration/enums/account-migration-run-statuses.enum';
import { ValidationTarget } from 'app/worker/workflows/administration/shared/enums/validation-target.enum';
import {
    findRelation,
    getAvailableTenant,
    getRegionalDataSource,
    prepareDbConnection,
} from 'app/worker/workflows/administration/shared/tools/database.helper';
import {
    getS3Client,
    getS3ObjectDetails,
    uploadFile,
} from 'app/worker/workflows/administration/shared/tools/s3.helper';
import {
    EXPORT_ACCOUNT_QUERIES,
    IGNORED_TABLES,
    TABLES_FOR_ACCOUNT_MIGRATE,
} from 'app/worker/workflows/administration/shared/tools/table.helper';
import {
    clearIntervalId,
    setHeartbeats,
} from 'app/worker/workflows/administration/shared/tools/temporal.helper';
import {
    AccountBucketRegionWorkflowRequest,
    AccountSeparateTablesWorkflowRequest,
    AccountWorkflowRequest,
    AcctBucketRegionSeparateTablesWorkflowRequest,
    Table,
    VALID_ACCOUNT_STATUSES_FOR_DELETE,
    ValidateExistenceRequest,
} from 'app/worker/workflows/administration/shared/tools/types';
import {
    getValidationErrorMessage,
    validateAccountId,
    validateAccountStatus,
    ValidationFunctions,
} from 'app/worker/workflows/administration/shared/tools/validation.helper';
import { AccountStatus } from 'commons/enums/auth/account-status.enum';
import { RefreshTokenType } from 'commons/enums/auth/refresh-token-type.enum';
import { fqtn } from 'commons/helpers/database.helper';
import { AccountIdType } from 'commons/types/account-id.type';
import path from 'path';

export async function getTenantDatabaseOrNextAvailableHost(accountId: string) {
    const { logWarn } = getContext();
    try {
        return await getTenantDatabaseHost(accountId);
    } catch (error) {
        logWarn(error.message, { accountId, error });
        const { dns_name: tenantDatabaseHost, port: tenantDatabasePort } =
            await getAvailableTenant();
        return { tenantDatabaseHost, tenantDatabasePort };
    }
}

export async function getTenantDatabaseHost(accountId: string): Promise<{
    tenantDatabaseHost: string;
    tenantDatabasePort: number;
}> {
    const regionalDb = await getRegionalDataSource();
    await regionalDb.initialize();
    let results: Array<{ tenantDatabaseHost: string; tenantDatabasePort: number }> = [];
    const tablePath = fqtn(config.get('db.databaseName'), 'account');
    results = await regionalDb.query(
        `SELECT tdh.dns_name AS tenantDatabaseHost, tdh.port AS tenantDatabasePort
        FROM ${tablePath} a
        INNER JOIN tenant_database_host tdh ON a.fk_tenant_database_host_id = tdh.id
        WHERE a.id = ?`,
        [accountId],
    );

    if (results.length < 1) {
        results = await regionalDb.query(
            `SELECT db_host AS tenantDatabaseHost, db_port AS tenantDatabasePort
            FROM ${tablePath}
            WHERE id = ?`,
            [accountId],
        );

        await regionalDb.destroy();
        if (results.length < 1) {
            throw new Error(`Could not find tenant database host info for account ${accountId}.`);
        }

        return results[0];
    }

    await regionalDb.destroy();
    return results[0];
}

export async function checkRegionalDatabaseTables(): Promise<void> {
    const regionalDb = await getRegionalDataSource();
    await regionalDb.initialize();

    const records: Array<{ Tables_in_drata: string }> = await regionalDb.query('show tables;');
    const tables: Record<string, Table> = {};

    for (const item of records) {
        const name = item.Tables_in_drata;
        tables[name] = { name, related: new Set() };
    }

    const referencedTables: Array<{
        TABLE_NAME: string;
        REFERENCED_TABLE_NAME: string;
    }> = await regionalDb.query(`
        SELECT
            TABLE_NAME, REFERENCED_TABLE_NAME
        FROM
            information_schema.KEY_COLUMN_USAGE
        WHERE
            TABLE_SCHEMA = "drata";
    `);

    for (const item of referencedTables) {
        if (item.REFERENCED_TABLE_NAME === null) {
            continue;
        }
        // create a mutual relation between both table objects
        tables[item.TABLE_NAME].related.add(tables[item.REFERENCED_TABLE_NAME]);
        tables[item.REFERENCED_TABLE_NAME].related.add(tables[item.TABLE_NAME]);
    }

    for (const table of Object.values(tables)) {
        const isRelated = findRelation(table, tables['account'], new Set());
        const knownTable =
            TABLES_FOR_ACCOUNT_MIGRATE.has(table.name) || IGNORED_TABLES.has(table.name);
        if (isRelated && !knownTable) {
            throw new Error(`Unknown table found: ${table.name}`);
        }
    }

    await regionalDb.destroy();
}

export async function validateExistence({
    target,
    validationArgs,
    expectedResult,
}: ValidateExistenceRequest) {
    const validationFunction = ValidationFunctions[target];
    if (validationFunction) {
        if (isNil(expectedResult)) {
            await validationFunction(...validationArgs);
        } else {
            await validationFunction(...validationArgs, expectedResult);
        }
    } else {
        throw new Error(`Validation function not found for ${target}`);
    }
}

export async function validateAccountWorkflowRequest(request: AccountWorkflowRequest) {
    try {
        z.object({
            accountId: z.string().uuid(),
            dryRun: z.boolean().optional(),
        }).parse(request);
    } catch (error) {
        const message = getValidationErrorMessage(error);
        throw ApplicationFailure.fromError(error, {
            message,
            nonRetryable: true,
        });
    }
}

export async function validateAccountSeparateTablesWorkflowRequest(
    request: AccountSeparateTablesWorkflowRequest,
) {
    try {
        z.object({
            accountId: z.string().uuid(),
            tablesToHandleSeparately: z.string().array(),
            dryRun: z.boolean().optional(),
        }).parse(request);
    } catch (error) {
        const message = getValidationErrorMessage(error);
        throw ApplicationFailure.fromError(error, {
            message,
            nonRetryable: true,
        });
    }
}

export async function validateAccountBucketRegionWorkflowRequest(
    request: AccountBucketRegionWorkflowRequest,
) {
    try {
        z.object({
            accountId: z.string().uuid(),
            sourceS3Bucket: z.string(),
            sourceRegion: z.string(),
            dryRun: z.boolean().optional(),
        }).parse(request);
    } catch (error) {
        const message = getValidationErrorMessage(error);
        throw ApplicationFailure.fromError(error, {
            message,
            nonRetryable: true,
        });
    }
}

export async function validateAcctBucketRegionSeparateTablesWorkflowRequest(
    request: AcctBucketRegionSeparateTablesWorkflowRequest,
) {
    try {
        z.object({
            accountId: z.string().uuid(),
            sourceS3Bucket: z.string(),
            sourceRegion: z.string(),
            tablesToHandleSeparately: z.string().array(),
            dryRun: z.boolean().optional(),
        }).parse(request);
    } catch (error) {
        const message = getValidationErrorMessage(error);
        throw ApplicationFailure.fromError(error, {
            message,
            nonRetryable: true,
        });
    }
}

export async function validateAccountForDeletion(accountId: string) {
    await validateExistence({
        target: ValidationTarget.ACCOUNT,
        validationArgs: [accountId],
        expectedResult: true,
    });
    await validateAccountStatus(accountId, VALID_ACCOUNT_STATUSES_FOR_DELETE);
}

export async function revokeAccountAgentTokens({
    accountId,
    dryRun = true,
}: AccountWorkflowRequest) {
    const { logInfo } = getContext();

    await validateAccountId(accountId);
    await validateExistence({
        target: ValidationTarget.ACCOUNT,
        validationArgs: [accountId],
        expectedResult: true,
    });

    const regionalDb = await getRegionalDataSource();
    await regionalDb.initialize();
    const tableName = 'refresh_token';
    const whereClause = EXPORT_ACCOUNT_QUERIES[tableName](accountId);
    if (!whereClause) {
        throw new Error(`Unable to find parameters for query of ${accountId}.${tableName}`);
    }
    const updateQuery = `UPDATE ${tableName} SET deleted_at = CURRENT_TIMESTAMP, is_revoked = 1
        WHERE ${whereClause} AND type = ${RefreshTokenType.AGENT};`;
    if (dryRun) {
        logInfo(`Dry run! Not revoking agent tokens in regional database.`, {
            accountId,
            dryRun,
            query: updateQuery,
        });
    } else {
        await regionalDb.query(updateQuery);
    }
    await regionalDb.destroy();
}

export async function revokeAccountPublicApiTokens({
    accountId,
    dryRun = true,
}: AccountWorkflowRequest) {
    const { logInfo } = getContext();

    await validateAccountId(accountId);
    await validateExistence({
        target: ValidationTarget.ACCOUNT,
        validationArgs: [accountId],
        expectedResult: true,
    });

    const regionalDb = await getRegionalDataSource();
    await regionalDb.initialize();
    const tableName = 'public_api_key';
    const whereClause = EXPORT_ACCOUNT_QUERIES[tableName](accountId);
    if (!whereClause) {
        throw new Error(`Unable to find parameters for query of ${accountId}.${tableName}`);
    }
    const updateQuery = `UPDATE ${tableName} SET deleted_at = CURRENT_TIMESTAMP WHERE ${whereClause};`;
    if (dryRun) {
        logInfo(`Dry run! Not revoking public api keys in regional database.`, {
            accountId,
            dryRun,
            query: updateQuery,
        });
    } else {
        await regionalDb.query(updateQuery);
    }
    await regionalDb.destroy();
}

export async function updateAccountStatus(
    accountId: AccountIdType,
    testRun = false,
    newStatus: AccountStatus,
): Promise<void> {
    const { logInfo } = getContext();
    const intervalId = await setHeartbeats({ accountId });

    if (isNil(newStatus)) {
        throw new Error('Account status is required to update account status');
    }

    if (testRun) {
        logInfo(`[Test Run] Not updating account status.`, { accountId, testRun });
        clearIntervalId(intervalId);
        return;
    }

    const databaseName = config.get('db.databaseName');
    const databaseHost = config.get('db.host');
    const databasePort = config.get('db.port');

    const queryRunner = await prepareDbConnection(databaseHost, databasePort, databaseName);

    logInfo(`Updating account status to ${AccountStatus[newStatus]}`, {
        accountId,
        testRun,
    });

    try {
        await queryRunner.query(`UPDATE account SET status = ? WHERE id = ?`, [
            newStatus,
            accountId,
        ]);
    } finally {
        await queryRunner.release();
        clearIntervalId(intervalId);
    }
}

export async function updateAccountMigrationRunStatus(
    accountId: AccountIdType,
    step: AccountMigrationSteps,
    newStatus: AccountMigrationRunStatus,
    sourceRegion?: string,
): Promise<void> {
    const { logInfo } = getContext();
    const intervalId = await setHeartbeats({ accountId });

    if (isNil(newStatus)) {
        throw new Error(
            'Account migration run status is required to update account migration run status',
        );
    }

    if (isNil(step)) {
        throw new Error(
            'Account migration step is required to update account migration run status',
        );
    }

    const logRegion: string = sourceRegion ?? config.get('accountMigration.region');

    const s3Client = await getS3Client(logRegion);

    const { body } = await getS3ObjectDetails(s3Client, {
        Bucket: config.get('accountMigration.s3.bucketName'),
        Key: path.join(accountId, 'migrationStatus.json'),
    });

    const migrationStatus: AccountMigrationStatusObject = body ? JSON.parse(body.toString()) : {};

    logInfo(`Current migration status: ${JSON.stringify(migrationStatus)}`, {
        accountId,
    });

    migrationStatus[step] = newStatus;

    try {
        logInfo(`Updating account migration run status to ${newStatus} for step ${step}`, {
            accountId,
        });

        await uploadFile({
            body: JSON.stringify(migrationStatus),
            s3Bucket: config.get('accountMigration.s3.bucketName'),
            s3Key: path.join(accountId, 'migrationStatus.json'),
            s3Client,
        });
    } finally {
        clearIntervalId(intervalId);
    }
}

export async function validateAccountMigrationRunStatus(
    accountId: string,
    step: AccountMigrationSteps,
    expectedStatus: AccountMigrationRunStatus,
    sourceRegion?: string,
) {
    const { logInfo } = getContext();
    const intervalId = await setHeartbeats({ accountId });

    try {
        const s3Client = await getS3Client(sourceRegion);

        const { body } = await getS3ObjectDetails(s3Client, {
            Bucket: config.get('accountMigration.s3.bucketName'),
            Key: path.join(accountId, 'migrationStatus.json'),
        });

        const migrationStatus: AccountMigrationStatusObject = body
            ? JSON.parse(body.toString())
            : {};

        logInfo(`Current migration status: ${JSON.stringify(migrationStatus)}`, {
            accountId,
        });

        if (migrationStatus[step] !== expectedStatus) {
            throw new Error(
                `Expected status ${expectedStatus} for step ${step} but found ${migrationStatus[step]}`,
            );
        }

        return true;
    } catch (error) {
        throw ApplicationFailure.fromError(error, {
            message: `Failed to validate account migration run status for step ${step}`,
            nonRetryable: true,
        });
    } finally {
        clearIntervalId(intervalId);
    }
}
