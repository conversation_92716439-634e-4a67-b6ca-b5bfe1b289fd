import { executeChild, proxyActivities } from '@temporalio/workflow';

import {
    AccountMigrationRunStatus,
    AccountMigrationSteps,
} from 'app/worker/workflows/administration/account-migration/enums/account-migration-run-statuses.enum';
import { importAccountDynamoDbItemsWorkflowV3 } from 'app/worker/workflows/administration/account-migration/import/import-account-dynamodb-items.v3.workflow';
import { importAccountS3ObjectsWorkflowV3 } from 'app/worker/workflows/administration/account-migration/import/import-account-s3-objects.v3.workflow';
import { importAccountSqlWorkflowV4 } from 'app/worker/workflows/administration/account-migration/import/import-account-sql.v3.workflow';
import type * as sharedActivities from 'app/worker/workflows/administration/shared/activities/shared.activities';
import { revokeAccountAgentTokensWorkflowV3 } from 'app/worker/workflows/administration/shared/revoke-account-agent-tokens.v3.workflow';
import type { ImportAccountWorkflowRequest } from 'app/worker/workflows/administration/shared/tools/types';
import { updateAdminAccountWorkflowMemo } from 'app/worker/workflows/administration/shared/tools/workflow.helper';

const { updateAccountMigrationRunStatus, validateAccountMigrationRunStatus } = proxyActivities<
    typeof sharedActivities
>({
    startToCloseTimeout: '10 minutes',
    retry: {
        maximumAttempts: 3,
    },
    heartbeatTimeout: '10 minutes',
});

export async function importAccountWorkflowV6({
    accountId,
    sourceS3Bucket,
    sourceRegion,
    tablesToHandleSeparately,
    dryRun = true,
    testRun = true,
}: ImportAccountWorkflowRequest): Promise<void> {
    const memo = await updateAdminAccountWorkflowMemo(accountId, true, dryRun);

    await validateAccountMigrationRunStatus(
        accountId,
        AccountMigrationSteps.EXPORT,
        AccountMigrationRunStatus.COMPLETED,
    );

    try {
        await updateAccountMigrationRunStatus(
            accountId,
            AccountMigrationSteps.IMPORT,
            AccountMigrationRunStatus.IN_PROGRESS,
            sourceRegion,
        );

        await executeChild(importAccountSqlWorkflowV4, {
            args: [
                {
                    accountId,
                    sourceS3Bucket,
                    sourceRegion,
                    tablesToHandleSeparately,
                    testRun,
                },
            ],
            workflowId: `${importAccountSqlWorkflowV4.name}-${accountId}`,
            memo,
        });

        await executeChild(importAccountDynamoDbItemsWorkflowV3, {
            args: [
                {
                    accountId,
                    sourceS3Bucket,
                    sourceRegion,
                },
            ],
            workflowId: `${importAccountDynamoDbItemsWorkflowV3.name}-${accountId}`,
            memo,
        });

        await executeChild(importAccountS3ObjectsWorkflowV3, {
            args: [
                {
                    accountId,
                    sourceS3Bucket,
                    sourceRegion,
                },
            ],
            workflowId: `${importAccountS3ObjectsWorkflowV3.name}-${accountId}`,
            memo,
        });

        await executeChild(revokeAccountAgentTokensWorkflowV3, {
            args: [{ accountId, dryRun }],
            workflowId: `${revokeAccountAgentTokensWorkflowV3.name}-${accountId}`,
            memo,
        });

        await updateAccountMigrationRunStatus(
            accountId,
            AccountMigrationSteps.IMPORT,
            AccountMigrationRunStatus.COMPLETED,
            sourceRegion,
        );
    } catch (error) {
        await updateAccountMigrationRunStatus(
            accountId,
            AccountMigrationSteps.IMPORT,
            AccountMigrationRunStatus.FAILED,
            sourceRegion,
        );
        throw error;
    }
}
