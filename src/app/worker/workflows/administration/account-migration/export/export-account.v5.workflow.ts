import { executeChild, proxyActivities } from '@temporalio/workflow';
import {
    AccountMigrationRunStatus,
    AccountMigrationSteps,
} from 'app/worker/workflows/administration/account-migration/enums/account-migration-run-statuses.enum';

import { exportAccountDynamoDbItemsWorkflowV3 } from 'app/worker/workflows/administration/account-migration/export/export-account-dynamodb-items.v3.workflow';
import { exportAccountRegionalDatabaseDataWorkflowV4 } from 'app/worker/workflows/administration/account-migration/export/export-account-regional-database-data.v4.workflow';
import { exportAccountS3ObjectsWorkflowV3 } from 'app/worker/workflows/administration/account-migration/export/export-account-s3-objects.v3.workflow';
import { exportAccountTenantDatabaseWorkflowV3 } from 'app/worker/workflows/administration/account-migration/export/export-account-tenant-database.v3.workflow';
import { exportEncryptionContextSettingsWorkflowV2 } from 'app/worker/workflows/administration/account-migration/export/export-encryption-context-settings.v2.workflow';
import type { ExportAccountWorkflowRequest } from 'app/worker/workflows/administration/account-migration/tools/types';
import type * as sharedActivities from 'app/worker/workflows/administration/shared/activities/shared.activities';
import { updateAdminAccountWorkflowMemo } from 'app/worker/workflows/administration/shared/tools/workflow.helper';

const { updateAccountMigrationRunStatus } = proxyActivities<typeof sharedActivities>({
    startToCloseTimeout: '10 minutes',
    retry: {
        maximumAttempts: 3,
    },
    heartbeatTimeout: '10 minutes',
});

export async function exportAccountWorkflowV5({
    accountId,
    tablesToHandleSeparately,
    testRun = true,
}: ExportAccountWorkflowRequest): Promise<void> {
    const memo = await updateAdminAccountWorkflowMemo(accountId, true, false);
    try {
        await updateAccountMigrationRunStatus(
            accountId,
            AccountMigrationSteps.EXPORT,
            AccountMigrationRunStatus.IN_PROGRESS,
        );

        await executeChild(exportAccountRegionalDatabaseDataWorkflowV4, {
            args: [{ accountId, testRun }],
            workflowId: `${exportAccountRegionalDatabaseDataWorkflowV4.name}-${accountId}`,
            memo,
        });

        await executeChild(exportAccountTenantDatabaseWorkflowV3, {
            args: [{ accountId, tablesToHandleSeparately }],
            workflowId: `${exportAccountTenantDatabaseWorkflowV3.name}-${accountId}`,
            memo,
        });

        await executeChild(exportEncryptionContextSettingsWorkflowV2, {
            args: [{ accountId }],
            workflowId: `${exportEncryptionContextSettingsWorkflowV2.name}-${accountId}`,
            memo,
        });

        await executeChild(exportAccountDynamoDbItemsWorkflowV3, {
            args: [{ accountId }],
            workflowId: `${exportAccountDynamoDbItemsWorkflowV3.name}-${accountId}`,
            memo,
        });

        await executeChild(exportAccountS3ObjectsWorkflowV3, {
            args: [{ accountId }],
            workflowId: `${exportAccountS3ObjectsWorkflowV3.name}-${accountId}`,
            memo,
        });

        await updateAccountMigrationRunStatus(
            accountId,
            AccountMigrationSteps.EXPORT,
            AccountMigrationRunStatus.COMPLETED,
        );
    } catch (error) {
        await updateAccountMigrationRunStatus(
            accountId,
            AccountMigrationSteps.EXPORT,
            AccountMigrationRunStatus.FAILED,
        );
        throw error;
    }
}
