import { executeChild, proxyActivities } from '@temporalio/workflow';

import {
    AccountMigrationRunStatus,
    AccountMigrationSteps,
} from 'app/worker/workflows/administration/account-migration/enums/account-migration-run-statuses.enum';
import type * as sharedActivities from 'app/worker/workflows/administration/shared/activities/shared.activities';
import { revokeAccountAccessWorkflowV3 } from 'app/worker/workflows/administration/shared/revoke-account-access.v3.workflow';
import type { AccountWorkflowRequest } from 'app/worker/workflows/administration/shared/tools/types';
import { updateAdminAccountWorkflowMemo } from 'app/worker/workflows/administration/shared/tools/workflow.helper';
import { AccountStatus } from 'commons/enums/auth/account-status.enum';

const { updateAccountMigrationRunStatus, updateAccountStatus, validateAccountMigrationRunStatus } =
    proxyActivities<typeof sharedActivities>({
        startToCloseTimeout: '10 minutes',
        retry: {
            maximumAttempts: 3,
        },
        heartbeatTimeout: '10 minutes',
    });

export async function finalizeAccountMigrationWorkflowV1({
    accountId,
    dryRun = true,
}: AccountWorkflowRequest): Promise<void> {
    const memo = await updateAdminAccountWorkflowMemo(accountId, true, dryRun);

    await validateAccountMigrationRunStatus(
        accountId,
        AccountMigrationSteps.IMPORT,
        AccountMigrationRunStatus.COMPLETED,
    );

    try {
        await updateAccountMigrationRunStatus(
            accountId,
            AccountMigrationSteps.MIGRATION,
            AccountMigrationRunStatus.IN_PROGRESS,
        );

        await executeChild(revokeAccountAccessWorkflowV3, {
            args: [{ accountId, dryRun }],
            workflowId: `${revokeAccountAccessWorkflowV3.name}-${accountId}`,
            memo,
        });

        await updateAccountStatus(accountId, dryRun, AccountStatus.MIGRATED);

        await updateAccountMigrationRunStatus(
            accountId,
            AccountMigrationSteps.MIGRATION,
            AccountMigrationRunStatus.COMPLETED,
        );
    } catch (error) {
        await updateAccountMigrationRunStatus(
            accountId,
            AccountMigrationSteps.MIGRATION,
            AccountMigrationRunStatus.FAILED,
        );
        throw error;
    }
}
