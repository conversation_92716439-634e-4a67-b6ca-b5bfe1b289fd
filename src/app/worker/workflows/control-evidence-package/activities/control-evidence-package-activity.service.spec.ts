import { AgentPlatform, AuditorFrameworkType, AuditType, SocketEvent } from '@drata/enums';
import { EventBus } from '@nestjs/cqrs';
import { TestingModule } from '@nestjs/testing';
import { customerRequestRepositoryMock } from 'app/access-review/mocks/repositories/customer-request-respository.mock';
import { CollectCustomerRequestControlsEvidenceService } from 'app/audit-hub/evidence-strategies/collect-customer-request-controls-evidence.service';
import { CollectSelectControlsEvidenceService } from 'app/audit-hub/evidence-strategies/collect-select-controls-evidence.service';
import { controlEvidenceConnectionMock } from 'app/audit-hub/evidence-strategies/mocks/evidence-strategy-connection.mock';
import { Company } from 'app/companies/entities/company.entity';
import { Product } from 'app/companies/products/entities/product.entity';
import { WorkspacesCoreService } from 'app/companies/products/services/workspaces-core.service';
import { CustomerRequest } from 'app/customer-request/entities/customer-request.entity';
import { EventsAppService } from 'app/events/events-app.service';
import { RequirementIndex } from 'app/frameworks/entities/requirement-index.entity';
import { Requirement } from 'app/frameworks/entities/requirement.entity';
import { RequirementsCoreService } from 'app/frameworks/services/requirements-core.service';
import { Control } from 'app/grc/entities/control.entity';
import { User } from 'app/users/entities/user.entity';
import { PersonnelCoreService } from 'app/users/personnel/services/personnel-core.service';
import { auditorFrameworkRepositoryMock as auditRepositoryMock } from 'app/users/policies/services/mocks/repositories/auditor-framework-repository.mock';
import { auditorRepositoryMock } from 'app/users/policies/services/mocks/repositories/auditor-repository.mock';
import { ControlEvidencePackageActivityService } from 'app/worker/workflows/control-evidence-package/activities/control-evidence-package-activity.service';
import { ControlEvidencePackageEvent } from 'app/worker/workflows/control-evidence-package/interface/control-evidence-package-event.interface';
import { ControlEvidenceJsonUploaderService } from 'app/worker/workflows/control-evidence-package/services/control-evidence-json-uploader.service';
import { UploadedControlEvidenceKeys } from 'app/worker/workflows/control-evidence-package/types/uploaded-control-evidence-keys.type';
import { AuditorClient } from 'auditors/entities/auditor-client.entity';
import { Auditor } from 'auditors/entities/auditor.entity';
import { AuditorFrameworkRepository } from 'auditors/repositories/auditor-framework.repository';
import { AuditorRepository } from 'auditors/repositories/auditor.repository';
import { AuditInfoService } from 'auditors/services/audit-info.service';
import { CollectAllControlEvidenceService } from 'auditors/services/collect-all-control-evidence.service';
import { Audit } from 'audits/entities/audit.entity';
import { Account } from 'auth/entities/account.entity';
import { Entry } from 'auth/entities/entry.entity';
import { CacheService } from 'cache/cache.service';
import { EmailConfig } from 'commons/configs/email.config';
import { createAppTestingModule } from 'commons/helpers/app-testing-module.helper';
import { MockFactory } from 'commons/mocks/factories/mock.factory';
import { MockType } from 'commons/mocks/types/mock.type';
import { EmailService } from 'commons/services/email.service';
import { SymlinkType } from 'commons/types/symlink.type';
import * as databaseHelpers from 'database/typeorm/typeorm.extensions.helper';
import { Deleter } from 'dependencies/deleter/deleter';
import { Downloader } from 'dependencies/downloader/downloader';
import { Socket } from 'dependencies/socket/socket';
import { FeatureFlagService } from 'feature-flags/feature-flag.service';
import { ReleaseFilenameAbbreviation } from 'feature-flags/implementations/release-windows-filename-abbreviation-feature-flag';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { PolloLogger } from 'pollo-logger/pollo.logger';
import { AuditorFrameworkTypeTemplate } from 'site-admin/entities/auditor-framework-type-template.entity';

jest.mock('tenancy/contexts/tenant-wrapper', () => ({
    tenantWrapper: (_, fn) => {
        return fn();
    },
}));

describe('ControlEvidencePackageActivityService', () => {
    const auditId = 'audit-id-1';

    let service: ControlEvidencePackageActivityService;
    let mockPolloLogger: MockType<PolloLogger<PolloMessage>>;
    let event: ControlEvidencePackageEvent;
    const controlEvidenceJsonUploaderServiceMock = MockFactory.getMock(
        ControlEvidenceJsonUploaderService,
    );
    const collectSelectControlsEvidenceService = MockFactory.getMock(
        CollectSelectControlsEvidenceService,
    );
    const collectCustomerRequestControlsEvidenceService = MockFactory.getMock(
        CollectCustomerRequestControlsEvidenceService,
    );
    const featureFlagServiceMock = MockFactory.getMock(FeatureFlagService);
    const collectAllControlEvidenceService = MockFactory.getMock(CollectAllControlEvidenceService);
    const emailConfig = {
        controlEvidenceReadyEmail: jest.fn(),
    };
    const emailService = {
        sendEmail: async (...input) => input,
    };

    const getPrivateFileWithBucket = jest.fn();
    const account = new Account();
    account.id = 'account-id';
    const user = new User();
    user.id = 1;
    const company = new Company();
    company.id = 1;

    const deleteObject = jest.fn();

    const sendMessageFn = jest.fn();
    const getDownloadUrlFn = jest.fn();

    beforeEach(async () => {
        jest.resetAllMocks();
        jest.spyOn(databaseHelpers, 'getCustomRepository').mockImplementation(
            controlEvidenceConnectionMock.getCustomRepository,
        );

        mockPolloLogger = {
            log: jest.fn(),
            error: jest.fn(),
        };

        event = {
            account,
            user,
            company,
            auditId,
        };

        const module: TestingModule = await createAppTestingModule({
            providers: [
                ControlEvidencePackageActivityService,
                ReleaseFilenameAbbreviation,
                {
                    provide: FeatureFlagService,
                    useValue: featureFlagServiceMock,
                },
                {
                    provide: ControlEvidenceJsonUploaderService,
                    useValue: controlEvidenceJsonUploaderServiceMock,
                },
                {
                    provide: PolloLogger,
                    useValue: mockPolloLogger,
                },
                {
                    provide: Downloader,
                    useValue: {
                        getDownloadUrl: getDownloadUrlFn,
                        getPrivateFileWithBucket: getPrivateFileWithBucket,
                    },
                },
                {
                    provide: AuditorFrameworkRepository,
                    useValue: auditRepositoryMock,
                },
                {
                    provide: AuditorRepository,
                    useValue: auditorRepositoryMock,
                },
                {
                    provide: Socket,
                    useValue: {
                        sendMessage: sendMessageFn,
                    },
                },
                {
                    provide: Deleter,
                    useValue: {
                        deleteObject,
                    },
                },
                {
                    provide: EventsAppService,
                    useValue: {},
                },
                {
                    provide: CollectSelectControlsEvidenceService,
                    useValue: collectSelectControlsEvidenceService,
                },
                {
                    provide: CollectCustomerRequestControlsEvidenceService,
                    useValue: collectCustomerRequestControlsEvidenceService,
                },
                {
                    provide: CollectAllControlEvidenceService,
                    useValue: collectAllControlEvidenceService,
                },
                {
                    provide: EmailConfig,
                    useValue: emailConfig,
                },
                {
                    provide: EmailService,
                    useValue: emailService,
                },

                {
                    provide: EventBus,
                    useValue: {},
                },
                {
                    provide: CacheService,
                    useValue: {},
                },
                {
                    provide: WorkspacesCoreService,
                    useValue: {
                        getProductById: jest.fn().mockResolvedValue(new Product()),
                    },
                },
                { provide: AuditInfoService, useValue: {} },
                { provide: PersonnelCoreService, useValue: {} },
                { provide: RequirementsCoreService, useValue: {} },
            ],
        }).compile();

        service = module.get<ControlEvidencePackageActivityService>(
            ControlEvidencePackageActivityService,
        );
    });

    it('should be defined', () => {
        expect(service).toBeDefined();
    });

    describe('getControlsAndRequirementIndexes', () => {
        const controlIdA = 4;
        const controlIdB = 5;

        const controlA = new Control();
        controlA.id = controlIdA;
        const controlB = new Control();
        controlB.id = controlIdB;
        const customerRequest = new CustomerRequest();
        customerRequest.id = 1;
        customerRequest.controls = [controlA, controlB];

        const requirementIndex = new RequirementIndex();
        const requirement = new Requirement();
        requirement.controls = [controlA, controlB];
        requirementIndex.requirement = requirement;

        beforeEach(async () => {
            const sampleData = {
                currentPersonnelIds: [1, 2, 3],
                hiredPersonnelIds: [4, 5, 6],
                formerPersonnelIds: [7, 8, 9],
                dates: ['2024-10-10'],
                platform: AgentPlatform.MACOS,
            };

            const audit = new Audit();
            audit.id = auditId;

            auditRepositoryMock.getAuditByIdAndAccount?.mockResolvedValue(audit);

            collectSelectControlsEvidenceService.getAuditSampleData?.mockResolvedValue(sampleData);
            collectCustomerRequestControlsEvidenceService.getAuditSampleData?.mockResolvedValue(
                sampleData,
            );
            collectAllControlEvidenceService.getAuditSampleData?.mockResolvedValue(sampleData);

            customerRequestRepositoryMock.getCustomerRequestByIdWithControlsAndTestInstancesOrFail?.mockResolvedValue(
                customerRequest,
            );
        });

        describe('Given a customer request Id and control Ids', () => {
            beforeEach(() => {
                event = {
                    account,
                    user,
                    company,
                    auditId,
                    controlIds: [controlIdA, controlIdB],
                    customerRequestId: 1,
                };

                collectSelectControlsEvidenceService.getControlEvidencePackageParameters?.mockResolvedValue(
                    {
                        controls: [controlA, controlB],
                        requirementIndexes: [requirementIndex],
                    },
                );
            });

            it('Should return an upload key', async () => {
                const fileKey = 'FILE_KEY';
                controlEvidenceJsonUploaderServiceMock.uploadControlsRequirements?.mockResolvedValue(
                    fileKey,
                );

                const key = await service.getControlsAndRequirementIndexes(event);
                expect(
                    controlEvidenceJsonUploaderServiceMock.uploadControlsRequirements,
                ).toHaveBeenCalledWith(account, auditId, {
                    controls: [controlA, controlB],
                    requirementIndexes: [requirementIndex],
                });
                expect(key).toBe(fileKey);
            });
        });

        describe('Given a customer request Id and no control Ids', () => {
            beforeEach(() => {
                event = {
                    account,
                    user,
                    company,
                    auditId,
                    controlIds: [],
                    customerRequestId: 1,
                };
                collectCustomerRequestControlsEvidenceService.getControlEvidencePackageParameters?.mockResolvedValue(
                    {
                        controls: [controlA, controlB],
                        requirementIndexes: [requirementIndex],
                    },
                );
            });

            it('Should return an upload key', async () => {
                const fileKey = 'FILE_KEY';
                controlEvidenceJsonUploaderServiceMock.uploadControlsRequirements?.mockResolvedValue(
                    fileKey,
                );

                const key = await service.getControlsAndRequirementIndexes(event);
                expect(
                    controlEvidenceJsonUploaderServiceMock.uploadControlsRequirements,
                ).toHaveBeenCalledWith(account, auditId, {
                    controls: [controlA, controlB],
                    requirementIndexes: [requirementIndex],
                });
                expect(key).toBe(fileKey);
            });
        });

        describe('Given an no customer request Id and no control Ids', () => {
            event = {
                account,
                user,
                company,
                auditId,
                controlIds: [],
                customerRequestId: undefined,
            };

            beforeEach(() => {
                collectAllControlEvidenceService.getControlEvidencePackageParameters?.mockResolvedValue(
                    {
                        controls: [controlA, controlB],
                        requirementIndexes: [requirementIndex],
                    },
                );
            });

            it('Should return an upload key', async () => {
                const fileKey = 'FILE_KEY';
                controlEvidenceJsonUploaderServiceMock.uploadControlsRequirements?.mockResolvedValue(
                    fileKey,
                );

                const key = await service.getControlsAndRequirementIndexes(event);
                expect(
                    controlEvidenceJsonUploaderServiceMock.uploadControlsRequirements,
                ).toHaveBeenCalledWith(account, auditId, {
                    controls: [controlA, controlB],
                    requirementIndexes: [requirementIndex],
                });
                expect(key).toBe(fileKey);
            });
        });
    });

    describe('uploadControlEvidencePackage', () => {
        const audit = new Audit();
        audit.id = 'auditId';
        audit.frameworkType = AuditorFrameworkType.SOC_2_TYPE_2;

        const mockControlEvidenceJson = [
            { stream: Buffer.from('file1-content').toString('base64'), filename: 'file1.txt' },
            { stream: Buffer.from('file2-content').toString('base64'), filename: 'file2.txt' },
        ];

        const sampleData = {
            currentPersonnelIds: [1, 2, 3],
            hiredPersonnelIds: [4, 5, 6],
            formerPersonnelIds: [7, 8, 9],
            dates: ['2024-10-10'],
            platform: AgentPlatform.MACOS,
        };

        const mockSymlinksData: SymlinkType[] = [{ link: 'symlink-1', original: 'target-1' }];

        beforeEach(() => {
            getPrivateFileWithBucket.mockReset();
            auditRepositoryMock.getAuditByIdAndAccount?.mockResolvedValue(audit);
            collectAllControlEvidenceService.getAuditSampleData?.mockResolvedValue(sampleData);
            controlEvidenceJsonUploaderServiceMock.uploadPackage?.mockResolvedValue({
                Key: 'uploaded-key',
            });
        });

        afterEach(() => {
            jest.clearAllMocks();
        });

        describe('given both keys (`buffersKey` and `symlinksKey`)', () => {
            const mockControlEvidenceKeys: UploadedControlEvidenceKeys = {
                buffersKey: ['mock-buffers-key'],
                symlinksKey: ['mock-symlinks-key'],
            };

            it('should return the uploaded key.', async () => {
                getPrivateFileWithBucket
                    .mockResolvedValueOnce(mockControlEvidenceJson)
                    .mockResolvedValueOnce(mockSymlinksData);

                const key = await service.uploadControlEvidencePackage(
                    mockControlEvidenceKeys,
                    event,
                );

                expect(key).toBe('uploaded-key');
                expect(getPrivateFileWithBucket).toHaveBeenCalledTimes(2);
            });
        });

        describe('given only 1 key (buffersKey)', () => {
            const singleKey: UploadedControlEvidenceKeys = {
                buffersKey: ['mock-buffers-key'],
                symlinksKey: undefined,
            };

            it('should return the uploaded key.', async () => {
                getPrivateFileWithBucket.mockResolvedValueOnce(mockControlEvidenceJson);

                const key = await service.uploadControlEvidencePackage(singleKey, event);

                expect(key).toBe('uploaded-key');
                expect(getPrivateFileWithBucket).toHaveBeenCalledTimes(1);
            });
        });
    });

    describe('getControlEvidencePackageDownloadLink', () => {
        describe('Given a file key and a company archive Id', () => {
            const fileKey = 'some-file-key';
            const downloadLink = 'https://download-link.com';

            beforeEach(() => {
                event = {
                    account,
                    user,
                    company,
                    auditId,
                    companyArchiveId: 1,
                };

                getDownloadUrlFn.mockResolvedValue({ signedUrl: downloadLink });
            });

            it(`
                Should mark the company archive status as complete,
                and send a message with the event GENERATE_CONTROL_EVIDENCE_PACKAGE_COMPLETED
            `, async () => {
                await service.getControlEvidencePackageDownloadLink(fileKey, event);
                expect(sendMessageFn).toHaveBeenCalledWith(
                    event.account.id,
                    SocketEvent.GENERATE_CONTROL_EVIDENCE_PACKAGE_COMPLETED,
                    {},
                );
            });
        });

        describe('Given a file key and a customer request Id', () => {
            const fileKey = 'some-file-key';
            const downloadLink = 'https://download-link.com';

            beforeEach(() => {
                event = {
                    account,
                    user,
                    company,
                    auditId,
                    customerRequestId: 1,
                    controlIds: [1, 2],
                };
                getDownloadUrlFn.mockResolvedValue({ signedUrl: downloadLink });
            });

            it(`
                Should mark the company archive status as complete,
                and send a message with the event GENERATE_CONTROL_EVIDENCE_PACKAGE_COMPLETED
            `, async () => {
                await service.getControlEvidencePackageDownloadLink(fileKey, event);
                expect(sendMessageFn).toHaveBeenCalledWith(
                    event.account.id,
                    SocketEvent.GENERATE_REQUEST_CONTROL_EVIDENCE_PACKAGE_COMPLETED,
                    downloadLink,
                    event.customerRequestId,
                );
            });
        });
    });

    describe('cleanUpTemporaryFiles', () => {
        it('Should return an array of boolean with the same length as the input', async () => {
            const fileKeys = ['temp-file-1', 'temp-file-2', 'temp-file-3'];
            deleteObject.mockResolvedValue(true);
            const result = await service.cleanUpTemporaryFiles(fileKeys, account);
            expect(result).toHaveLength(fileKeys.length);
        });
    });

    describe('getControlEvidenceDownloadReadyEmailParams', () => {
        describe('Given that the request is not for full control evidence package', () => {
            describe('If customer request Id is passed', () => {
                const audit = new Audit();
                const auditor = new Auditor();

                beforeEach(() => {
                    event = {
                        account,
                        user,
                        company,
                        auditId,
                        customerRequestId: 1,
                    };

                    auditRepositoryMock.getAuditByIdAndAccount?.mockResolvedValue(audit);

                    auditorRepositoryMock.getAuditorsByAccountAndAudit?.mockResolvedValue([
                        auditor,
                    ]);
                });

                it('Should return an empty array', async () => {
                    const result = await service.getControlEvidenceDownloadReadyEmailParams(event);
                    expect(result).toEqual([]);
                });
            });

            describe('If both control Ids and request Id is passed', () => {
                const audit = new Audit();
                const auditor = new Auditor();

                beforeEach(() => {
                    event = {
                        account,
                        user,
                        company,
                        auditId,
                        controlIds: [1, 2, 3],
                        customerRequestId: 1,
                    };

                    auditRepositoryMock.getAuditByIdAndAccount?.mockResolvedValue(audit);

                    auditorRepositoryMock.getAuditorsByAccountAndAudit?.mockResolvedValue([
                        auditor,
                    ]);
                });

                it('Should return an empty array', async () => {
                    const result = await service.getControlEvidenceDownloadReadyEmailParams(event);
                    expect(result).toEqual([]);
                });
            });
        });

        describe('Given that the request is for full control evidence', () => {
            describe('Given an audit type of download only', () => {
                let audit: Audit;
                let auditors: Auditor[];
                let userA: User;

                beforeEach(() => {
                    const product = new Product();
                    product.name = 'Company-01';

                    audit = new Audit();
                    audit.id = `audit-id`;
                    audit.auditType = AuditType.DOWNLOAD_ONLY_AUDIT;
                    const auditorFrameworkType = new AuditorFrameworkTypeTemplate();
                    auditorFrameworkType.label = 'SOC2';
                    audit.auditorFrameworkType = auditorFrameworkType;

                    const auditor = new Auditor();
                    const entry = new Entry();
                    entry.email = '<EMAIL>';
                    auditor.entry = entry;
                    auditors = [auditor];

                    const companyA = new Company();
                    companyA.products = [product];

                    const accountA = new Account();
                    accountA.setCurrentProduct(product);

                    userA = new User();
                    userA.email = '<EMAIL>';

                    event = {
                        account: accountA,
                        user: userA,
                        company: companyA,
                        auditId,
                    };

                    auditRepositoryMock.getAuditByIdAndAccount?.mockResolvedValue(audit);

                    auditorRepositoryMock.getAuditorsByAccountAndAudit?.mockResolvedValue(auditors);
                });

                it('Should return an array with one item only and containing the email of the user', async () => {
                    const result = await service.getControlEvidenceDownloadReadyEmailParams(event);
                    expect(result).toHaveLength(1);
                    expect(result[0].recipientEmail).toEqual(userA.email);
                });
            });

            describe('Given an audit type of full audit with 2 auditors', () => {
                let audit: Audit;
                let auditors: Auditor[];
                let userA: User;

                beforeEach(() => {
                    const product = new Product();
                    product.name = 'Company-01';

                    audit = new Audit();
                    audit.id = `audit-id`;
                    audit.auditType = AuditType.FULL_AUDIT;
                    const auditorFrameworkType = new AuditorFrameworkTypeTemplate();
                    auditorFrameworkType.label = 'SOC2';
                    audit.auditorFrameworkType = auditorFrameworkType;

                    const auditorA = new Auditor();
                    const entryA = new Entry();
                    entryA.email = '<EMAIL>';
                    auditorA.entry = entryA;
                    const auditorClientA = new AuditorClient();
                    auditorClientA.id = '1';
                    entryA.auditorClients = [auditorClientA];

                    const auditorB = new Auditor();
                    const entryB = new Entry();
                    entryB.email = '<EMAIL>';
                    auditorB.entry = entryB;
                    const auditorClientB = new AuditorClient();
                    auditorClientB.id = '1';
                    entryB.auditorClients = [auditorClientB];

                    auditors = [auditorA, auditorB];

                    const companyA = new Company();
                    companyA.products = [product];

                    const accountA = new Account();
                    accountA.setCurrentProduct(product);

                    userA = new User();
                    userA.email = '<EMAIL>';

                    event = {
                        account: accountA,
                        user: userA,
                        company: companyA,
                        auditId,
                    };

                    auditRepositoryMock.getAuditByIdAndAccount?.mockResolvedValue(audit);

                    auditorRepositoryMock.getAuditorsByAccountAndAudit?.mockResolvedValue(auditors);
                });

                it('Should return an array with two items and containing the email of the auditors', async () => {
                    const result = await service.getControlEvidenceDownloadReadyEmailParams(event);
                    expect(result).toHaveLength(2);
                    expect(result[0].recipientEmail).toEqual(auditors[0].entry.email);
                    expect(result[1].recipientEmail).toEqual(auditors[1].entry.email);
                });
            });
        });
    });
});
