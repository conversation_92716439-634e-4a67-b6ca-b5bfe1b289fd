import { CustomWorkflowTrigger, SocketEvent } from '@drata/enums';
import { EventsHandler } from '@nestjs/cqrs';
import { TenantEventHandler } from 'app/events/tenant-event.handler';
import {
    PolicyApprovalRollbackService,
    StartWorkflowRollbackState,
} from 'app/policy-approvals/policy-approvals-commerce/services/policy-approval-rollback.service';
import { PolicyApprovalsCommerceService } from 'app/policy-approvals/policy-approvals-commerce/services/policy-approvals-commerce.service';
import {
    FullApprovalConfiguration,
    FullReviewGroupConfiguration,
} from 'app/policy-approvals/policy-approvals-commerce/types/full-approval-configuration.type';
import { PolicyApprovalLockPayload } from 'app/policy-approvals/policy-approvals-commerce/types/policy-approval-lock-payload.type';
import { ApprovalWorkflowHandlerType } from 'app/policy-approvals/policy-approvals-events/enums/policy-approval-workflow-handler-type.enum';
import { StartPolicyApprovalWorkflowEvent } from 'app/policy-approvals/policy-approvals-events/events/start-policy-approval-workflow.event';
import { startApprovalWorkflowV1 } from 'app/worker/workflows';
import { Account } from 'auth/entities/account.entity';
import { PolloAdapter } from 'commons/adapters/pollo.adapter';
import { UpcomingTaskType } from 'commons/enums/upcoming-task-type-enum';
import { getTemporalClient } from 'commons/helpers/temporal/client';
import config from 'config';
import { Socket } from 'dependencies/socket/socket';
import { first, isEmpty, sortBy } from 'lodash';

@EventsHandler(StartPolicyApprovalWorkflowEvent)
export class StartPolicyApprovalWorkflowHandler extends TenantEventHandler<StartPolicyApprovalWorkflowEvent> {
    constructor(
        private readonly policyApprovalsCommerceService: PolicyApprovalsCommerceService,
        private readonly policyApprovalRollbackService: PolicyApprovalRollbackService,
        private readonly socket: Socket,
    ) {
        super();
    }

    async handleEvent({ account, user, payload }: StartPolicyApprovalWorkflowEvent): Promise<void> {
        const { policy, policyVersion } = payload;

        const policyApprovalLockPayload: PolicyApprovalLockPayload = {
            reviewId: null,
            startedByUserId: user.id,
            handlerType: ApprovalWorkflowHandlerType.START,
        };

        let approvalConfigurationId: number | null = null;
        let rollbackState: StartWorkflowRollbackState | null = null;

        try {
            // Capture state for potential rollback before workflow execution
            rollbackState =
                await this.policyApprovalRollbackService.captureStateForStartWorkflowRollback(
                    policy.id,
                    policyVersion.id,
                );
            const lockAcquired = await this.lockWorkflow(
                account,
                approvalConfigurationId,
                policy.id,
                policyVersion.id,
                user.id,
                policyApprovalLockPayload,
            );

            if (!lockAcquired) {
                this.logger.warn(
                    PolloAdapter.acct(
                        'Policy approval start operation already in progress, skipping',
                        account,
                        this.constructor.name,
                    ).setIdentifier({
                        policyId: policy.id,
                        policyVersionId: policyVersion.id,
                    }),
                );
                return;
            }

            const { approvalConfiguration, reviewGroupConfiguration } =
                await this.getApprovalConfigurationForPolicy(user.id, policy.id);

            approvalConfigurationId = approvalConfiguration.id;
            if (!reviewGroupConfiguration) {
                this.logger.warn(
                    PolloAdapter.acct(
                        `No review group configuration found for approval configuration`,
                        account,
                        this.constructor.name,
                    ).setIdentifier({
                        policyId: policy.id,
                        policyVersionId: policyVersion.id,
                    }),
                );
                return;
            }

            // Start the temporal workflow
            const temporalClient = await getTemporalClient(account.domain);
            await temporalClient.startWorkflow(startApprovalWorkflowV1, {
                taskQueue: config.get('temporal.taskQueues.temporal-default'),
                args: [
                    {
                        configurationId: approvalConfiguration.id,
                        deadlineExceededPolicy: approvalConfiguration.deadlineExceededPolicy,
                        reviewGroups: approvalConfiguration.reviewGroupConfigurations.map(
                            rgConfig => ({
                                configurationId: rgConfig.id,
                                flexibleAssignmentId: rgConfig.customAssignment.id,
                                consensusRule: rgConfig.consensusRule,
                                timeline: rgConfig.timeline,
                                hierarchyLevel: rgConfig.hierarchicalLevel,
                                deadlineExceededPolicy: rgConfig.deadlineExceededPolicy,
                            }),
                        ),
                    },
                    {
                        customTaskDescription: 'CUSTOM TASK FROM POLICY APPROVAL',
                        customTaskTitle: policy?.name || 'CUSTOM TASK FROM POLICY APPROVAL',
                        customTaskTaskType: UpcomingTaskType.POLICY_APPROVALS,
                    },
                    {
                        type: CustomWorkflowTrigger.POLICY_VERSION_PUBLISHED,
                        details: {
                            triggeringUserId: user.id,
                            policyId: policy.id,
                            policyVersionId: policyVersion.id,
                        },
                    },
                ],
                memo: { accountId: account.id, domain: account.domain },
            });

            // NOTE: StartReviewGroupReviewCollectionWorkflowController.handlePolicyReviewGroupReadyForReview
            // handles the policy version status update, the release lock and the socket notifications

            this.logger.log(
                PolloAdapter.acct(
                    `Successfully started approval workflow`,
                    account,
                    this.constructor.name,
                ).setIdentifier({
                    policyId: policy.id,
                    policyVersionId: policyVersion.id,
                }),
            );
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(
                    `Failed to start approval workflow`,
                    account,
                    this.constructor.name,
                )
                    .setIdentifier({
                        policyId: policy.id,
                        policyVersionId: policyVersion.id,
                    })
                    .setError(error),
            );

            // Only perform rollback for workflow execution errors when rollback state exists
            if (rollbackState && this.isWorkflowExecutionError(error as Error)) {
                try {
                    await this.policyApprovalRollbackService.performStartWorkflowRollbackInSeparateTransaction(
                        account,
                        rollbackState,
                    );
                    this.logger.log(
                        PolloAdapter.acct(
                            'Successfully performed rollback after start workflow failure',
                            account,
                            this.constructor.name,
                        ).setIdentifier({
                            policyId: policy.id,
                            policyVersionId: policyVersion.id,
                        }),
                    );
                } catch (rollbackError) {
                    this.logger.error(
                        PolloAdapter.acct(
                            'Failed to perform rollback after start workflow failure',
                            account,
                            this.constructor.name,
                        )
                            .setIdentifier({
                                policyId: policy.id,
                                policyVersionId: policyVersion.id,
                            })
                            .setError(rollbackError),
                    );
                }
            } else {
                // Re-throw error for non-workflow errors or when no rollback state
                throw error;
            }
        } finally {
            await this.releaseLock(
                account,
                approvalConfigurationId,
                policy.id,
                policyVersion.id,
                user.id,
            );
        }
    }

    private async lockWorkflow(
        account: Account,
        approvalConfigurationId: number | null,
        policyId: number,
        policyVersionId: number,
        startedByUserId: number,
        policyApprovalLockPayload: PolicyApprovalLockPayload,
    ): Promise<boolean> {
        const { acquired } =
            await this.policyApprovalsCommerceService.lockPolicyApprovalStartWorkflow(
                account,
                policyId,
                policyVersionId,
                policyApprovalLockPayload,
            );

        if (acquired) {
            await this.notifyApprovalWorkflowStarted(
                account,
                approvalConfigurationId,
                policyVersionId,
                startedByUserId,
                true,
            );
        }

        return acquired;
    }

    private async releaseLock(
        account: Account,
        approvalConfigurationId: number | null,
        policyId: number,
        policyVersionId: number,
        startedByUserId: number,
    ): Promise<void> {
        await this.policyApprovalsCommerceService.releasePolicyApprovalStartWorkflow(
            account,
            policyId,
            policyVersionId,
        );

        await this.notifyApprovalWorkflowStarted(
            account,
            approvalConfigurationId,
            policyVersionId,
            startedByUserId,
            false,
        );
    }

    /**
     * Sends a socket notification about the state of the approval workflow handler
     */
    private async notifyApprovalWorkflowStarted(
        account: Account,
        approvalConfigurationId: number | null,
        policyVersionId: number,
        startedByUserId: number,
        isLocked: boolean,
    ): Promise<void> {
        try {
            await this.socket.sendMessage(
                account.id,
                SocketEvent.APPROVAL_WORKFLOW_HANDLER_STATE_UPDATED,
                {
                    approvalConfigurationId,
                    handlerType: ApprovalWorkflowHandlerType.START,
                    isLocked,
                    policyVersionId,
                    startedByUserId,
                },
            );
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(
                    `Failed to notify approval workflow started`,
                    account,
                    this.constructor.name,
                )
                    .setIdentifier({
                        policyVersionId,
                    })
                    .setError(error),
            );
        }
    }

    private async getApprovalConfigurationForPolicy(
        userId: number,
        policyId: number,
    ): Promise<{
        approvalConfiguration: FullApprovalConfiguration;
        reviewGroupConfiguration?: FullReviewGroupConfiguration;
    }> {
        let fullApprovalConfigurations =
            await this.policyApprovalsCommerceService.getApprovalConfigurationsForPolicy(policyId);

        if (isEmpty(fullApprovalConfigurations)) {
            await this.policyApprovalsCommerceService.createInitialApprovalConfiguration(
                userId,
                policyId,
            );
            fullApprovalConfigurations =
                await this.policyApprovalsCommerceService.getApprovalConfigurationsForPolicy(
                    policyId,
                );
        }

        const sortedConfigurations = sortBy(
            fullApprovalConfigurations,
            approvalConfig => approvalConfig.id,
        );

        const approvalConfiguration = sortedConfigurations[sortedConfigurations.length - 1];
        const reviewGroupConfiguration = first(approvalConfiguration.reviewGroupConfigurations);

        return { approvalConfiguration, reviewGroupConfiguration };
    }

    /**
     * Determines if the error occurred during workflow execution and requires rollback
     */
    private isWorkflowExecutionError(error: Error): boolean {
        // Check for Temporal error patterns in error message or structure
        const errorMessage = error.message?.toLowerCase() || '';
        const errorName = error.name?.toLowerCase() || '';

        // Check for common Temporal error indicators
        if (
            errorMessage.includes('workflow') ||
            errorMessage.includes('temporal') ||
            errorMessage.includes('activity') ||
            errorName.includes('workflow') ||
            errorName.includes('temporal')
        ) {
            return true;
        }

        // Check for nested Temporal errors with cause structure
        const errorObj = error as any;
        if (errorObj?.cause) {
            // Check for activity failure indicators
            if (
                errorObj.cause.activityType ||
                errorObj.cause.activityId ||
                errorObj.cause.retryState === 'MAXIMUM_ATTEMPTS_REACHED' ||
                errorObj.cause.failure?.activityFailureInfo
            ) {
                return true;
            }

            // Check for nested failure messages indicating workflow/activity errors
            const nestedFailure = errorObj.cause.failure;
            if (nestedFailure?.message || nestedFailure?.applicationFailureInfo) {
                return true;
            }
        }

        return false;
    }
}
