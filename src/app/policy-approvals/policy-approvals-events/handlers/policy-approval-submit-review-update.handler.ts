import { SocketEvent } from '@drata/enums';
import { <PERSON><PERSON>andler } from '@nestjs/cqrs';
import { WorkflowFailedError } from '@temporalio/client';
import { ApplicationFailure, WorkflowError } from '@temporalio/workflow';
import { ReviewStatus } from 'app/approvals/v2/approval-review-groups-core/types/review-status.enum';
import { ApprovalStatusType } from 'app/approvals/v2/approvals-core/types/approval-status.enum';
import { TenantEventHandler } from 'app/events/tenant-event.handler';
import {
    PolicyApprovalRollbackService,
    RollbackState,
} from 'app/policy-approvals/policy-approvals-commerce/services/policy-approval-rollback.service';
import { PolicyApprovalsCommerceService } from 'app/policy-approvals/policy-approvals-commerce/services/policy-approvals-commerce.service';
import { PolicyApprovalLockPayload } from 'app/policy-approvals/policy-approvals-commerce/types/policy-approval-lock-payload.type';
import { ApprovalWorkflowHandlerType } from 'app/policy-approvals/policy-approvals-events/enums/policy-approval-workflow-handler-type.enum';
import { PolicyApprovalSubmitReviewUpdateEvent } from 'app/policy-approvals/policy-approvals-events/events/policy-approval-submit-review-update.event';
import { submitReviewUpdate } from 'app/worker/workflows';
import { Account } from 'auth/entities/account.entity';

import { PolloAdapter } from 'commons/adapters/pollo.adapter';
import { TemporalClient } from 'commons/helpers/temporal/client';
import { Socket } from 'dependencies/socket/socket';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';

@EventsHandler(PolicyApprovalSubmitReviewUpdateEvent)
export class PolicyApprovalSubmitReviewUpdateHandler extends TenantEventHandler<PolicyApprovalSubmitReviewUpdateEvent> {
    constructor(
        private readonly policyApprovalsCommerceService: PolicyApprovalsCommerceService,
        private readonly policyApprovalRollbackService: PolicyApprovalRollbackService,
        private readonly socket: Socket,
    ) {
        super();
    }

    async handleEvent(event: PolicyApprovalSubmitReviewUpdateEvent): Promise<void> {
        const { account, payload, user } = event;
        const {
            approvalId,
            newReviewStatus,
            policy,
            policyId,
            policyVersion,
            rejectionMessage,
            reviewGroupId,
            reviewId,
            waasRunId,
        } = payload;

        const handlerType =
            newReviewStatus === ReviewStatus.APPROVED
                ? ApprovalWorkflowHandlerType.APPROVED
                : ApprovalWorkflowHandlerType.CHANGES_REQUESTED;

        const policyApprovalLockPayload: PolicyApprovalLockPayload = {
            reviewId,
            startedByUserId: user.id,
            handlerType,
        };

        let rollbackState: RollbackState | null = null;

        try {
            const reviewersIds =
                await this.policyApprovalsCommerceService.getReviewGroupReviewersIds(reviewGroupId);

            const tierName =
                await this.policyApprovalsCommerceService.getReviewGroupName(reviewGroupId);

            rollbackState = await this.policyApprovalRollbackService.captureStateForRollback(
                reviewId,
                reviewGroupId,
                approvalId,
                policyVersion.id,
            );

            const lockAcquired = await this.lockWorkflow(
                account,
                policyId,
                policyVersion.id,
                approvalId,
                reviewGroupId,
                policyApprovalLockPayload,
            );

            if (!lockAcquired) {
                this.logger.warn(
                    PolloAdapter.acct(
                        'Policy approval operation already in progress, skipping',
                        account,
                        this.constructor.name,
                    ).setIdentifier({
                        policyId,
                        policyVersionId: policyVersion.id,
                        reviewId,
                    }),
                );
                return;
            }

            const temporalClient = await TemporalClient.getInstance();
            const [reviewStatus, newApprovalStatus] = await temporalClient.sendUpdate(
                waasRunId,
                submitReviewUpdate,
                [reviewId, newReviewStatus, rejectionMessage],
            );

            if (reviewStatus === ReviewStatus.APPROVED) {
                await this.policyApprovalsCommerceService.handlePolicyApprovalReviewApproved(
                    account,
                    user,
                    policy,
                    policyVersion,
                    tierName,
                );
            }

            // TODO https://drata.atlassian.net/browse/ENG-73826 Send socket message about review and approval status
            if (newApprovalStatus === ApprovalStatusType.APPROVED) {
                await this.policyApprovalsCommerceService.handlePolicyApprovalApproved(
                    account,
                    user,
                    policy,
                    policyVersion,
                );
            } else if (newApprovalStatus === ApprovalStatusType.CHANGES_REQUESTED) {
                await this.policyApprovalsCommerceService.handlePolicyApprovalChangesRequested(
                    account,
                    user,
                    policy,
                    policyVersion,
                    rejectionMessage ?? '',
                    reviewersIds,
                );
            }

            this.logger.log(
                PolloAdapter.acct(
                    'Successfully sent temporal workflow update',
                    account,
                    this.constructor.name,
                ).setIdentifier({
                    policyId,
                    policyVersionId: policyVersion.id,
                    reviewId,
                    waasRunId,
                    reviewStatus,
                    newApprovalStatus,
                }),
            );
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct('Policy approval workflow failed', account, this.constructor.name)
                    .setIdentifier({
                        policyId,
                        policyVersionId: policyVersion.id,
                        reviewId,
                        ...(rollbackState ? { approvalId: rollbackState.approvalId } : {}),
                    })
                    .setError(error),
            );

            if (rollbackState && this.isWorkflowExecutionError(error)) {
                try {
                    await this.policyApprovalRollbackService.performRollbackInSeparateTransaction(
                        account,
                        rollbackState,
                    );
                    this.logger.log(
                        PolloAdapter.acct(
                            'Successfully performed rollback after workflow failure',
                            account,
                            this.constructor.name,
                        ).setIdentifier({
                            policyId,
                            policyVersionId: policyVersion.id,
                            reviewId,
                        }),
                    );
                } catch (rollbackError) {
                    this.logger.error(
                        PolloAdapter.acct(
                            'Failed to perform rollback after workflow failure',
                            account,
                            this.constructor.name,
                        )
                            .setIdentifier({
                                policyId,
                                policyVersionId: policyVersion.id,
                                reviewId,
                            })
                            .setError(rollbackError),
                    );
                }
            } else {
                throw error;
            }
        } finally {
            try {
                await this.releaseLock(
                    account,
                    approvalId,
                    policyId,
                    policyVersion.id,
                    reviewGroupId,
                    policyApprovalLockPayload,
                );
            } catch (error) {
                // Log the error but don't throw to prevent masking the original error
                this.logger.error(
                    PolloMessage.msg('Failed to release policy approval lock')
                        .setError(error)
                        .setIdentifier({
                            accountId: account.id,
                            policyId,
                            policyVersionId: policyVersion.id,
                            approvalId,
                            reviewGroupId,
                        }),
                );
            }
        }
    }

    /**
     * Acquires a lock for the policy approval workflow and sends socket notification
     * @param account - Account context
     * @param policyId - Policy ID
     * @param policyVersionId - Policy Version ID
     * @param approvalId - Approval ID
     * @param reviewGroupId - Review Group ID
     * @returns true if lock was acquired, false otherwise
     */
    private async lockWorkflow(
        account: Account,
        policyId: number,
        policyVersionId: number,
        approvalId: number,
        reviewGroupId: number,
        policyApprovalLockPayload: PolicyApprovalLockPayload,
    ): Promise<boolean> {
        const { acquired } = await this.policyApprovalsCommerceService.lockPolicyApprovalWorkflow(
            account,
            policyId,
            policyVersionId,
            approvalId,
            policyApprovalLockPayload,
        );

        if (acquired) {
            await this.notifyApprovalWorkflowHandlerState(
                account,
                approvalId,
                policyVersionId,
                reviewGroupId,
                policyApprovalLockPayload,
                true,
            );
        }

        return acquired;
    }

    /**
     * Releases the lock for the policy approval workflow and sends socket notification
     * @param account - Account context
     * @param approvalId - Approval ID
     * @param policyId - Policy ID
     * @param policyVersionId - Policy Version ID
     * @param reviewGroupId - Review Group ID
     */
    private async releaseLock(
        account: Account,
        approvalId: number,
        policyId: number,
        policyVersionId: number,
        reviewGroupId: number,
        payload: PolicyApprovalLockPayload,
    ): Promise<void> {
        await this.policyApprovalsCommerceService.releasePolicyApprovalWorkflow(
            account,
            approvalId,
            policyId,
            policyVersionId,
        );

        await this.notifyApprovalWorkflowHandlerState(
            account,
            approvalId,
            policyVersionId,
            reviewGroupId,
            payload,
            false,
        );
    }

    /**
     * Sends a socket notification about the state of the approval workflow handler
     */
    private async notifyApprovalWorkflowHandlerState(
        account: Account,
        approvalId: number,
        policyVersionId: number,
        reviewGroupId: number,
        payload: PolicyApprovalLockPayload,
        locked: boolean,
    ) {
        try {
            await this.socket.sendMessage(
                account.id,
                SocketEvent.APPROVAL_WORKFLOW_HANDLER_STATE_UPDATED,
                {
                    approvalId,
                    isLocked: locked,
                    policyVersionId,
                    reviewGroupId,
                    ...payload,
                },
            );
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(
                    'Failed to send socket notification for approval workflow handler state',
                    account,
                )
                    .setIdentifier({
                        approvalId,
                        policyVersionId,
                        reviewGroupId,
                        ...payload,
                    })
                    .setError(error),
            );
        }
    }

    /**
     * Determines if the error occurred during workflow execution and requires rollback
     */
    private isWorkflowExecutionError(error: Error): boolean {
        // Check for direct Temporal error instances
        if (
            error instanceof WorkflowFailedError ||
            error instanceof ApplicationFailure ||
            error instanceof WorkflowError
        ) {
            return true;
        }

        // Check for nested Temporal errors with cause.cause structure
        const errorObj = error as any;
        if (errorObj?.cause?.cause) {
            // Check for activity failure indicators
            if (
                errorObj.cause.activityType ||
                errorObj.cause.activityId ||
                errorObj.cause.retryState === 'MAXIMUM_ATTEMPTS_REACHED' ||
                errorObj.cause.failure?.activityFailureInfo
            ) {
                return true;
            }

            // Check for nested failure messages indicating workflow/activity errors
            const nestedFailure = errorObj.cause.cause.failure || errorObj.cause.failure;
            if (nestedFailure?.message || nestedFailure?.applicationFailureInfo) {
                return true;
            }
        }

        return false;
    }
}
