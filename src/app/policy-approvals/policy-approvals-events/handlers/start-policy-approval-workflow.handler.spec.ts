import { temporal } from '@temporalio/proto/protos/root';
import { ConsensusRule } from 'app/approvals/v2/approval-configuration-core/types/consensus-rule.enum';
import { DeadlineExceededPolicy } from 'app/approvals/v2/approval-configuration-core/types/deadline-exceeded-policy.enum';
import { PolicyApprovalRollbackService } from 'app/policy-approvals/policy-approvals-commerce/services/policy-approval-rollback.service';
import { PolicyApprovalsCommerceService } from 'app/policy-approvals/policy-approvals-commerce/services/policy-approvals-commerce.service';
import { StartPolicyApprovalWorkflowEvent } from 'app/policy-approvals/policy-approvals-events/events/start-policy-approval-workflow.event';
import { StartPolicyApprovalWorkflowHandler } from 'app/policy-approvals/policy-approvals-events/handlers/start-policy-approval-workflow.handler';
import { User } from 'app/users/entities/user.entity';
import { PolicyVersion } from 'app/users/policies/entities/policy-version.entity';
import { Policy } from 'app/users/policies/entities/policy.entity';
import { startApprovalWorkflowV1 } from 'app/worker/workflows';
import { Account } from 'auth/entities/account.entity';
import { PolicyVersionStatus } from 'commons/enums/users/policies/policy-version-status.enum';
import { createAppTestingModule } from 'commons/helpers/app-testing-module.helper';
import { getTemporalClient } from 'commons/helpers/temporal/client';
import config from 'config';
import { Socket } from 'dependencies/socket/socket';
import { mock, MockProxy } from 'jest-mock-extended';

// Mock external dependencies
jest.mock('app/worker/workflows');
jest.mock('commons/helpers/temporal/client');
jest.mock('config');

describe('StartPolicyApprovalWorkflowHandler', () => {
    let handler: StartPolicyApprovalWorkflowHandler;
    let mockPolicyApprovalsCommerceService: MockProxy<PolicyApprovalsCommerceService>;
    let mockPolicyApprovalRollbackService: MockProxy<PolicyApprovalRollbackService>;
    let mockSocket: MockProxy<Socket>;
    let mockTemporalClient: MockProxy<any>;

    const mockAccount = mock<Account>();
    const mockUser = mock<User>();
    const mockPolicy = mock<Policy>();
    const mockPolicyVersion = mock<PolicyVersion>();

    // Set up mock properties
    (mockAccount as any).id = 1;
    mockAccount.domain = 'test.com';
    mockUser.id = 1;
    mockPolicy.id = 1;
    mockPolicy.name = 'Test Policy';
    mockPolicyVersion.id = 1;
    mockPolicyVersion.policyVersionStatus = PolicyVersionStatus.DRAFT;

    beforeEach(async () => {
        // Reset all mocks
        jest.clearAllMocks();

        // Setup temporal client mock
        mockTemporalClient = {
            startWorkflow: jest.fn().mockResolvedValue({ workflowId: 'test-workflow-id' }),
            describeWorkflowExecution: jest.fn().mockResolvedValue({
                history: {
                    events: [
                        {
                            eventType:
                                temporal.api.enums.v1.EventType
                                    .EVENT_TYPE_CHILD_WORKFLOW_EXECUTION_STARTED,
                        },
                    ],
                },
            }),
        };
        (getTemporalClient as jest.Mock).mockResolvedValue(mockTemporalClient);

        // Setup config mock
        (config.get as jest.Mock).mockReturnValue('test-queue');

        const module = await createAppTestingModule({
            providers: [
                StartPolicyApprovalWorkflowHandler,
                {
                    provide: PolicyApprovalsCommerceService,
                    useValue: mock<PolicyApprovalsCommerceService>(),
                },
                {
                    provide: PolicyApprovalRollbackService,
                    useValue: mock<PolicyApprovalRollbackService>(),
                },
                {
                    provide: Socket,
                    useValue: mock<Socket>(),
                },
            ],
        }).compile();

        handler = module.get<StartPolicyApprovalWorkflowHandler>(
            StartPolicyApprovalWorkflowHandler,
        );
        mockPolicyApprovalsCommerceService = module.get(PolicyApprovalsCommerceService);
        mockPolicyApprovalRollbackService = module.get(PolicyApprovalRollbackService);
        mockSocket = module.get(Socket);
    });

    describe('handleEvent', () => {
        const mockEvent = new StartPolicyApprovalWorkflowEvent(mockAccount, mockUser, {
            policy: mockPolicy,
            policyVersion: mockPolicyVersion,
        });

        const mockApprovalConfiguration = {
            id: 1,
            deadlineExceededPolicy: DeadlineExceededPolicy.WAIT,
            createdAt: new Date(),
            reviewGroupConfigurations: [
                {
                    id: 1,
                    customAssignment: { id: 1 } as any,
                    consensusRule: ConsensusRule.ALL,
                    timeline: '14d' as any,
                    hierarchicalLevel: 1,
                    deadlineExceededPolicy: DeadlineExceededPolicy.WAIT,
                },
            ],
        };

        beforeEach(() => {
            // Setup default mocks
            mockPolicyApprovalsCommerceService.getApprovalConfigurationsForPolicy.mockResolvedValue(
                [mockApprovalConfiguration],
            );
            mockPolicyApprovalsCommerceService.handlePolicyApprovalWorkflowStarted.mockResolvedValue();
            mockPolicyApprovalsCommerceService.lockPolicyApprovalStartWorkflow.mockResolvedValue({
                lockKey: 'test-lock-key',
                acquired: true,
            });
            mockPolicyApprovalsCommerceService.releasePolicyApprovalStartWorkflow.mockResolvedValue();
            mockPolicyApprovalsCommerceService.createInitialApprovalConfiguration.mockResolvedValue();
            mockPolicyApprovalRollbackService.captureStateForStartWorkflowRollback.mockResolvedValue(
                {
                    policyId: mockPolicy.id,
                    policyVersionId: mockPolicyVersion.id,
                    previousPolicyVersionStatus: mockPolicyVersion.policyVersionStatus,
                },
            );
            mockPolicyApprovalRollbackService.performStartWorkflowRollbackInSeparateTransaction.mockResolvedValue();
            mockSocket.sendMessage.mockResolvedValue({} as any);
        });

        it('should create initial approval configuration when none exists', async () => {
            // Arrange
            mockPolicyApprovalsCommerceService.getApprovalConfigurationsForPolicy
                .mockResolvedValueOnce([])
                .mockResolvedValueOnce([mockApprovalConfiguration]);

            // Act
            await handler.handleEvent(mockEvent);

            // Assert
            expect(
                mockPolicyApprovalsCommerceService.createInitialApprovalConfiguration,
            ).toHaveBeenCalledWith(1, 1);
            expect(
                mockPolicyApprovalsCommerceService.getApprovalConfigurationsForPolicy,
            ).toHaveBeenCalledTimes(2);
        });

        it('should handle missing review group configuration gracefully', async () => {
            // Arrange
            const configWithoutReviewGroups = {
                ...mockApprovalConfiguration,
                reviewGroupConfigurations: [],
            };
            mockPolicyApprovalsCommerceService.getApprovalConfigurationsForPolicy.mockResolvedValue(
                [configWithoutReviewGroups],
            );

            // Act
            await handler.handleEvent(mockEvent);

            // Assert
            expect(mockTemporalClient.startWorkflow).not.toHaveBeenCalled();
            expect(
                mockPolicyApprovalsCommerceService.handlePolicyApprovalWorkflowStarted,
            ).not.toHaveBeenCalled();
        });

        it('should handle errors gracefully and not re-throw them', async () => {
            // Arrange
            const error = new Error('Temporal workflow failed');
            mockTemporalClient.startWorkflow.mockRejectedValue(error);

            // Act & Assert - should not throw, errors are handled internally
            await expect(handler.handleEvent(mockEvent)).resolves.not.toThrow();
        });

        it('should use default policy name when policy name is null', async () => {
            // Arrange
            const mockEventWithNullPolicyName = new StartPolicyApprovalWorkflowEvent(
                mockAccount,
                mockUser,
                {
                    policy: { ...mockPolicy, name: null } as any,
                    policyVersion: mockPolicyVersion,
                },
            );

            // Act
            await handler.handleEvent(mockEventWithNullPolicyName);

            // Assert
            expect(mockTemporalClient.startWorkflow).toHaveBeenCalledWith(
                startApprovalWorkflowV1,
                expect.objectContaining({
                    args: expect.arrayContaining([
                        expect.objectContaining({
                            customTaskTitle: 'CUSTOM TASK FROM POLICY APPROVAL',
                        }),
                    ]),
                }),
            );
        });

        it('should perform rollback when workflow execution fails', async () => {
            // Arrange
            const workflowError = new Error('Workflow failed');
            workflowError.name = 'WorkflowFailedError';
            mockTemporalClient.startWorkflow.mockRejectedValue(workflowError);

            // Act
            await handler.handleEvent(mockEvent);

            // Assert
            expect(
                mockPolicyApprovalRollbackService.captureStateForStartWorkflowRollback,
            ).toHaveBeenCalledWith(mockPolicy.id, mockPolicyVersion.id);
            expect(
                mockPolicyApprovalRollbackService.performStartWorkflowRollbackInSeparateTransaction,
            ).toHaveBeenCalledWith(mockAccount, {
                policyId: mockPolicy.id,
                policyVersionId: mockPolicyVersion.id,
                previousPolicyVersionStatus: mockPolicyVersion.policyVersionStatus,
            });
        });

        it('should not perform rollback for non-workflow errors', async () => {
            // Arrange
            const nonWorkflowError = new Error('Database connection failed');
            mockPolicyApprovalsCommerceService.getApprovalConfigurationsForPolicy.mockRejectedValue(
                nonWorkflowError,
            );

            // Act & Assert
            await expect(handler.handleEvent(mockEvent)).rejects.toThrow(
                'Database connection failed',
            );
            expect(
                mockPolicyApprovalRollbackService.performStartWorkflowRollbackInSeparateTransaction,
            ).not.toHaveBeenCalled();
        });

        it('should handle rollback failure gracefully', async () => {
            // Arrange
            const workflowError = new Error('Workflow failed');
            workflowError.name = 'WorkflowFailedError';
            const rollbackError = new Error('Rollback failed');

            mockTemporalClient.startWorkflow.mockRejectedValue(workflowError);
            mockPolicyApprovalRollbackService.performStartWorkflowRollbackInSeparateTransaction.mockRejectedValue(
                rollbackError,
            );

            // Act
            await handler.handleEvent(mockEvent);

            // Assert
            expect(
                mockPolicyApprovalRollbackService.performStartWorkflowRollbackInSeparateTransaction,
            ).toHaveBeenCalled();
            // Should not throw the rollback error, just log it
        });
    });
});
