import type { User } from 'app/users/entities/user.entity';
import type { PolicyVersion } from 'app/users/policies/entities/policy-version.entity';
import type { Policy } from 'app/users/policies/entities/policy.entity';
import type { Account } from 'auth/entities/account.entity';
import { BaseEvent } from 'commons/observables/events/base.event';

export interface StartPolicyApprovalWorkflowEventPayload {
    policy: Policy;
    policyVersion: PolicyVersion;
}

export class StartPolicyApprovalWorkflowEvent extends BaseEvent {
    constructor(
        readonly account: Account,
        readonly user: User,
        readonly payload: StartPolicyApprovalWorkflowEventPayload,
    ) {
        super(account);
    }
}
