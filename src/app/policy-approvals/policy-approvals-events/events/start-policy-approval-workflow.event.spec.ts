import { StartPolicyApprovalWorkflowEvent } from 'app/policy-approvals/policy-approvals-events/events/start-policy-approval-workflow.event';
import { User } from 'app/users/entities/user.entity';
import { Account } from 'auth/entities/account.entity';

describe('StartPolicyApprovalWorkflowEvent', () => {
    const mockAccount = { id: 1, domain: 'test.com' } as Account;
    const mockUser = { id: 1, name: 'Test User' } as User;
    const mockPayload = {
        policyId: 1,
        policyVersionId: 2,
    };

    describe('constructor', () => {
        it('should create event with correct properties', () => {
            // Act
            const event = new StartPolicyApprovalWorkflowEvent(mockAccount, mockUser, mockPayload);

            // Assert
            expect(event.account).toBe(mockAccount);
            expect(event.user).toBe(mockUser);
            expect(event.payload).toBe(mockPayload);
            expect(event.payload.policyId).toBe(1);
            expect(event.payload.policyVersionId).toBe(2);
        });

        it('should extend BaseEvent', () => {
            // Act
            const event = new StartPolicyApprovalWorkflowEvent(mockAccount, mockUser, mockPayload);

            // Assert
            expect(event.account).toBe(mockAccount);
        });
    });
});
