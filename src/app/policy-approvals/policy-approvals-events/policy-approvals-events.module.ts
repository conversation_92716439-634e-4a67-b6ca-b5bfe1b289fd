import { Module } from '@nestjs/common';
import { PolicyApprovalsCommerceModule } from 'app/policy-approvals/policy-approvals-commerce/policy-approvals-commerce.module';
import { PolicyApprovalCancelHandler } from 'app/policy-approvals/policy-approvals-events/handlers/policy-approval-cancel-workflow.handler';
import { PolicyApprovalSubmitReviewUpdateHandler } from 'app/policy-approvals/policy-approvals-events/handlers/policy-approval-submit-review-update.handler';
import { PolicyApprovalTerminateHandler } from 'app/policy-approvals/policy-approvals-events/handlers/policy-approval-terminate-workflow.handler';
import { PolicyVersionApprovalOverrideHandler } from 'app/policy-approvals/policy-approvals-events/handlers/policy-version-approval-override.handler';
import { StartPolicyApprovalWorkflowHandler } from 'app/policy-approvals/policy-approvals-events/handlers/start-policy-approval-workflow.handler';
import { CacheModule } from 'cache/cache.module';
import { ModuleType, ModuleTypes } from 'commons/decorators/module-type.decorator';

@ModuleType(ModuleTypes.CONTROL_PLANE)
@Module({
    // eslint-disable-next-line local-rules/restrict-control-plane-module-imports
    imports: [PolicyApprovalsCommerceModule, CacheModule],
    providers: [
        PolicyApprovalCancelHandler,
        PolicyApprovalSubmitReviewUpdateHandler,
        PolicyApprovalTerminateHandler,
        PolicyVersionApprovalOverrideHandler,
        StartPolicyApprovalWorkflowHandler,
    ],
    exports: [],
})
export class PolicyApprovalsEventsModule {}
