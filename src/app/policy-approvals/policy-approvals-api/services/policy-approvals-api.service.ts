import { ErrorCode } from '@drata/enums';
import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { StringValue } from '@temporalio/common';
import { NotFoundError } from 'app/apis/exceptions/app.error';
import { ApprovalConfigurationCoreService } from 'app/approvals/v2/approval-configuration-core/services/approval-configuration-core.service';
import { ConsensusRule } from 'app/approvals/v2/approval-configuration-core/types/consensus-rule.enum';
import { CreateReviewGroupConfigurationRequest } from 'app/approvals/v2/approval-configuration-core/types/create-review-group-configuration-request.type';
import { DeadlineExceededPolicy } from 'app/approvals/v2/approval-configuration-core/types/deadline-exceeded-policy.enum';
import { ReviewGroupCoreService } from 'app/approvals/v2/approval-review-groups-core/services/review-group-core.service';
import { ReviewGroup } from 'app/approvals/v2/approval-review-groups-core/types/review-group.type';
import { ReviewStatus } from 'app/approvals/v2/approval-review-groups-core/types/review-status.enum';
import { ApprovalStatusType } from 'app/approvals/v2/approvals-core/types/approval-status.enum';
import { ApprovalsOrchestrationService } from 'app/approvals/v2/approvals-orchestration/services/approvals-orchestration.service';
import { CustomAssignmentResponsibilityEnum } from 'app/custom-assignment-core/enums/custom-assignment.enums';
import { CustomAssignmentCoreService } from 'app/custom-assignment-core/services/custom-assignment-core.service';
import { CustomAssignmentType } from 'app/custom-assignment-core/types/custom-assignment.types';
import {
    FullApprovalConfigurationResponse,
    ResponsibilityAssignment,
    RoleAssignment,
} from 'app/policy-approvals/policy-approvals-api/types/full-approval-configuration-response.type';
import { FullApprovalResponse } from 'app/policy-approvals/policy-approvals-api/types/full-approval-response.type';
import { PolicyApprovalReviewRequest } from 'app/policy-approvals/policy-approvals-api/types/policy-approval-review-request.type';
import { ReviewGroupConfiguration } from 'app/policy-approvals/policy-approvals-api/types/review-group-configuration.type';
import { ReviewWithUser } from 'app/policy-approvals/policy-approvals-api/types/reviews.type';
import { PolicyApprovalsCommerceService } from 'app/policy-approvals/policy-approvals-commerce/services/policy-approvals-commerce.service';
import { FullApprovalConfiguration } from 'app/policy-approvals/policy-approvals-commerce/types/full-approval-configuration.type';
import { FullApprovalWithReviews } from 'app/policy-approvals/policy-approvals-commerce/types/full-approval-with-reviews.type';
import { PolicyApprovalLockPayload } from 'app/policy-approvals/policy-approvals-commerce/types/policy-approval-lock-payload.type';
import { PolicyVersionApprovalOverrideEvent } from 'app/policy-approvals/policy-approvals-events/events/policy-version-approval-override.event';
import { User } from 'app/users/entities/user.entity';
import { PolicyVersion } from 'app/users/policies/entities/policy-version.entity';
import { Policy } from 'app/users/policies/entities/policy.entity';
import { PolicyApprovalConfigurationUpdatedEvent } from 'app/users/policies/observables/events/policy-approval-configuration-updated.event';
import { PolicyApprovedByApproverEvent } from 'app/users/policies/observables/events/policy-approved-by-approver.event';
import { PoliciesCoreService } from 'app/users/policies/services/policies-core.service';
import { PolicyEmailsCoreService } from 'app/users/policies/services/policy-emails-core.service';
import { GetSummarizedPolicyWithoutDeletedVersions } from 'app/users/policies/types/get-policy-summarized.type';
import { UsersCoreService } from 'app/users/services/users-core.service';
import {
    overrideApprovalAndReviewGroupsUpdate,
    overrideReviewGroupUpdate,
} from 'app/worker/workflows';
import { Account } from 'auth/entities/account.entity';
import { PolloAdapter } from 'commons/adapters/pollo.adapter';
import { TenancyTransaction } from 'commons/decorators/transaction';
import { PolicyVersionStatus } from 'commons/enums/users/policies/policy-version-status.enum';
import { Role } from 'commons/enums/users/role.enum';
import { ForbiddenException } from 'commons/exceptions/forbidden.exception';
import { approvalHasFinalizedStatus } from 'commons/helpers/policy-approval.helper';
import { isPolicyOwner } from 'commons/helpers/policy.helper';
import { TemporalClient } from 'commons/helpers/temporal/client';
import { hasRole } from 'commons/helpers/user.helper';
import { AppService } from 'commons/services/app.service';
import _ from 'lodash';
import ms from 'ms';
import { Span } from 'nestjs-ddtrace';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';

@Injectable()
export class PolicyApprovalsApiService extends AppService {
    constructor(
        private readonly approvalConfigurationCoreService: ApprovalConfigurationCoreService,
        private readonly approvalsOrchestrationService: ApprovalsOrchestrationService,
        private readonly policiesCoreService: PoliciesCoreService,
        private readonly policyApprovalsCommerceService: PolicyApprovalsCommerceService,
        private readonly policyEmailsCoreService: PolicyEmailsCoreService,
        private readonly reviewGroupCoreService: ReviewGroupCoreService,
        private readonly userCoreService: UsersCoreService,
        private readonly customAssignmentCoreService: CustomAssignmentCoreService,
    ) {
        super();
    }

    @Span()
    @TenancyTransaction()
    public async createApprovalConfiguration(
        policyId: number,
        account: Account,
        user: User,
        reviewGroups: ReviewGroupConfiguration[],
    ): Promise<FullApprovalConfigurationResponse> {
        const policy = await this.policiesCoreService.getPolicyById(policyId);
        const configurationCreator = await this.userCoreService.getUserById(user.id);

        const oldReviewGroupConfigurations = await this.policyApprovalsCommerceService
            .getApprovalConfigurationsForPolicy(policyId)
            .then(res => _.maxBy(res, config => config.createdAt)?.reviewGroupConfigurations ?? []);

        const newApprovalConfiguration =
            await this.approvalConfigurationCoreService.createApprovalConfiguration({
                ownerId: configurationCreator.id,
            });

        await this.policyApprovalsCommerceService.linkPolicyAndApprovalConfiguration(
            policyId,
            newApprovalConfiguration.id,
        );

        const tasks = _.map(reviewGroups, async group => {
            const reviewGroupConfigurationRequest = {
                name: group.name,
                consensusRule: group.consensusRule,
                consensusRuleSubsetNumber: group?.consensusRuleNumber,
                timeline: group.timeline,
                hierarchicalLevel: group.hierarchyLevel,
                deadlineExceededPolicy: DeadlineExceededPolicy.WAIT,
            } as CreateReviewGroupConfigurationRequest;

            const reviewGroupConfiguration =
                await this.approvalsOrchestrationService.createReviewGroupConfigurationWithCustomAssignment(
                    newApprovalConfiguration.id,
                    {
                        ...reviewGroupConfigurationRequest,
                        customAssignment: {
                            userIds: group.userIds,
                        },
                    },
                );

            const reviewGroupUsers = await this.userCoreService.getUsersByIds(group.userIds);

            return { reviewGroupUsers, reviewGroupConfiguration };
        });

        const responses = await Promise.all(tasks);
        const response: FullApprovalConfigurationResponse = {
            id: newApprovalConfiguration.id,
            reviewGroupConfigurations: _.map(
                responses,
                ({ reviewGroupConfiguration, reviewGroupUsers }) => ({
                    ...reviewGroupConfiguration,
                    userAssignmentList: reviewGroupUsers,
                    roleAssignmentList: [],
                    responsibilityAssignment: null,
                    tier: reviewGroupConfiguration.hierarchicalLevel,
                }),
            ),
            // The total deadline is equal to the sum of all reviewGroup deadlines
            timeline: ms(
                _.sumBy(responses, ({ reviewGroupConfiguration }) =>
                    ms(reviewGroupConfiguration.timeline),
                ),
            ),
        };

        this._eventBus.publish(
            await PolicyApprovalConfigurationUpdatedEvent.buildEvent(
                account,
                user,
                policy,
                oldReviewGroupConfigurations,
                response.reviewGroupConfigurations,
                this.userCoreService,
            ),
        );

        return response;
    }

    @Span()
    public async getPolicyReviewerList(
        policyId: number,
        versionId: number,
        approvedOnly: boolean,
    ): Promise<ReviewWithUser[]> {
        const [latestApproval] = await this.policyApprovalsCommerceService.getApprovalsForPolicy(
            policyId,
            versionId,
        );

        if (!latestApproval) {
            return [];
        }

        const userIds = _(latestApproval.reviewGroups)
            .flatMap(group => group.reviews)
            .filter(({ status }) => !approvedOnly || status === ReviewStatus.APPROVED)
            .map(review => review.userId)
            .uniq()
            .value();

        const reviewUsers = await this.userCoreService
            .getUsersByIds(userIds)
            .then(users => _.keyBy(users, 'id'));

        return _.flatMap(latestApproval.reviewGroups, group =>
            _(group.reviews)
                .map(review => ({
                    ...review,
                    user: reviewUsers[review.userId],
                    groupId: group.reviewGroupConfiguration.id,
                    groupName: group.reviewGroupConfiguration.name,
                }))
                .filter('user')
                .value(),
        );
    }

    @Span()
    @TenancyTransaction()
    public async saveApprovalReview(
        account: Account,
        user: User,
        policyId: number,
        versionId: number,
        approvalId: number,
        request: PolicyApprovalReviewRequest,
    ): Promise<[ReviewStatus, ApprovalStatusType]> {
        const approval =
            await this.policyApprovalsCommerceService.getApprovalByIdForReview(approvalId);

        if (approvalHasFinalizedStatus(approval)) {
            this.logger.warn(
                PolloAdapter.acct(
                    `Cannot submit policy approval review. Approval status is ${approval.status}`,
                    account,
                    this.constructor.name,
                ).setIdentifier({
                    policyId,
                    policyVersionId: versionId,
                    approvalId,
                    currentStatus: approval.status,
                }),
            );
            return [request.status, approval.status];
        }

        const reviewGroups = approval.reviewGroups as unknown as Array<ReviewGroup>;
        const [userLatestReviewGroup] = this.getUserReviewGroups(reviewGroups, user.id);

        const review = userLatestReviewGroup?.reviews?.find(r => r.userId === user.id);

        if (_.isNil(review?.id)) {
            throw new NotFoundError(ErrorCode.ENTITY_NOT_FOUND);
        }

        await this.policyApprovalsCommerceService.submitPolicyApprovalReview(
            account,
            user,
            policyId,
            versionId,
            review.id,
            request.status,
            request.changeRequest,
        );

        return [request.status, approval.status];
    }

    @Span()
    @TenancyTransaction()
    public async overrideApprovalReview(
        account: Account,
        user: User,
        policyId: number,
        approvalId: number,
        overrideReason: string,
        reviewGroupId?: number,
    ): Promise<void> {
        // TODO https://drata.atlassian.net/browse/ENG-73771 create a new event and handler for the override process
        // NOTE: at the moment, just Approved is allowed.
        const status = ReviewStatus.APPROVED;

        const policy = await this.policiesCoreService.getPolicyById(policyId);
        if (!policy) {
            throw new NotFoundException(ErrorCode.POLICY_NOT_FOUND);
        }

        // TODO: validateIsUserOwner method from policy-version-validation.service.ts https://drata.atlassian.net/browse/ENG-68230
        if (!isPolicyOwner(policy, user) && !hasRole(user, [Role.ADMIN])) {
            throw new ForbiddenException(
                'User is not allowed to perform this action',
                ErrorCode.USER_IS_NOT_POLICY_OWNER,
            );
        }

        const policyVersion = policy.latestVersion();
        if (!policyVersion) {
            throw new NotFoundException(ErrorCode.POLICY_VERSION_NOT_FOUND);
        }

        if (policyVersion.policyVersionStatus !== PolicyVersionStatus.NEEDS_APPROVAL) {
            throw new BadRequestException(
                `Cannot override approval for policy version that is not in ${PolicyVersionStatus.NEEDS_APPROVAL} status`,
            );
        }

        const approval =
            await this.policyApprovalsCommerceService.getApprovalByIdForReview(approvalId);

        if (
            approval.status === ApprovalStatusType.INITIALIZED ||
            approval.status === ApprovalStatusType.APPROVED ||
            approval.status === ApprovalStatusType.CANCELLED
        ) {
            this.logger.warn(
                PolloAdapter.acct(
                    `Cannot override policy approval. Approval status is ${approval.status}`,
                    account,
                    this.constructor.name,
                ).setIdentifier({
                    policyId,
                    policyVersionId: policyVersion.id,
                    approvalId,
                    currentStatus: approval.status,
                }),
            );

            return;
        }

        // The changes requested status doesn't need the workflow execution because it's already completed.
        if (approval.status === ApprovalStatusType.CHANGES_REQUESTED) {
            await this.policyApprovalsCommerceService.overrideCompletedApprovalReviewGroups(
                approvalId,
                policyId,
                user.id,
                overrideReason,
            );

            await this.policiesCoreService.updatePolicyVersionStatus(
                policyVersion.id,
                PolicyVersionStatus.APPROVED,
            );

            this._eventBus.publish(
                new PolicyApprovedByApproverEvent(account, user, policy.name, policyVersion, null),
            );

            return this.notifyPolicyApprovalOverride(
                account,
                user,
                policy,
                policyVersion,
                approval,
                reviewGroupId,
                overrideReason,
                ApprovalStatusType.APPROVED,
            );
        }

        if (!approval.waasRunId) {
            throw new BadRequestException(
                'Cannot override approval that is not in a running workflow',
            );
        }

        const approvalWaasRunId: string = approval.waasRunId;
        const temporalClient = await TemporalClient.getInstance();
        if (!reviewGroupId) {
            temporalClient
                .sendUpdate(approvalWaasRunId, overrideApprovalAndReviewGroupsUpdate, [
                    user.id,
                    status,
                    overrideReason,
                ])
                .then(async () => {
                    await this.policiesCoreService.updatePolicyVersionStatus(
                        policyVersion.id,
                        PolicyVersionStatus.APPROVED,
                    );
                    await this.notifyPolicyApprovalOverride(
                        account,
                        user,
                        policy,
                        policyVersion,
                        approval,
                        reviewGroupId,
                        overrideReason,
                        ApprovalStatusType.APPROVED,
                    );
                    this.logger.log(
                        PolloAdapter.acct(
                            `Successfully sent temporal workflow update`,
                            account,
                            this.constructor.name,
                        ).setIdentifier({
                            approvalId,
                            policyId,
                            policyVersionId: policyVersion.id,
                        }),
                    );
                })
                .catch(async error => {
                    this.logger.error(
                        PolloAdapter.acct(
                            `Failed to send temporal workflow update`,
                            account,
                            this.constructor.name,
                        )
                            .setSubContext(this.overrideApprovalReview.name)
                            .setIdentifier({
                                approvalId,
                                policyId,
                                policyVersionId: policyVersion.id,
                                waasRunId: approvalWaasRunId,
                            })
                            .setError(error),
                    );
                });
        } else {
            const reviewGroup = approval.reviewGroups.find(group => group.id === reviewGroupId);
            if (!reviewGroup) throw new NotFoundException(ErrorCode.ENTITY_NOT_FOUND);

            if (!reviewGroup.waasRunId) {
                throw new BadRequestException(
                    'Cannot override review group that is not in a running workflow',
                );
            }

            const reviewGroupWaasRunId: string = reviewGroup.waasRunId;
            temporalClient
                .sendUpdate(reviewGroupWaasRunId, overrideReviewGroupUpdate, [
                    user.id,
                    status,
                    overrideReason,
                ])
                .then(async ([, newApprovalStatus]) => {
                    if (newApprovalStatus === ApprovalStatusType.APPROVED) {
                        await this.policiesCoreService.updatePolicyVersionStatus(
                            policyVersion.id,
                            PolicyVersionStatus.APPROVED,
                        );
                    }

                    await this.notifyPolicyApprovalOverride(
                        account,
                        user,
                        policy,
                        policyVersion,
                        approval,
                        reviewGroupId,
                        overrideReason,
                        newApprovalStatus,
                    );
                    this.logger.log(
                        PolloAdapter.acct(
                            `Successfully sent temporal workflow update`,
                            account,
                            this.constructor.name,
                        ).setIdentifier({
                            approvalId,
                            policyId,
                            policyVersionId: policyVersion.id,
                            reviewGroupId,
                            waasRunId: reviewGroupWaasRunId,
                            newApprovalStatus,
                        }),
                    );
                })
                .catch(async error => {
                    this.logger.error(
                        PolloAdapter.acct(
                            `Failed to send temporal workflow update`,
                            account,
                            this.constructor.name,
                        )
                            .setSubContext(this.overrideApprovalReview.name)
                            .setIdentifier({
                                approvalId,
                                policyId,
                                policyVersionId: policyVersion.id,
                                reviewGroupId,
                                waasRunId: reviewGroupWaasRunId,
                            })
                            .setError(error),
                    );
                });
        }
    }

    @Span()
    public async getConfiguration(
        policyId: number,
        latestOnly: boolean,
    ): Promise<FullApprovalConfigurationResponse[]> {
        const fullApprovalConfigurations: FullApprovalConfiguration[] =
            await this.policyApprovalsCommerceService.getApprovalConfigurationsForPolicy(policyId);

        // If there are no configurations linked to the policy, throw a 404
        if (_.isNil(fullApprovalConfigurations) || _.isEmpty(fullApprovalConfigurations)) {
            throw new NotFoundError(ErrorCode.ENTITY_NOT_FOUND);
        }

        // In policy approvals specifically, we should only have one entry, so let's grab it.
        const sortedConfigurations = _.sortBy(
            fullApprovalConfigurations,
            config => config.createdAt,
        );
        if (_.isNil(sortedConfigurations) || _.isEmpty(sortedConfigurations)) {
            throw new NotFoundError(ErrorCode.ENTITY_NOT_FOUND);
        }

        // Latest configuration should return the latest configuration ONLY
        const configurations = latestOnly
            ? [sortedConfigurations[sortedConfigurations.length - 1]]
            : sortedConfigurations;

        const expandedConfigurations = await this.expandConfigurations(configurations, policyId);

        return expandedConfigurations;
    }

    /**
     * Checks if a policy approval is currently locked (workflow in progress)
     * This is used by the frontend to show appropriate UI states
     */
    @Span()
    public async getPolicyApprovalLockStatus(
        account: Account,
        policyId: number,
        policyVersionId: number,
        approvalId?: number,
    ): Promise<{ isLocked: boolean; payload: PolicyApprovalLockPayload | null }> {
        const policy = await this.policiesCoreService.getPolicyByIdAndVersionId(
            policyId,
            policyVersionId,
        );
        if (!policy) {
            throw new NotFoundException(ErrorCode.POLICY_NOT_FOUND);
        }

        return this.policyApprovalsCommerceService.getPolicyApprovalLockStatus(
            account,
            policyId,
            policyVersionId,
            approvalId,
        );
    }

    @Span()
    public async getApproval(
        policyId: number,
        versionId: number,
        latestOnly: boolean,
    ): Promise<FullApprovalResponse[]> {
        const configurations = await this.getConfiguration(policyId, latestOnly).catch(() => []);

        if (_.isEmpty(configurations)) {
            return [];
        }

        const approvals = await this.policyApprovalsCommerceService.getApprovalsForPolicy(
            policyId,
            versionId,
        );

        const [firstApproval] = approvals;
        if (_.isEmpty(approvals) || firstApproval?.status === ApprovalStatusType.CANCELLED) {
            return _.map(configurations, configuration => ({
                id: null,
                status: 'NOT_STARTED' as ApprovalStatusType,
                createdAt: new Date(),
                reviewGroups: configuration.reviewGroupConfigurations.map(groupConfig => ({
                    id: null,
                    consensusRule: groupConfig.consensusRule,
                    hasReachedConsensus: false,
                    tier: groupConfig.tier,
                    name: groupConfig.name,
                    reviews: groupConfig.userAssignmentList.map(user => ({
                        id: null,
                        user,
                        userId: user.id,
                        status: null,
                        statusUpdatedAt: null,
                        isOverridden: false,
                    })),
                    consensusDecision: undefined,
                    timeline: groupConfig.timeline as StringValue,
                    createdAt: new Date(),
                })),
            }));
        }

        if (latestOnly) {
            return this.expandPolicyApproval([this.getLatestApprovalOrFail(approvals)], policyId);
        }

        return this.expandPolicyApproval(approvals, policyId);
    }

    private async expandCustomAssignment(
        customAssignment: CustomAssignmentType,
        policyId: number,
    ): Promise<{
        users: User[];
        roles: RoleAssignment[];
        responsibility: ResponsibilityAssignment | null;
    }> {
        // Get users for the assignment
        const users = await this.userCoreService.getUsersByIds(customAssignment.userIds ?? []);

        // Get the users from roles and then format the shape
        const roleUsers = await this.userCoreService.getUsersByRoles(
            customAssignment.roleNames ?? [],
        );
        const roleUserMappings = _.reduce(
            roleUsers,
            (accumulator, current) => {
                for (const role of current.roles) {
                    if (!_.isNil(role.role)) {
                        accumulator.set(role.role, [
                            ...(accumulator.get(role.role) ?? []),
                            current,
                        ]);
                    }
                }
                return accumulator;
            },
            new Map<Role, User[]>(),
        );
        const roleAssignments: RoleAssignment[] = Array.from(roleUserMappings.entries()).map(
            ([role, usersWithRole]) => ({
                role: role,
                users: usersWithRole,
            }),
        );

        // We only want to deal with POLICY_OWNERS
        const responsibilityUser = customAssignment.responsibilities?.some(
            resp => resp === CustomAssignmentResponsibilityEnum.POLICY_OWNER,
        )
            ? (await this.policiesCoreService.getPolicyById(policyId))?.currentOwner
            : null;

        return {
            users,
            roles: roleAssignments,
            responsibility: !_.isNil(responsibilityUser)
                ? {
                      responsibility: CustomAssignmentResponsibilityEnum.POLICY_OWNER,
                      user: responsibilityUser,
                  }
                : null,
        };
    }

    private async expandPolicyApproval(
        approvals: FullApprovalWithReviews[],
        policyId: number,
    ): Promise<FullApprovalResponse[]> {
        return Promise.all(
            approvals.map(async approval => {
                const reviewGroups = approval.reviewGroups.map(async group => {
                    const users = await this.userCoreService.getUsersByIds(
                        group.reviews.map(review => review.userId),
                    );

                    const userMap = users.reduce(
                        (acc, user) => {
                            acc[user.id] = user;
                            return acc;
                        },
                        {} as Record<number, User>,
                    );

                    const remapUnfinishedReviews = (review: ReviewWithUser): ReviewWithUser => {
                        const resetReview = () => {
                            review.status = 'NOT_STARTED' as ReviewStatus;
                            // Don't pay too much attention to this line. It's a hack to get the type to work.
                            review.statusUpdatedAt = null as unknown as Date;
                        };

                        if (
                            !_.isNil(review.status) &&
                            ![ReviewStatus.APPROVED, ReviewStatus.CHANGES_REQUESTED].includes(
                                review.status,
                            ) &&
                            [
                                ApprovalStatusType.APPROVED,
                                ApprovalStatusType.CHANGES_REQUESTED,
                            ].includes(approval.status) &&
                            group.reviewGroupConfiguration.consensusRule === ConsensusRule.ANY
                        ) {
                            resetReview();
                        }

                        return review;
                    };

                    const reviews = group.reviews
                        .map(review => ({
                            id: review.id,
                            user: userMap[review.userId],
                            userId: review.userId,
                            status: review.status,
                            changeRequest: review?.changeRequest,
                            statusUpdatedAt: review.statusUpdatedAt,
                            isOverridden: review.isOverridden,
                        }))
                        .map(review => remapUnfinishedReviews(review as ReviewWithUser));

                    return {
                        id: group.id,
                        consensusRule: group.reviewGroupConfiguration.consensusRule,
                        name: group.reviewGroupConfiguration?.name,
                        hasReachedConsensus: group.consensusReached,
                        tier: group.reviewGroupConfiguration.hierarchicalLevel,
                        reviews,
                        reviewGroupsConfiguration: group.reviewGroupConfiguration,
                        consensusDecision: group.consensusDecision,
                        timeline: group.reviewGroupConfiguration.timeline,
                        createdAt: approval.createdAt,
                    };
                });

                const approvalReviewGroups = await Promise.all(reviewGroups);
                const approvalReviewGroupsConfigIds = new Set(
                    approvalReviewGroups.map(g => g.reviewGroupsConfiguration?.id),
                );

                const approvalConfiguration =
                    await this.approvalConfigurationCoreService.getFullApprovalConfigurationById(
                        approval.approvalConfigurationId,
                    );
                const expadedConfigurations = await this.expandConfigurations(
                    [approvalConfiguration],
                    policyId,
                );
                const reviewGroupsConfigurations =
                    expadedConfigurations[0].reviewGroupConfigurations ?? [];

                // Get the review group configurations that are not part of the approval
                const nonApprovalReviewGroupConfigurations = reviewGroupsConfigurations.filter(
                    config => !approvalReviewGroupsConfigIds.has(config.id),
                );

                // Map the review group configurations to the review group response type
                const mappedNonApprovalReviewGroupConfigurations =
                    nonApprovalReviewGroupConfigurations.map(groupConfig => ({
                        id: null,
                        consensusRule: groupConfig.consensusRule,
                        hasReachedConsensus: false,
                        tier: groupConfig.tier,
                        name: groupConfig?.name,
                        reviews: groupConfig.userAssignmentList.map(user => ({
                            id: null,
                            user,
                            userId: user.id,
                            status: null,
                            statusUpdatedAt: null,
                            isOverridden: false, // Add the missing property
                        })),
                        consensusDecision: undefined,
                        timeline: groupConfig.timeline as StringValue,
                        createdAt: new Date(),
                    }));

                return {
                    id: approval.id,
                    status: approval.status,
                    reviewGroups: [
                        ...approvalReviewGroups,
                        ...mappedNonApprovalReviewGroupConfigurations,
                    ].sort((a, b) => a.tier - b.tier),
                    createdAt: approval.createdAt,
                };
            }),
        );
    }

    /**
     * @returns Sorted array of review groups that the user is a reviewer of
     */
    private getUserReviewGroups(reviewGroups: ReviewGroup[], userId: number) {
        if (!reviewGroups) return [];
        const getTierNumber = ({ waasRunId }: ReviewGroup) => {
            if (_.isNil(waasRunId)) return;
            const waasIdSeparator = '::';
            const tierSeparator = '-';
            const [, , tier] = waasRunId.split(waasIdSeparator);
            return tier.split(tierSeparator).pop();
        };
        return _.sortBy(
            reviewGroups.filter(
                reviewGroup =>
                    // Filter out review groups that have been completed
                    !_.isNil(reviewGroup.waasRunId) &&
                    !reviewGroup.consensusReached &&
                    reviewGroup.reviews.some(review => review.userId === userId),
            ),
            getTierNumber,
        );
    }

    private getLatestApprovalOrFail(approvals: FullApprovalWithReviews[]) {
        const latestsApproval = _.maxBy(approvals, approval => approval.statusUpdatedAt);

        if (_.isNil(latestsApproval)) {
            throw new NotFoundError(ErrorCode.ENTITY_NOT_FOUND);
        }

        return latestsApproval;
    }

    private async expandConfigurations(
        configurations: FullApprovalConfiguration[],
        policyId: number,
    ): Promise<FullApprovalConfigurationResponse[]> {
        return Promise.all(
            _.map(configurations, async config => {
                // The total deadline is equal to the sum of all reviewGroup deadlines
                const totalDeadline = ms(
                    _.sumBy(config.reviewGroupConfigurations, reviewGroupConfig =>
                        ms(reviewGroupConfig.timeline),
                    ),
                );

                // Expand out all the necessary sub-objects for the view
                const reviewGroupConfigurations = await Promise.all(
                    _.map(config.reviewGroupConfigurations, async groupConfig => {
                        const expandedCustomAssignment = await this.expandCustomAssignment(
                            groupConfig.customAssignment,
                            policyId,
                        );
                        return {
                            id: groupConfig.id,
                            name: groupConfig.customAssignment.name,
                            consensusRule: groupConfig.consensusRule,
                            timeline: ms(ms(groupConfig.timeline), { long: true }),
                            tier: groupConfig.hierarchicalLevel,
                            userAssignmentList: expandedCustomAssignment.users,
                            roleAssignmentList: expandedCustomAssignment.roles,
                            responsibilityAssignment: expandedCustomAssignment.responsibility,
                        };
                    }),
                );

                return {
                    id: config.id,
                    reviewGroupConfigurations: reviewGroupConfigurations,
                    timeline: totalDeadline,
                };
            }),
        );
    }

    private async notifyPolicyApprovalOverride(
        account: Account,
        user: User,
        policy: Policy,
        policyVersion: PolicyVersion,
        approval: FullApprovalWithReviews,
        reviewGroupId: number | undefined,
        overrideReason: string,
        newApprovalStatus: ApprovalStatusType,
    ): Promise<void> {
        this._eventBus.publish(
            new PolicyVersionApprovalOverrideEvent(account, user, {
                policy: policy as GetSummarizedPolicyWithoutDeletedVersions,
                policyVersion,
                approval,
                reviewGroupId,
                overrideReason,
            }),
        );

        const policyOwner = policy.currentOwner;
        if (!policyOwner) {
            this.logger.warn(
                PolloMessage.msg(
                    `Policy ${policy.id} has no owner. Cannot send approval override email.`,
                )
                    .setAccountId(account.id)
                    .setContext(this.constructor.name)
                    .setSubContext(this.notifyPolicyApprovalOverride.name)
                    .setIdentifier({
                        policyId: policy.id,
                        policyName: policy.name,
                    }),
            );
            return;
        }

        let reviewersIds: number[] = [];
        let tierName: string | undefined = undefined;
        if (!_.isNil(reviewGroupId)) {
            const reviewGroup = await this.reviewGroupCoreService.getReviewGroupById(reviewGroupId);

            reviewersIds = reviewGroup.reviews
                .filter(review => review.userId !== policyOwner.id)
                .map(review => review.userId);

            const reviewGroupConfiguration =
                await this.approvalConfigurationCoreService.getReviewGroupConfigurationById(
                    reviewGroup.reviewGroupConfigurationId,
                );

            tierName =
                newApprovalStatus !== ApprovalStatusType.APPROVED
                    ? reviewGroupConfiguration.name
                    : undefined;
        } else {
            const configuration =
                await this.approvalConfigurationCoreService.getFullApprovalConfigurationById(
                    approval.approvalConfigurationId,
                );

            const currentReviewGroupUserIds = configuration.reviewGroupConfigurations
                .map(group => group.customAssignment)
                .map(customAssignment => customAssignment.userIds)
                .flatMap(userIds => userIds);

            reviewersIds = _.uniq(currentReviewGroupUserIds);
        }

        reviewersIds.push(policyOwner.id);
        const receivingUsers = await this.userCoreService.getUsersByIds(reviewersIds);

        await Promise.all(
            receivingUsers
                .filter(receivingUser => !_.isNil(receivingUser))
                .map(async receivingUser => {
                    await this.policyEmailsCoreService.sendPolicyApprovalOverrideEmail(
                        account,
                        policyOwner,
                        receivingUser,
                        policy,
                        policyVersion,
                        overrideReason,
                        tierName,
                    );
                }),
        );
    }
}
