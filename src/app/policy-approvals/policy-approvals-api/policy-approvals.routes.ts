export enum PolicyApprovalsRoutes {
    // GET
    GET_POLICY_VERSIONS_APPROVAL = '/policy/:policyId/versions/:versionId/approval',
    GET_POLICY_APPROVALS_WORKFLOW_CONFIGURATION = '/policy/:policyId/approval-workflow-configurations',
    GET_POLICY_VERSION_REVIEWERS = '/policy/:policyId/version/:versionId/reviewers',
    GET_POLICY_APPROVAL_LOCK_STATUS = '/policy/:policyId/version/:versionId/approval-lock-status',

    // POST
    POST_POLICY_APPROVAL_CONFIGURATIONS = '/policy/:policyId/approval-configurations',
    POST_POLICY_APPROVALS_OVERRIDE = '/policy/:policyId/approvals/:approvalId/override',
    POST_POLICY_APPROVALS_REVIEW_GROUP_OVERRIDE = '/policy/:policyId/approvals/:approvalId/review-groups/:reviewGroupId/override',
    POST_POLICY_APPROVALS_SUBMIT_REVIEW = '/policy/:policyId/version/:versionId/approval/:approvalId/reviews/submit-review',
}
