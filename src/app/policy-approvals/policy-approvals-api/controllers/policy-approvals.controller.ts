import { MongoAbility } from '@casl/ability';
import {
    Body,
    Controller,
    Get,
    Param,
    ParseBoolPipe,
    ParseIntPipe,
    Post,
    Query,
} from '@nestjs/common';
import {
    ApiBadRequestResponse,
    ApiCreatedResponse,
    ApiNotFoundResponse,
    ApiOkResponse,
    ApiOperation,
    ApiTags,
} from '@nestjs/swagger';
import { AppController } from 'app/app.controller';
import { ReviewStatus } from 'app/approvals/v2/approval-review-groups-core/types/review-status.enum';
import { ApprovalStatusType } from 'app/approvals/v2/approvals-core/types/approval-status.enum';
import { PolicyApprovalConfigurationPaginatedResponseDto } from 'app/policy-approvals/policy-approvals-api/dto/approval-configuration/policy-approval-configuration-paginated-response.dto';
import { PolicyApprovalConfigurationResponseDto } from 'app/policy-approvals/policy-approvals-api/dto/approval-configuration/policy-approval-configuration-response.dto';
import { PolicyApprovalReviewSubmitResponseDto } from 'app/policy-approvals/policy-approvals-api/dto/approval-review/policy-approval-review-submit-response.dto';
import { PolicyReviewerPaginatedResponseDto } from 'app/policy-approvals/policy-approvals-api/dto/approval-review/policy-reviewer-list-response.dto';
import { PolicyApprovalPaginatedResponseDto } from 'app/policy-approvals/policy-approvals-api/dto/approval/policy-approval-paginated-response.dto';
import { PolicyApprovalConfigurationRequestDto } from 'app/policy-approvals/policy-approvals-api/dto/policy-approval-configuration-request.dto';
import { PolicyApprovalLockStatusResponse } from 'app/policy-approvals/policy-approvals-api/dto/policy-approval-lock-status.response.dto';
import { PolicyApprovalOverrideRequestDto } from 'app/policy-approvals/policy-approvals-api/dto/policy-approval-override-request.dto';
import { PolicyApprovalReviewRequestDto } from 'app/policy-approvals/policy-approvals-api/dto/policy-approval-review-request.dto';
import { PolicyApprovalsRoutes } from 'app/policy-approvals/policy-approvals-api/policy-approvals.routes';
import { PolicyApprovalsApiService } from 'app/policy-approvals/policy-approvals-api/services/policy-approvals-api.service';
import { FullApprovalConfigurationResponse } from 'app/policy-approvals/policy-approvals-api/types/full-approval-configuration-response.type';
import { FullApprovalResponse } from 'app/policy-approvals/policy-approvals-api/types/full-approval-response.type';
import { ReviewWithUser } from 'app/policy-approvals/policy-approvals-api/types/reviews.type';
import { User } from 'app/users/entities/user.entity';
import { Account } from 'auth/entities/account.entity';
import { CheckAbilities } from 'casl/ability.handlers';
import { Action } from 'casl/actions';
import { Dto } from 'commons/decorators/dto.decorator';
import { GetAccount } from 'commons/decorators/get-account.decorator';
import { GetUser } from 'commons/decorators/get-user.decorator';
import { Area, ProductArea } from 'commons/decorators/product-area.decorator';
import { Roles } from 'commons/decorators/roles.decorator';
import { ExceptionResponseDto } from 'commons/dtos/exception-response.dto';
import { ApiResponse } from 'commons/enums/api-response.enum';
import { Role } from 'commons/enums/users/role.enum';
import { Subject } from 'commons/enums/users/subject.enum';
import { IsPositiveIntPipe } from 'commons/pipes/is-positive-int-pipe';

@Controller()
@ProductArea(Area.POLICY_CENTER)
@ApiTags('Policy Approvals')
export class PolicyApprovalsController extends AppController {
    constructor(private readonly apiService: PolicyApprovalsApiService) {
        super();
    }

    @ApiOperation({
        description: `Get policy approval data
        <br><br>
        Allowed Roles: [
            ${Role[Role.ADMIN]},
            ${Role[Role.ACT_AS_READ_ONLY]},
            ${Role[Role.POLICY_MANAGER]},
            ${Role[Role.WORKSPACE_ADMINISTRATOR]},
            ${Role[Role.TECHGOV]}
        ]`,
    })
    @CheckAbilities((ability: MongoAbility) => ability.can(Action.Read, Subject[Subject.Policy]))
    @Get(PolicyApprovalsRoutes.GET_POLICY_VERSIONS_APPROVAL)
    @Roles(
        Role.ADMIN,
        Role.ACT_AS_READ_ONLY,
        Role.POLICY_MANAGER,
        Role.WORKSPACE_ADMINISTRATOR,
        Role.TECHGOV,
    )
    @ApiOkResponse({ type: PolicyApprovalPaginatedResponseDto })
    @ApiNotFoundResponse({ description: ApiResponse.NOT_FOUND, type: ExceptionResponseDto })
    @Dto(PolicyApprovalPaginatedResponseDto)
    getApproval(
        @Param('policyId', IsPositiveIntPipe) policyId: number,
        @Param('versionId', IsPositiveIntPipe) versionId: number,
        @Query('latest', ParseBoolPipe) latestOnly: boolean,
    ): Promise<FullApprovalResponse[]> {
        return this.apiService.getApproval(policyId, versionId, latestOnly);
    }

    @ApiOperation({
        description: `Get policy approval workflow configuration
        <br><br>
        Allowed Roles: [
            ${Role[Role.ADMIN]},
            ${Role[Role.ACT_AS_READ_ONLY]},
            ${Role[Role.POLICY_MANAGER]},
            ${Role[Role.WORKSPACE_ADMINISTRATOR]},
            ${Role[Role.TECHGOV]}
        ]`,
    })
    @CheckAbilities((ability: MongoAbility) => ability.can(Action.Manage, Subject[Subject.Policy]))
    @Get(PolicyApprovalsRoutes.GET_POLICY_APPROVALS_WORKFLOW_CONFIGURATION)
    @Roles(
        Role.ADMIN,
        Role.ACT_AS_READ_ONLY,
        Role.POLICY_MANAGER,
        Role.WORKSPACE_ADMINISTRATOR,
        Role.TECHGOV,
    )
    @ApiOkResponse({ type: PolicyApprovalConfigurationPaginatedResponseDto })
    @ApiNotFoundResponse({ description: ApiResponse.NOT_FOUND, type: ExceptionResponseDto })
    @Dto(PolicyApprovalConfigurationPaginatedResponseDto)
    getConfiguration(
        @Param('policyId', IsPositiveIntPipe) policyId: number,
        @Query('latest', ParseBoolPipe) latestOnly: boolean,
    ): Promise<FullApprovalConfigurationResponse[]> {
        return this.apiService.getConfiguration(policyId, latestOnly);
    }

    @ApiOperation({
        description: `Check if a policy approval is currently locked (workflow in progress)
        <br><br>
        Allowed Roles: [
            ${Role[Role.ADMIN]},
            ${Role[Role.ACT_AS_READ_ONLY]},
            ${Role[Role.POLICY_MANAGER]},
            ${Role[Role.WORKSPACE_ADMINISTRATOR]},
            ${Role[Role.TECHGOV]}
        ]`,
    })
    @CheckAbilities((ability: MongoAbility) => ability.can(Action.Read, Subject[Subject.Policy]))
    @Get(PolicyApprovalsRoutes.GET_POLICY_APPROVAL_LOCK_STATUS)
    @Roles(
        Role.ADMIN,
        Role.ACT_AS_READ_ONLY,
        Role.POLICY_MANAGER,
        Role.WORKSPACE_ADMINISTRATOR,
        Role.TECHGOV,
    )
    @ApiOkResponse({ type: PolicyApprovalLockStatusResponse })
    @ApiNotFoundResponse({ description: ApiResponse.NOT_FOUND, type: ExceptionResponseDto })
    @Dto(PolicyApprovalLockStatusResponse)
    async getPolicyApprovalLockStatus(
        @Param('policyId', IsPositiveIntPipe) policyId: number,
        @Param('versionId', IsPositiveIntPipe) versionId: number,
        @Query('approvalId', ParseIntPipe) approvalId: number,
        @GetAccount() account: Account,
    ) {
        const { isLocked, payload } = await this.apiService.getPolicyApprovalLockStatus(
            account,
            policyId,
            versionId,
            approvalId,
        );

        return {
            approvalId,
            isLocked,
            policyId,
            policyVersionId: versionId,
            ...payload,
        };
    }

    @ApiOperation({
        description: `Submit approval review for the given policy version.
            <br><br>
            Allowed Roles: [
                ${Role[Role.TECHGOV]},
                ${Role[Role.ADMIN]},
                ${Role[Role.WORKSPACE_ADMINISTRATOR]},
                ${Role[Role.POLICY_MANAGER]},
            ]`,
    })
    @ApiCreatedResponse({
        description: ApiResponse.CREATED,
        type: PolicyApprovalReviewSubmitResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @Roles(Role.TECHGOV, Role.ADMIN, Role.POLICY_MANAGER, Role.WORKSPACE_ADMINISTRATOR)
    @Dto(PolicyApprovalReviewSubmitResponseDto)
    @CheckAbilities((ability: MongoAbility) => ability.can(Action.Create, Subject[Subject.Policy]))
    @Post(PolicyApprovalsRoutes.POST_POLICY_APPROVALS_SUBMIT_REVIEW)
    postApprovalReview(
        @GetAccount() account: Account,
        @Param('policyId', IsPositiveIntPipe) policyId: number,
        @Param('versionId', IsPositiveIntPipe) versionId: number,
        @Param('approvalId', IsPositiveIntPipe) approvalId: number,
        @Body() dto: PolicyApprovalReviewRequestDto,
        @GetUser() user: User,
    ): Promise<[ReviewStatus, ApprovalStatusType]> {
        return this.apiService.saveApprovalReview(
            account,
            user,
            policyId,
            versionId,
            approvalId,
            dto,
        );
    }

    @ApiOperation({
        description: `Retrieve a list of policies with their in progress version
            <br><br>
            Allowed Roles: [
                ${Role[Role.TECHGOV]},
                ${Role[Role.ADMIN]},
                ${Role[Role.WORKSPACE_ADMINISTRATOR]},
                ${Role[Role.POLICY_MANAGER]},
            ]`,
    })
    @ApiOkResponse({
        type: PolicyReviewerPaginatedResponseDto,
    })
    @Roles(Role.TECHGOV, Role.ADMIN, Role.POLICY_MANAGER, Role.WORKSPACE_ADMINISTRATOR)
    @Dto(PolicyReviewerPaginatedResponseDto)
    @Get(PolicyApprovalsRoutes.GET_POLICY_VERSION_REVIEWERS)
    @CheckAbilities((ability: MongoAbility) => ability.can(Action.Read, Subject[Subject.Policy]))
    getPolicyReviewerList(
        @Param('policyId', IsPositiveIntPipe) policyId: number,
        @Param('versionId', IsPositiveIntPipe) versionId: number,
        @Query('approved', ParseBoolPipe) approvedOnly: boolean,
    ): Promise<ReviewWithUser[]> {
        return this.apiService.getPolicyReviewerList(policyId, versionId, approvedOnly);
    }

    @ApiOperation({
        description: `Create policy approval configuration
        <br><br>
        Allowed Roles: [
            ${Role[Role.ADMIN]},
            ${Role[Role.ACT_AS_READ_ONLY]},
            ${Role[Role.POLICY_MANAGER]},
            ${Role[Role.WORKSPACE_ADMINISTRATOR]},
            ${Role[Role.TECHGOV]}
        ]`,
    })
    @ApiCreatedResponse({
        description: ApiResponse.CREATED,
        type: PolicyApprovalConfigurationResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @Post(PolicyApprovalsRoutes.POST_POLICY_APPROVAL_CONFIGURATIONS)
    @Roles(Role.ADMIN, Role.POLICY_MANAGER, Role.WORKSPACE_ADMINISTRATOR, Role.TECHGOV)
    @CheckAbilities((ability: MongoAbility) => ability.can(Action.Manage, Subject[Subject.Policy]))
    @Dto(PolicyApprovalConfigurationResponseDto)
    createApprovalConfiguration(
        @Param('policyId', IsPositiveIntPipe) policyId: number,
        @Body() dto: PolicyApprovalConfigurationRequestDto,
        @GetAccount() account: Account,
        @GetUser() user: User,
    ): Promise<FullApprovalConfigurationResponse> {
        return this.apiService.createApprovalConfiguration(
            policyId,
            account,
            user,
            dto.reviewGroups,
        );
    }

    @ApiOperation({
        description: `Override policy approval process for a policy version.
            This endpoint allows Policy Owners and Admins to bypass the standard approval process.
            <br><br>
            Allowed Roles: [
                ${Role[Role.ADMIN]},
                ${Role[Role.POLICY_MANAGER]},
                ${Role[Role.TECHGOV]},
                ${Role[Role.WORKSPACE_ADMINISTRATOR]},
            ]`,
    })
    @ApiCreatedResponse({
        description: ApiResponse.CREATED,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @Roles(Role.ADMIN, Role.POLICY_MANAGER, Role.TECHGOV, Role.WORKSPACE_ADMINISTRATOR)
    @CheckAbilities((ability: MongoAbility) => ability.can(Action.Manage, Subject[Subject.Policy]))
    @Post(PolicyApprovalsRoutes.POST_POLICY_APPROVALS_OVERRIDE)
    async overrideApproval(
        @GetAccount() account: Account,
        @GetUser() user: User,
        @Param('policyId', IsPositiveIntPipe) policyId: number,
        @Param('approvalId', IsPositiveIntPipe) approvalId: number,
        @Body() dto: PolicyApprovalOverrideRequestDto,
    ): Promise<void> {
        await this.apiService.overrideApprovalReview(
            account,
            user,
            policyId,
            approvalId,
            dto.overrideReason,
        );
    }

    @ApiOperation({
        description: `Override policy approval process for a review group (tier) of a policy version.
            This endpoint allows Policy Owners and Admins to bypass the standard approval process.
            <br><br>
            Allowed Roles: [
                ${Role[Role.ADMIN]},
                ${Role[Role.POLICY_MANAGER]},
                ${Role[Role.TECHGOV]},
                ${Role[Role.WORKSPACE_ADMINISTRATOR]},
            ]`,
    })
    @ApiCreatedResponse({
        description: ApiResponse.CREATED,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @Roles(Role.ADMIN, Role.POLICY_MANAGER, Role.TECHGOV, Role.WORKSPACE_ADMINISTRATOR)
    @CheckAbilities((ability: MongoAbility) => ability.can(Action.Manage, Subject[Subject.Policy]))
    @Post(PolicyApprovalsRoutes.POST_POLICY_APPROVALS_REVIEW_GROUP_OVERRIDE)
    async overrideReviewGroupApproval(
        @GetAccount() account: Account,
        @GetUser() user: User,
        @Param('policyId', IsPositiveIntPipe) policyId: number,
        @Param('approvalId', IsPositiveIntPipe) approvalId: number,
        @Param('reviewGroupId', IsPositiveIntPipe) reviewGroupId: number,
        @Body() dto: PolicyApprovalOverrideRequestDto,
    ): Promise<void> {
        await this.apiService.overrideApprovalReview(
            account,
            user,
            policyId,
            approvalId,
            dto.overrideReason,
            reviewGroupId,
        );
    }
}
