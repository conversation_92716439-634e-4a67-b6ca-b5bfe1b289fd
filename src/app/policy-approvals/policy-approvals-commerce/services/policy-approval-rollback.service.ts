import { Injectable } from '@nestjs/common';
import { ApprovalNoteEntity } from 'app/approvals/v2/approval-review-groups-core/entities/approval-note.entity';
import { ReviewGroupEntity } from 'app/approvals/v2/approval-review-groups-core/entities/review-group.entity';
import { ReviewEntity } from 'app/approvals/v2/approval-review-groups-core/entities/review.entity';
import { ReviewStatus } from 'app/approvals/v2/approval-review-groups-core/types/review-status.enum';
import { ApprovalEntity } from 'app/approvals/v2/approvals-core/entities/approval.entity';
import { ApprovalStatusType } from 'app/approvals/v2/approvals-core/types/approval-status.enum';
import { CustomTask } from 'app/grc/entities/custom-task.entity';
import { PolicyApprovalsAssociationEntity } from 'app/policy-approvals/policy-approvals-commerce/entities/policy-approvals-associations.entity';
import { PolicyVersion } from 'app/users/policies/entities/policy-version.entity';
import { Account } from 'auth/entities/account.entity';
import { PolloAdapter } from 'commons/adapters/pollo.adapter';
import { DrataDataSource } from 'commons/classes/drata-data-source.class';
import { TenancyTransaction } from 'commons/decorators/transaction';
import { PolicyVersionStatus } from 'commons/enums/users/policies/policy-version-status.enum';
import { ConnectionFactory } from 'commons/factories/connection.factory';
import { AppService } from 'commons/services/app.service';
import { isEmpty } from 'lodash';
import { In } from 'typeorm';

export interface RollbackState {
    reviewId: number;
    previousReviewStatus: ReviewStatus;
    reviewGroupId: number;
    previousConsensusState: boolean;
    previousConsensusDecision?: ReviewStatus;
    approvalId: number;
    previousApprovalStatus: ApprovalStatusType;
    policyVersionId: number;
    previousPolicyVersionStatus: PolicyVersionStatus | null;
}

export interface StartWorkflowRollbackState {
    policyId: number;
    policyVersionId: number;
    previousPolicyVersionStatus: PolicyVersionStatus | null;
}

@Injectable()
export class PolicyApprovalRollbackService extends AppService {
    /**
     * Captures the current state before workflow execution for potential rollback
     */
    @TenancyTransaction()
    async captureStateForRollback(
        reviewId: number,
        reviewGroupId: number,
        approvalId: number,
        policyVersionId: number,
    ): Promise<RollbackState> {
        const [review, reviewGroup, approval, policyVersion] = await Promise.all([
            this.reviewRepository.findOneOrFail({ where: { id: reviewId } }),
            this.reviewGroupRepository.findOneOrFail({ where: { id: reviewGroupId } }),
            this.approvalRepository.findOneOrFail({ where: { id: approvalId } }),
            this.policyVersionRepository.findOneOrFail({ where: { id: policyVersionId } }),
        ]);

        return {
            reviewId,
            previousReviewStatus: review.status,
            reviewGroupId,
            previousConsensusState: reviewGroup.hasReachedConsensus,
            previousConsensusDecision: reviewGroup.consensusDecision,
            approvalId,
            previousApprovalStatus: approval.status,
            policyVersionId,
            previousPolicyVersionStatus: policyVersion.policyVersionStatus,
        };
    }

    /**
     * Performs rollback in a separate transaction to ensure rollback operations persist
     * even if the main transaction fails. This prevents the rollback from being rolled back.
     */
    async performRollbackInSeparateTransaction(
        account: Account,
        rollbackState: RollbackState,
    ): Promise<void> {
        // Get a fresh connection for the separate transaction
        const connection: DrataDataSource = await ConnectionFactory.getConnection(account);

        // Execute rollback in a separate transaction with READ COMMITTED isolation
        await connection.transaction('READ COMMITTED', async transactionalEntityManager => {
            try {
                this.logger.log(
                    PolloAdapter.acct(
                        'Starting policy approval workflow rollback in separate transaction',
                        account,
                        this.constructor.name,
                    ).setIdentifier({
                        reviewId: rollbackState.reviewId,
                        approvalId: rollbackState.approvalId,
                        policyVersionId: rollbackState.policyVersionId,
                    }),
                );

                // Get repositories from the transactional entity manager
                const reviewRepo = transactionalEntityManager.getRepository(ReviewEntity);
                const reviewGroupRepo = transactionalEntityManager.getRepository(ReviewGroupEntity);
                const approvalRepo = transactionalEntityManager.getRepository(ApprovalEntity);
                const policyVersionRepo = transactionalEntityManager.getRepository(PolicyVersion);
                const approvalNoteRepo =
                    transactionalEntityManager.getRepository(ApprovalNoteEntity);

                // Delete approval notes associated with the review (if any exist)
                // This handles cases where rejection messages were saved as notes
                await approvalNoteRepo.delete({
                    reviewId: rollbackState.reviewId,
                });

                // Rollback review status
                await reviewRepo.update(rollbackState.reviewId, {
                    status: rollbackState.previousReviewStatus,
                    statusUpdatedAt: new Date(),
                });

                // Rollback review group consensus state
                await reviewGroupRepo.update(rollbackState.reviewGroupId, {
                    hasReachedConsensus: rollbackState.previousConsensusState,
                    consensusDecision: rollbackState.previousConsensusDecision,
                });

                // Rollback approval status
                await approvalRepo.update(rollbackState.approvalId, {
                    status: rollbackState.previousApprovalStatus,
                    statusUpdatedAt: new Date(),
                });

                // Maintain policy version status as NEEDS_APPROVAL
                await policyVersionRepo.update(rollbackState.policyVersionId, {
                    policyVersionStatus: PolicyVersionStatus.NEEDS_APPROVAL,
                });

                this.logger.log(
                    PolloAdapter.acct(
                        'Successfully completed policy approval workflow rollback in separate transaction',
                        account,
                        this.constructor.name,
                    ).setIdentifier({
                        reviewId: rollbackState.reviewId,
                        approvalId: rollbackState.approvalId,
                        policyVersionId: rollbackState.policyVersionId,
                    }),
                );
            } catch (error) {
                this.logger.error(
                    PolloAdapter.acct(
                        'Failed to perform policy approval workflow rollback in separate transaction',
                        account,
                        this.constructor.name,
                    )
                        .setIdentifier({
                            reviewId: rollbackState.reviewId,
                            approvalId: rollbackState.approvalId,
                            policyVersionId: rollbackState.policyVersionId,
                        })
                        .setError(error),
                );
                throw error;
            }
        });
    }

    /**
     * Captures the current state before start workflow execution for potential rollback
     */
    @TenancyTransaction()
    async captureStateForStartWorkflowRollback(
        policyId: number,
        policyVersionId: number,
    ): Promise<StartWorkflowRollbackState> {
        const policyVersion = await this.policyVersionRepository.findOneOrFail({
            where: { id: policyVersionId },
        });

        return {
            policyId,
            policyVersionId,
            previousPolicyVersionStatus: policyVersion.policyVersionStatus,
        };
    }

    /**
     * Cancels policy approval regardless of Temporal workflow errors.
     * This method mimics the cancellation process without relying on workflows.
     *
     * @param account - The account context
     * @param approvalId - The approval ID to cancel
     * @param policyId - The policy ID associated with the approval
     * @param policyVersionId - The policy version ID associated with the approval
     */
    async cancelPolicyApprovalRegardlessOfErrors(
        account: Account,
        approvalId: number,
        policyId: number,
        policyVersionId: number,
    ): Promise<void> {
        // Get a fresh connection for the separate transaction
        const connection: DrataDataSource = await ConnectionFactory.getConnection(account);

        // Execute cancellation in a separate transaction with READ COMMITTED isolation
        await connection.transaction('READ COMMITTED', async transactionalEntityManager => {
            try {
                this.logger.log(
                    PolloAdapter.acct(
                        'Starting policy approval cancellation regardless of Temporal errors',
                        account,
                        this.constructor.name,
                    ).setIdentifier({
                        approvalId,
                        policyId,
                        policyVersionId,
                    }),
                );

                const approvalRepo = transactionalEntityManager.getRepository(ApprovalEntity);
                const customTaskRepo = transactionalEntityManager.getRepository(CustomTask);
                const policyVersionRepo = transactionalEntityManager.getRepository(PolicyVersion);

                await approvalRepo.update(approvalId, {
                    status: ApprovalStatusType.CANCELLED,
                    statusUpdatedAt: new Date(),
                });
                const customTasks = await customTaskRepo
                    .createQueryBuilder('task')
                    .innerJoin('task.policies', 'policy', 'policy.id = :policyId', { policyId })
                    .select(['task.id'])
                    .getMany();

                if (!isEmpty(customTasks)) {
                    const taskIds = customTasks.map(task => task.id);
                    await customTaskRepo.softDelete({ id: In(taskIds) });
                }

                await policyVersionRepo.update(policyVersionId, {
                    policyVersionStatus: PolicyVersionStatus.DRAFT,
                });

                this.logger.log(
                    PolloAdapter.acct(
                        'Successfully completed policy approval cancellation regardless of Temporal errors',
                        account,
                        this.constructor.name,
                    ).setIdentifier({
                        approvalId,
                        policyId,
                        policyVersionId,
                        customTasksRemoved: customTasks.length,
                        policyVersionStatusUpdatedTo: PolicyVersionStatus.DRAFT,
                    }),
                );
            } catch (error) {
                this.logger.error(
                    PolloAdapter.acct(
                        'Failed to cancel policy approval regardless of Temporal errors',
                        account,
                        this.constructor.name,
                    )
                        .setIdentifier({
                            approvalId,
                            policyId,
                            policyVersionId,
                        })
                        .setError(error),
                );
                throw error;
            }
        });
    }

    /**
     * Performs rollback for start workflow in a separate transaction to ensure rollback operations persist
     * even if the main transaction fails. This prevents the rollback from being rolled back.
     *
     * This rollback:
     * 1. Removes policy_approval_associations generated from the workflow
     * 2. Sets the policy_version status back to DRAFT
     */
    async performStartWorkflowRollbackInSeparateTransaction(
        account: Account,
        rollbackState: StartWorkflowRollbackState,
    ): Promise<void> {
        // Get a fresh connection for the separate transaction
        const connection: DrataDataSource = await ConnectionFactory.getConnection(account);

        // Execute rollback in a separate transaction with READ COMMITTED isolation
        await connection.transaction('READ COMMITTED', async transactionalEntityManager => {
            try {
                this.logger.log(
                    PolloAdapter.acct(
                        'Starting policy approval start workflow rollback in separate transaction',
                        account,
                        this.constructor.name,
                    ).setIdentifier({
                        policyId: rollbackState.policyId,
                        policyVersionId: rollbackState.policyVersionId,
                    }),
                );

                // Get repositories from the transactional entity manager
                const policyVersionRepo = transactionalEntityManager.getRepository(PolicyVersion);
                const policyApprovalAssociationsRepo = transactionalEntityManager.getRepository(
                    PolicyApprovalsAssociationEntity,
                );

                // Remove policy approval associations generated from the workflow
                await policyApprovalAssociationsRepo.softDelete({
                    policyId: rollbackState.policyId,
                    policyVersionId: rollbackState.policyVersionId,
                });

                // Set policy version status back to the previous status
                await policyVersionRepo.update(rollbackState.policyVersionId, {
                    policyVersionStatus: rollbackState.previousPolicyVersionStatus,
                });

                this.logger.log(
                    PolloAdapter.acct(
                        'Successfully completed policy approval start workflow rollback in separate transaction',
                        account,
                        this.constructor.name,
                    ).setIdentifier({
                        policyId: rollbackState.policyId,
                        policyVersionId: rollbackState.policyVersionId,
                    }),
                );
            } catch (error) {
                this.logger.error(
                    PolloAdapter.acct(
                        'Failed to perform policy approval start workflow rollback in separate transaction',
                        account,
                        this.constructor.name,
                    )
                        .setIdentifier({
                            policyId: rollbackState.policyId,
                            policyVersionId: rollbackState.policyVersionId,
                        })
                        .setError(error),
                );
                throw error;
            }
        });
    }

    private get approvalRepository() {
        return this.getTenantRepository(ApprovalEntity);
    }
    private get policyApprovalAssociationsRepository() {
        return this.getTenantRepository(PolicyApprovalsAssociationEntity);
    }
    private get policyVersionRepository() {
        return this.getTenantRepository(PolicyVersion);
    }
    private get reviewGroupRepository() {
        return this.getTenantRepository(ReviewGroupEntity);
    }
    private get reviewRepository() {
        return this.getTenantRepository(ReviewEntity);
    }
}
