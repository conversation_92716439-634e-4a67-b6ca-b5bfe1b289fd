import { ErrorCode } from '@drata/enums';
import { Injectable } from '@nestjs/common';
import { ApprovalConfigurationCoreService } from 'app/approvals/v2/approval-configuration-core/services/approval-configuration-core.service';
import { ConsensusRule } from 'app/approvals/v2/approval-configuration-core/types/consensus-rule.enum';
import { DeadlineExceededPolicy } from 'app/approvals/v2/approval-configuration-core/types/deadline-exceeded-policy.enum';
import { ReviewGroupCoreService } from 'app/approvals/v2/approval-review-groups-core/services/review-group-core.service';
import { ReviewStatus } from 'app/approvals/v2/approval-review-groups-core/types/review-status.enum';
import { ApprovalsCoreService } from 'app/approvals/v2/approvals-core/services/approvals-core.service';
import { ApprovalStatusType } from 'app/approvals/v2/approvals-core/types/approval-status.enum';
import { ApprovalsOrchestrationService } from 'app/approvals/v2/approvals-orchestration/services/approvals-orchestration.service';
import { CreateReviewGroupAndCustomAssignmentType } from 'app/approvals/v2/approvals-orchestration/types/create-review-group-custom-assignment.type';
import { CustomAssignmentCoreService } from 'app/custom-assignment-core/services/custom-assignment-core.service';
import {
    ObjectContext,
    SupportedObjectTypes,
} from 'app/custom-assignment-core/types/object-context.type';
import { buildFullApprovalConfigurationFromEntity } from 'app/policy-approvals/policy-approvals-commerce/adapters/full-approval-configurations.adapters';
import { buildFullApprovalFromEntity } from 'app/policy-approvals/policy-approvals-commerce/adapters/full-approval-with-reviews.adapters';
import { PolicyApprovalConfigurationsAssociationEntity } from 'app/policy-approvals/policy-approvals-commerce/entities/policy-approval-configuration-associations.entity';
import { PolicyApprovalsAssociationEntity } from 'app/policy-approvals/policy-approvals-commerce/entities/policy-approvals-associations.entity';
import {
    PolicyApprovalNotFoundError,
    PolicyReviewSubmissionNotAvailable,
} from 'app/policy-approvals/policy-approvals-commerce/types/errors.classes';
import { FullApprovalConfiguration } from 'app/policy-approvals/policy-approvals-commerce/types/full-approval-configuration.type';
import { FullApprovalWithReviews } from 'app/policy-approvals/policy-approvals-commerce/types/full-approval-with-reviews.type';
import { PolicyApprovalLockPayload } from 'app/policy-approvals/policy-approvals-commerce/types/policy-approval-lock-payload.type';
import { PolicyApprovalSubmitReviewUpdateEvent } from 'app/policy-approvals/policy-approvals-events/events/policy-approval-submit-review-update.event';
import { StartPolicyApprovalWorkflowEvent } from 'app/policy-approvals/policy-approvals-events/events/start-policy-approval-workflow.event';
import { User } from 'app/users/entities/user.entity';
import { PolicyVersion } from 'app/users/policies/entities/policy-version.entity';
import { Policy } from 'app/users/policies/entities/policy.entity';
import { PolicyApprovedByApproverEvent } from 'app/users/policies/observables/events/policy-approved-by-approver.event';
import { PolicyVersionCancelApprovalEvent } from 'app/users/policies/observables/events/policy-version-cancel-approval.event';
import { PolicyVersionRequestChangesEvent } from 'app/users/policies/observables/events/policy-version-request-changes.event';
import { PolicyVersionStatusCancelApprovalEvent } from 'app/users/policies/observables/events/policy-version-status-cancel-approval.event';
import { PolicyVersionRepository } from 'app/users/policies/repositories/policy-version.repository';
import { PolicyRepository } from 'app/users/policies/repositories/policy.repository';
import { PolicyEmailsCoreService } from 'app/users/policies/services/policy-emails-core.service';
import {
    createPolicyApprovalLockKey,
    validateApprovalNotFinalized,
    validateApprovalReviewGroups,
    validatePolicyApprovalAssociationExists,
    validatePolicyOwner,
    validatePolicyStateForApproval,
    validatePolicyVersionNeedsApproval,
    validateReviewExists,
    validateReviewGroupConsensusNotReached,
    validateReviewGroupExists,
    validateReviewReadyForApproval,
    validateReviewStatusNotDuplicate,
    validateUserInActiveApprovalTier,
    validateWorkflowRunIdExists,
} from 'commons/helpers/policy-approval.helper';

import { PolicyApprovalTerminateEvent } from 'app/policy-approvals/policy-approvals-events/events/policy-approval-terminate-workflow.event';
import { PolicyVersionOperationService } from 'app/users/policies/services/policy-version-operation.service';
import { GetSummarizedPolicyWithoutDeletedVersions } from 'app/users/policies/types/get-policy-summarized.type';
import { UsersCoreService } from 'app/users/services/users-core.service';
import { overrideReviewGroupUpdate } from 'app/worker/workflows';
import { Account } from 'auth/entities/account.entity';
import { PolloAdapter } from 'commons/adapters/pollo.adapter';
import { TenancyTransaction } from 'commons/decorators/transaction';
import { NotFoundException } from 'commons/exceptions/not-found.exception';
import { TemporalClient } from 'commons/helpers/temporal/client';
import { AppService } from 'commons/services/app.service';
import config from 'config';
import { cloneDeep, first, isEmpty, isNil, sortBy, uniq } from 'lodash';
import { Span } from 'nestjs-ddtrace';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { Repository } from 'typeorm';

@Injectable()
export class PolicyApprovalsCommerceService extends AppService {
    constructor(
        private readonly approvalConfigurationCoreService: ApprovalConfigurationCoreService,
        private readonly approvalsCoreService: ApprovalsCoreService,
        private readonly approvalsOrchestrationService: ApprovalsOrchestrationService,
        private readonly customAssignmentCoreService: CustomAssignmentCoreService,
        private readonly policyEmailsCoreService: PolicyEmailsCoreService,
        private readonly policyVersionOperationService: PolicyVersionOperationService,
        private readonly reviewGroupCoreService: ReviewGroupCoreService,
        private readonly userCoreService: UsersCoreService,
    ) {
        super();
    }

    @Span()
    @TenancyTransaction()
    public async linkPolicyAndApprovalConfiguration(
        policyId: number,
        approvalConfigurationId: number,
    ): Promise<void> {
        await this.policyApprovalConfigurationAssociationsRepository.softDelete({
            policyId,
        });
        await this.policyApprovalConfigurationAssociationsRepository.insert({
            approvalConfigurationId,
            policyId,
        });
    }

    @Span()
    @TenancyTransaction()
    public async getApprovalsForPolicy(
        policyId: number,
        policyVersionId: number,
    ): Promise<FullApprovalWithReviews[]> {
        const approvalsMappings = await this.policyApprovalAssociationsRepository.find({
            where: {
                policyId,
                policyVersionId,
            },
            relations: {
                approval: {
                    reviewGroups: {
                        reviews: { approvalNote: true },
                        reviewGroupConfiguration: { customAssignment: true },
                    },
                },
            },
        });

        return approvalsMappings.map(map => buildFullApprovalFromEntity(map.approval));
    }

    @Span()
    @TenancyTransaction()
    public async getApprovalConfigurationsForPolicy(
        policyId: number,
    ): Promise<FullApprovalConfiguration[]> {
        const approvalsMappings = await this.policyApprovalConfigurationAssociationsRepository.find(
            {
                where: {
                    policyId,
                },
                relations: {
                    approvalConfiguration: {
                        reviewGroupConfigurations: {
                            customAssignment: {
                                customGroupUsers: true,
                                customGroupRoles: true,
                                customGroupResponsibilities: true,
                            },
                        },
                    },
                },
            },
        );

        // Since we're retrieving the configuration, we should include the custom assignment settings.

        return approvalsMappings.map(map =>
            buildFullApprovalConfigurationFromEntity(map.approvalConfiguration),
        );
    }

    @Span()
    @TenancyTransaction()
    public async getPolicyForApproval(
        approvalId: number,
    ): Promise<PolicyApprovalsAssociationEntity> {
        return this.policyApprovalAssociationsRepository.findOneOrFail({
            where: {
                approvalId,
            },
            relations: {
                approval: true,
                policy: true,
                policyVersion: true,
            },
            withDeleted: true,
        });
    }

    @Span()
    @TenancyTransaction()
    public async cancelPolicyApprovalWorkflow(
        account: Account,
        user: User,
        policyId: number,
        policyVersionId: number,
        dto: { notify: boolean },
    ): Promise<void> {
        const policyApprovalAssociation =
            await this.getPolicyApprovalAssociationByPolicyIdAndVersionId(
                policyId,
                policyVersionId,
            );
        validatePolicyApprovalAssociationExists(policyApprovalAssociation);
        const { approval, policyVersion, policy } = policyApprovalAssociation;

        validatePolicyOwner(policy, user);
        validateApprovalReviewGroups(approval);

        const { isLocked } = await this.getPolicyApprovalLockStatus(
            account,
            policyId,
            policyVersionId,
            approval.id,
        );
        if (isLocked) {
            throw new PolicyReviewSubmissionNotAvailable(
                'Policy approval operation is already in progress. Please try again later.',
            );
        }

        this._eventBus.publish(
            // NOTE: This should be PolicyApprovalCancelEvent
            new PolicyApprovalTerminateEvent(account, user, {
                approvalId: approval.id,
                policyId,
                policyVersion,
                notify: dto.notify,
            }),
        );
    }

    @Span()
    @TenancyTransaction()
    public async submitPolicyApprovalReview(
        account: Account,
        user: User,
        policyId: number,
        policyVersionId: number,
        reviewId: number,
        newReviewStatus: ReviewStatus,
        rejectionMessage?: string,
    ): Promise<void> {
        const policyApprovalAssociation =
            await this.getPolicyApprovalAssociationByPolicyIdAndVersionId(
                policyId,
                policyVersionId,
            );

        validatePolicyApprovalAssociationExists(policyApprovalAssociation);
        const { approval, policy, policyVersion } = policyApprovalAssociation;
        const { reviewGroups } = approval;
        validatePolicyStateForApproval(policy);
        validatePolicyVersionNeedsApproval(policyVersion);
        validateApprovalNotFinalized(approval);
        validateUserInActiveApprovalTier(reviewGroups, user.id);
        const reviewGroup = reviewGroups.find(g => g.reviews.find(r => r.id === reviewId));
        validateReviewGroupExists(reviewGroup);
        const review = reviewGroup.reviews.find(r => r.id === reviewId);
        validateReviewExists(review);
        validateReviewReadyForApproval(review);
        validateReviewGroupConsensusNotReached(reviewGroup);
        validateReviewStatusNotDuplicate(review, newReviewStatus);
        const waasRunId = reviewGroup.waasRunId;
        validateWorkflowRunIdExists(waasRunId);

        const { isLocked } = await this.getPolicyApprovalLockStatus(
            account,
            policyId,
            policyVersionId,
            approval.id,
        );

        if (isLocked) {
            throw new PolicyReviewSubmissionNotAvailable(
                'Policy approval operation is already in progress. Please try again later.',
            );
        }

        this._eventBus.publish(
            new PolicyApprovalSubmitReviewUpdateEvent(account, user, {
                approvalId: approval.id,
                newReviewStatus,
                policy,
                policyId,
                policyVersion,
                rejectionMessage,
                reviewGroupId: reviewGroup.id,
                reviewId,
                waasRunId,
            }),
        );
    }

    // TODO https://drata.atlassian.net/browse/ENG-73771 remove this method because it is no longer used
    @Span()
    @TenancyTransaction()
    public async overridePolicyApproval(
        account: Account,
        user: User,
        policyId: number,
        policyVersionId: number,
        approvalId: number,
        overrideReason: string,
        overrideStatus: ReviewStatus,
        reviewGroupId?: number,
    ): Promise<void> {
        const approvalsMapping = await this.policyApprovalAssociationsRepository.findOneOrFail({
            where: {
                policyId,
                policyVersionId,
                approvalId,
                approval: {
                    status: ApprovalStatusType.READY_FOR_REVIEWS,
                },
            },
            relations: {
                approval: { reviewGroups: true },
            },
        });

        const isMultiOverride = isNil(reviewGroupId);
        const temporalClient = await TemporalClient.getInstance();

        if (isMultiOverride) {
            const reviewGroups = approvalsMapping.approval?.reviewGroups ?? [];
            await Promise.all(
                reviewGroups.map(async reviewGroup => {
                    if (reviewGroup.waasRunId) {
                        await temporalClient.sendUpdate(
                            reviewGroup.waasRunId,
                            overrideReviewGroupUpdate,
                            [user.id, overrideStatus, overrideReason],
                        );
                    } else {
                        this.logger.log(
                            PolloAdapter.acct(
                                `Review group ${reviewGroup.id} does not have a waasRunId. Skipping override.`,
                                account,
                                this.constructor.name,
                            ).setIdentifier(reviewGroup),
                        );
                    }
                }),
            );
        } else {
            const reviewGroup = approvalsMapping.approval?.reviewGroups?.find(
                group => group.id === reviewGroupId,
            );

            if (isNil(reviewGroup)) {
                throw new PolicyApprovalNotFoundError('Approval group not found with Review ID');
            }

            if (isNil(reviewGroup.waasRunId)) {
                throw new PolicyReviewSubmissionNotAvailable(
                    'The approval group is no longer accepting inputs.',
                );
            }

            temporalClient
                .sendUpdate(reviewGroup.waasRunId, overrideReviewGroupUpdate, [
                    user.id,
                    overrideStatus,
                    overrideReason,
                ])
                .then(([reviewStatus, newApprovalStatus]) => {
                    this.logger.log(
                        PolloAdapter.acct(
                            `Successfully sent temporal workflow update`,
                            account,
                            this.constructor.name,
                        ).setIdentifier({
                            policyId,
                            policyVersionId,
                            reviewGroupId,
                            waasRunId: reviewGroup.waasRunId,
                            reviewStatus,
                            newApprovalStatus,
                        }),
                    );
                })
                .catch(error => {
                    this.logger.error(
                        PolloAdapter.acct(
                            `Failed to send temporal workflow update`,
                            account,
                            this.constructor.name,
                        )
                            .setIdentifier({
                                policyId,
                                policyVersionId,
                                reviewGroupId,
                                waasRunId: reviewGroup.waasRunId,
                            })
                            .setError(error),
                    );
                });
        }
    }

    @Span()
    @TenancyTransaction()
    public async linkPolicyAndApproval(
        policyId: number,
        policyVersionId: number,
        approvalId: number,
    ): Promise<void> {
        await this.policyApprovalAssociationsRepository.softDelete({
            policyId,
            policyVersionId,
        });
        await this.policyApprovalAssociationsRepository.insert({
            approvalId,
            policyId,
            policyVersionId,
        });
    }

    @Span()
    @TenancyTransaction()
    public async createInitialApprovalConfiguration(
        ownerId: number,
        policyId: number,
    ): Promise<void> {
        const newApprovalConfiguration =
            await this.approvalConfigurationCoreService.createApprovalConfiguration({
                ownerId: ownerId,
            });

        await this.linkPolicyAndApprovalConfiguration(policyId, newApprovalConfiguration.id);

        const reviewGroupConfigurationData: Omit<
            CreateReviewGroupAndCustomAssignmentType,
            'customAssignmentId'
        > = {
            name: '',
            consensusRule: ConsensusRule.ALL,
            consensusRuleSubsetNumber: 1,
            timeline: '14d',
            hierarchicalLevel: 1,
            deadlineExceededPolicy: DeadlineExceededPolicy.WAIT,
            customAssignment: { userIds: [ownerId] },
        };

        await this.approvalsOrchestrationService.createReviewGroupConfigurationWithCustomAssignment(
            newApprovalConfiguration.id,
            reviewGroupConfigurationData,
        );
    }

    @Span()
    @TenancyTransaction()
    public async startPolicyApprovalWorkflowFromLatestConfiguration(
        policyId: number,
        policyVersionId: number,
        user: User,
        account: Account,
    ): Promise<void> {
        try {
            const policy = await this.policyRepository.findOneOrFail({
                where: { id: policyId },
            });

            const policyVersion = await this.policyVersionRepository.findOneOrFail({
                where: { id: policyVersionId },
            });

            await this._eventBus.publish(
                new StartPolicyApprovalWorkflowEvent(account, user, { policy, policyVersion }),
            );

            this.logger.log(
                PolloAdapter.acct(
                    `Published StartPolicyApprovalWorkflowEvent`,
                    account,
                    this.constructor.name,
                ).setIdentifier({
                    policyId,
                    policyVersionId,
                }),
            );
        } catch (error) {
            this.logger.error(
                PolloMessage.msg(error.message)
                    .setAccountId(account.id)
                    .setError(error)
                    .setContext(this.constructor.name)
                    .setSubContext('startPolicyApprovalWorkflowFromLatestConfiguration'),
            );
            throw error;
        }
    }

    private async publishCancelApprovalEvent(
        account: Account,
        user: User,
        policyId: number,
        policyVersionId: number,
    ): Promise<void> {
        const policy = await this.policyRepository.getPolicyByIdAndVersionId(
            policyId,
            policyVersionId,
        );

        const policyVersion = cloneDeep(first(policy.versions));
        if (isNil(policyVersion)) {
            throw new NotFoundException(ErrorCode.POLICY_VERSION_NOT_FOUND);
        }

        this._eventBus.publish(
            new PolicyVersionCancelApprovalEvent(
                account,
                user,
                policy as unknown as GetSummarizedPolicyWithoutDeletedVersions,
                policyVersion,
            ),
        );
    }

    @Span()
    @TenancyTransaction()
    public async getApprovalByIdForReview(approvalId: number): Promise<FullApprovalWithReviews> {
        const result = await this.policyApprovalAssociationsRepository.findOneOrFail({
            where: { approvalId },
            relations: {
                approval: {
                    reviewGroups: {
                        reviews: { approvalNote: true },
                        reviewGroupConfiguration: {
                            customAssignment: true,
                        },
                    },
                },
            },
        });

        return buildFullApprovalFromEntity(result.approval);
    }

    /**
     * Retrieves the user IDs of all reviewers assigned to a specific review group.
     *
     * This method fetches the review group by ID and extracts the user IDs from all
     * associated reviews within that group. It's commonly used to identify who needs
     * to be notified about policy approval workflow changes or to determine the scope
     * of reviewers for a particular approval tier.
     *
     * @param reviewGroupId - The unique identifier of the review group
     * @returns Promise that resolves to an array of user IDs representing the reviewers
     */
    @Span()
    @TenancyTransaction()
    public async getReviewGroupReviewersIds(reviewGroupId: number): Promise<number[]> {
        const reviewGroup = await this.reviewGroupCoreService.getReviewGroupById(reviewGroupId);
        return reviewGroup.reviews.map(review => review.userId);
    }

    /**
     * Retrieves the display name of a review group based on its configuration.
     *
     * This method fetches the review group and its associated configuration to return
     * the human-readable name. The name is typically defined in the approval configuration
     * and used for display purposes in the UI, notifications, and audit logs.
     *
     * @param reviewGroupId - The unique identifier of the review group
     * @returns Promise that resolves to the review group name, or null if no name is configured
     */
    @Span()
    @TenancyTransaction()
    public async getReviewGroupName(reviewGroupId: number): Promise<string | null> {
        const reviewGroup = await this.reviewGroupCoreService.getReviewGroupById(reviewGroupId);
        const reviewGroupConfiguration =
            await this.approvalConfigurationCoreService.getReviewGroupConfigurationById(
                reviewGroup.reviewGroupConfigurationId,
            );
        return reviewGroupConfiguration?.name || null;
    }

    @Span()
    @TenancyTransaction()
    public async overrideCompletedApprovalReviewGroups(
        approvalId: number,
        policyId: number,
        userId: number,
        overrideReason: string,
    ): Promise<ApprovalStatusType> {
        const approval = await this.getApprovalByIdForReview(approvalId);
        const configuration =
            await this.approvalConfigurationCoreService.getFullApprovalConfigurationById(
                approval.approvalConfigurationId,
            );

        const storedConfigurationIds = new Set(
            approval.reviewGroups.map(group => group.reviewGroupConfiguration.id),
        );

        const pendingReviewGroups = sortBy(
            configuration.reviewGroupConfigurations.filter(
                group => !storedConfigurationIds.has(group.id),
            ),
            group => group.hierarchicalLevel,
        );

        const storedReviewGroupIds = approval.reviewGroups
            .filter(group => group.consensusDecision !== ReviewStatus.APPROVED)
            .map(group => group.id);

        for await (const reviewGroupConfiguration of pendingReviewGroups) {
            const reviewGroup = await this.reviewGroupCoreService.createReviewGroup({
                approvalId: approval.id,
                reviewGroupConfigurationId: reviewGroupConfiguration.id,
            });

            const userIds = await this.customAssignmentCoreService.denormalizeUsersInCustomGroup(
                reviewGroupConfiguration.customAssignment.id,
                {
                    type: SupportedObjectTypes.POLICY,
                    policyId,
                },
            );

            await this.reviewGroupCoreService.createReviewsInReviewGroup(reviewGroup.id, userIds);

            storedReviewGroupIds.push(reviewGroup.id);
        }

        await Promise.all(
            storedReviewGroupIds.map(async id => {
                await this.reviewGroupCoreService.setOverrideReview(
                    id,
                    userId,
                    ReviewStatus.APPROVED,
                );
                await this.reviewGroupCoreService.createReviewNote(id, overrideReason);
            }),
        );

        await this.approvalsCoreService.updateApproval({
            id: approval.id,
            status: ApprovalStatusType.APPROVED,
        });

        return ApprovalStatusType.APPROVED;
    }

    @Span()
    @TenancyTransaction()
    public getPolicyApprovalAssociationByPolicyIdAndVersionId(
        policyId: number,
        policyVersionId: number,
    ): Promise<PolicyApprovalsAssociationEntity | null> {
        return this.policyApprovalAssociationsRepository.findOne({
            where: {
                policyId,
                policyVersionId,
            },
            relations: {
                approval: {
                    reviewGroups: {
                        reviews: true,
                        reviewGroupConfiguration: { customAssignment: true },
                    },
                },
                policy: { currentOwner: true },
                policyVersion: { owner: true },
            },
        });
    }

    @Span()
    @TenancyTransaction()
    public async doesPolicyApprovalAssociationExist(
        policyId: number,
        policyVersionId: number,
        approvalId: number,
    ): Promise<boolean> {
        return this.policyApprovalAssociationsRepository.existsBy({
            policyId,
            policyVersionId,
            approvalId,
        });
    }

    @Span()
    @TenancyTransaction()
    public async handlePolicyApprovalReviewApproved(
        account: Account,
        user: User,
        policy: Policy,
        policyVersion: PolicyVersion,
        tier: string | null,
    ) {
        this._eventBus.publish(
            new PolicyApprovedByApproverEvent(account, user, policy.name, policyVersion, tier),
        );
    }

    @Span()
    @TenancyTransaction()
    public async handlePolicyApprovalApproved(
        account: Account,
        user: User,
        policy: Policy,
        policyVersion: PolicyVersion,
    ) {
        await this.policyVersionOperationService.updatePolicyVersionStatusToApproved(
            account,
            policy,
            policyVersion,
            user,
        );

        const approvedAt = policyVersion.approvedAt ?? new Date();
        await this.policyEmailsCoreService.sendPolicyApprovedEmail(
            account,
            policy,
            policyVersion,
            approvedAt,
        );
    }

    @Span()
    @TenancyTransaction()
    public async handlePolicyApprovalChangesRequested(
        account: Account,
        user: User,
        policy: Policy,
        policyVersion: PolicyVersion,
        rejectionMessage: string,
        reviewerIds: number[],
    ) {
        const owner = policy.currentOwner || policyVersion?.owner;
        if (owner) {
            const reviewers = await this.userCoreService.getUsersByIds(
                uniq([...reviewerIds, owner.id]).filter(id => id !== user.id),
            );
            await Promise.allSettled(
                reviewers.map(async userToNotify => {
                    await this.policyEmailsCoreService.sendPolicyRequestChangesEmail(
                        account,
                        policy,
                        policyVersion,
                        user,
                        rejectionMessage,
                        userToNotify,
                    );
                }),
            );
        } else {
            this.logger.warn(
                PolloMessage.msg(
                    `Policy ${policy.id} (${policy.name}) has no assigned owner. Cannot send changes requested email.`,
                )
                    .setAccountId(account.id)
                    .setContext(this.constructor.name)
                    .setSubContext('handlePolicyVersionChangesRequested')
                    .setIdentifier({
                        policyId: policy.id,
                        policyVersionId: policyVersion.id,
                        policyName: policy.name,
                    }),
            );
        }
        this._eventBus.publish(
            new PolicyVersionRequestChangesEvent(
                account,
                user,
                policy as any, // Type casting to match event requirements
                policyVersion as any, // Type casting to match event requirements
                rejectionMessage,
            ),
        );
    }

    @Span()
    @TenancyTransaction()
    public async handlePolicyApprovalWorkflowStarted(
        account: Account,
        user: User,
        policyId: number,
        policyVersionId: number,
    ) {
        try {
            const policyVersion = await this.policyVersionRepository.findOneOrFail({
                where: { id: policyVersionId },
            });

            // NOTE: we are no longer sending emails when the policy approval workflow has started because the workflow already does that
            await this.policyVersionOperationService.updatePolicyVersionStatusToNeedsApproval(
                account,
                policyId,
                policyVersion,
                user,
            );
        } catch (error) {
            this.logger.error(
                PolloMessage.msg('Failed to update policy version status to needs approval')
                    .setAccountId(account.id)
                    .setError(error)
                    .setContext(this.constructor.name)
                    .setSubContext(this.handlePolicyApprovalWorkflowStarted.name)
                    .setIdentifier({
                        policyId,
                        policyVersionId,
                    }),
            );
        } finally {
            await this.releasePolicyApprovalStartWorkflow(account, policyId, policyVersionId);
        }
    }

    private get policyApprovalAssociationsRepository(): Repository<PolicyApprovalsAssociationEntity> {
        return this.getTenantRepository<PolicyApprovalsAssociationEntity>(
            PolicyApprovalsAssociationEntity,
        );
    }

    private get policyRepository(): PolicyRepository {
        return this.getCustomTenantRepository(PolicyRepository);
    }

    private get policyVersionRepository(): PolicyVersionRepository {
        return this.getCustomTenantRepository(PolicyVersionRepository);
    }

    private get policyApprovalConfigurationAssociationsRepository(): Repository<PolicyApprovalConfigurationsAssociationEntity> {
        return this.getTenantRepository<PolicyApprovalConfigurationsAssociationEntity>(
            PolicyApprovalConfigurationsAssociationEntity,
        );
    }

    /**
     * Checks if a policy approval operation is currently locked
     * @returns true if locked, false if available
     */
    @Span()
    public async getPolicyApprovalLockStatus(
        account: Account,
        policyId: number,
        policyVersionId: number,
        approvalId?: number,
    ): Promise<{ payload: PolicyApprovalLockPayload | null; isLocked: boolean }> {
        const lockKey = createPolicyApprovalLockKey(
            account.id,
            policyId,
            policyVersionId,
            approvalId,
        );

        const lockValue = await this._cacheService.getLockData<PolicyApprovalLockPayload>(lockKey);
        if (isNil(lockValue)) {
            return { isLocked: false, payload: null };
        }

        return { isLocked: true, payload: lockValue };
    }

    /**
     * Attempts to get the lock for policy approval start workflow operation
     * @returns object with lockKey and whether lock was acquired
     */
    @Span()
    public async lockPolicyApprovalStartWorkflow(
        account: Account,
        policyId: number,
        policyVersionId: number,
        payload: PolicyApprovalLockPayload,
    ): Promise<{ lockKey: string; acquired: boolean }> {
        const lockKey = createPolicyApprovalLockKey(account.id, policyId, policyVersionId);

        const acquired = await this._cacheService.acquireLock(
            lockKey,
            config.get('cache.ttl.hour'),
            payload,
        );

        return { lockKey, acquired };
    }

    /**
     * Releases the Redis lock for policy approval start workflow operation
     */
    @Span()
    public async releasePolicyApprovalStartWorkflow(
        account: Account,
        policyId: number,
        policyVersionId: number,
    ): Promise<void> {
        try {
            const lockKey = createPolicyApprovalLockKey(account.id, policyId, policyVersionId);
            await this._cacheService.releaseLock(lockKey);
        } catch (lockReleaseError) {
            this.logger.error(
                PolloAdapter.acct(
                    'Failed to release policy approval start workflow lock',
                    account,
                    this.constructor.name,
                )
                    .setIdentifier({
                        policyId,
                        policyVersionId,
                    })
                    .setError(lockReleaseError),
            );
        }
    }

    /**
     * Attempts to get the lock for policy approval handler operations (submit approval & cancel)
     * @returns object with lockKey and whether lock was acquired
     */
    @Span()
    public async lockPolicyApprovalWorkflow(
        account: Account,
        policyId: number,
        policyVersionId: number,
        approvalId: number,
        payload: PolicyApprovalLockPayload,
    ): Promise<{ lockKey: string; acquired: boolean }> {
        const lockKey = createPolicyApprovalLockKey(
            account.id,
            policyId,
            policyVersionId,
            approvalId,
        );

        const acquired = await this._cacheService.acquireLock(
            lockKey,
            config.get('cache.ttl.hour'),
            payload,
        );

        return { lockKey, acquired };
    }

    /**
     * Releases the Redis lock for policy approval handler operations (submit approval & cancel)
     */
    @Span()
    public async releasePolicyApprovalWorkflow(
        account: Account,
        approvalId: number,
        policyId: number,
        policyVersionId: number,
    ): Promise<void> {
        try {
            const lockKey = createPolicyApprovalLockKey(
                account.id,
                policyId,
                policyVersionId,
                approvalId,
            );
            await this._cacheService.releaseLock(lockKey);
        } catch (lockReleaseError) {
            this.logger.error(
                PolloAdapter.acct(
                    'Failed to release policy approval lock',
                    account,
                    this.constructor.name,
                )
                    .setIdentifier({
                        policyId,
                        policyVersionId,
                        approvalId,
                    })
                    .setError(lockReleaseError),
            );
        }
    }

    /**
     * Handles successful policy approval cancellation by emitting events and performing cleanup
     * This method orchestrates all post-cancellation operations including:
     * - Removing custom tasks from review groups
     * - Publishing policy version cancel approval event
     * - Updating policy version status to DRAFT
     * - Sending email notifications to reviewers
     */
    @Span()
    public async handleSuccessfulPolicyApprovalCancellation(
        account: Account,
        user: User,
        policy: Policy,
        policyVersion: PolicyVersion,
        reviewerIds: number[],
        notify: boolean = true,
    ): Promise<void> {
        try {
            const objectContext: ObjectContext = {
                type: SupportedObjectTypes.POLICY,
                policyId: policy.id,
            };
            await this.approvalsOrchestrationService.removeCustomTasksInReviewGroup(objectContext);
            await this.policyVersionOperationService.updatePolicyVersionStatusOnNeedsApprovalToDraft(
                account,
                policyVersion,
            );
            this.logger.log(
                PolloAdapter.acct(
                    'Updated policy version status to DRAFT after cancellation',
                    account,
                    this.constructor.name,
                ).setIdentifier({
                    policyId: policy.id,
                    policyVersionId: policyVersion.id,
                }),
            );
            if (notify) {
                this._eventBus.publish(
                    new PolicyVersionCancelApprovalEvent(
                        account,
                        user,
                        policy as unknown as GetSummarizedPolicyWithoutDeletedVersions,
                        policyVersion,
                    ),
                );
                this._eventBus.publish(
                    new PolicyVersionStatusCancelApprovalEvent(
                        account,
                        user,
                        policy as unknown as GetSummarizedPolicyWithoutDeletedVersions,
                        policyVersion,
                    ),
                );
                if (!isEmpty(reviewerIds)) {
                    await this.sendCancelApprovalEmailsToReviewers(
                        account,
                        user,
                        reviewerIds,
                        policy.id,
                        policyVersion,
                    );
                }
            }

            this.logger.log(
                PolloAdapter.acct(
                    'Successfully completed post-cancellation operations',
                    account,
                    this.constructor.name,
                ).setIdentifier({
                    policyId: policy.id,
                    policyVersionId: policyVersion.id,
                    reviewerCount: reviewerIds.length,
                }),
            );
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(
                    'Failed to complete post-cancellation operations',
                    account,
                    this.constructor.name,
                )
                    .setIdentifier({
                        policyId: policy.id,
                        policyVersionId: policyVersion.id,
                    })
                    .setError(error),
            );
            throw error;
        }
    }

    /**
     * Sends cancel approval emails to all reviewers
     */
    @Span()
    private async sendCancelApprovalEmailsToReviewers(
        account: Account,
        owner: User,
        reviewerIds: number[],
        policyId: number,
        policyVersion: PolicyVersion,
    ): Promise<void> {
        try {
            // Get policy and reviewer details
            const [policy, reviewers] = await Promise.all([
                this.policyRepository.findOneOrFail({ where: { id: policyId } }),
                this.userCoreService.getUsersByIds(reviewerIds),
            ]);

            // Send emails to all reviewers
            await Promise.allSettled(
                reviewers.map(async reviewer => {
                    if (reviewer) {
                        await this.policyEmailsCoreService.sendPolicyApprovalCancelEmail(
                            account,
                            owner,
                            reviewer,
                            policy,
                            policyVersion.formattedVersion(),
                        );
                    }
                }),
            );

            this.logger.log(
                PolloAdapter.acct(
                    'Successfully sent cancel approval emails to reviewers',
                    account,
                    this.constructor.name,
                ).setIdentifier({
                    policyId,
                    policyVersionId: policyVersion.id,
                    reviewerCount: reviewers.length,
                }),
            );
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(
                    'Failed to send cancel approval emails to reviewers',
                    account,
                    this.constructor.name,
                )
                    .setIdentifier({
                        policyId,
                        policyVersionId: policyVersion.id,
                        reviewerCount: reviewerIds.length,
                    })
                    .setError(error),
            );
            // Don't throw error as email failures shouldn't fail the entire operation
        }
    }
}
