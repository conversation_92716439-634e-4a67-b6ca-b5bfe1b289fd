import { ApprovalNoteEntity } from 'app/approvals/v2/approval-review-groups-core/entities/approval-note.entity';
import { ReviewGroupEntity } from 'app/approvals/v2/approval-review-groups-core/entities/review-group.entity';
import { ReviewEntity } from 'app/approvals/v2/approval-review-groups-core/entities/review.entity';
import { ReviewStatus } from 'app/approvals/v2/approval-review-groups-core/types/review-status.enum';
import { ApprovalEntity } from 'app/approvals/v2/approvals-core/entities/approval.entity';
import { ApprovalStatusType } from 'app/approvals/v2/approvals-core/types/approval-status.enum';
import { PolicyApprovalsAssociationEntity } from 'app/policy-approvals/policy-approvals-commerce/entities/policy-approvals-associations.entity';
import {
    PolicyApprovalRollbackService,
    RollbackState,
    StartWorkflowRollbackState,
} from 'app/policy-approvals/policy-approvals-commerce/services/policy-approval-rollback.service';
import { PolicyVersion } from 'app/users/policies/entities/policy-version.entity';
import { Account } from 'auth/entities/account.entity';
import { DrataDataSource } from 'commons/classes/drata-data-source.class';
import { PolicyVersionStatus } from 'commons/enums/users/policies/policy-version-status.enum';
import { ConnectionFactory } from 'commons/factories/connection.factory';
import { createAppTestingModule } from 'commons/helpers/app-testing-module.helper';
import { MockProxy, mock } from 'jest-mock-extended';
import { TenancyContext } from 'tenancy/contexts/tenancy.context';
import { Repository } from 'typeorm';

// Mock the transaction decorator to avoid database connection issues
jest.mock('commons/decorators/transaction', () => ({
    TenancyTransaction() {
        return jest.fn();
    },
}));

describe('PolicyApprovalRollbackService', () => {
    let service: PolicyApprovalRollbackService;
    let mockReviewRepository: MockProxy<Repository<ReviewEntity>>;
    let mockReviewGroupRepository: MockProxy<Repository<ReviewGroupEntity>>;
    let mockApprovalRepository: MockProxy<Repository<ApprovalEntity>>;
    let mockPolicyVersionRepository: MockProxy<Repository<PolicyVersion>>;
    let mockPolicyApprovalAssociationsRepository: MockProxy<
        Repository<PolicyApprovalsAssociationEntity>
    >;

    let mockConnection: MockProxy<DrataDataSource>;
    let mockTransactionalEntityManager: MockProxy<any>;

    const mockAccount = mock<Account>();
    mockAccount.id = '1' as any; // Account.id is a UUID string, but for testing we use a simple string
    mockAccount.domain = 'test-domain';

    beforeEach(async () => {
        mockReviewRepository = mock<Repository<ReviewEntity>>();
        mockReviewGroupRepository = mock<Repository<ReviewGroupEntity>>();
        mockApprovalRepository = mock<Repository<ApprovalEntity>>();
        mockPolicyVersionRepository = mock<Repository<PolicyVersion>>();
        mockPolicyApprovalAssociationsRepository =
            mock<Repository<PolicyApprovalsAssociationEntity>>();

        mockConnection = mock<DrataDataSource>();
        mockTransactionalEntityManager = mock<any>();

        const module = await createAppTestingModule({
            providers: [PolicyApprovalRollbackService],
        }).compile();

        // Override the TenancyContext after module creation
        const tenancyContext = module.get(TenancyContext);
        jest.spyOn(tenancyContext, 'getRepository').mockImplementation((target: any) => {
            if (target === ReviewEntity) return mockReviewRepository;
            if (target === ReviewGroupEntity) return mockReviewGroupRepository;
            if (target === ApprovalEntity) return mockApprovalRepository;
            if (target === PolicyVersion) return mockPolicyVersionRepository;
            if (target === PolicyApprovalsAssociationEntity)
                return mockPolicyApprovalAssociationsRepository;
            return mock<Repository<any>>();
        });

        service = module.get<PolicyApprovalRollbackService>(PolicyApprovalRollbackService);

        // Mock ConnectionFactory
        jest.spyOn(ConnectionFactory, 'getConnection').mockResolvedValue(mockConnection);
        mockConnection.transaction.mockImplementation(async (_isolationLevel, callback) => {
            return callback(mockTransactionalEntityManager);
        });

        // Setup transactional entity manager mocks
        mockTransactionalEntityManager.getRepository.mockImplementation((entity: any) => {
            if (entity === ReviewEntity) return mockReviewRepository;
            if (entity === ReviewGroupEntity) return mockReviewGroupRepository;
            if (entity === ApprovalEntity) return mockApprovalRepository;
            if (entity === PolicyVersion) return mockPolicyVersionRepository;
            if (entity === PolicyApprovalsAssociationEntity)
                return mockPolicyApprovalAssociationsRepository;
            return mock<Repository<any>>();
        });
    });

    describe('captureStateForRollback', () => {
        it('should capture current state for rollback', async () => {
            // Arrange
            const reviewId = 1;
            const reviewGroupId = 2;
            const approvalId = 3;
            const policyVersionId = 4;

            const mockReview = {
                id: reviewId,
                status: ReviewStatus.READY_FOR_REVIEW,
            } as ReviewEntity;

            const mockReviewGroup = {
                id: reviewGroupId,
                hasReachedConsensus: false,
                consensusDecision: undefined,
            } as ReviewGroupEntity;

            const mockApproval = {
                id: approvalId,
                status: ApprovalStatusType.READY_FOR_REVIEWS,
            } as ApprovalEntity;

            const mockPolicyVersion = {
                id: policyVersionId,
                policyVersionStatus: PolicyVersionStatus.DRAFT,
            } as PolicyVersion;

            mockReviewRepository.findOneOrFail.mockResolvedValue(mockReview);
            mockReviewGroupRepository.findOneOrFail.mockResolvedValue(mockReviewGroup);
            mockApprovalRepository.findOneOrFail.mockResolvedValue(mockApproval);
            mockPolicyVersionRepository.findOneOrFail.mockResolvedValue(mockPolicyVersion);

            // Act
            const result = await service.captureStateForRollback(
                reviewId,
                reviewGroupId,
                approvalId,
                policyVersionId,
            );

            // Assert
            expect(result).toEqual({
                reviewId,
                previousReviewStatus: ReviewStatus.READY_FOR_REVIEW,
                reviewGroupId,
                previousConsensusState: false,
                previousConsensusDecision: undefined,
                approvalId,
                previousApprovalStatus: ApprovalStatusType.READY_FOR_REVIEWS,
                policyVersionId,
                previousPolicyVersionStatus: PolicyVersionStatus.DRAFT,
            });

            expect(mockReviewRepository.findOneOrFail).toHaveBeenCalledWith({
                where: { id: reviewId },
            });
            expect(mockReviewGroupRepository.findOneOrFail).toHaveBeenCalledWith({
                where: { id: reviewGroupId },
            });
            expect(mockApprovalRepository.findOneOrFail).toHaveBeenCalledWith({
                where: { id: approvalId },
            });
            expect(mockPolicyVersionRepository.findOneOrFail).toHaveBeenCalledWith({
                where: { id: policyVersionId },
            });
        });
    });

    describe('captureStateForStartWorkflowRollback', () => {
        it('should capture current state for start workflow rollback', async () => {
            // Arrange
            const policyId = 1;
            const policyVersionId = 2;

            const mockPolicyVersion = {
                id: policyVersionId,
                policyVersionStatus: PolicyVersionStatus.DRAFT,
            } as PolicyVersion;

            mockPolicyVersionRepository.findOneOrFail.mockResolvedValue(mockPolicyVersion);

            // Act
            const result = await service.captureStateForStartWorkflowRollback(
                policyId,
                policyVersionId,
            );

            // Assert
            expect(result).toEqual({
                policyId,
                policyVersionId,
                previousPolicyVersionStatus: PolicyVersionStatus.DRAFT,
            });

            expect(mockPolicyVersionRepository.findOneOrFail).toHaveBeenCalledWith({
                where: { id: policyVersionId },
            });
        });
    });

    describe('performRollbackInSeparateTransaction', () => {
        it('should perform rollback successfully', async () => {
            // Arrange
            const rollbackState: RollbackState = {
                reviewId: 1,
                previousReviewStatus: ReviewStatus.READY_FOR_REVIEW,
                reviewGroupId: 2,
                previousConsensusState: false,
                previousConsensusDecision: undefined,
                approvalId: 3,
                previousApprovalStatus: ApprovalStatusType.READY_FOR_REVIEWS,
                policyVersionId: 4,
                previousPolicyVersionStatus: PolicyVersionStatus.DRAFT,
            };

            // Act
            await service.performRollbackInSeparateTransaction(mockAccount, rollbackState);

            // Assert
            expect(ConnectionFactory.getConnection).toHaveBeenCalledWith(mockAccount);
            expect(mockConnection.transaction).toHaveBeenCalledWith(
                'READ COMMITTED',
                expect.any(Function),
            );
            expect(mockTransactionalEntityManager.getRepository).toHaveBeenCalledWith(
                ApprovalNoteEntity,
            );
            expect(mockTransactionalEntityManager.getRepository).toHaveBeenCalledWith(ReviewEntity);
            expect(mockTransactionalEntityManager.getRepository).toHaveBeenCalledWith(
                ReviewGroupEntity,
            );
            expect(mockTransactionalEntityManager.getRepository).toHaveBeenCalledWith(
                ApprovalEntity,
            );
            expect(mockTransactionalEntityManager.getRepository).toHaveBeenCalledWith(
                PolicyVersion,
            );
        });
    });

    describe('performStartWorkflowRollbackInSeparateTransaction', () => {
        it('should perform start workflow rollback successfully', async () => {
            // Arrange
            const rollbackState: StartWorkflowRollbackState = {
                policyId: 1,
                policyVersionId: 2,
                previousPolicyVersionStatus: PolicyVersionStatus.DRAFT,
            };

            // Act
            await service.performStartWorkflowRollbackInSeparateTransaction(
                mockAccount,
                rollbackState,
            );

            // Assert
            expect(ConnectionFactory.getConnection).toHaveBeenCalledWith(mockAccount);
            expect(mockConnection.transaction).toHaveBeenCalledWith(
                'READ COMMITTED',
                expect.any(Function),
            );
            expect(mockTransactionalEntityManager.getRepository).toHaveBeenCalledWith(
                PolicyVersion,
            );
            expect(mockTransactionalEntityManager.getRepository).toHaveBeenCalledWith(
                PolicyApprovalsAssociationEntity,
            );
            expect(mockPolicyApprovalAssociationsRepository.softDelete).toHaveBeenCalledWith({
                policyId: rollbackState.policyId,
                policyVersionId: rollbackState.policyVersionId,
            });
            expect(mockPolicyVersionRepository.update).toHaveBeenCalledWith(
                rollbackState.policyVersionId,
                {
                    policyVersionStatus: PolicyVersionStatus.DRAFT,
                },
            );
        });

        it('should handle rollback errors and rethrow', async () => {
            // Arrange
            const rollbackState: StartWorkflowRollbackState = {
                policyId: 1,
                policyVersionId: 2,
                previousPolicyVersionStatus: PolicyVersionStatus.DRAFT,
            };

            const rollbackError = new Error('Rollback failed');
            mockConnection.transaction.mockRejectedValue(rollbackError);

            // Act & Assert
            await expect(
                service.performStartWorkflowRollbackInSeparateTransaction(
                    mockAccount,
                    rollbackState,
                ),
            ).rejects.toThrow('Rollback failed');
        });
    });
});
