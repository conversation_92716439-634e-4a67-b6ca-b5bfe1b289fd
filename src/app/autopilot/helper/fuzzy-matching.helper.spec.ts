import { ApiData } from 'app/apis/classes/api/api-data.class';
import { MonitorData } from 'app/autopilot/classes/monitor-data.class';
import {
    getComposedFindingTargetIdForApMonitorTask,
    getUniqueValuePropertyNames,
    isSubset,
    is<PERSON><PERSON><PERSON><PERSON><PERSON>,
    resultExclusionFuzzyMatch,
} from 'app/autopilot/helper/fuzzy-matching.helper';
import { MonitorInstanceExclusion as Exclusion } from 'app/monitors/entities/monitor-instance-exclusion.entity';

describe('resultExclusionFuzzyMatch', () => {
    describe('Given that exclusion targetId matches failure id', () => {
        const failure: MonitorData = {
            id: 'resource-123',
            accountId: '*********',
            region: 'us-east-1',
        } as MonitorData;
        const exclusion: Exclusion = { targetId: 'resource-123' } as Exclusion;

        it('should return true', () => {
            const result = resultExclusionFuzzyMatch(failure, exclusion);

            expect(result).toBe(true);
        });
    });

    describe('Given that failure resource has no accountId', () => {
        const failure: MonitorData = {
            id: 'resource-123',
            accountId: undefined,
            region: 'us-east-1',
        } as MonitorData;
        const exclusion: Exclusion = { targetId: '1234567::resource-123' } as Exclusion;

        it('should return false', () => {
            const result = resultExclusionFuzzyMatch(failure, exclusion);

            expect(result).toBe(false);
        });
    });

    describe('Given that exclusion.targetId matches failure resource id composed accountId::id', () => {
        const failure: MonitorData = {
            id: 'resource-123',
            accountId: '************',
            region: 'us-east-1',
        } as MonitorData;
        const exclusion: Exclusion = { targetId: '************::resource-123' } as Exclusion;

        it('should return true', () => {
            const result = resultExclusionFuzzyMatch(failure, exclusion);

            expect(result).toBe(true);
        });
    });

    describe('Given that exclusion.targetId matches failure resource composed accountId::region/id', () => {
        const failure: MonitorData = {
            id: 'resource-123',
            accountId: '************',
            region: 'us-east-1',
        } as MonitorData;

        const exclusion: Exclusion = {
            targetId: '************::us-east-1/resource-123',
        } as Exclusion;

        it('should return true', () => {
            const result = resultExclusionFuzzyMatch(failure, exclusion);

            expect(result).toBe(true);
        });
    });

    describe('Given that exclusion targetId has a different accountId from failure resource', () => {
        const failure: MonitorData = {
            id: 'resource-123',
            accountId: '************',
            region: 'us-east-1',
        } as MonitorData;

        describe('Given that the failing resourceId is different from the exclusion targetId', () => {
            const exclusion: Exclusion = {
                targetId: '312456::resource-1234',
            } as Exclusion;

            it('should return true', () => {
                const result = resultExclusionFuzzyMatch(failure, exclusion);

                expect(result).toBe(false);
            });
        });
        describe('Given that the failing resourceId is the same as the exclusion targetId with no accountId', () => {
            const exclusion: Exclusion = {
                targetId: '312456::resource-123',
            } as Exclusion;

            it('should return true', () => {
                const result = resultExclusionFuzzyMatch(failure, exclusion);

                expect(result).toBe(true);
            });
        });
    });

    describe('Given that exclusion.targetId matches failure.id', () => {
        const failure = {
            id: 'resource-123',
            accountId: '************',
            region: 'us-east-1',
        } as MonitorData;

        const exclusion = { targetId: 'resource-123' } as Exclusion;

        it('should return true', () => {
            const result = resultExclusionFuzzyMatch(failure, exclusion);

            expect(result).toBe(true);
        });
    });
});

describe('getUniqueValuePropertyNames', () => {
    describe('Given an object with all unique values', () => {
        const obj = {
            id: 'resource-123',
            name: 'test-resource',
            region: 'us-east-1',
            accountId: '************',
        };

        it('should return all property names', () => {
            const result = getUniqueValuePropertyNames(obj);

            expect(result).toEqual(['id', 'name', 'region', 'accountId']);
        });
    });

    describe('Given an object with duplicate values', () => {
        const obj = {
            id: 'resource-123',
            name: 'resource-123', // duplicate value with id
            region: 'us-east-1',
            accountId: 'account-456', // unique value
        };

        it('should return only property names with unique values', () => {
            const result = getUniqueValuePropertyNames(obj);

            // Only 'region' and 'accountId' have unique values
            // 'id' and 'name' both have the same value 'resource-123'
            expect(result).toEqual(['region', 'accountId']);
        });
    });

    describe('Given an object with null and undefined values', () => {
        const obj = {
            id: 'resource-123',
            name: null,
            region: undefined,
            accountId: '************',
            dimension: '',
        };

        it('should exclude null and undefined values but include empty strings', () => {
            const result = getUniqueValuePropertyNames(obj);

            expect(result).toEqual(['id', 'accountId', 'dimension']);
        });
    });

    describe('Given an empty object', () => {
        const obj = {};

        it('should return an empty array', () => {
            const result = getUniqueValuePropertyNames(obj);

            expect(result).toEqual([]);
        });
    });

    describe('Given an object with all identical values', () => {
        const obj = {
            prop1: 'same-value',
            prop2: 'same-value',
            prop3: 'same-value',
        };

        it('should return an empty array', () => {
            const result = getUniqueValuePropertyNames(obj);

            expect(result).toEqual([]);
        });
    });

    describe('Given an object with mixed data types', () => {
        const obj = {
            stringProp: 'test',
            numberProp: 123,
            booleanProp: true,
            objectProp: { nested: 'value' },
            arrayProp: ['item1', 'item2'],
        };

        it('should handle different data types correctly', () => {
            const result = getUniqueValuePropertyNames(obj);

            expect(result).toEqual([
                'stringProp',
                'numberProp',
                'booleanProp',
                'objectProp',
                'arrayProp',
            ]);
        });
    });
});

describe('getComposedFindingTargetIdForApMonitorTask', () => {
    const baseInstance: ApiData = {
        id: 'resource-123',
        name: 'test-resource',
        accountId: '************',
        region: 'us-east-1',
        dimension: 'test-dimension',
        resourceArn: 'arn:aws:s3:::test-bucket',
    } as ApiData;

    describe('Given unique properties includes dimension', () => {
        const uniqueProperties = ['dimension', 'accountId', 'region'];

        it('should use dimension as id and return region-based format', () => {
            const result = getComposedFindingTargetIdForApMonitorTask(
                baseInstance,
                uniqueProperties,
            );

            expect(result).toBe('************::us-east-1/test-dimension');
        });

        describe('Given dimension is null', () => {
            const instanceWithNullDimension = { ...baseInstance, dimension: undefined } as ApiData;

            it('should fallback to original id and return region-based format', () => {
                const result = getComposedFindingTargetIdForApMonitorTask(
                    instanceWithNullDimension,
                    uniqueProperties,
                );

                expect(result).toBe('************::us-east-1/resource-123');
            });
        });

        describe('Given dimension is undefined', () => {
            const instanceWithUndefinedDimension = {
                ...baseInstance,
                dimension: undefined,
            } as ApiData;

            it('should fallback to original id and return region-based format', () => {
                const result = getComposedFindingTargetIdForApMonitorTask(
                    instanceWithUndefinedDimension,
                    uniqueProperties,
                );

                expect(result).toBe('************::us-east-1/resource-123');
            });
        });
    });

    describe('Given unique properties includes region but not dimension', () => {
        const uniqueProperties = ['region', 'accountId'];

        it('should return region-based format with original id', () => {
            const result = getComposedFindingTargetIdForApMonitorTask(
                baseInstance,
                uniqueProperties,
            );

            expect(result).toBe('************::us-east-1/resource-123');
        });
    });

    describe('Given unique properties includes accountId but not region or dimension', () => {
        const uniqueProperties = ['accountId', 'name'];

        it('should return accountId-based format', () => {
            const result = getComposedFindingTargetIdForApMonitorTask(
                baseInstance,
                uniqueProperties,
            );

            expect(result).toBe('************::resource-123');
        });
    });

    describe('Given unique properties includes name but not accountId, region, or dimension', () => {
        const uniqueProperties = ['name'];

        describe('Given name is different from id', () => {
            it('should return name-based format', () => {
                const result = getComposedFindingTargetIdForApMonitorTask(
                    baseInstance,
                    uniqueProperties,
                );

                expect(result).toBe('resource-123::test-resource');
            });
        });

        describe('Given name is same as id', () => {
            const instanceWithSameName = { ...baseInstance, name: 'resource-123' } as ApiData;

            it('should continue to next condition (resourceArn)', () => {
                const uniquePropertiesWithArn = ['name', 'resourceArn'];
                const result = getComposedFindingTargetIdForApMonitorTask(
                    instanceWithSameName,
                    uniquePropertiesWithArn,
                );

                expect(result).toBe('arn:aws:s3:::test-bucket');
            });
        });
    });

    describe('Given unique properties includes resourceArn', () => {
        const uniqueProperties = ['resourceArn'];

        it('should return the resourceArn', () => {
            const result = getComposedFindingTargetIdForApMonitorTask(
                baseInstance,
                uniqueProperties,
            );

            expect(result).toBe('arn:aws:s3:::test-bucket');
        });
    });

    describe('Given unique properties includes only id', () => {
        const uniqueProperties = ['id'];

        it('should return the id', () => {
            const result = getComposedFindingTargetIdForApMonitorTask(
                baseInstance,
                uniqueProperties,
            );

            expect(result).toBe('resource-123');
        });
    });

    describe('Given unique properties includes none of the special properties', () => {
        const uniqueProperties = ['someOtherProperty'];

        it('should return empty string', () => {
            const result = getComposedFindingTargetIdForApMonitorTask(
                baseInstance,
                uniqueProperties,
            );

            expect(result).toBe('');
        });
    });

    describe('Given empty unique properties array', () => {
        const uniqueProperties: string[] = [];

        it('should return empty string', () => {
            const result = getComposedFindingTargetIdForApMonitorTask(
                baseInstance,
                uniqueProperties,
            );

            expect(result).toBe('');
        });
    });

    describe('Given multiple conditions match', () => {
        describe('Given region takes precedence over accountId', () => {
            const uniqueProperties = ['region', 'accountId'];

            it('should return region-based format', () => {
                const result = getComposedFindingTargetIdForApMonitorTask(
                    baseInstance,
                    uniqueProperties,
                );

                expect(result).toBe('************::us-east-1/resource-123');
            });
        });

        describe('Given accountId takes precedence over name', () => {
            const uniqueProperties = ['accountId', 'name'];

            it('should return accountId-based format', () => {
                const result = getComposedFindingTargetIdForApMonitorTask(
                    baseInstance,
                    uniqueProperties,
                );

                expect(result).toBe('************::resource-123');
            });
        });

        describe('Given name takes precedence over resourceArn', () => {
            const uniqueProperties = ['name', 'resourceArn'];

            it('should return name-based format', () => {
                const result = getComposedFindingTargetIdForApMonitorTask(
                    baseInstance,
                    uniqueProperties,
                );

                expect(result).toBe('resource-123::test-resource');
            });
        });

        describe('Given resourceArn takes precedence over id', () => {
            const uniqueProperties = ['resourceArn', 'id'];

            it('should return resourceArn', () => {
                const result = getComposedFindingTargetIdForApMonitorTask(
                    baseInstance,
                    uniqueProperties,
                );

                expect(result).toBe('arn:aws:s3:::test-bucket');
            });
        });
    });
});

describe('isValidJson', () => {
    describe('Given a valid JSON string', () => {
        it('should return true for valid object JSON', () => {
            const validJson = '{"key": "value", "number": 123}';
            const result = isValidJson(validJson);

            expect(result).toBe(true);
        });

        it('should return true for valid array JSON', () => {
            const validJson = '["item1", "item2", 123]';
            const result = isValidJson(validJson);

            expect(result).toBe(true);
        });

        it('should return true for empty object', () => {
            const validJson = '{}';
            const result = isValidJson(validJson);

            expect(result).toBe(true);
        });

        it('should return true for empty array', () => {
            const validJson = '[]';
            const result = isValidJson(validJson);

            expect(result).toBe(true);
        });
    });

    describe('Given an invalid JSON string', () => {
        it('should return false for malformed JSON', () => {
            const invalidJson = '{"key": "value"';
            const result = isValidJson(invalidJson);

            expect(result).toBe(false);
        });

        it('should return false for JSON with trailing comma', () => {
            const invalidJson = '{"key": "value",}';
            const result = isValidJson(invalidJson);

            expect(result).toBe(false);
        });

        it('should return false for single quotes instead of double quotes', () => {
            const invalidJson = "{'key': 'value'}";
            const result = isValidJson(invalidJson);

            expect(result).toBe(false);
        });
    });

    describe('Given non-string input', () => {
        it('should return false for number input', () => {
            const result = isValidJson(123 as any);

            expect(result).toBe(false);
        });

        it('should return false for object input', () => {
            const result = isValidJson({ key: 'value' } as any);

            expect(result).toBe(false);
        });

        it('should return false for null input', () => {
            const result = isValidJson(null as any);

            expect(result).toBe(false);
        });

        it('should return false for undefined input', () => {
            const result = isValidJson(undefined as any);

            expect(result).toBe(false);
        });
    });

    describe('Given primitive JSON values', () => {
        it('should return false for string primitive', () => {
            const result = isValidJson('"just a string"');

            expect(result).toBe(false);
        });

        it('should return false for number primitive', () => {
            const result = isValidJson('123');

            expect(result).toBe(false);
        });

        it('should return false for boolean primitive', () => {
            const result = isValidJson('true');

            expect(result).toBe(false);
        });

        it('should return false for null primitive', () => {
            const result = isValidJson('null');

            expect(result).toBe(false);
        });
    });
});

describe('isSubset', () => {
    describe('Given valid JSON strings', () => {
        it('should return true when subset properties are contained in superset', () => {
            const subset = '{"name": "test", "id": "123"}';
            const superset =
                '{"name": "test", "id": "123", "region": "us-east-1", "accountId": "456"}';

            const result = isSubset(subset, superset);

            expect(result).toBe(true);
        });

        it('should return true when subset is identical to superset', () => {
            const subset = '{"name": "test", "id": "123"}';
            const superset = '{"name": "test", "id": "123"}';

            const result = isSubset(subset, superset);

            expect(result).toBe(true);
        });

        it('should return true when subset is empty object', () => {
            const subset = '{}';
            const superset = '{"name": "test", "id": "123"}';

            const result = isSubset(subset, superset);

            expect(result).toBe(true);
        });

        it('should return false when subset has properties not in superset', () => {
            const subset = '{"name": "test", "id": "123", "extra": "value"}';
            const superset = '{"name": "test", "id": "123"}';

            const result = isSubset(subset, superset);

            expect(result).toBe(false);
        });

        it('should return false when subset has different values for same properties', () => {
            const subset = '{"name": "test", "id": "456"}';
            const superset = '{"name": "test", "id": "123"}';

            const result = isSubset(subset, superset);

            expect(result).toBe(false);
        });
    });

    describe('Given invalid JSON strings', () => {
        it('should return false when subset is invalid JSON', () => {
            const subset = '{"name": "test"';
            const superset = '{"name": "test", "id": "123"}';

            const result = isSubset(subset, superset);

            expect(result).toBe(false);
        });

        it('should return false when superset is invalid JSON', () => {
            const subset = '{"name": "test"}';
            const superset = '{"name": "test", "id": "123"';

            const result = isSubset(subset, superset);

            expect(result).toBe(false);
        });

        it('should return false when both are invalid JSON', () => {
            const subset = '{"name": "test"';
            const superset = '{"name": "test", "id": "123"';

            const result = isSubset(subset, superset);

            expect(result).toBe(false);
        });
    });

    describe('Given edge cases', () => {
        it('should handle null values correctly', () => {
            const subset = '{"value": null}';
            const superset = '{"value": null, "other": "test"}';

            const result = isSubset(subset, superset);

            expect(result).toBe(true);
        });

        it('should return false when subset has null but superset has different value', () => {
            const subset = '{"value": null}';
            const superset = '{"value": "not-null", "other": "test"}';

            const result = isSubset(subset, superset);

            expect(result).toBe(false);
        });

        it('should handle boolean values correctly', () => {
            const subset = '{"enabled": false}';
            const superset = '{"enabled": false, "name": "test"}';

            const result = isSubset(subset, superset);

            expect(result).toBe(true);
        });

        it('should handle number values correctly', () => {
            const subset = '{"count": 0}';
            const superset = '{"count": 0, "name": "test"}';

            const result = isSubset(subset, superset);

            expect(result).toBe(true);
        });
    });
});
