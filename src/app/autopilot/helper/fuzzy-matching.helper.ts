import { ApiData } from 'app/apis/classes/api/api-data.class';
import { MonitorData } from 'app/autopilot/classes/monitor-data.class';
import {
    exclusionTargetHasWrongAccountId,
    hasSameId,
    resourceHasRegion,
    resourceIdHasRegion,
} from 'app/autopilot/helper/task-result-exclusions.helper';
import { ConnectionEntity } from 'app/companies/connections/entities/connection.entity';
import { MonitorInstanceExclusion as Exclusion } from 'app/monitors/entities/monitor-instance-exclusion.entity';
import { MonitorInstance } from 'app/monitors/entities/monitor-instance.entity';
import { isNil } from 'lodash';

export function resultExclusionFuzzyMatch<T extends MonitorData>(
    failure: T,
    exclusion: Exclusion,
): boolean {
    let composedId: string;

    // simple match between exclusion targetId and failure resource id
    if (hasSameId(exclusion.targetId.toLowerCase(), failure.id.toLowerCase())) {
        return true;
    }

    // all failing resources (AWS) should have an accountId
    if (isNil(failure.accountId)) {
        return false;
    }

    // case 1: check for accountId property
    if (
        (typeof failure.region === 'string' && resourceIdHasRegion(failure.id, failure.region)) ||
        !resourceHasRegion(failure.region)
    ) {
        composedId = `${failure.accountId}::${failure.id}`;

        if (hasSameId(exclusion.targetId.toLowerCase(), composedId.toLowerCase())) {
            return true;
        }
    }

    // case 2:  check for region and accountid properties
    if (resourceHasRegion(failure.region)) {
        composedId = `${failure.accountId}::${failure.region}/${failure.id}`;

        if (hasSameId(exclusion.targetId.toLowerCase(), composedId.toLowerCase())) {
            return true;
        }
    }

    // case 3: exclusion target id has a different account id - target id with no account matches failure id
    if (exclusionTargetHasWrongAccountId(exclusion.targetId, failure.accountId)) {
        // gets the rest of the string from targetId without account id
        const noAccountTargetId = exclusion.targetId.split('::')[1];

        if (hasSameId(noAccountTargetId.toLowerCase(), failure.id.toLowerCase())) {
            return true;
        }
    }

    // case 4: region exists but not as part of the failure id, check for account id property
    composedId = `${failure.accountId}::${failure.id}`;

    if (composedId.toLowerCase() === exclusion.targetId.toLowerCase()) {
        return true;
    }

    // NEW: Check if exclusion.targetId matches failure.findingTargetId moving it last for backward compatibility
    // This handles cases where exclusions were stored with new findingTargetId format
    if (
        failure.findingTargetId &&
        hasSameId(exclusion.targetId.toLowerCase(), failure.findingTargetId.toLowerCase())
    ) {
        return true;
    }

    // extra case: subsets contained on failure resource data
    const targetId: string = exclusion.targetId.replace(`${failure.accountId}::`, '');

    if (!isValidJson(targetId) || !isValidJson(failure.id)) {
        return false;
    }

    // check if targetId json properties are contained in failure.id --> in cases where we add more properties to failure.id
    if (isSubset(targetId, failure.id)) {
        return true;
    }

    // check if failure.id json properties are contained in targetId --> in cases where we remove properties from failure.id
    if (isSubset(failure.id, targetId)) {
        return true;
    }

    return false;
}

export function isSubset(subsetJson: string, supersetJson: string): boolean {
    try {
        const subset = JSON.parse(subsetJson);
        const superset = JSON.parse(supersetJson);

        return Object.entries(subset).every(
            ([key, value]) => superset.hasOwnProperty(key) && superset[key] === value,
        );
    } catch (error) {
        return false;
    }
}

export function isValidJson(value: string): boolean {
    if (typeof value !== 'string') {
        return false;
    }

    try {
        const parsed = JSON.parse(value);
        return typeof parsed === 'object' && parsed !== null;
    } catch {
        return false;
    }
}

export function getUniqueValuePropertyNames(obj: Record<string, any>): string[] {
    const properties = Object.keys(obj);
    const values = Object.values(obj);
    const uniquePropertyNames: string[] = [];

    // Count occurrences of each value
    const valueCounts = new Map();
    values.forEach(value => {
        if (!isNil(value)) {
            valueCounts.set(value, (valueCounts.get(value) || 0) + 1);
        }
    });

    // Only include properties whose values appear exactly once
    values.forEach((value, index) => {
        if (!isNil(value) && valueCounts.get(value) === 1) {
            uniquePropertyNames.push(properties[index]);
        }
    });

    return uniquePropertyNames;
}

export function getComposedFindingTargetIdForApMonitorTask(
    instance: ApiData,
    uniqueProperties: string[],
): string {
    let id = instance.id;

    // use dimension if it exists and is unique
    // review getIdAndName function from autopilot-infrastructure-monitor.task.ts
    if (!isNil(instance.dimension) && uniqueProperties.includes('dimension')) {
        id = instance.dimension ?? id;
    }

    // use this logic to match the fuzzy match logic from resultExclusionFuzzyMatch
    if (
        !isNil(instance.region) &&
        uniqueProperties.includes('region') &&
        !isNil(instance.accountId) &&
        uniqueProperties.includes('accountId')
    ) {
        return `${instance.accountId}::${instance.region}/${id}`;
    }

    if (!isNil(instance.accountId) && uniqueProperties.includes('accountId')) {
        return `${instance.accountId}::${instance.id}`;
    }

    if (
        !isNil(instance.name) &&
        uniqueProperties.includes('name') &&
        instance.name !== instance.id
    ) {
        return `${instance.id}::${instance.name}`;
    }

    if (!isNil(instance.resourceArn) && uniqueProperties.includes('resourceArn')) {
        return `${instance.resourceArn}`;
    }

    if (!isNil(instance.id) && uniqueProperties.includes('id')) {
        return `${instance.id}`;
    }

    return '';
}

export function getFindingTargetId(
    monitorInstances: MonitorInstance[],
    connection: ConnectionEntity | null,
    originalTargetId: string,
): string {
    if (!monitorInstances?.length || !originalTargetId) {
        return originalTargetId;
    }

    for (const monitorInstance of monitorInstances) {
        const metadata = monitorInstance.getMetadata();
        for (const [, metadataItem] of metadata) {
            const { monitorResult } = metadataItem;
            if (
                monitorResult.connectionId &&
                connection?.id &&
                monitorResult.connectionId !== connection.id
            ) {
                continue;
            }

            const failItems = monitorResult.fail || [];
            const matchedFailItem = failItems.find(
                failItem =>
                    failItem.id === originalTargetId ||
                    failItem.name === originalTargetId ||
                    failItem.findingTargetId === originalTargetId,
            );

            if (matchedFailItem) {
                if (matchedFailItem.findingTargetId?.includes('undefined::')) {
                    return originalTargetId;
                }
                return matchedFailItem.findingTargetId || originalTargetId;
            }
        }
    }

    return originalTargetId;
}
