import { <PERSON>rror<PERSON><PERSON>, FrameworkTag } from '@drata/enums';
import { ProductFramework } from 'app/companies/products/entities/product-framework.entity';
import { FrameworksRequestDto } from 'app/frameworks/dtos/frameworks-request.dto';
import { FrameworkIsReadyIndexView } from 'app/frameworks/entities/framework-is-ready-index-view.entity';
import { Framework } from 'app/frameworks/entities/framework.entity';
import { FrameworkReady } from 'app/frameworks/types/framework-ready.type';
import { Account } from 'auth/entities/account.entity';
import { NotFoundException } from 'commons/exceptions/not-found.exception';
import { fqtn, like } from 'commons/helpers/database.helper';
import { getSkip } from 'commons/helpers/pagination.helper';
import { getProductId } from 'commons/helpers/products.helper';
import { BaseRepository } from 'commons/repositories/base.repository';
import { JoinTypes, WhereOptions } from 'commons/repositories/query.builder';
import { PaginationType } from 'commons/types/pagination.type';
import { CustomRepository } from 'database/typeorm/typeorm.extensions.decorator';
import { isEmpty, isNil, pick, toNumber } from 'lodash';
import { Brackets, In, IsNull, Not, SelectQueryBuilder } from 'typeorm';

@CustomRepository(Framework)
export class FrameworkRepository extends BaseRepository<Framework> {
    /**
     * Get all the frameworks, regardless if they are enabled
     * for the product in question.
     *
     * @param productId
     * @returns
     */
    async getFrameworksByProduct(productId: number): Promise<Framework[]> {
        return this.createQueryBuilder('Framework')
            .innerJoin('Framework.products', 'Product')
            .where('Product.productId = :productId', { productId })
            .getMany();
    }
    /**
     *
     * @param productId
     * @returns
     */
    async getEnabledFrameworksByProductId(productId: number): Promise<Framework[]> {
        return this.createQueryBuilder('Framework')
            .innerJoin('Framework.products', 'Product')
            .where('Product.enabledAt IS NOT NULL')
            .andWhere('Framework.enabledAt IS NOT NULL')
            .andWhere('Product.productId = :productId', { productId })
            .getMany();
    }

    /**
     *
     * @returns Framework[]
     */
    async getAllFrameworksWithControls(): Promise<Framework[]> {
        return this.createQueryBuilder('Framework')
            .leftJoinAndSelect('Framework.requirementIndexes', 'RequirementIndexes')
            .leftJoinAndSelect('RequirementIndexes.requirement', 'Requirement')
            .leftJoinAndSelect('Requirement.controls', 'Controls')
            .leftJoinAndMapOne(
                'Framework.frameworkIsReady',
                FrameworkIsReadyIndexView,
                'FrameworkIsReadyIndexView',
                'FrameworkIsReadyIndexView.fk_framework_id = Framework.id',
            )
            .orderBy('Framework.name', 'ASC')
            .getMany();
    }

    /**
     *
     * @param withDeleted
     * @returns
     */
    async getAllFrameworksWithControlsGroupByFrameworkId(
        withDeleted = false,
    ): Promise<Framework[]> {
        const query = this.createQueryBuilder('framework')
            .where('framework.enabledAt IS NOT NULL')
            .orderBy('framework.name', 'ASC')
            .innerJoinAndSelect('framework.requirementIndexes', 'requirementIndexes')
            .innerJoinAndSelect('requirementIndexes.requirement', 'requirement')
            .innerJoinAndSelect('requirement.controls', 'controls')
            .groupBy('framework.id');
        if (withDeleted) {
            query.withDeleted();
        }

        return query.getMany();
    }

    /**
     *
     * @param frameworkTag
     * @param productId
     * @param customFrameworkId
     * @returns
     */
    async getFrameworkWithControls(
        frameworkTag: FrameworkTag,
        productId: number,
        customFrameworkId?: string,
    ): Promise<Framework> {
        const query = this.createQueryBuilder('Framework')
            .leftJoinAndSelect('Framework.requirementIndexes', 'RequirementIndexes')
            .innerJoinAndSelect('RequirementIndexes.requirement', 'Requirement')
            .leftJoinAndSelect('Requirement.controls', 'Controls')
            .leftJoinAndSelect('Controls.controlTestInstances', 'ControlTestInstances')
            .leftJoinAndSelect('ControlTestInstances.monitorInstances', 'MonitorInstances')
            .innerJoin('Framework.products', 'Product')
            .where('Framework.enabledAt IS NOT NULL')
            .andWhere('Framework.tag = :frameworkTag', {
                frameworkTag,
            })
            .andWhere('Product.productId = :productId', { productId });

        if (frameworkTag === FrameworkTag.CUSTOM) {
            query.andWhere('custom_framework_id = :customFrameworkId', {
                customFrameworkId,
            });
        }

        return query.getOneOrFail();
    }

    /**
     *
     * @param frameworkTag
     * @param productId
     * @param customFrameworkId
     * @returns
     */
    async getFrameworkWithSpecificControls(
        frameworkTag: FrameworkTag,
        productId: number,
        controlIds: number[],
        customFrameworkId?: string,
    ): Promise<Framework> {
        const query = this.createQueryBuilder('Framework')
            .select([
                'Framework.id',
                'Controls.id',
                'Controls.code',
                'Controls.name',
                'Controls.archivedAt',
                'ControlTestInstances.id',
                'ControlTestInstances.testId',
                'MonitorInstances.autopilotTaskType',
                'RequirementIndexes.id',
                'Requirement.id',
                'Requirement.archivedAt',
                'Requirement.name',
                'Requirement.rationale',
            ])
            .leftJoin('Framework.requirementIndexes', 'RequirementIndexes')
            .innerJoin('RequirementIndexes.requirement', 'Requirement')
            .innerJoin('Requirement.controls', 'Controls', 'Controls.id IN (:...controlIds)', {
                controlIds,
            })
            .leftJoin('Controls.controlTestInstances', 'ControlTestInstances')
            .leftJoin('ControlTestInstances.monitorInstances', 'MonitorInstances')
            .innerJoin('Framework.products', 'Product')
            .where('Framework.enabledAt IS NOT NULL')
            .andWhere('Product.productId = :productId', { productId });

        if (frameworkTag === FrameworkTag.CUSTOM) {
            query.andWhere('custom_framework_id = :customFrameworkId', {
                customFrameworkId,
            });
        }

        return query.getOneOrFail();
    }

    async getEnabledFrameworks(): Promise<Framework[]> {
        return this.find({
            where: {
                enabledAt: Not(IsNull()),
            },
        });
    }

    /**
     *
     * @param frameworkTags
     * @returns
     */
    async areFrameworksEnabled(frameworkTags: FrameworkTag[]): Promise<boolean> {
        const enabledFrameworks = await this.findBy({
            tag: In(frameworkTags),
            enabledAt: Not(IsNull()),
        });

        return enabledFrameworks.length === frameworkTags.length;
    }

    /**
     *
     * @param frameworkTag
     * @returns
     */
    async isFrameworkEnabled(frameworkTag: FrameworkTag): Promise<boolean> {
        const enabledFrameworks = await this.findBy({
            tag: frameworkTag,
            enabledAt: Not(IsNull()),
        });

        return enabledFrameworks.length > 0;
    }

    private buildFrameworkReadyFromFramework(
        framework: Framework,
        productId?: number | null,
    ): FrameworkReady {
        // if using workspaces, the framework is enabled by the ProductMap
        // BUG: when disabling a framework from the site admin, the workspace enabled flag is not cleared
        // this checks both enabled at values to ensure we want the framework to be enabled.
        let enabledAt = framework.enabledAt;
        if (!isNil(productId)) {
            const productFrameworkEnabledAt = framework.products?.find(
                product => product.productId === productId,
            )?.enabledAt;
            if (!isNil(productFrameworkEnabledAt) && !isNil(framework.enabledAt)) {
                enabledAt = productFrameworkEnabledAt;
            } else {
                enabledAt = null;
            }
        }
        framework.enabledAt = enabledAt;
        const {
            frameworkIsReady: { numInScopeReadyRequirements, numInScopeRequirements, isReady } = {},
        } = framework;
        return {
            framework,
            numInScopeRequirements,
            numReadyInScopeRequirements: numInScopeReadyRequirements,
            isReady,
        };
    }

    /**
     *
     * @param account
     * @param id
     * @param slug
     * @param tag
     * @param searchParams
     * @returns
     */
    async getWithReady(account: Account, id: number): Promise<FrameworkReady> {
        const query = `
        WITH ready AS (
            SELECT
                fk_framework_id,
                num_in_scope_requirements,
                num_ready_in_scope_requirements,
                num_in_scope_controls,
                num_ready_in_scope_enabled_controls,
                is_ready
            FROM ${fqtn(account.databaseName, 'vw_framework_is_ready_index')}
        ),
        framework AS (
            SELECT *
            FROM ${fqtn(account.databaseName, 'framework')}
        ),
        frameworkProfile AS (
            SELECT pd.id, pd.oscal_id, pd.name, ps.fk_framework_id
            FROM ${fqtn(account.databaseName, 'profile_selection')}  ps
            INNER JOIN ${fqtn(
                account.databaseName,
                'profile_details',
            )}  pd ON ps.fk_profile_details_id = pd.id
            WHERE ps.selected = 1 AND pd.deleted_at IS NULL
        )
        SELECT
            framework.*,
            ready.num_in_scope_controls AS total_in_scope_controls,
            ready.is_ready,
            ready.num_in_scope_requirements,
            ready.num_ready_in_scope_requirements,
            ready.num_ready_in_scope_enabled_controls,
            frameworkProfile.oscal_id AS selected_profile_oscal_id
        FROM framework
        LEFT JOIN ready ON framework.id = ready.fk_framework_id
        LEFT JOIN frameworkProfile ON framework.id = frameworkProfile.fk_framework_id
        WHERE framework.id = ? AND framework.enabled_at IS NOT NULL`;
        const values = [];
        values.push(id);
        const queryResults = await this.query(query, values);

        if (isEmpty(queryResults)) {
            throw new NotFoundException(ErrorCode.FRAMEWORK_NOT_FOUND);
        }

        const framework = queryResults[0];

        const frameworkIsReady = new FrameworkIsReadyIndexView();
        frameworkIsReady.frameworkId = toNumber(framework['id']);
        frameworkIsReady.numInScopeReadyRequirements = toNumber(
            framework['num_ready_in_scope_requirements'],
        );
        frameworkIsReady.numInScopeRequirements = toNumber(framework['num_in_scope_requirements']);
        frameworkIsReady.numInScopeControls = toNumber(framework['total_in_scope_controls']);
        frameworkIsReady.numReadyInScopeControls = toNumber(
            framework['num_ready_in_scope_enabled_controls'],
        );
        frameworkIsReady.isReady = framework['is_ready'] === '1';

        const frame = new Framework();
        frame.id = toNumber(framework['id']);
        frame.name = framework['name'];
        frame.description = framework['description'];
        frame.longDescription = framework['long_description'];
        frame.slug = framework['slug'];
        frame.tag = framework['tag'];
        frame.pill = framework['pill'];
        frame.frameworkIsReady = frameworkIsReady;
        frame.enabledAt = framework['enabled_at'];
        frame.controlsEnabledAt = framework['controls_enabled_at'];
        frame.deletedAt = framework['deleted_at'];
        frame.createdAt = framework['created_at'];
        frame.updatedAt = framework['updated_at'];
        frame.selectedLevel = framework['selected_level'];
        frame.hasLevel = parseInt(framework['has_level']) === 1;
        frame.levelLabel = framework['level_label'];
        frame.privacy = framework['privacy'];
        frame.externalId = framework['external_id'];
        return {
            framework: frame,
            numInScopeRequirements: parseInt(framework['num_in_scope_requirements']),
            numReadyInScopeRequirements: parseInt(framework['num_ready_in_scope_requirements']),
            isReady: framework['is_ready'] === '1',
            profileOscalId: framework['selected_profile_oscal_id'],
        };
    }

    async getWithReadyBySlug(
        account: Account,
        workspaceFrameworkIds: number[],
        slug: string,
    ): Promise<FrameworkReady> {
        /**
         * NOTE: this is the same query as `getWithReady` , however
         * it filters by slug instead of framework id. this duplication is intentional so we can have separate endpoints and get data more efficiently
         * on the UI depending on what information we have on the client
         */
        const query = `
        WITH ready AS (
            SELECT
                fk_framework_id,
                num_in_scope_requirements,
                num_ready_in_scope_requirements,
                num_in_scope_controls,
                num_ready_in_scope_enabled_controls,
                is_ready
            FROM ${fqtn(account.databaseName, 'vw_framework_is_ready_index')}
        ),
        framework AS (
            SELECT *
            FROM ${fqtn(account.databaseName, 'framework')}
        ),
        frameworkProfile AS (
            SELECT pd.id, pd.oscal_id, pd.name, ps.fk_framework_id
            FROM ${fqtn(account.databaseName, 'profile_selection')}  ps
            INNER JOIN ${fqtn(
                account.databaseName,
                'profile_details',
            )}  pd ON ps.fk_profile_details_id = pd.id
            WHERE ps.selected = 1 AND pd.deleted_at IS NULL
        )
        SELECT
            framework.*,
            ready.num_in_scope_controls AS total_in_scope_controls,
            ready.is_ready,
            ready.num_in_scope_requirements,
            ready.num_ready_in_scope_requirements,
            ready.num_ready_in_scope_enabled_controls,
            frameworkProfile.oscal_id AS selected_profile_oscal_id
        FROM framework
        LEFT JOIN ready ON framework.id = ready.fk_framework_id
        LEFT JOIN frameworkProfile ON framework.id = frameworkProfile.fk_framework_id
        WHERE framework.slug = ? AND framework.id IN(?) AND framework.enabled_at IS NOT NULL`;
        const queryResults = await this.query(query, [slug, workspaceFrameworkIds]);

        if (isEmpty(queryResults)) {
            throw new NotFoundException(ErrorCode.FRAMEWORK_NOT_FOUND);
        }

        const framework = queryResults[0];

        const frameworkIsReady = new FrameworkIsReadyIndexView();
        frameworkIsReady.frameworkId = toNumber(framework['id']);
        frameworkIsReady.numInScopeReadyRequirements = toNumber(
            framework['num_ready_in_scope_requirements'],
        );
        frameworkIsReady.numInScopeRequirements = toNumber(framework['num_in_scope_requirements']);
        frameworkIsReady.numInScopeControls = toNumber(framework['total_in_scope_controls']);
        frameworkIsReady.numReadyInScopeControls = toNumber(
            framework['num_ready_in_scope_enabled_controls'],
        );
        frameworkIsReady.isReady = framework['is_ready'] === '1';

        const frame = new Framework();
        frame.id = toNumber(framework['id']);
        frame.name = framework['name'];
        frame.description = framework['description'];
        frame.longDescription = framework['long_description'];
        frame.slug = framework['slug'];
        frame.tag = framework['tag'];
        frame.pill = framework['pill'];
        frame.frameworkIsReady = frameworkIsReady;
        frame.enabledAt = framework['enabled_at'];
        frame.controlsEnabledAt = framework['controls_enabled_at'];
        frame.deletedAt = framework['deleted_at'];
        frame.createdAt = framework['created_at'];
        frame.updatedAt = framework['updated_at'];
        frame.selectedLevel = framework['selected_level'];
        frame.hasLevel = parseInt(framework['has_level']) === 1;
        frame.levelLabel = framework['level_label'];
        frame.privacy = framework['privacy'];
        frame.externalId = framework['external_id'];
        return {
            framework: frame,
            numInScopeRequirements: parseInt(framework['num_in_scope_requirements']),
            numReadyInScopeRequirements: parseInt(framework['num_ready_in_scope_requirements']),
            isReady: framework['is_ready'] === '1',
            profileOscalId: framework['selected_profile_oscal_id'],
        };
    }

    async getFrameworkIdsByWorkspaceId(workspaceId: number): Promise<number[]> {
        return (
            await this.createQueryBuilder('Framework')
                .select('Framework.id')
                .innerJoin('Framework.products', 'Product')
                .where('Product.productId = :workspaceId', { workspaceId })
                .getMany()
        ).map(f => f.id);
    }

    async getFrameworkReadyByIds(
        productId: number | null,
        frameworkIds: number[],
    ): Promise<FrameworkReady[]> {
        if (isEmpty(frameworkIds)) {
            return [];
        }
        const query = this.createQueryBuilder('Framework')
            .leftJoinAndMapOne(
                'Framework.frameworkIsReady',
                FrameworkIsReadyIndexView,
                'FrameworkIsReadyIndexView',
                'FrameworkIsReadyIndexView.fk_framework_id = Framework.id',
            )
            .leftJoin('Framework.requirementIndexes', 'RequirementIndex')
            .where(
                new Brackets(qb => {
                    qb.where('RequirementIndex.framework.id = Framework.id').orWhere(
                        'Framework.customFrameworkId IS NOT NULL',
                    );
                }),
            )
            .andWhere('Framework.deletedAt IS NULL')
            .andWhere('Framework.id IN (:frameworkIds)', { frameworkIds });

        if (!isNil(productId)) {
            query
                .innerJoinAndSelect(
                    'Framework.products',
                    'ProductMap',
                    'ProductMap.fk_framework_id = Framework.id',
                )
                .andWhere('ProductMap.fk_product_id = :productId', {
                    productId,
                });
        }

        query.orderBy('Framework.name', 'ASC');

        const queryResults = await query.getMany();

        if (isEmpty(queryResults)) {
            throw new NotFoundException(ErrorCode.FRAMEWORK_NOT_FOUND);
        }

        const ret: FrameworkReady[] = [];

        for (const frame of queryResults) {
            ret.push(this.buildFrameworkReadyFromFramework(frame, productId));
        }

        return ret;
    }

    async searchFrameworks(productId: number | null, searchTerm: string): Promise<number[]> {
        const query = this.createQueryBuilder('Framework')
            .select('Framework.id')
            .leftJoin('Framework.requirementIndexes', 'RequirementIndex')
            .where(
                new Brackets(qb => {
                    qb.where('RequirementIndex.framework.id = Framework.id').orWhere(
                        'Framework.customFrameworkId IS NOT NULL',
                    );
                }),
            )
            .andWhere('Framework.name LIKE :searchTerm', {
                searchTerm: `%${searchTerm}%`,
            })
            .andWhere('Framework.deletedAt IS NULL');

        if (!isNil(productId)) {
            query
                .innerJoin(
                    'products_frameworks_map',
                    'ProductMap',
                    'ProductMap.fk_framework_id = Framework.id',
                )
                .andWhere('ProductMap.fk_product_id = :productId', {
                    productId,
                });
        }

        const results = await query.getMany();

        return results.map(r => r.id);
    }

    async filterEnabledFrameworks(productId: number | null, isEnabled: boolean): Promise<number[]> {
        const query = this.createQueryBuilder('Framework')
            .select('Framework.id')
            .leftJoin('Framework.requirementIndexes', 'RequirementIndex')
            .where(
                new Brackets(qb => {
                    qb.where('RequirementIndex.framework.id = Framework.id').orWhere(
                        'Framework.customFrameworkId IS NOT NULL',
                    );
                }),
            )
            .andWhere('Framework.deletedAt IS NULL');

        if (isEnabled) {
            query.where('Framework.enabledAt IS NOT NULL');
        } else {
            query.where('Framework.enabledAt IS NULL');
        }

        if (!isNil(productId)) {
            query
                .innerJoin(
                    'products_frameworks_map',
                    'ProductMap',
                    'ProductMap.fk_framework_id = Framework.id',
                )
                .andWhere('ProductMap.fk_product_id = :productId', {
                    productId,
                });

            if (isEnabled) {
                query.andWhere('ProductMap.enabledAt IS NOT NULL');
            }
        }

        const results = await query.getMany();

        return results.map(r => r.id);
    }

    async getPage(
        productId: number | null,
        includeFrameworkIds: number[],
        excludeFrameworkIds: number[],
        page: number,
        limit: number,
        getAll: boolean,
    ): Promise<PaginationType<number>> {
        const query = this.createQueryBuilder('Framework').where('Framework.deletedAt IS NULL');

        const skip = getSkip(page, limit);
        if (!getAll) {
            query.take(limit);
            query.skip(skip);
        }

        if (!isNil(productId)) {
            query
                .innerJoin(
                    'products_frameworks_map',
                    'ProductMap',
                    'ProductMap.fk_framework_id = Framework.id',
                )
                .andWhere('ProductMap.fk_product_id = :productId', {
                    productId,
                });
        }

        if (!isEmpty(includeFrameworkIds)) {
            query.andWhere('Framework.id IN (:...includeFrameworkIds)', {
                includeFrameworkIds,
            });
        }

        if (!isEmpty(excludeFrameworkIds)) {
            query.andWhere('Framework.id NOT IN (:...excludeFrameworkIds)', {
                excludeframeworkIds: excludeFrameworkIds,
            });
        }

        this.resolveSort(query);

        const [results, total] = await query.getManyAndCount();

        return {
            data: results.map(r => r.id),
            page: page,
            limit: limit,
            total: total,
        };
    }

    async getEnabledFrameworkByTag(
        frameworkTag: FrameworkTag,
        productId: number | null | undefined,
        customFrameworkId?: string | null,
    ): Promise<Framework | null> {
        const query = this.createQueryBuilder('Framework')
            .where('Framework.enabledAt IS NOT NULL')
            .andWhere('Framework.tag = :frameworkTag', {
                frameworkTag,
            });

        if (!isNil(productId)) {
            query
                .innerJoin('Framework.products', 'Product')
                .andWhere('Product.productId = :productId', { productId });
        }
        if (frameworkTag === FrameworkTag.CUSTOM) {
            query.andWhere('custom_framework_id = :customFrameworkId', {
                customFrameworkId,
            });
        }

        return query.getOne();
    }
    async getEnabledFrameworksByTags(
        frameworkTag: FrameworkTag,
        productId: number | null,
        customFrameworkIds: string[],
    ): Promise<Framework[]> {
        const query = this.createQueryBuilder('Framework')
            .where('Framework.enabledAt IS NOT NULL')
            .andWhere('Framework.tag = :frameworkTag', {
                frameworkTag,
            });

        if (!isNil(productId)) {
            query
                .innerJoin('Framework.products', 'Product')
                .andWhere('Product.productId = :productId', { productId });
        }

        if (frameworkTag === FrameworkTag.CUSTOM && !isEmpty(customFrameworkIds)) {
            query.andWhere('custom_framework_id IN  (:...ids)', { ids: customFrameworkIds });
        }
        return query.getMany();
    }

    async getCustomFrameworkById(frameworkId: number): Promise<Framework> {
        return this.findOneOrFail({
            where: { id: frameworkId, tag: FrameworkTag.CUSTOM },
            order: { name: 'ASC' },
            join: {
                alias: 'framework',
                leftJoinAndSelect: {
                    requirementIndexes: 'framework.requirementIndexes',
                    requirement: 'requirementIndexes.requirement',
                    customCategory: 'requirementIndexes.customCategory',
                    controls: 'requirement.controls',
                },
            },
        });
    }

    async getEnabledCustomFrameworks(): Promise<Framework[]> {
        return this.createQueryBuilder('Framework')
            .select(['Framework.id', 'Framework.enabled_at', 'Framework.tag'])
            .innerJoin('Framework.products', 'ProductFramework')
            .where('Framework.tag = :customFrameworkTag', {
                customFrameworkTag: FrameworkTag.CUSTOM,
            })
            .andWhere('Framework.enabledAt IS NOT NULL')
            .andWhere('ProductFramework.enabledAt IS NOT NULL')
            .getMany();
    }

    async getEnabledCustomFrameworksByWorkspaceId(workspaceId: number): Promise<Framework[]> {
        return this.createQueryBuilder('Framework')
            .innerJoin('Framework.products', 'ProductFramework')
            .where('Framework.tag = :customFrameworkTag', {
                customFrameworkTag: FrameworkTag.CUSTOM,
            })
            .andWhere('Framework.enabledAt IS NOT NULL')
            .andWhere('ProductFramework.enabledAt IS NOT NULL')
            .andWhere('ProductFramework.productId = :productId', { productId: workspaceId })
            .getMany();
    }

    async getEnabledCustomFrameworksByProductsCount(productsIds?: number[]): Promise<number> {
        const query = this.createQueryBuilder('Framework')
            .select(['Framework.id', 'Framework.enabled_at', 'Framework.tag'])
            .innerJoin('Framework.products', 'ProductFramework')
            .where('Framework.tag = :customFrameworkTag', {
                customFrameworkTag: FrameworkTag.CUSTOM,
            })
            .andWhere('Framework.enabledAt IS NOT NULL')
            .andWhere('ProductFramework.enabledAt IS NOT NULL');

        if (!isEmpty(productsIds)) {
            query.andWhere('ProductFramework.productId IN (:...productsIds)', { productsIds });
        }

        return query.getCount();
    }

    async disableCustomFrameworksByIds(customFrameworkIds: number[]): Promise<void> {
        await this.createQueryBuilder('Framework')
            .update()
            .set({ enabledAt: null })
            .whereInIds(customFrameworkIds)
            .execute();
    }

    async getCustomFrameworkNames(): Promise<string[]> {
        const customFrameworks = await this.find({
            select: { name: true },
            where: { tag: FrameworkTag.CUSTOM },
        });

        return customFrameworks.map(customFramework => customFramework.name);
    }

    async listCustomFrameworks(
        account: Account,
        dto: FrameworksRequestDto,
    ): Promise<PaginationType<Framework>> {
        const { page, limit } = dto;

        const query = this.buildQuery({
            mainAlias: 'Framework',
            relations: {
                products: [JoinTypes.innerJoinAndSelect, { framework: false }],
            },
            withRelationsOptimization: true,
            where: this.resolveCustomFrameworkFilters(account, dto),
            take: limit,
            skip: getSkip(page, limit),
        });

        const [data, total] = await query.getManyAndCount();

        return {
            data,
            page,
            limit,
            total,
        };
    }

    async getFrameworksByFrameworksIds(ids: number[]): Promise<Framework[]> {
        return this.findWithQuery({
            where: {
                id: { in$: ids },
            },
        });
    }

    async getFrameworkSlugs(): Promise<string[]> {
        const data = await this.createQueryBuilder('Framework')
            .select('Framework.slug', 'slug')
            .withDeleted()
            .execute();

        return data?.map(result => result.slug) ?? [];
    }

    async getEnabledFrameworksSimple(): Promise<Framework[]> {
        return this.createQueryBuilder('Framework')
            .select(['Framework.id', 'Framework.name', 'Framework.slug'])
            .where('Framework.enabled_at IS NOT NULL')
            .getMany();
    }

    async getWorkspaceEnabledFrameworks(): Promise<Framework[]> {
        return this.createQueryBuilder('Framework')
            .select(['Framework.id', 'Framework.name', 'Framework.slug'])
            .leftJoin(
                ProductFramework,
                'products_frameworks_map',
                'Framework.id = products_frameworks_map.fk_framework_id',
            )
            .where('products_frameworks_map.enabled_at IS NOT NULL')
            .getMany();
    }

    async getFrameworksByControlIds(controlIds: number[]): Promise<Framework[]> {
        return this.createQueryBuilder('Framework')
            .select([
                'Framework.id',
                'Framework.name',
                'Framework.pill',
                'Framework.color',
                'Framework.bgColor',
                'Framework.slug',
            ])
            .innerJoin('Framework.requirementIndexes', 'RequirementIndex')
            .innerJoin('RequirementIndex.requirement', 'Requirement')
            .innerJoin('Requirement.controls', 'Control')
            .where('Control.id IN (:...controlIds)', { controlIds })
            .andWhere('Framework.enabledAt IS NOT NULL')
            .getMany();
    }
    async getFrameworksForNewAudit(productId: number, searchTerm?: string): Promise<Framework[]> {
        const query = this.createQueryBuilder('Framework')
            .select([
                'Framework',
                'AuditorFrameworkTypes',
                'FrameworkProfileSelections',
                'FrameworkProfileDetails',
            ])
            .innerJoin(
                'products_frameworks_map',
                'ProductMap',
                'ProductMap.fk_framework_id = Framework.id',
            )
            .leftJoin('Framework.auditorFrameworkTypes', 'AuditorFrameworkTypes')
            .leftJoin('Framework.profileSelections', 'FrameworkProfileSelections')
            .leftJoin('FrameworkProfileSelections.profileDetails', 'FrameworkProfileDetails')
            .andWhere('ProductMap.fk_product_id = :productId', {
                productId,
            })
            .andWhere('Framework.enabledAt IS NOT NULL')
            .andWhere('ProductMap.enabledAt IS NOT NULL');

        if (searchTerm) {
            query.andWhere('Framework.name LIKE :searchTerm', {
                searchTerm: `%${searchTerm}%`,
            });
        }

        return query.getMany();
    }

    async getFrameworkByIdWithProfileRequirements(frameworkId: number): Promise<Framework> {
        return this.createQueryBuilder('Framework')
            .innerJoin('Framework.products', 'Product')
            .innerJoin('Framework.requirementIndexes', 'RequirementIndex')
            .innerJoin('RequirementIndex.requirement', 'Requirement')
            .leftJoin('Requirement.controls', 'Control')
            .select([
                'Framework.id',
                'Framework.tag',
                'RequirementIndex.id',
                'Requirement.id',
                'Requirement.externalId',
                'Requirement.name',
                'Requirement.description',
                'Requirement.sortOrder',
                'Control.id',
                'Control.code',
                'Product.id',
                'Product.productId',
            ])
            .where('Framework.id = :frameworkId', { frameworkId })
            .getOne();
    }

    async getEnabledFrameworksOrderedByName(): Promise<Framework[]> {
        return this.createQueryBuilder('Framework')
            .innerJoinAndMapOne(
                'Framework.frameworkIsReady',
                FrameworkIsReadyIndexView,
                'FrameworkIsReadyIndexView',
                'FrameworkIsReadyIndexView.fk_framework_id = Framework.id',
            )
            .leftJoin('Framework.requirementIndexes', 'RequirementIndex')
            .where(
                new Brackets(qb => {
                    qb.where('RequirementIndex.framework.id = Framework.id').orWhere(
                        'Framework.customFrameworkId IS NOT NULL',
                    );
                }),
            )
            .andWhere('Framework.enabled_at IS NOT NULL')
            .orderBy('Framework.name', 'ASC')
            .getMany();
    }

    async getFrameworksWithPositiveInScopeControlCounts(
        productFrameworkIds: number[],
    ): Promise<Framework[]> {
        return this.createQueryBuilder('Framework')
            .leftJoinAndSelect('Framework.requirementIndexes', 'RequirementIndexes')
            .leftJoin('RequirementIndexes.requirement', 'Requirement')
            .leftJoinAndMapOne(
                'Framework.frameworkIsReady',
                FrameworkIsReadyIndexView,
                'FrameworkIsReadyIndex',
                'FrameworkIsReadyIndex.fk_framework_id = Framework.id',
            )
            .addSelect(['Requirement.id'])
            .where('Framework.enabled_at IS NOT NULL')
            .andWhere('FrameworkIsReadyIndex.num_in_scope_controls > 0')
            .andWhere('Framework.id IN (:...productFrameworkIds)', {
                productFrameworkIds,
            })
            .getMany();
    }

    async getFrameworksWithControlsById(frameworkIds: number[]): Promise<Framework[]> {
        return this.createQueryBuilder('Framework')
            .select(['Framework.id'])
            .innerJoin('Framework.requirementIndexes', 'RequirementIndexes')
            .innerJoin('RequirementIndexes.requirement', 'Requirement')
            .innerJoin('Requirement.controls', 'Controls')
            .where('Framework.id IN (:...frameworkIds)', {
                frameworkIds,
            })
            .getMany();
    }

    async getFrameworkWithControlsByTagAndWorkspace(
        frameworkTag: FrameworkTag,
        workspaceId: number,
        customFrameworkId: string | null,
    ): Promise<Framework | null> {
        const query = this.createQueryBuilder('Framework')
            .select(['Framework.id'])
            .innerJoin('Framework.requirementIndexes', 'RequirementIndexes')
            .innerJoin('RequirementIndexes.requirement', 'Requirement')
            .innerJoin('Requirement.controls', 'Controls')
            .innerJoin('Framework.products', 'ProductFramework')
            .where('Framework.tag = :frameworkTag', {
                frameworkTag,
            })
            .andWhere('ProductFramework.productId = :workspaceId', { workspaceId });

        if (frameworkTag === FrameworkTag.CUSTOM) {
            query.andWhere('custom_framework_id = :customFrameworkId', {
                customFrameworkId,
            });
        }

        return query.getOne();
    }

    async getRequirementsOnlyFrameworksForWorkspace(productId: number): Promise<Framework[]> {
        return this.createQueryBuilder('Framework')
            .leftJoin('Framework.requirementIndexes', 'RequirementIndexes')
            .leftJoin('RequirementIndexes.requirement', 'Requirement')
            .leftJoin('Requirement.controls', 'Controls')
            .leftJoin('Framework.products', 'Product')
            .where('Product.productId = :workspaceId', { workspaceId: productId })
            .groupBy('Framework.id')
            .having('COUNT(Controls.id) = 0')
            .getMany();
    }

    getEnabledFrameworksReadyByProduct(productId: number): Promise<Framework[]> {
        return this.createQueryBuilder('Framework')
            .leftJoinAndMapOne(
                'Framework.frameworkIsReady',
                FrameworkIsReadyIndexView,
                'FrameworkIsReadyIndexView',
                'FrameworkIsReadyIndexView.fk_framework_id = Framework.id',
            )
            .innerJoinAndSelect(
                'Framework.products',
                'ProductMap',
                'ProductMap.fk_framework_id = Framework.id',
            )
            .where('Framework.enabledAt IS NOT NULL')
            .andWhere('ProductMap.enabled_at IS NOT NULL')
            .andWhere('ProductMap.fk_product_id = :productId', {
                productId,
            })
            .getMany();
    }

    private resolveSort(query: SelectQueryBuilder<Framework>): void {
        query.orderBy('Framework.name', 'ASC');
    }

    private resolveCustomFrameworkFilters(
        account: Account,
        dto: FrameworksRequestDto,
    ): WhereOptions {
        const { q, getAll } = dto;

        const whereOptions = {
            tag: FrameworkTag.CUSTOM,
            products: { productId: getProductId(account) },
            enabledAt: { not$: null },
            and$: [
                { id: { like$: `%${q}%` } },
                { name: { like$: `%${q}%` } },
                { slug: { like$: `%${q}%` } },
                { pill: { like$: `%${q}%` } },
                { customFrameworkId: { like$: `%${q}%` } },
            ],
        };

        return pick(whereOptions, [
            'tag',
            'products',
            getAll && 'enabledAt',
            !isEmpty(q) && 'and$',
        ]);
    }

    async getCustomFrameworksCount(): Promise<number> {
        return this.createQueryBuilder('Framework')
            .where('Framework.enabledAt IS NOT NULL')
            .andWhere('Framework.customFrameworkId IS NOT NULL')
            .getCount();
    }

    findFrameworksByTagsAndWorkspaceId(
        workspaceId: number,
        tags: FrameworkTag[],
    ): Promise<Framework[]> {
        return this.createQueryBuilder('Framework')
            .innerJoin('Framework.products', 'Product')
            .where('Product.productId = :workspaceId', { workspaceId })
            .andWhere('Framework.tag IN (:...tags)', {
                tags,
            })
            .getMany();
    }

    async getEnabledFrameworksWithoutReadiness(): Promise<Framework[]> {
        return this.createQueryBuilder('Framework')
            .where('Framework.enabled_at IS NOT NULL')
            .orderBy('Framework.name', 'ASC')
            .getMany();
    }

    async getCustomFrameworkIdsBySearchTerm(
        searchTerm: string,
        workspaceId?: number,
    ): Promise<string[]> {
        const query = this.createQueryBuilder('Framework')
            .select(['Framework.customFrameworkId'])
            .where('Framework.tag = :customTag', { customTag: FrameworkTag.CUSTOM })
            .andWhere(
                new Brackets(qb => {
                    qb.where(`Framework.name ${like()} :searchTerm`, {
                        searchTerm: `%${searchTerm}%`,
                    }).orWhere(`Framework.pill ${like()} :searchTerm`, {
                        searchTerm: `%${searchTerm}%`,
                    });
                }),
            )
            .andWhere('Framework.deletedAt IS NULL')
            .andWhere('Framework.enabledAt IS NOT NULL')
            .andWhere('Framework.customFrameworkId IS NOT NULL');

        if (workspaceId) {
            query
                .innerJoin('Framework.products', 'ProductFramework')
                .andWhere('ProductFramework.productId = :workspaceId', { workspaceId });
        }

        const results = await query.getMany();
        return results
            .map(framework => framework.customFrameworkId)
            .filter((id): id is string => Boolean(id));
    }
}
