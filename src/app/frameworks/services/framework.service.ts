import {
    ErrorCode,
    FrameworkFilename,
    FrameworkTag,
    OscalSupportedFrameworks,
    RequirementIndexCategory,
    RequirementIndexTag,
    TrustServiceCriteria,
} from '@drata/enums';
import { Injectable } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';
import { InjectRepository } from '@nestjs/typeorm';
import { ArchiveControlEvent } from 'app/analytics/observables/events/archive-control';
import { ControlsToRequirementsDownloadedEvent } from 'app/analytics/observables/events/control-to-requirements-downloaded.event';
import { FilteredControlsToRequirementsDownloadedEvent } from 'app/analytics/observables/events/filtered-controls-to-requirements-downloaded-event';
import { FilteredRequirementsToControlsDownloadedEvent } from 'app/analytics/observables/events/filtered-requirements-to-controls-downloaded-event';
import { RequirementsToControlsDownloadedEvent } from 'app/analytics/observables/events/requirements-to-controls-downloaded.event';
import { UnarchiveControlEvent } from 'app/analytics/observables/events/unarchive-control';
import { isFrameworkRestrictedByAuditType } from 'app/audit/helpers/audit.helper';
import { ConnectionsCoreService } from 'app/companies/connections/services/connections-core.service';
import { ProductFramework } from 'app/companies/products/entities/product-framework.entity';
import { Product } from 'app/companies/products/entities/product.entity';
import { ProductRepository } from 'app/companies/products/repositories/product.repository';
import { WorkspacesCoreService } from 'app/companies/products/services/workspaces-core.service';
import { WorkspacesFrameworkCoreService } from 'app/companies/products/services/workspaces-framework-core.service';
import { ControlService } from 'app/control/control.service';
import { ControlMarkedInScopeEvent } from 'app/custom-workflows/custom-workflows-common/triggers/events/control-marked-in-scope.workflow.event';
import { ControlMarkedOutOfScopeEvent } from 'app/custom-workflows/custom-workflows-common/triggers/events/control-marked-out-of-scope.workflow.event';
import { LibraryDocumentControlsMap } from 'app/document-library/entities/library-document-controls-map.entity';
import { LibraryDocumentProductsMap } from 'app/document-library/entities/library-document-products-map.entity';
import { LibraryDocumentRenewalSchema } from 'app/document-library/entities/library-document-renewal-schema.entity';
import { LibraryDocumentVersion } from 'app/document-library/entities/library-document-version.entity';
import { LibraryDocumentWorkflow } from 'app/document-library/entities/library-document-workflow.entity';
import { LibraryDocument } from 'app/document-library/entities/library-document.entity';
import { LibraryDocumentControlsMapRepository } from 'app/document-library/repositories/library-document-controls-map.repository';
import { LibraryDocumentRepository } from 'app/document-library/repositories/library-document.repository';
import { ControlsArchivedEvent } from 'app/events/observables/events/controls-archived.event';
import { ControlsDisabledEvent } from 'app/events/observables/events/controls-disabled.event';
import { ControlsUnarchivedEvent } from 'app/events/observables/events/controls-unarchived.event';
import { EvidenceTemplateUpdatedEvent } from 'app/events/observables/events/evidence-template-updated.event';
import { FrameworkDisabledEvent } from 'app/events/observables/events/framework-disabled.event';
import { FrameworkEnabledEvent } from 'app/events/observables/events/framework-enabled.event';
import { FrameworkLevelImpactUpdatedEvent } from 'app/events/observables/events/framework-level-impact-updated.event';
import { FrameworksDratameterRequestDto } from 'app/frameworks/dtos/frameworks-dratameter-request.dto';
import { FrameworksForNewAuditRequestDto } from 'app/frameworks/dtos/frameworks-for-new-audit-request.dto';
import { FrameworksRequestDto } from 'app/frameworks/dtos/frameworks-request.dto';
import { RequirementDownloadRequestDto } from 'app/frameworks/dtos/requirement-download-request.dto';
import { UpgradeFrameworkRequestDto } from 'app/frameworks/dtos/upgrade-framework-request.dto';
import { AuditorFrameworkType } from 'app/frameworks/entities/auditor-framework-type.entity';
import { Framework } from 'app/frameworks/entities/framework.entity';
import { RequirementIndex } from 'app/frameworks/entities/requirement-index.entity';
import { RequirementIsReadyView } from 'app/frameworks/entities/requirement-is-ready-view.entity';
import { Requirement } from 'app/frameworks/entities/requirement.entity';
import {
    checkUpgradePath,
    transformFrameworkReadyToFrameworkReadiness,
} from 'app/frameworks/helpers/framework.helper';
import { extractProfileRequirementsJSONReduced } from 'app/frameworks/helpers/profile-requirement.helper';
import { FrameworkIsReadyIndexViewRepository } from 'app/frameworks/repositories/framework-is-ready-view.repository';
import { FrameworkReducedRepository } from 'app/frameworks/repositories/framework-reduced.repository';
import { FrameworkRepository } from 'app/frameworks/repositories/framework.repository';
import { ProfileRequirementsToControlsMapRepository } from 'app/frameworks/repositories/profile-requirements-to-controls-map.repository';
import { ProfileSelectionRepository } from 'app/frameworks/repositories/profile-selection.repository';
import { RequirementIndexTagRepository } from 'app/frameworks/repositories/requirement-index-tag.repository';
import { RequirementIndexViewRepository } from 'app/frameworks/repositories/requirement-index-view.repository';
import { RequirementIndexRepository } from 'app/frameworks/repositories/requirement-index.repository';
import { RequirementRepository } from 'app/frameworks/repositories/requirement.repository';
import { AccountProvisioningOrchestrationService } from 'app/frameworks/services/account-provisioning-orchestration.service';
import { ProfileRequirementService } from 'app/frameworks/services/profile-requirement.service';
import { FrameworkDisabledReady } from 'app/frameworks/types/framework-disabled-ready.type';
import { FrameworkReadiness } from 'app/frameworks/types/framework-readiness.type';
import { FrameworkReady } from 'app/frameworks/types/framework-ready.type';
import { FrameworkWithControlFlag } from 'app/frameworks/types/framework-with-control-flag.type';
import { ArchiveControlRequestDto } from 'app/grc/dtos/archive-control-request.dto';
import { ControlsRequestDto } from 'app/grc/dtos/controls-request.dto';
import { LevelImpactRequestDto } from 'app/grc/dtos/level-impact-request.dto';
import { SwitchProfileRequestDto } from 'app/grc/dtos/switch-profile.request.dto';
import { UnarchiveControlRequestDto } from 'app/grc/dtos/unarchive-control-request.dto';
import { ControlIsReadyView } from 'app/grc/entities/control-is-ready-view.entity';
import { Control } from 'app/grc/entities/control.entity';
import { FacetRunner } from 'app/grc/facets/facet-runner.class';
import { IsEnabledFacet } from 'app/grc/facets/framework/is-enabled-facet.class';
import { IsReadyFacet } from 'app/grc/facets/framework/is-ready-facet.class';
import { SearchFacet } from 'app/grc/facets/framework/search-facet.class';
import { extractIds } from 'app/grc/helpers/entity.helper';
import { ControlsReindexEvent } from 'app/grc/observables/events/controls-reindex.event';
import { MappedControlsResetForFrameworkEvent } from 'app/grc/observables/events/mapped-controls-reset-for-framework.event';
import { ControlRepository } from 'app/grc/repositories/control.repository';
import { Dratameter, DratameterFull } from 'app/grc/types/dratameter.type';
import { LevelImpactType } from 'app/grc/types/level-impact-type';
import { ControlWithReady, RequirementWithReady } from 'app/grc/types/with-ready.type';
import { IndexMonitorResultControlsAndFrameworksEvent } from 'app/monitors/observables/events/index-monitor-result-controls-and-frameworks.event';
import { MonitorsCoreService } from 'app/monitors/services/monitors-core.service';
import { UserDocument } from 'app/users/entities/user-document.entity';
import { User } from 'app/users/entities/user.entity';
import { UserDocumentDownloadedEvent } from 'app/users/observables/events/user-document-downloaded.event';
import { Policy } from 'app/users/policies/entities/policy.entity';
import { PoliciesCoreService } from 'app/users/policies/services/policies-core.service';
import { UsersCoreService } from 'app/users/services/users-core.service';
import { Account } from 'auth/entities/account.entity';
import { AccountFrameworksType } from 'auth/types/account-frameworks.type';
import { AutopilotRecipeTemplate } from 'autopilot2/entities/autopilot-recipe-template.entity';
import { CacheBusterWithPrefix, CacheWithPrefix } from 'cache/cache.decorator';
import { PolloAdapter } from 'commons/adapters/pollo.adapter';
import { type DrataDataSource } from 'commons/classes/drata-data-source.class';
import { getProvisionFrameworkLockKey } from 'commons/constants/cache-lock-keys.constants';
import { Caches } from 'commons/enums/cache.enum';
import { FeatureType } from 'commons/enums/feature-toggling/feature.enum';
import { LibraryDocumentVersionType } from 'commons/enums/library-document-version-type.enum';
import { LibraryDocumentWorkflowType } from 'commons/enums/library-document-workflow-type.enum';
import { RenewalScheduleType } from 'commons/enums/renewal-schedule.enum';
import { AuditLogEventType } from 'commons/enums/site-admin/audit-log-event-type.enum';
import { AuditLogTargetType } from 'commons/enums/site-admin/audit-log-target-type.enum';
import { BadRequestException } from 'commons/exceptions/bad-request.exception';
import { NotFoundException } from 'commons/exceptions/not-found.exception';
import { PreconditionFailedException } from 'commons/exceptions/precondition-failed.exception';
import { getGlobalDataSource } from 'commons/factories/data-source.manager';
import { getEntitlementLimitDifference } from 'commons/helpers/account.helper';
import { asyncForEach } from 'commons/helpers/array.helper';
import { shouldEmitEvent } from 'commons/helpers/event.helper';
import { isPositiveInteger } from 'commons/helpers/number.helper';
import { getSkip } from 'commons/helpers/pagination.helper';
import {
    associateControlsToProduct,
    associateControlTestInstancesToProduct,
    associateFrameworksToProduct,
    associateRecipesToProduct,
    filterFrameworks,
    getProductId,
    isMultiProductEnabled,
} from 'commons/helpers/products.helper';
import {
    frameworkTemplateToFrameworkEntity,
    publishFrameworkDisabled,
    publishFrameworks,
} from 'commons/helpers/publish-frameworks.helper';
import { syncControlScopeFromRequirementMappings } from 'commons/helpers/scope-management.helper';
import { associatePoliciesToFrameworks } from 'commons/seeds/app-seeding.helper';
import { mapProfileDetailsTemplatesToFrameworkTemplates } from 'commons/seeds/global-seeding.helper';
import { AppService } from 'commons/services/app.service';
import { CsvDataSetType } from 'commons/types/csv-data-set.type';
import { PaginationType } from 'commons/types/pagination.type';
import config from 'config';
import { getCustomRepository } from 'database/typeorm/typeorm.extensions.helper';
import { FeatureFlagCategory } from 'feature-flags/enums/feature-flag-category.enum';
import { FeatureFlag } from 'feature-flags/feature-flag.enum';
import { FeatureFlagService } from 'feature-flags/feature-flag.service';
import { clone, get, intersection, isEmpty, isNil, map, size, uniq, uniqBy } from 'lodash';
import moment from 'moment';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { FRAMEWORK_PROVISIONING_CONFIG } from 'scripts/recurring/framework/configs/framework-configs';
import {
    FrameworkData,
    getFrameworkEvidenceTemplates,
    getFrameworkPolicyMappings,
} from 'scripts/recurring/framework/helpers/framework.data';
import { compareEntities } from 'scripts/recurring/framework/helpers/framework.helpers';
import {
    createEmptyFrameworkLog,
    FrameworkContext,
    FrameworkLog,
    FrameworkToUpdate,
} from 'scripts/recurring/framework/helpers/framework.types';
import { defaultPropertiesComparisonConfig } from 'scripts/recurring/framework/helpers/property-comparison-configs/default-property-comparison.config';
import { requirementPropertiesComparisonConfig } from 'scripts/recurring/framework/helpers/property-comparison-configs/requirement-property-comparison.config';
import { AuditorFrameworkTypeTemplate } from 'site-admin/entities/auditor-framework-type-template.entity';
import { ControlTemplate } from 'site-admin/entities/control-template.entity';
import { EvidenceTemplate } from 'site-admin/entities/evidence-template.entity';
import { FrameworkTemplate } from 'site-admin/entities/framework-template.entity';
import { PolicyTemplate } from 'site-admin/entities/policy-template.entity';
import { RequirementIndexTemplate } from 'site-admin/entities/requirement-index-template.entity';
import { RequirementTemplate } from 'site-admin/entities/requirement-template.entity';
import { SiteAdmin } from 'site-admin/entities/site-admin.entity';
import { AuditLogEvent } from 'site-admin/observables/events/audit-log.event';
import { EvidenceTemplateRepository } from 'site-admin/repositories/evidence-template.repository';
import { FrameworkTemplateRepository } from 'site-admin/repositories/framework-template.repository';
import { PolicyTemplateRepository } from 'site-admin/repositories/policy-template.repository';
import { RequirementTemplateRepository } from 'site-admin/repositories/requirement-template.repository';
import { ControlTemplateService } from 'template/services/control-template.service';
import { FrameworkTemplateService } from 'template/services/framework-template.service';
import { Brackets, In, IsNull, Not, Repository } from 'typeorm';

@Injectable()
export class FrameworkService extends AppService {
    constructor(
        private readonly workspacesCoreService: WorkspacesCoreService,
        private readonly workspacesFrameworkCoreService: WorkspacesFrameworkCoreService,
        private readonly commandBus: CommandBus,
        private readonly policiesCoreService: PoliciesCoreService,
        private readonly connectionsCoreService: ConnectionsCoreService,
        private readonly usersCoreService: UsersCoreService,
        private readonly controlService: ControlService,
        private readonly frameworkTemplateService: FrameworkTemplateService,
        private readonly controlTemplateService: ControlTemplateService,
        private readonly profileRequirementService: ProfileRequirementService,
        private readonly featureFlagService: FeatureFlagService,
        @InjectRepository(EvidenceTemplateRepository)
        private readonly evidenceTemplateRepository: Repository<EvidenceTemplate>,
        @InjectRepository(FrameworkTemplateRepository)
        private readonly frameworkTemplateRepository: Repository<FrameworkTemplate>,
        @InjectRepository(PolicyTemplateRepository)
        private readonly policyTemplateRepository: Repository<PolicyTemplate>,
        @InjectRepository(RequirementTemplateRepository)
        private readonly requirementTemplateRepository: Repository<RequirementTemplate>,
        @InjectRepository(RequirementIndexTemplate)
        private readonly requirementIndexTemplateRepository: Repository<RequirementIndexTemplate>,
        private readonly accountProvisioningOrchestrationService: AccountProvisioningOrchestrationService,
        private readonly monitorsCoreService: MonitorsCoreService,
    ) {
        super();
    }

    /**
     * @deprecated Use FrameworksCoreService.findFrameworkById
     */
    @CacheWithPrefix<FrameworkReady>(null, {
        store: Caches.FRAMEWORK,
        useArgs: 1,
        ttl: config.get('cache.ttl.hour'),
    })
    findFrameworkById(frameworkId: number, account: Account): Promise<FrameworkReady> {
        return this.frameworkRepository.getWithReady(account, frameworkId);
    }

    async findFrameworkBySlug(
        account: Account,
        workspaceId: number,
        slug: string,
    ): Promise<FrameworkReady> {
        const workspaceFrameworkIds =
            await this.frameworkRepository.getFrameworkIdsByWorkspaceId(workspaceId);

        if (isEmpty(workspaceFrameworkIds)) {
            throw new NotFoundException(ErrorCode.FRAMEWORK_NOT_FOUND);
        }
        return this.frameworkRepository.getWithReadyBySlug(account, workspaceFrameworkIds, slug);
    }

    /**
     * @deprecated Use FrameworksCoreModule.getControlsEnabled
     *
     * @returns
     */
    async getControlsEnabled(): Promise<Framework[]> {
        return this.frameworkRepository.find({
            where: {
                controlsEnabledAt: Not(IsNull()),
            },
        });
    }

    async getLevel(frameworkId: number): Promise<Framework> {
        return this.frameworkRepository.findOne({
            where: { id: frameworkId },
            relations: ['requirementIndexes', 'requirementIndexes.requirementIndexTags'],
        });
    }

    /**
     *
     * @param frameworkId
     * @param requestDto
     * @returns
     */
    async getLevelImpact(
        frameworkId: number,
        requestDto: LevelImpactRequestDto,
    ): Promise<LevelImpactType> {
        const { level, privacy, profile } = requestDto;
        if (!isNil(profile)) {
            const currentlyInScope: string[] = [];
            let currentControls: string[] = [];
            let newControls: string[] = [];
            let newInScope: string[] = [];
            let numMarkedInScope = 0;
            let numMarkedOutOfScope = 0;
            let numRequirementsChanged = 0;
            let numControlsMapped = 0;
            let numControlsUnmapped = 0;
            const framework = await this.frameworkRepository.findOneOrFail({
                where: { id: frameworkId },
                join: {
                    alias: 'framework',
                    leftJoinAndSelect: {
                        requirementIndexes: 'framework.requirementIndexes',
                        requirement: 'requirementIndexes.requirement',
                        controls: 'requirement.controls',
                    },
                },
            });
            for (const index of framework.requirementIndexes) {
                if (isNil(index.requirement.archivedAt)) {
                    if (index.requirement.externalId) {
                        currentlyInScope.push(index.requirement.externalId);
                    }

                    for (const control of index.requirement.controls.filter(
                        con => !isNil(con.controlTemplateId), // only DCF controls to be considered
                    )) {
                        currentControls.push(control.code);
                    }
                }
            }
            currentControls = uniq(currentControls);
            const newProfileMappings = await this.profileRequirementsToControlsMapRepository.find({
                where: { profileDetails: { id: profile } },
            });
            const newProfile = await this.profileSelectionRepository.findOne({
                where: { profileDetails: { id: profile } },
                relations: ['profileDetails'],
            });
            const profileReqMap = extractProfileRequirementsJSONReduced(newProfile.profileDetails);

            // ++InScope = new in scope having something that currently in scope does not
            // ++OutScope = new in scope not having something that currently in scope does
            for (const key of Object.keys(profileReqMap)) {
                if (!currentlyInScope.includes(key)) {
                    ++numMarkedInScope;
                }
            }
            for (const cur of currentlyInScope) {
                if (!profileReqMap[cur]) {
                    ++numMarkedOutOfScope;
                }
            }

            if (!isEmpty(newProfileMappings)) {
                for (const mapping of newProfileMappings) {
                    newInScope.push(mapping.requirement);
                    newControls.push(mapping.controlCode);
                }
            } else {
                // Use the profile requirements from profileReqMap and find corresponding controls
                for (const index of framework.requirementIndexes) {
                    const requirementExternalId = index.requirement?.externalId;

                    // Check if this requirement is in the profile
                    if (requirementExternalId && profileReqMap[requirementExternalId]) {
                        newInScope.push(requirementExternalId);

                        // Add all DCF controls from this requirement to newControls
                        if (index.requirement?.controls) {
                            for (const control of index.requirement.controls.filter(
                                con => !isNil(con.controlTemplateId),
                            )) {
                                newControls.push(control.code);
                            }
                        }
                    }
                }
            }

            newInScope = uniq(newInScope);

            newControls = uniq(newControls);

            // ++controlMarkedInScope = control in newControls that isn't in currentControls
            // ++controlMarkedOutOfScope = control in currentControls that isn't in newControls
            for (const cur of currentControls) {
                if (!newControls.includes(cur)) {
                    ++numControlsUnmapped;
                }
            }

            for (const newCon of newControls) {
                if (!currentControls.includes(newCon)) {
                    ++numControlsMapped;
                }
            }

            numRequirementsChanged = intersection(newInScope, currentlyInScope).length;

            return {
                numMarkedInScope,
                numMarkedOutOfScope,
                numRequirementsChanged,
                numControlsMapped,
                numControlsUnmapped,
            };
        } else if (!isNil(level)) {
            const framework = await this.frameworkRepository.findOneOrFail({
                where: { id: frameworkId },
                relations: ['requirementIndexes', 'requirementIndexes.requirementIndexTags'],
            });

            let numMarkedOutOfScope = 0;
            let numMarkedInScope = 0;

            for (const index of framework.requirementIndexes) {
                const foundNew =
                    !isNil(index.requirementIndexTags.find(tag => tag.id === level)) ||
                    (privacy && index.topic === TrustServiceCriteria.NIST80053_PRIVACY);

                if (!foundNew && isNil(index.requirement.archivedAt)) {
                    numMarkedOutOfScope++;
                }

                if (foundNew && !isNil(index.requirement.archivedAt)) {
                    numMarkedInScope++;
                }
            }

            return {
                numMarkedInScope,
                numMarkedOutOfScope,
                numRequirementsChanged: 0,
                numControlsMapped: 0,
                numControlsUnmapped: 0,
            };
        } else {
            throw new BadRequestException('Level or profile required');
        }
    }

    async getUnmappedFrameworks(): Promise<Pick<Framework, 'id' | 'name'>[]> {
        const enabledFrameworks = await this.frameworkRepository.getEnabledFrameworksSimple();

        const enabledWorkspaceMappings =
            await this.frameworkRepository.getWorkspaceEnabledFrameworks();

        /**
         * When having multiple workspaces, each framework gets created n times.
         * Identifying enabled framework mapping via slugs is a way of covering the scenario where
         * a framework gets created twice, but assigned to just 1 workspace.
         */
        const uniqueSlugs = enabledWorkspaceMappings.map(ewm => ewm.slug);

        const unMappedFrameworks = enabledFrameworks.filter(fr => !uniqueSlugs.includes(fr.slug));

        return unMappedFrameworks.map(({ id, name }) => ({ id, name }));
    }

    /**
     *
     * @param frameworkId
     * @param requestDto
     * @returns
     */
    @CacheBusterWithPrefix<Framework>({
        stores: [
            Caches.USER_BY_ENTRY_ID,
            Caches.FRAMEWORK,
            Caches.LIST_CONTROLS,
            Caches.REQUIREMENTS,
        ],
    })
    async postLevel(
        user: User,
        frameworkId: number,
        requestDto: LevelImpactRequestDto,
        account: Account,
    ): Promise<Framework> {
        const framework = await this.frameworkRepository.findOne({
            where: { id: frameworkId },
            relations: [
                'requirementIndexes',
                'requirementIndexes.requirementIndexTags',
                'requirementIndexes.requirement',
                'requirementIndexes.requirement.controls',
            ],
        });

        if (isNil(framework)) {
            throw new NotFoundException(ErrorCode.FRAMEWORK_NOT_FOUND);
        }

        const { level, privacy } = requestDto;
        const outOfScopeTime = new Date();

        const selectedRequirementIndexTag = await this.requirementIndexTagRepository.findOne({
            where: { id: level },
        });

        let tag = null;
        const updatedRequirementIds: number[] = [];
        const matchedControlIds: number[] = [];

        for (const requirementIndex of framework.requirementIndexes) {
            let foundTag =
                privacy && requirementIndex.topic === TrustServiceCriteria.NIST80053_PRIVACY;

            for (tag of requirementIndex.requirementIndexTags) {
                if (tag.id === selectedRequirementIndexTag.id) {
                    foundTag = true;
                    break;
                }
            }

            if (!foundTag) {
                requirementIndex.requirement.archivedAt = outOfScopeTime;
                requirementIndex.requirement.rationale = `${config.get('frameworks.rationale')} ${
                    framework.name
                }`;
            } else {
                requirementIndex.requirement.archivedAt = null;
                requirementIndex.requirement.rationale = null;
            }

            updatedRequirementIds.push(requirementIndex.requirement.id);

            if (!isEmpty(requirementIndex.requirement?.controls)) {
                for (const control of requirementIndex.requirement.controls) {
                    matchedControlIds.push(control.id);
                }
            }
        }

        framework.selectedLevel =
            RequirementIndexTag[selectedRequirementIndexTag.requirementIndexTagTemplateId];

        if (!isNil(privacy)) {
            framework.privacy = privacy;
        }

        framework.beforeChange();
        await this.frameworkRepository.save(framework);

        await this.requirementRepository.save(
            framework.requirementIndexes.map(ri => ri.requirement),
        );

        if (!isEmpty(matchedControlIds)) {
            const uniqueControlIds = [...new Set(matchedControlIds)];

            const controls = await this.controlRepository.find({
                where: { id: In(uniqueControlIds) },
                relations: [
                    'requirements',
                    'requirements.requirementIndex',
                    'requirements.requirementIndex.framework',
                ],
            });

            const { controlsToUpdate } = syncControlScopeFromRequirementMappings(controls);

            if (!isEmpty(controlsToUpdate)) {
                await this.controlRepository.save(controlsToUpdate);

                if (!isNil(account) && !isNil(user)) {
                    this._eventBus.publish(
                        new FrameworkLevelImpactUpdatedEvent(
                            account,
                            user,
                            controlsToUpdate,
                            framework.requirementIndexes.map(ri => ri.requirement),
                            framework.id,
                            framework.tag,
                            level,
                        ),
                    );
                }
            }
        }

        return framework;
    }

    async getAllDrataFrameworks(withDeleted = false): Promise<Framework[]> {
        return this.frameworkRepository.find({
            join: {
                alias: 'framework',
                leftJoinAndSelect: {
                    requirementIndexes: 'framework.requirementIndexes',
                    requirement: 'requirementIndexes.requirement',
                    controls: 'requirement.controls',
                },
            },
            where: {
                tag: Not(FrameworkTag.CUSTOM),
            },
            withDeleted,
        });
    }

    /**
     * @deprecated Use FrameworksCoreService.getAllFrameworks
     *
     * @param withDeleted
     * @returns
     */
    async getAllFrameworks(withDeleted = false): Promise<Framework[]> {
        return this.frameworkRepository.find({
            join: {
                alias: 'framework',
                leftJoinAndSelect: {
                    requirementIndexes: 'framework.requirementIndexes',
                    requirement: 'requirementIndexes.requirement',
                    controls: 'requirement.controls',
                },
            },
            withDeleted,
        });
    }

    /**
     * @deprecated Use FrameworksCoreService.getFrameworkSlugs
     */
    async getFrameworkSlugs(): Promise<string[]> {
        return this.frameworkRepository.getFrameworkSlugs();
    }

    /**
     *
     * @param dto
     * @returns
     */
    async getDratameter(dto: FrameworksDratameterRequestDto): Promise<DratameterFull> {
        let topics = [];
        let categories = [];
        let subCategories = [];

        if (!isEmpty(dto.topics)) {
            topics = await this.formatDratameter(dto.topics, 'topic');
        }

        if (!isEmpty(dto.categories)) {
            categories = await this.formatDratameter(dto.categories, 'category');
        }

        if (!isEmpty(dto.subCategories)) {
            subCategories = await this.formatDratameter(dto.subCategories, 'subCategory');
        }

        return { topics, categories, subCategories };
    }

    /**
     *
     * @returns
     */
    async getAllEnabledFrameworkTypes(): Promise<AuditorFrameworkType[]> {
        return this.auditorFrameworkTypeRepository.find({
            relations: ['relatedFramework'],
            where: {
                relatedFramework: {
                    enabledAt: Not(IsNull()),
                },
            },
        });
    }

    async getAllEnabledFrameworks(account: Account): Promise<Framework[]> {
        const productId = getProductId(account);

        return this.frameworkRepository.find({
            relations: ['products'],
            where: {
                products: { productId, enabledAt: Not(IsNull()) },
                enabledAt: Not(IsNull()),
            },
        });
    }

    /**
     * @param frameworkTag
     * @param productId
     * @param customFrameworkId
     * @returns
     * @deprecated Use FrameworksCoreService.getEnabledFrameworkByTag
     */
    async getEnabledFrameworkByTag(
        frameworkTag: FrameworkTag,
        productId: number | null,
        customFrameworkId?: string | null,
    ): Promise<Framework | null> {
        return this.frameworkRepository.getEnabledFrameworkByTag(
            frameworkTag,
            productId,
            customFrameworkId,
        );
    }

    /**
     * @deprecated Use FrameworksCoreService.getEnabledFrameworksByTags
     */
    async getEnabledFrameworksByTags(
        frameworkTag: FrameworkTag,
        productId: number | null,
        customFrameworkIds: string[],
    ): Promise<Framework[]> {
        return this.frameworkRepository.getEnabledFrameworksByTags(
            frameworkTag,
            productId,
            customFrameworkIds,
        );
    }

    /**
     * @deprecated Use FrameworksCoreService.getFrameworksWithControls
     *
     * @returns
     */
    async getFrameworksWithControls(): Promise<Framework[]> {
        return this.frameworkRepository.getAllFrameworksWithControlsGroupByFrameworkId(false);
    }

    /**
     * @deprecated Use FrameworksCoreService.getEnabledFrameworksOrderedByName
     *
     * @returns
     */
    async getEnabledFrameworksOrderedByName(): Promise<Framework[]> {
        return this.frameworkRepository.getEnabledFrameworksOrderedByName();
    }

    /**
     * @deprecated User FrameworksCoreService.getFrameworksWithControlsForSecurityReport
     *
     * @param account
     * @returns
     */
    async getFrameworksWithControlsForSecurityReport(account: Account): Promise<Framework[]> {
        const frameworks = await this.getFrameworksWithControls();

        /**
         * Adding the products to this already overloaded series of left joins
         * is not preferred - get the products seperately and merge them here
         */
        const frameworkProducts = await this.frameworkRepository.find({
            where: { enabledAt: Not(IsNull()) },
            loadEagerRelations: false,
            relations: ['products'],
        });

        for (const framework of frameworks) {
            const frameworkProduct = frameworkProducts.find(target => {
                return target.id === framework.id;
            });

            framework.products = get(frameworkProduct, 'products', []);
        }

        return filterFrameworks(account, frameworks);
    }

    /**
     * @deprecated Use FrameworksCoreService.getFrameworkWithControls
     * @param frameworkTag
     * @param productId
     * @param customFrameworkId
     * @returns
     */
    async getFrameworkWithControls(
        frameworkTag: FrameworkTag,
        productId: number,
        customFrameworkId?: string,
    ): Promise<Framework> {
        return this.frameworkRepository.getFrameworkWithControls(
            frameworkTag,
            productId,
            customFrameworkId,
        );
    }

    /**
     * @deprecated Use FrameworksCoreService.getFrameworkWithSpecificControls
     *
     * @param frameworkTag
     * @param productId
     * @param controlIds
     * @param customFrameworkId
     * @returns
     */
    async getFrameworkWithSpecificControls(
        frameworkTag: FrameworkTag,
        productId: number,
        controlIds: number[],
        customFrameworkId?: string,
    ): Promise<Framework> {
        return this.frameworkRepository.getFrameworkWithSpecificControls(
            frameworkTag,
            productId,
            controlIds,
            customFrameworkId,
        );
    }

    /**
     * @deprecated Use FrameworksCoreService.getPaginatedEnabledFrameworksByProductId
     *
     * @param account
     * @param requestDto
     * @param productId
     * @returns
     */
    async getPaginatedEnabledFrameworksByProductId(
        account: Account,
        requestDto: FrameworksRequestDto,
        productId: number,
    ): Promise<PaginationType<Framework>> {
        const query = this.frameworkRepository
            .createQueryBuilder('Framework')
            .innerJoin('Framework.products', 'Product')
            .leftJoin('Framework.requirementIndexes', 'RequirementIndex')
            .where(
                new Brackets(qb => {
                    qb.where('RequirementIndex.framework.id = Framework.id').orWhere(
                        'Framework.customFrameworkId IS NOT NULL',
                    );
                }),
            )
            .andWhere('Product.productId = :productId', { productId })
            .andWhere('Framework.enabledAt IS NOT NULL')
            .andWhere('Product.enabledAt IS NOT NULL')
            .orderBy('Framework.name', 'ASC');

        if (!isNil(requestDto.page)) {
            query.skip(getSkip(requestDto.page, requestDto.limit)).take(requestDto.limit);
        }

        const [data, total] = await query.getManyAndCount();

        return { data, total, page: requestDto.page, limit: requestDto.limit };
    }
    /**
     * @deprecated Use FrameworksCoreService.listFrameworks
     *
     * @param account
     * @param requestDto
     * @returns
     */
    async listFrameworks(
        account: Account,
        requestDto: FrameworksRequestDto,
    ): Promise<PaginationType<FrameworkReady>> {
        const searchFacet = new SearchFacet(account, this.frameworkRepository, requestDto);

        const isEnabledFacet = new IsEnabledFacet(account, this.frameworkRepository, requestDto);

        const isReadyFacet = new IsReadyFacet(
            account,
            this.frameworkIsReadyIndexViewRepository,
            requestDto,
        );

        const facetRunner = new FacetRunner();
        facetRunner.addFacet(searchFacet);
        facetRunner.addFacet(isReadyFacet);
        facetRunner.addFacet(isEnabledFacet);
        facetRunner.addExcludeIds(requestDto.excludeIds ?? []);

        const filteredFrameworkIds = await facetRunner.run();

        if (filteredFrameworkIds.include.length === 0 && facetRunner.isAnyFacetActivated()) {
            return {
                data: [],
                page: requestDto.page,
                limit: requestDto.limit,
                total: 0,
            };
        }

        const page = await this.frameworkRepository.getPage(
            getProductId(account),
            filteredFrameworkIds.include,
            filteredFrameworkIds.exclude,
            requestDto.page,
            requestDto.limit,
            !isNil(requestDto.getAll) ? requestDto.getAll : false, // TODO: Remove getAll - no pattern should allow this
        );

        const frameworks = await this.frameworkRepository.getFrameworkReadyByIds(
            getProductId(account),
            page.data,
        );

        const externalFrameworks = frameworks
            .filter(fr => !isNil(fr.framework.externalId))
            .map(f => f.framework.id);

        if (!isEmpty(externalFrameworks)) {
            const selections = await this.profileSelectionRepository.find({
                where: {
                    selected: true,
                    framework: { id: In(externalFrameworks) },
                },
                relations: ['framework', 'profileDetails'],
            });

            for (const selection of selections) {
                frameworks.find(fr => fr.framework.id === selection.framework.id).profileOscalId =
                    selection.profileDetails.oscalId;
            }
        }

        return {
            data: frameworks,
            page: page.page,
            limit: page.limit,
            total: page.total,
        };
    }

    /**
     *
     * @param frameworkSlug
     * @param account
     * @param user
     * @param requirementsToControl
     * @returns
     */
    async getControlsReport(
        frameworkSlug: string,
        account: Account,
        user: User,
        requirementsToControl: boolean,
        requestDto?: RequirementDownloadRequestDto,
    ): Promise<CsvDataSetType> {
        const { data, filename } = await this.getControlsForReport(
            frameworkSlug,
            getProductId(account),
            account,
            requirementsToControl,
            requestDto,
        );
        if (isEmpty(data)) {
            throw new NotFoundException(ErrorCode.NO_CONTROLS_FOUND);
        }

        if (shouldEmitEvent(account, user)) {
            this._eventBus.publish(
                new UserDocumentDownloadedEvent(account, user, filename, {
                    name: filename,
                } as UserDocument),
            );
        }

        const filteredViewCategory = requestDto.category;
        const companyName = account.companyName;
        if (!isNil(filteredViewCategory)) {
            if (requirementsToControl) {
                this._eventBus.publish(
                    new FilteredRequirementsToControlsDownloadedEvent(
                        user,
                        account,
                        companyName,
                        frameworkSlug,
                        filteredViewCategory.map(cat => RequirementIndexCategory[cat]),
                    ),
                );
            } else {
                this._eventBus.publish(
                    new FilteredControlsToRequirementsDownloadedEvent(
                        user,
                        account,
                        companyName,
                        frameworkSlug,
                        filteredViewCategory.map(cat => RequirementIndexCategory[cat]),
                    ),
                );
            }
        } else if (isNil(filteredViewCategory)) {
            if (requirementsToControl) {
                this._eventBus.publish(
                    new RequirementsToControlsDownloadedEvent(
                        user,
                        account,
                        companyName,
                        frameworkSlug,
                    ),
                );
            } else {
                this._eventBus.publish(
                    new ControlsToRequirementsDownloadedEvent(
                        user,
                        account,
                        companyName,
                        frameworkSlug,
                    ),
                );
            }
        }

        return {
            data,
            filename,
        };
    }

    /**
     *
     * @param frameworkSlug
     * @param productId
     * @param account
     * @param requirementsToControl
     * @returns
     */
    async getControlsForReport(
        frameworkSlug: string,
        productId: number,
        account: Account,
        requirementsToControl: boolean,
        requestDto?: RequirementDownloadRequestDto,
    ): Promise<CsvDataSetType> {
        const data = [];
        let filename = null;

        if (isNil(productId)) {
            throw new NotFoundException(ErrorCode.MULTIPLE_PRODUCT_SUPPORT_PRODUCT_NOT_FOUND);
        }

        const productFramework =
            await this.workspacesFrameworkCoreService.getFrameworkByProductAndSlug(
                productId,
                frameworkSlug,
            );
        const productFrameworkId = get(productFramework, 'frameworkId');

        const framework = (await this.findFrameworkById(productFrameworkId, account)).framework;

        if (requirementsToControl) {
            const requirements = await this.requirementRepository.getRequirementsForCsvDownloads(
                framework.id,
                true,
                requestDto,
            );
            requirements.sort((a, b) =>
                new Intl.Collator(undefined, { numeric: true, sensitivity: 'base' }).compare(
                    a.name,
                    b.name,
                ),
            );
            const requirementReadys = await this.requirementReadyRepository.find({
                where: {
                    requirementId: In(requirements.map(r => r.id)),
                },
            });

            // to enable multiple rows per requirement, add them as separate rows per mapped control
            for (const requirement of requirements) {
                requirement.controls = requirement.controls.sort(this.controlNumberSort);

                for (const control of requirement.controls) {
                    if (isNil(control.archivedAt)) {
                        control.description = this.addCompany(
                            control.description,
                            account.companyName,
                        );
                        const row: RequirementWithReady = {
                            requirement: clone(requirement),
                            isReady: requirementReadys.some(
                                requirementReady =>
                                    requirementReady.requirementId === requirement.id &&
                                    requirementReady.isReady,
                            ),
                        };
                        row.requirement.controls = [control];
                        data.push(row);
                    }
                }
            }
            if (framework.tag === FrameworkTag.CUSTOM) {
                filename = `${framework.pill}-${config.get(
                    'reports.requirementsToControls',
                )}-${moment().format('MMDDYYYY')}`;
            } else {
                filename = `${FrameworkFilename[frameworkSlug]}-${config.get(
                    'reports.requirementsToControls',
                )}-${moment().format('MMDDYYYY')}`;
            }
        } else {
            const controls = await this.controlRepository.getControlMap(framework.id, requestDto);

            controls.forEach(control => {
                control.requirements.sort((a, b) =>
                    new Intl.Collator(undefined, { numeric: true, sensitivity: 'base' }).compare(
                        a.name,
                        b.name,
                    ),
                );
            });

            const controlReadys = await this.controlReadyRepository.find({
                where: {
                    controlId: In(controls.map(c => c.id)),
                },
            });

            for (const control of controls) {
                control.description = this.addCompany(control.description, account.companyName);

                const isReady =
                    controlReadys.find(entry => entry.controlId === control.id)?.isReady || false;

                data.push({
                    control,
                    isReady,
                });
            }

            data.sort((a: ControlWithReady, b: ControlWithReady) =>
                this.controlNumberSort(a.control, b.control),
            );
            if (framework.tag === FrameworkTag.CUSTOM) {
                filename = `${framework.pill}-${config.get(
                    'reports.controlsToRequirements',
                )}-${moment().format('MMDDYYYY')}`;
            } else {
                filename = `${FrameworkFilename[frameworkSlug]}-${config.get(
                    'reports.controlsToRequirements',
                )}-${moment().format('MMDDYYYY')}`;
            }
        }
        return { data, filename };
    }

    @CacheBusterWithPrefix<Array<Control>>({
        stores: [
            Caches.USER_BY_ENTRY_ID,
            Caches.LIST_CONTROLS,
            Caches.FRAMEWORK,
            Caches.REQUIREMENTS,
        ],
    })
    async archiveControls(
        requestDto: ArchiveControlRequestDto,
        account: Account,
        user: User,
    ): Promise<Array<Control>> {
        const controlIds = await this.getControls(requestDto);
        const controls = await this.controlRepository.archiveControls(
            controlIds,
            requestDto.rationale,
        );
        await this.willPublishEvent(controls, true, account, user, requestDto.rationale);

        this._eventBus.publish(
            new IndexMonitorResultControlsAndFrameworksEvent(account, getProductId(account), null),
        );

        if (!isNil(controls) && !isEmpty(controls)) {
            const currentWorkspaceId = account.getCurrentProduct()?.id;
            const archivedControlIds = extractIds(controls);

            // Trigger OpenSearch reindexing
            if (!isNil(currentWorkspaceId)) {
                this._eventBus.publish(
                    new ControlsReindexEvent(account, currentWorkspaceId, archivedControlIds),
                );
            }

            // Trigger for Custom Workflow
            controls.forEach(control => {
                this._eventBus.publish(
                    new ControlMarkedOutOfScopeEvent(account, user, control.id, currentWorkspaceId),
                );
            });
        }
        return controls;
    }

    @CacheBusterWithPrefix<Array<Control>>({
        stores: [
            Caches.USER_BY_ENTRY_ID,
            Caches.LIST_CONTROLS,
            Caches.FRAMEWORK,
            Caches.REQUIREMENTS,
        ],
    })
    async unArchiveControls(
        requestDto: UnarchiveControlRequestDto,
        account: Account,
        user: User,
    ): Promise<Array<Control>> {
        const controlIds = await this.getControls(requestDto as ArchiveControlRequestDto);
        const controls = await this.controlRepository.unArchiveControls(controlIds);
        await this.willPublishEvent(controls, false, account, user, null);

        this._eventBus.publish(
            new IndexMonitorResultControlsAndFrameworksEvent(account, getProductId(account), null),
        );

        if (!isNil(controls) && !isEmpty(controls)) {
            const currentWorkspaceId = account.getCurrentProduct()?.id;
            const unarchivedControlIds = extractIds(controls);

            // Trigger OpenSearch reindexing
            if (!isNil(currentWorkspaceId)) {
                this._eventBus.publish(
                    new ControlsReindexEvent(account, currentWorkspaceId, unarchivedControlIds),
                );
            }

            // Trigger for Custom Workflow
            controls.forEach(control => {
                this._eventBus.publish(
                    new ControlMarkedInScopeEvent(account, user, control.id, currentWorkspaceId),
                );
            });
        }
        return controls;
    }

    @CacheBusterWithPrefix<Array<Framework>>({
        stores: [
            Caches.USER_BY_ENTRY_ID,
            Caches.LIST_CONTROLS,
            Caches.FRAMEWORK,
            Caches.REQUIREMENTS,
        ],
    })
    async updateEnabledAt(
        requestDto: AccountFrameworksType,
        isMpsEnabled: boolean,
        account: Account,
        supportUser?: User,
    ): Promise<Array<Framework>> {
        const frameworks = await this.frameworkRepository.find({
            relations: ['products'],
        });

        const enableTags = requestDto.enableFrameworkTags || [];
        let frameworkToUpdate: FrameworkToUpdate | null = null;

        const scopedFrameworks = frameworks.filter(
            f => f.tag !== FrameworkTag.CUSTOM && f.tag !== FrameworkTag.NONE,
        );

        const frameworksByTag: Record<number, Framework[]> = scopedFrameworks.reduce(
            (acc, framework) => {
                if (!acc[framework.tag]) {
                    acc[framework.tag] = [];
                }
                acc[framework.tag].push(framework);
                return acc;
            },
            {} as Record<number, Framework[]>,
        );

        for (const [tag, tagFrameworks] of Object.entries(frameworksByTag)) {
            const numericTag = Number(tag);
            const shouldEnable = enableTags.includes(numericTag);

            const allFrameworksMeetEnableCondition = tagFrameworks.every(f => isNil(f.enabledAt));
            const anyFrameworkMeetsDisableCondition = tagFrameworks.some(f => !isNil(f.enabledAt));
            const orphanFramework = tagFrameworks.find(f => f.products.length === 0);

            if (shouldEnable && allFrameworksMeetEnableCondition) {
                frameworkToUpdate = {
                    tag: numericTag,
                    enable: true,
                    orphan: orphanFramework,
                };
            } else if (!shouldEnable && anyFrameworkMeetsDisableCondition) {
                frameworkToUpdate = {
                    tag: numericTag,
                    enable: false,
                    orphan: orphanFramework,
                };
            }
        }

        if (frameworkToUpdate) {
            await this.enableOrDisableFramework(
                frameworkToUpdate,
                account,
                isMpsEnabled,
                supportUser,
            );
        }
        return this.frameworkRepository.getAllFrameworksWithControls();
    }

    /**
     * @param enabledFrameworkTags
     * @param enabledControlsForFrameworkTags
     * @param account
     **/
    async provisionNewFrameworks(
        enabledFrameworkTags: FrameworkTag[],
        enabledControlsForFrameworkTags: FrameworkTag[],
        account: Account,
        user: User,
    ): Promise<void> {
        const primaryProduct = await this.workspacesCoreService.getPrimaryProduct();
        const policies = await this.policiesCoreService.getPoliciesForWorkspaceProvisioning();

        if (isMultiProductEnabled(account)) {
            await this.enableFrameworks(
                account,
                enabledFrameworkTags,
                enabledControlsForFrameworkTags,
                user,
            );

            this._eventBus.publish(
                new IndexMonitorResultControlsAndFrameworksEvent(account, null, null),
            );
        } else {
            await this.provisionFrameworks(
                account,
                enabledControlsForFrameworkTags,
                enabledFrameworkTags,
                policies,
                primaryProduct,
                user,
            );

            this._eventBus.publish(
                new IndexMonitorResultControlsAndFrameworksEvent(account, primaryProduct.id, null),
            );
        }
    }

    /**
     * Re-check the policies<>frameworks map when a framework is re-enabled
     */
    async updatePolicies(frameworks: Framework[]): Promise<void> {
        const policies = await this.policiesCoreService.getPoliciesForWorkspaceProvisioning();
        await associatePoliciesToFrameworks(
            policies,
            frameworks,
            this._tenancyContext.getConnection() as DrataDataSource,
        );
    }

    /**
     *
     * @param account
     * @param enableFrameworkTags
     * @param enabledControlsForFrameworkTags
     */
    async enableFrameworks(
        account: Account,
        enableFrameworkTags: FrameworkTag[],
        enabledControlsForFrameworkTags: FrameworkTag[],
        user: User,
    ): Promise<void> {
        const globalDataSource = await getGlobalDataSource();

        const frameworkTemplates = await this.getFrameworkTemplates(
            globalDataSource,
            enableFrameworkTags,
            false,
        );

        const { releaseFedRamp20x, releaseHiTrust, releaseMssspaV11 } =
            await this.getFrameworkFeatureFlags(account, user);

        for (const frameworkTemplate of frameworkTemplates) {
            if (frameworkTemplate.tag === FrameworkTag.FEDRAMP20X && !releaseFedRamp20x) {
                this.log(
                    `Feature flag not enabled. Skipping ${frameworkTemplate.name} framework...`,
                    account,
                );
                continue;
            }

            if (frameworkTemplate.tag === FrameworkTag.HITRUST && !releaseHiTrust) {
                this.log(
                    `Feature flag not enabled. Skipping ${frameworkTemplate.name} framework...`,
                    account,
                );
                continue;
            }

            if (frameworkTemplate.tag === FrameworkTag.MSSSPA11 && !releaseMssspaV11) {
                this.log(
                    `Feature flag not enabled. Skipping ${frameworkTemplate.name} framework...`,
                    account,
                );
                continue;
            }

            this.log(`Enabling ${frameworkTemplate.name} framework...`, account);

            // eslint-disable-next-line no-await-in-loop
            const frameworkEntity = await frameworkTemplateToFrameworkEntity(
                frameworkTemplate,
                enableFrameworkTags,
                enabledControlsForFrameworkTags,
            );

            // eslint-disable-next-line no-await-in-loop
            await this.frameworkRepository.save(frameworkEntity);

            this.log(`Done enabling ${frameworkTemplate.name} framework.`, account);
        }
    }

    /**
     * @deprecated Use AccountProvisioningOrchestration.provisionFrameworks
     * @param account
     * @param enabledControlsForFrameworkTags
     * @param enableFrameworkTags
     * @param policies
     * @param product
     */
    async provisionFrameworks(
        account: Account,
        enabledControlsForFrameworkTags: FrameworkTag[],
        enableFrameworkTags: FrameworkTag[],
        policies: Policy[],
        product: Product,
        user: User,
    ): Promise<void> {
        this.log('Provisioning frameworks...', account);

        const frameworkTags = map(FrameworkTag, tag => tag);
        const releaseFedRamp20x = await this.featureFlagService.evaluate(
            {
                category: FeatureFlagCategory.NONE,
                name: FeatureFlag.RELEASE_FED_RAMP_20_X,
                defaultValue: false,
            },
            user,
            account,
        );
        if (!releaseFedRamp20x) {
            this.log(
                `Feature flag not enabled. Skipping ${FrameworkTag.FEDRAMP20X} framework...`,
                account,
            );
            enableFrameworkTags = enableFrameworkTags.filter(f => f !== FrameworkTag.FEDRAMP20X);
        }

        const releaseHiTrust = await this.featureFlagService.evaluate(
            {
                category: FeatureFlagCategory.NONE,
                name: FeatureFlag.RELEASE_HITRUST,
                defaultValue: false,
            },
            user,
            account,
        );
        if (!releaseHiTrust) {
            this.log(
                `Feature flag not enabled. Skipping ${FrameworkTag.HITRUST} framework...`,
                account,
            );
            enableFrameworkTags = enableFrameworkTags.filter(f => f !== FrameworkTag.HITRUST);
        }

        const releaseMssspaV11 = await this.featureFlagService.evaluate(
            {
                category: FeatureFlagCategory.NONE,
                name: FeatureFlag.RELEASE_MSSSPA_V11,
                defaultValue: false,
            },
            user,
            account,
        );
        if (!releaseMssspaV11) {
            this.log(
                `Feature flag not enabled. Skipping ${FrameworkTag.MSSSPA11} framework...`,
                account,
            );
            enableFrameworkTags = enableFrameworkTags.filter(f => f !== FrameworkTag.MSSSPA11);
        }

        const globalDataSource = await getGlobalDataSource();

        const frameworkTemplates = await this.getFrameworkTemplates(
            globalDataSource,
            enableFrameworkTags,
            true,
        );

        this.log('Mapping profile details templates to framework templates...', account);
        await mapProfileDetailsTemplatesToFrameworkTemplates(frameworkTemplates, globalDataSource);
        this.log('Done mapping profile details templates to framework templates.', account);

        const auditorFrameworkTypeTemplateRepository = globalDataSource.getRepository(
            AuditorFrameworkTypeTemplate,
        );

        const auditorFrameworkTypeTemplates = await auditorFrameworkTypeTemplateRepository.find({
            relations: ['relatedFramework'],
        });

        if (product.primary) {
            const allFrameworkTemplates = await this.getFrameworkTemplates(
                globalDataSource,
                frameworkTags,
                false,
            );
            await publishFrameworkDisabled(
                this._tenancyContext.getConnection() as DrataDataSource,
                allFrameworkTemplates,
            );
        }

        const autopilotRecipeTemplateRepository =
            globalDataSource.getRepository(AutopilotRecipeTemplate);

        const autopilotRecipeTemplates = await autopilotRecipeTemplateRepository.find({
            order: {
                id: 'ASC',
            },
        });

        const primaryProductId = await this.workspacesCoreService.getPrimaryProductId();

        const primaryFrameworks = isMultiProductEnabled(account)
            ? await this.frameworkRepository.getFrameworksByProduct(primaryProductId)
            : null;

        this.log('Publishing frameworks...', account);
        const { frameworkEntities, sharedControlTestInstances, sharedControlEntities } =
            await publishFrameworks(
                this._tenancyContext.getConnection() as DrataDataSource,
                {
                    account,
                    product,
                    frameworkTemplates,
                    enableFrameworkTags,
                    enabledControlsForFrameworkTags,
                    policies,
                    auditorFrameworkTypeTemplates,
                    primaryFrameworks,
                    adminUsers: [user],
                    skipEvent: true, // we need to wait for the products <> frameworks association
                },
                this._eventBus,
            );
        this.log('Done publishing frameworks.', account);

        this.log(
            `Associating ${size(policies)} policies to frameworks ${map(
                frameworkEntities,
                'name',
            ).join()}`,
            account,
        );
        await associatePoliciesToFrameworks(
            policies,
            frameworkEntities,
            this._tenancyContext.getConnection() as DrataDataSource,
        );
        this.log('Done associating policies to frameworks.', account);

        this.log(`Associating ${size(frameworkEntities)} frameworks to product`, account);
        await associateFrameworksToProduct(
            frameworkEntities,
            product,
            this._tenancyContext.getConnection() as DrataDataSource,
            enableFrameworkTags,
        );
        this.log('Done associating frameworks to product.', account);

        this._eventBus.publish(
            new EvidenceTemplateUpdatedEvent(
                this._tenancyContext.getConnection() as DrataDataSource,
                account,
                product,
                frameworkEntities,
            ),
        );

        this.log(`Associating ${size(sharedControlEntities)} controls to product`, account);
        await associateControlsToProduct(
            sharedControlEntities,
            product,
            this._tenancyContext.getConnection() as DrataDataSource,
        );
        this.log('Done associating controls to product.', account);

        this.log(
            `Associating ${size(sharedControlTestInstances)} controls test instances to product`,
            account,
        );
        await associateControlTestInstancesToProduct(
            sharedControlTestInstances,
            product,
            this._tenancyContext.getConnection() as DrataDataSource,
        );
        this.log('Done associating controls test instances to product.', account);

        this.log('Processing control test instances for active connections...', account);

        const connections = await this.connectionsCoreService.getActiveConnections();

        await asyncForEach(connections, async connection => {
            await this.accountProvisioningOrchestrationService.processControlTestInstancesForConnection(
                account,
                connection,
            );
        });

        this.log('Enabling policy control test instances...', account);

        const policyVersionsCount = await this.policiesCoreService.getPolicyVersionsCount();

        let controlTestInstances = [];

        if (policyVersionsCount > 0) {
            controlTestInstances =
                await this.monitorsCoreService.enablePolicyBasedControlTestInstances(account);
        }

        this.log(`${size(controlTestInstances)} policy control test instances enabled`, account);

        const soc2Frameworks = frameworkEntities.filter(f => f.tag === FrameworkTag.SOC_2);
        if (!isEmpty(soc2Frameworks)) {
            this.log('Processing SOC2 requirements for frameworks...', account);
            await Promise.all(
                soc2Frameworks.map(async frameworkEntity => {
                    try {
                        await this.accountProvisioningOrchestrationService.processSoc2Requirements(
                            frameworkEntity.id,
                            account,
                            user,
                        );
                    } catch (error) {
                        this.error(error, account);
                    }
                }),
            );
        }

        // Release the provision framework lock that was set in auth.service.ts or products.service.ts
        const lockKey = getProvisionFrameworkLockKey(account.id);
        await this._cacheService.releaseLock(lockKey);
        this.logger.log(
            PolloAdapter.acct(`Provisioning recipes to the product`, account).setIdentifier({
                productId: product.id,
                productName: product.name,
            }),
        );

        try {
            await associateRecipesToProduct(
                product,
                account,
                autopilotRecipeTemplates,
                this._tenancyContext.getConnection() as DrataDataSource,
            );
        } catch (e) {
            this.logger.error(
                PolloAdapter.acct(`Failed to provision recipes`, account)
                    .setError(e)
                    .setIdentifier({
                        productId: product.id,
                        productName: product.name,
                    }),
            );
        }
        await this.provisionProfileRequirementsForSpecialFrameworks(
            frameworkEntities,
            account,
            user,
        );

        // Trigger OpenSearch reindexing for newly provisioned framework controls
        if (!isEmpty(sharedControlEntities)) {
            const provisionedControlIds = extractIds(sharedControlEntities);
            this._eventBus.publish(
                new ControlsReindexEvent(account, product.id, provisionedControlIds),
            );
        }

        this.log('Done provisioning frameworks.', account);
    }

    /**
     *
     * @param frameworkId
     * @param account
     * @param user
     */
    @CacheBusterWithPrefix<void>({
        stores: [
            Caches.USER_BY_ENTRY_ID,
            Caches.FRAMEWORK,
            Caches.LIST_CONTROLS,
            Caches.REQUIREMENTS,
        ],
    })
    async resetFrameworkControlMappings(
        frameworkId: number,
        account: Account,
        user: User,
    ): Promise<void> {
        if (!isPositiveInteger(Number(frameworkId)))
            throw new NotFoundException(ErrorCode.ENTITY_NOT_FOUND);

        const framework = await this.frameworkRepository.findOneOrFail({
            where: { id: frameworkId },
            join: {
                alias: 'framework',
                leftJoinAndSelect: {
                    requirementIndexes: 'framework.requirementIndexes',
                    requirement: 'requirementIndexes.requirement',
                    controls: 'requirement.controls',
                    products: 'framework.products',
                },
            },
        });

        if (framework.tag === FrameworkTag.CUSTOM) {
            throw new PreconditionFailedException(
                ErrorCode.FRAMEWORK_IS_CUSTOM_CANNOT_RESET_CONTROL_MAPPINGS,
            );
        }

        const frameworkTemplate =
            await this.frameworkTemplateService.findFrameworkTemplateByFrameworkTagOrFail(
                framework.tag,
            );

        const controlValues: {
            _index: number;
            _controlTemplateIds: number[];
        }[] = [];
        frameworkTemplate.requirementIndexTemplates.map(requirementIndexTemplate => {
            const _index = framework.requirementIndexes.findIndex(requirementIndex => {
                return (
                    requirementIndex.requirement?.name ===
                    requirementIndexTemplate.requirementTemplate.name
                );
            });

            if (_index != -1) {
                // If match found
                const _controlTemplateIds =
                    requirementIndexTemplate.requirementTemplate.controlTemplates.map(
                        controlTemplate => controlTemplate.id,
                    );
                controlValues.push({
                    _index,
                    _controlTemplateIds,
                });
            }
        });

        const controlsToSave: Control[] = [];

        const workspaceId = framework.products[0]?.productId;
        const allRequirementIndexesWithControls = controlValues.map(async controlValue => {
            const { _index, _controlTemplateIds } = controlValue;
            if (!isEmpty(_controlTemplateIds)) {
                const controls =
                    await this.controlRepository.getControlsByTemplateIdsAndWorkspaceId(
                        _controlTemplateIds,
                        workspaceId,
                    );

                let missingControlTemplateIds: number[] = [];

                if (_controlTemplateIds.length > controls.length) {
                    const controlTemplateIdsInTenant = controls.map(c => c.controlTemplateId);
                    missingControlTemplateIds = _controlTemplateIds.filter(c => {
                        return !controlTemplateIdsInTenant.includes(c);
                    });

                    this.log(
                        'Some control templates are missing in tenant prior to framework reset',
                        account,
                        {
                            requirementIndexId: _index,
                            countInTemplate: _controlTemplateIds.length,
                            countInTenant: controls.length,
                            missingTemplateIds: missingControlTemplateIds,
                        },
                    );
                }

                controls.forEach(c => {
                    if (isNil(c.enabledAt)) {
                        c.enabledAt = new Date();
                        controlsToSave.push(c);
                    }
                });

                return {
                    index: _index,
                    controls: controls,
                    missingControlTemplateIds: missingControlTemplateIds,
                };
            } else {
                {
                    return {
                        index: _index,
                        controls: [],
                        missingControlTemplateIds: [],
                    };
                }
            }
        });

        const indexWithControls = await Promise.all(allRequirementIndexesWithControls);

        const indexMissingControls = indexWithControls
            .filter(indexWithControl => !isEmpty(indexWithControl.missingControlTemplateIds))
            .map(missingControls => missingControls.missingControlTemplateIds)
            .flat();

        let indexWithAllControls: {
            index: number;
            controls: Control[];
        }[] = [];

        if (!isEmpty(indexMissingControls)) {
            const uniqueMissingControls = indexMissingControls.filter(
                (control, index) => indexMissingControls.indexOf(control) === index,
            );

            // get the productId from frameworkProducts
            const productIds = framework.products.map(
                productFramework => productFramework.productId,
            );

            const products = await this.workspacesCoreService.getProductsByIds(productIds);

            const templateControlsToProvision =
                await this.controlTemplateService.getControlTemplatesByIds(uniqueMissingControls);

            const provisionedControls = await this.controlService.provisionMissingControlsToTenant(
                templateControlsToProvision,
                products,
            );

            this.logger.log(
                PolloAdapter.acct(
                    `Controls provisioned to tenant for reset framework: ${framework.name}`,
                    account,
                )
                    .setContext(this.constructor.name)
                    .setSubContext(this.resetFrameworkControlMappings.name)
                    .setIdentifier({
                        provisionedControls: provisionedControls.map(control => control.code),
                    }),
            );

            indexWithAllControls = indexWithControls.map(indexWithControl => {
                const { index, controls, missingControlTemplateIds } = indexWithControl;

                if (!isEmpty(missingControlTemplateIds)) {
                    const controlsToAddToIndex: Control[] = [];
                    for (const missingTemplateId of missingControlTemplateIds) {
                        provisionedControls.forEach(newControl => {
                            if (newControl.controlTemplateId === missingTemplateId) {
                                controlsToAddToIndex.push(newControl);
                            }
                        });
                    }
                    const updatedControls = [...controls, ...controlsToAddToIndex];

                    return {
                        index: index,
                        controls: updatedControls,
                    };
                } else {
                    return {
                        index: index,
                        controls: controls,
                    };
                }
            });
        } else {
            indexWithAllControls = indexWithControls;
        }

        const requirements: Requirement[] = [];
        indexWithAllControls.forEach(indexWithControl => {
            const { index, controls } = indexWithControl;
            const requirement = framework.requirementIndexes[index]?.requirement;
            if (!isNil(requirement)) {
                const existingControls = requirement.controls;
                const existingCustomControls = existingControls?.filter(con =>
                    isNil(con.controlTemplateId),
                );
                requirement.controls = !isEmpty(controls)
                    ? [...controls, ...existingCustomControls]
                    : existingCustomControls;
                requirements.push(requirement);
            } else {
                this.log(`Requirement framework mapping is not found, index: ${index}`, account);
            }
        });
        this._eventBus.publish(new MappedControlsResetForFrameworkEvent(account, user, framework));

        // saving at the framework level did not work. So saving at the requirement level
        await this.requirementRepository.save(requirements);
        await this.controlRepository.save(controlsToSave);

        // Trigger OpenSearch reindexing for updated controls
        if (!isEmpty(controlsToSave)) {
            const resetControlIds = extractIds(controlsToSave);
            const frameworkWorkspaceIds =
                framework.products?.map(product => product.productId) || [];

            frameworkWorkspaceIds.forEach(frameworkWorkspaceId => {
                this._eventBus.publish(
                    new ControlsReindexEvent(account, frameworkWorkspaceId, resetControlIds),
                );
            });
        }

        this._eventBus.publish(
            new IndexMonitorResultControlsAndFrameworksEvent(account, null, null),
        );
    }

    /**
     * @deprecated Use FrameworksCoreService.saveFramework
     */
    async saveFramework(framework: Framework): Promise<Framework> {
        return this.frameworkRepository.save(framework);
    }

    /**
     * @deprecated Use FrameworksCoreService.findCustomFrameworkToDelete
     */
    async findCustomFrameworkToDelete(frameworkId: number): Promise<Framework> {
        return this.frameworkRepository.findOneOrFail({
            where: {
                id: frameworkId,
            },
            relations: ['requirementIndexes', 'requirementIndexes.requirement'],
        });
    }

    /**
     * @deprecated Use FrameworksCoreService.softDeleteFramework
     */
    async softDeleteFramework(framework: Framework): Promise<Framework> {
        framework.deletedAt = new Date();
        framework.enabledAt = null;
        return this.frameworkRepository.save(framework);
    }

    /**
     * @deprecated Use FrameworksCoreService.getCustomFramework
     */
    async getCustomFramework(frameworkId: number): Promise<Framework> {
        return this.frameworkRepository.getCustomFrameworkById(frameworkId);
    }

    /**
     * @deprecated Use FrameworksCoreService.getCustomFrameworkNames
     *
     * Get custom framework names
     * @returns List of framework names
     * @deprecated Use FrameworksCoreService.getCustomFrameworkNames
     */
    async getCustomFrameworkNames(): Promise<string[]> {
        return this.frameworkRepository.getCustomFrameworkNames();
    }

    /**
     * @deprecated Use FrameworksCoreService.updateEnablementDate
     *
     * Update custom frameworks
     * @param tag
     * @param enabledAtDate
     */
    async updateEnablementDate(tag: number, enabledAtDate: Date): Promise<void> {
        await this.frameworkRepository.update({ tag }, { enabledAt: enabledAtDate });
    }

    /**
     * @deprecated Use FrameworksCoreService.getCustomFrameworks
     * Get all custom framework
     * @param account Account
     * @param requestDto Request DTO
     * @returns All Custom framework
     */
    async getCustomFrameworks(
        account: Account,
        requestDto: FrameworksRequestDto,
    ): Promise<PaginationType<Framework>> {
        return this.frameworkRepository.listCustomFrameworks(account, requestDto);
    }

    /**
     * @deprecated Use FrameworksCoreService.getFrameworksByFrameworksIds
     */
    async getFrameworksByFrameworksIds(ids: number[]): Promise<Framework[]> {
        if (isEmpty(ids)) {
            return Promise.resolve([]);
        }

        return this.frameworkRepository.getFrameworksByFrameworksIds(ids);
    }

    /**
     * @param frameworkSlugs
     * @returns
     */
    async areFrameWorksEnabled(frameworkTags: FrameworkTag[]): Promise<boolean> {
        if (isEmpty(frameworkTags)) {
            return Promise.resolve(false);
        }

        return this.frameworkRepository.areFrameworksEnabled(frameworkTags);
    }

    /**
     * @deprecated Use FrameworksCoreService.isFrameworkEnabled
     *
     * @param frameworkTag
     * @returns
     */
    async isFrameworkEnabled(frameworkTag: FrameworkTag): Promise<boolean> {
        return this.frameworkRepository.isFrameworkEnabled(frameworkTag);
    }

    async getReadinessStats(
        account: Account,
        dto: FrameworksRequestDto,
        user: User,
    ): Promise<FrameworkReadiness[]> {
        const frameworkResponse = await this.listFrameworks(account, dto);

        const foundUser = await this.usersCoreService.getUserWithFeaturesById(user.id);

        const readyByControl =
            foundUser.features.find(f => f.feature.featureType === FeatureType.READINESS_BY_CONTROL)
                ?.value === 'control';

        return transformFrameworkReadyToFrameworkReadiness(frameworkResponse.data, readyByControl);
    }

    async getFrameworksForNewAudit(
        requestDto: FrameworksForNewAuditRequestDto,
    ): Promise<FrameworkWithControlFlag[]> {
        const { workspaceId, q } = requestDto;
        const frameworks = await this.frameworkRepository.getFrameworksForNewAudit(workspaceId, q);

        if (isEmpty(frameworks)) {
            return [];
        }

        const auditFrameworks = this.flatMapFrameworksForNewAudit(frameworks);

        const filteredFrameworks = this.filterFrameworksForAudit(auditFrameworks, requestDto);

        const frameworksWithControls = await this.frameworkRepository.getFrameworksWithControlsById(
            filteredFrameworks.map(fw => fw.id),
        );

        const frameworksWithControlsIds = frameworksWithControls.map(fw => fw.id);

        return filteredFrameworks.map(framework => ({
            framework,
            hasControls: frameworksWithControlsIds.includes(framework.id),
        }));
    }

    flatMapFrameworksForNewAudit(frameworks: Framework[]): Framework[] {
        return frameworks.flatMap(framework => {
            if (framework.auditorFrameworkTypes?.length) {
                return framework.auditorFrameworkTypes.map(auditorFrameworkType => ({
                    ...framework,
                    auditorFrameworkTypes: [auditorFrameworkType],
                }));
            } else {
                return [{ ...framework, auditorFrameworkTypes: [] }];
            }
        }) as Framework[];
    }

    filterFrameworksForAudit(
        frameworks: Framework[],
        requestDto: FrameworksForNewAuditRequestDto,
    ): Framework[] {
        let auditFrameworks: Framework[] = frameworks;

        if (!isNil(requestDto.auditType)) {
            auditFrameworks = auditFrameworks.filter(frame => {
                return !isFrameworkRestrictedByAuditType(frame.tag, requestDto.auditType);
            });
        }

        return auditFrameworks;
    }

    /**
     * @deprecated Use FrameworksCoreService.getFrameworksDisabled
     *
     * @param account
     * @returns
     */
    async getFrameworksDisabled(account: Account): Promise<FrameworkDisabledReady[]> {
        return this.frameworkReducedRepository.getFrameworkDisabledByProduct(
            getProductId(account),
            account,
        );
    }

    /**
     * @deprecated Use AccountProvisioningOrchestration.getFrameworkTemplates
     */
    async getFrameworkTemplates(
        globalConnection: DrataDataSource,
        frameworkTags: FrameworkTag[],
        full: boolean,
    ): Promise<FrameworkTemplate[]> {
        if (isEmpty(frameworkTags)) {
            return [];
        }

        const frameworkTemplateRepository = getCustomRepository(
            FrameworkTemplateRepository,
            globalConnection,
        );

        const query = frameworkTemplateRepository
            .createQueryBuilder('FrameworkTemplate')
            .innerJoin('FrameworkTemplate.requirementIndexTemplates', 'RequirementIndexTemplate')
            .innerJoin('RequirementIndexTemplate.requirementTemplate', 'RequirementTemplate')
            .leftJoin('RequirementTemplate.controlTemplates', 'ControlTemplate')
            .select([
                'FrameworkTemplate.id',
                'FrameworkTemplate.name',
                'FrameworkTemplate.description',
                'FrameworkTemplate.longDescription',
                'FrameworkTemplate.slug',
                'FrameworkTemplate.tag',
                'FrameworkTemplate.pill',
                'FrameworkTemplate.color',
                'FrameworkTemplate.bgColor',
                'FrameworkTemplate.activeLogo',
                'FrameworkTemplate.inactiveLogo',
                'FrameworkTemplate.levelLabel',
                'FrameworkTemplate.privacy',
                'FrameworkTemplate.hasLevel',
                'FrameworkTemplate.externalId',
                'FrameworkTemplate.hasDynamicControlMapping',
                'RequirementIndexTemplate.id',
                'RequirementIndexTemplate.category',
                'RequirementIndexTemplate.subCategory',
                'RequirementIndexTemplate.topic',
                'RequirementTemplate.id',
                'RequirementTemplate.externalId',
                'RequirementTemplate.name',
                'RequirementTemplate.description',
                'RequirementTemplate.sortOrder',
                'RequirementTemplate.longDescription',
                'RequirementTemplate.additionalInfo',
                'RequirementTemplate.additionalInfo2',
                'RequirementTemplate.additionalInfo3',
                'RequirementTemplate.externalId',
                'RequirementTemplate.parts',
                'RequirementTemplate.params',
                'ControlTemplate.id',
                'ControlTemplate.name',
                'ControlTemplate.description',
                'ControlTemplate.code',
                'ControlTemplate.controlNumber',
                'ControlTemplate.question',
                'ControlTemplate.activity',
                'ControlTemplate.slug',
                'ControlTemplate.domain',
                'ControlTemplate.category',
            ])
            .where('FrameworkTemplate.tag IN (:...frameworkTags)', {
                frameworkTags,
            });

        if (full) {
            query
                .leftJoin(
                    'RequirementIndexTemplate.requirementIndexTagTemplates',
                    'RequirementIndexTagTemplate',
                )
                .leftJoin('ControlTemplate.policyTemplates', 'PolicyTemplate')
                .leftJoin('ControlTemplate.controlTestTemplates', 'ControlTestTemplate')
                .leftJoin('ControlTestTemplate.monitorTemplates', 'MonitorTemplate')
                .leftJoin('MonitorTemplate.monitorTemplateCheckTypes', 'MonitorTemplateCheckType')
                .addSelect([
                    'PolicyTemplate.id',
                    'RequirementIndexTagTemplate.id',
                    'RequirementIndexTagTemplate.tag',
                    'ControlTestTemplate.id',
                    'ControlTestTemplate.testId',
                    'ControlTestTemplate.name',
                    'ControlTestTemplate.description',
                    'ControlTestTemplate.priority',
                    'ControlTestTemplate.source',
                    'ControlTestTemplate.ap2EnabledAt',
                    'ControlTestTemplate.runMode',
                    'MonitorTemplate.id',
                    'MonitorTemplate.name',
                    'MonitorTemplate.checkFrequency',
                    'MonitorTemplate.autopilotTaskType',
                    'MonitorTemplate.failedTestDescription',
                    'MonitorTemplate.evidenceCollectionDescription',
                    'MonitorTemplate.remedyDescription',
                    'MonitorTemplate.url',
                    'MonitorTemplate.requestDescriptions',
                    'MonitorTemplateCheckType.id',
                    'MonitorTemplateCheckType.checkType',
                ]);
        }
        const frameworkTemplates = await query.getMany();

        if (full) {
            await this.mapEvidenceTemplatesToControlTemplates(globalConnection, frameworkTemplates);
        }

        return frameworkTemplates;
    }

    /**
     * @deprecated Use AccountProvisioningOrchestration.mapEvidenceTemplatesToControlTemplates
     * @param globalConnection The global connection used to retrieve global repositories
     * @param frameworkTemplates Array of framework templates for which to map the evidenceTemplate to their control templates
     *
     * Cycles through each control template of each requirement template associated to the framework template passed as a parameter
     * It maps and it assigns to the evidence template property the related evidence template
     */
    async mapEvidenceTemplatesToControlTemplates(
        globalConnection: DrataDataSource,
        frameworkTemplates: FrameworkTemplate[],
    ): Promise<void> {
        const evidenceTemplateRepository = getCustomRepository(
            EvidenceTemplateRepository,
            globalConnection,
        );
        for (const frameworkTemplate of frameworkTemplates) {
            for (const requirementIndexTemplate of frameworkTemplate.requirementIndexTemplates) {
                const requirementTemplate = requirementIndexTemplate.requirementTemplate;
                for (const controlTemplate of requirementTemplate.controlTemplates) {
                    controlTemplate.evidenceTemplates =
                        // eslint-disable-next-line no-await-in-loop
                        await evidenceTemplateRepository.find({
                            where: {
                                controlTemplates: {
                                    id: In([controlTemplate.id]),
                                },
                            },
                        });
                }
            }
        }
    }

    async upgradeFramework(
        account: Account,
        user: User,
        requestDto: UpgradeFrameworkRequestDto,
    ): Promise<void> {
        const currentProductId = account.getCurrentProduct().id;
        const allEnabledFrameworks = await this.frameworkRepository.find({
            where: { enabledAt: Not(IsNull()) },
            relations: ['products'],
        });
        const enabledFrameworks = allEnabledFrameworks.filter(
            eF => !isEmpty(eF.products.filter(p => p.productId === currentProductId)),
        );
        if (enabledFrameworks.map(ef => ef.tag).includes(requestDto.upgradeTag)) {
            throw new BadRequestException('Framework to upgrade to is already enabled');
        }
        checkUpgradePath(allEnabledFrameworks, requestDto.upgradeTag);
        const newTags = [requestDto.upgradeTag];
        const product = await this.workspacesCoreService.getProductById(currentProductId);
        const foundFrameworks = await this.frameworkRepository.find({
            where: { tag: requestDto.upgradeTag },
            relations: ['products'],
        });
        if (!isNil(foundFrameworks)) {
            const foundFramework = foundFrameworks.find(fr =>
                fr.products.find(pr => pr.productId === product.id),
            );
            if (!isNil(foundFramework)) {
                this.log('Updating previously disabled framework', account);
                foundFramework.enabledAt = new Date();
                foundFramework.controlsEnabledAt = new Date();
                await this.frameworkRepository.save(foundFramework);
                await this.updateControlsByFrameworkId(foundFramework.id, account, product.id);
                const productFramework =
                    await this.workspacesFrameworkCoreService.getProductFrameworkByFrameworkId(
                        foundFramework.id,
                    );
                productFramework.enabledAt = new Date();
                await this.workspacesFrameworkCoreService.saveProductFramework(productFramework);
            } else {
                this.log('Provisioning new framework', account);
                const policies =
                    await this.policiesCoreService.getPoliciesForWorkspaceProvisioning();
                this.provisionFrameworks(account, newTags, newTags, policies, product, user).catch(
                    error => {
                        this.error(
                            new Error('Promise rejection in `FrameworkService.upgradeFramework`'),
                            account,
                            { error },
                        );
                    },
                ); // no await, let the long running update happen in the background
            }
        }
    }

    async sendAuditLog(
        currentAdmin: SiteAdmin,
        account: Account,
        allFrameworksBeforeUpdates: Framework[],
    ) {
        const allFrameworksAfterUpdates = await this.getAllFrameworks();

        const createFrameworkStatusMap = (
            frameworks: Framework[],
            enabledAtField: keyof Framework,
            text: string = '',
        ) =>
            frameworks.map(
                framework =>
                    `${text}${framework.name}: ${
                        !isNil(framework[enabledAtField]) ? 'true' : 'false'
                    }`,
            );

        const frameworksBefore = createFrameworkStatusMap(allFrameworksBeforeUpdates, 'enabledAt');
        const frameworksAfter = createFrameworkStatusMap(allFrameworksAfterUpdates, 'enabledAt');

        const controlsBefore = createFrameworkStatusMap(
            allFrameworksBeforeUpdates,
            'controlsEnabledAt',
            'Controls of ',
        );
        const controlsAfter = createFrameworkStatusMap(
            allFrameworksAfterUpdates,
            'controlsEnabledAt',
            'Controls of ',
        );

        const frameworkDifference = frameworksAfter.filter(f => !frameworksBefore.includes(f));

        if (!isEmpty(frameworkDifference)) {
            this._eventBus.publish(
                new AuditLogEvent(
                    currentAdmin,
                    AuditLogEventType.ACCOUNT_FRAMEWORK_UPDATED,
                    AuditLogTargetType.ACCOUNT,
                    account.id,
                    undefined, // target
                    frameworksBefore,
                    frameworksAfter,
                ),
            );
        }

        const controlsDifference = controlsAfter.filter(f => !controlsBefore.includes(f));

        if (!isEmpty(controlsDifference)) {
            this._eventBus.publish(
                new AuditLogEvent(
                    currentAdmin,
                    AuditLogEventType.ACCOUNT_CONTROL_FRAMEWORK_UPDATED,
                    AuditLogTargetType.ACCOUNT,
                    account.id,
                    undefined, // target
                    controlsBefore,
                    controlsAfter,
                ),
            );
        }
    }

    /**
     *
     * @param ids
     * @param key
     * @returns
     */
    private async formatDratameter(ids: number[], key: string): Promise<Dratameter[]> {
        const ret: Dratameter[] = [];

        const query = await this.requirementReadyRepository
            .createQueryBuilder('Ready')
            .addSelect(`Ready.${key}`, key)
            .addSelect('COUNT(Ready.requirementId)', 'num_requirements')
            .addSelect('SUM(Ready.isReady)', 'num_ready_requirements')
            .innerJoin('Ready.requirement', 'Requirement')
            .where('Requirement.archivedAt IS NULL')
            .andWhere(`Ready.${key} IN (:...ids)`, {
                ids,
            })
            .groupBy(key)
            .orderBy(key)
            .getRawMany();

        for (const result of query) {
            const dm = {} as Dratameter;

            dm.id = parseInt(result[key]);
            dm.numInScopeRequirements = parseInt(result.num_requirements);
            dm.numReadyInScopeRequirements = parseInt(result.num_ready_requirements);

            ret.push(dm);
        }

        return ret;
    }

    /**
     *
     * @param requestDto
     * @returns
     */
    private async getControls(requestDto: ArchiveControlRequestDto): Promise<Array<number>> {
        let controlIds = requestDto.controlIds;

        if (requestDto.selectAll) {
            /**
             * We do not require product specific information here so
             * do not send the account object required for filtering
             */
            [controlIds] = await this.index.getControlIds(
                null,
                requestDto as unknown as ControlsRequestDto,
            );
        }

        return controlIds;
    }

    private async willPublishEvent(
        controls: Array<Control>,
        decrement: boolean,
        account: Account,
        user: User,
        rationale: string,
    ): Promise<void> {
        if (!isEmpty(controls)) {
            for (const control of controls) {
                control.description = this.addCompany(control.description, account.companyName);
            }

            decrement
                ? this._eventBus.publish(
                      new ArchiveControlEvent(account, user, controls, rationale),
                  )
                : this._eventBus.publish(new UnarchiveControlEvent(account, user, controls));
        }
    }

    private addCompany(description: string, companyName: string): string {
        return description.replace(/%s/g, companyName);
    }

    private controlNumberSort(a: Control, b: Control): number {
        if (a.controlNumber === b.controlNumber) {
            return a.code.localeCompare(b.code);
        } else if (isNil(a.controlNumber)) {
            return 1;
        } else if (isNil(b.controlNumber)) {
            return -1;
        }
        return a.controlNumber < b.controlNumber ? -1 : 1;
    }

    private async updateControlsByFrameworkId(
        frameworkId: number,
        account?: Account,
        workspaceId?: number,
    ): Promise<void> {
        const controls = await this.controlRepository.find({
            where: {
                controlTemplateId: Not(IsNull()),
            },
            relations: [
                'requirements',
                'requirements.requirementIndex',
                'requirements.requirementIndex.framework',
            ],
        });

        const updatedControls: Control[] = [];
        controls.forEach(control => {
            control.requirements.some(requirement => {
                if (requirement?.requirementIndex?.framework?.id === frameworkId) {
                    control.enabledAt = control.enabledAt ? control.enabledAt : new Date();
                    updatedControls.push(control);
                    return true;
                }
                return false;
            });
        });

        await this.controlRepository.save(controls);

        // Trigger OpenSearch reindexing for updated controls
        if (!isEmpty(updatedControls) && !isNil(account) && !isNil(workspaceId)) {
            const frameworkUpdatedControlIds = extractIds(updatedControls);
            this._eventBus.publish(
                new ControlsReindexEvent(account, workspaceId, frameworkUpdatedControlIds),
            );
        }
    }

    /**
     * @deprecated Use FrameworksOrchestrationService.updateCustomFrameworkLimit
     *
     * @param customFrameworkLimit
     * @returns
     */
    async updateCustomFrameworkLimit(customFrameworkLimit: number): Promise<void> {
        try {
            const customFrameworkIdsToDisable =
                await this.getCustomFrameworkIdsToDisable(customFrameworkLimit);

            if (isEmpty(customFrameworkIdsToDisable)) {
                return;
            }

            await Promise.all([
                this.frameworkRepository.disableCustomFrameworksByIds(customFrameworkIdsToDisable),
                this.workspacesFrameworkCoreService.disableProductFrameworkMapByFrameworkIds(
                    customFrameworkIdsToDisable,
                ),
            ]);
        } catch (error) {
            this.logger.error(
                PolloMessage.msg(
                    'Something went wrong updating the account custom framework limit',
                ).setError(error),
            );
            throw error;
        }
    }

    /**
     * @deprecated Use FrameworksCoreService.getCustomFrameworkIdsToDisable
     *
     * @param customFrameworkLimit
     * @returns
     */
    async getCustomFrameworkIdsToDisable(customFrameworkLimit: number): Promise<number[]> {
        const enabledCustomFrameworks = await this.frameworkRepository.getEnabledCustomFrameworks();

        if (enabledCustomFrameworks.length <= customFrameworkLimit) {
            return [];
        }

        const countDifference = getEntitlementLimitDifference(
            enabledCustomFrameworks.length,
            customFrameworkLimit,
        );

        return enabledCustomFrameworks.slice(-countDifference).map(framework => framework.id);
    }

    /**
     * @deprecated Use FrameworksCoreService.findFrameworksByTagsAndWorkspaceId
     */
    findFrameworksByTagsAndWorkspaceId(
        workspaceId: number,
        tags: FrameworkTag[],
    ): Promise<Framework[]> {
        return this.frameworkRepository.findFrameworksByTagsAndWorkspaceId(workspaceId, tags);
    }

    /**
     * @deprecated Use AccountProvisioningOrchestration.provisionProfileRequirementsForSpecialFrameworks
     */
    private async provisionProfileRequirementsForSpecialFrameworks(
        frameworks: Framework[],
        account: Account,
        user: User,
    ): Promise<void> {
        const filteredSpecialFrameworks = frameworks.filter(f =>
            OscalSupportedFrameworks.includes(f.tag),
        );
        for (const specialFramework of filteredSpecialFrameworks) {
            const { profileSelections } = specialFramework;
            if (!isNil(profileSelections)) {
                const profileSelection = profileSelections[0];
                const switchProfileDto = new SwitchProfileRequestDto();
                switchProfileDto.profileId = profileSelection.profileDetailsId;
                void this.profileRequirementService.switchFrameworkProfile(
                    profileSelection.frameworkId,
                    switchProfileDto,
                    account,
                    user,
                );
            }
        }
    }

    async getEnabledFrameworksUniqBySlug(account: Account): Promise<Framework[]> {
        try {
            const frameworks =
                await this.frameworkRepository.getEnabledFrameworksWithoutReadiness();

            return uniqBy(frameworks, framework => framework.slug);
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(
                    `Something went wrong while getting the enabled frameworks: ${error.message}`,
                    account,
                )
                    .setContext(this.getEnabledFrameworksUniqBySlug.name)
                    .setError(error),
            );
            throw error;
        }
    }

    async buildFrameworkContextTenant(
        frameworkTemplate: FrameworkData,
        logs: FrameworkLog,
    ): Promise<FrameworkContext[]> {
        const { controls, evidence, framework, requirements } = frameworkTemplate;
        const tenantFrameworks: FrameworkContext[] = [];

        const findFrameworks = await this.frameworkRepository.find({
            where: { tag: framework.tag },
            relations: ['products', 'policies'],
        });

        if (findFrameworks.length === 0) {
            logs.info.push(`No framework matching tag ${framework.tag} found to update`);
            return [];
        }

        for (const findFramework of findFrameworks) {
            if (findFramework.products.length === 0) {
                logs.info.push(`No products associated with framework ${findFramework.id}`);
                continue;
            }
            // eslint-disable-next-line no-await-in-loop
            const product = await this.productRepository.findOne({
                where: { id: findFramework.products[0].productId },
            });

            if (!product) {
                logs.info.push(`No product associated with framework ${findFramework.id}`);
                continue;
            }

            const controlCodes = Array.from(new Set(controls.map(c => c.code)));
            // eslint-disable-next-line no-await-in-loop
            const existingControls = await this.controlRepository.find({
                where: { code: In(controlCodes), products: { id: product.id } },
                relations: [
                    'products',
                    'requirements',
                    'requirements.requirementIndex',
                    'requirements.requirementIndex.framework',
                ],
            });

            const foundControlsMap = new Map(
                existingControls.map(control => [
                    control.code,
                    Object.fromEntries(
                        Object.entries(control).filter(([key]) => key !== 'products'),
                    ) as Control,
                ]),
            );

            const requirementNames = requirements.map(req => req.name);
            // eslint-disable-next-line no-await-in-loop
            const existingRequirements = await this.requirementRepository.find({
                where: {
                    name: In(requirementNames),
                    requirementIndex: { framework: { id: findFramework.id } },
                },
                relations: ['controls', 'requirementIndex.framework'],
            });

            const foundRequirementsMap = new Map(existingRequirements.map(r => [r.name, r]));

            const evidenceCodes = new Set(
                Object.values(evidence)
                    .map((e: { evidenceTemplateCode: string }) => e.evidenceTemplateCode)
                    .filter(Boolean),
            );

            // eslint-disable-next-line no-await-in-loop
            const existingEvidence = await this.libraryDocumentRepository.find({
                where: {
                    evidenceTemplateCode: In([...evidenceCodes]),
                },
                relations: ['libraryDocumentControlsMap'],
            });

            const foundEvidenceMap = new Map(
                existingEvidence
                    .filter(doc => doc.evidenceTemplateCode)
                    .map(doc => [doc.evidenceTemplateCode, doc] as [string, LibraryDocument]),
            );

            tenantFrameworks.push({
                framework: findFramework,
                product,
                controls: existingControls,
                requirements: existingRequirements,
                evidence: existingEvidence,
                foundControlsMap,
                foundRequirementsMap,
                foundEvidenceMap,
            });
        }

        return tenantFrameworks;
    }

    async buildFrameworkContextGlobal(frameworkTag: string): Promise<{ template: FrameworkData }> {
        const frameworkTemplate = await this.frameworkTemplateRepository.findOne({
            where: { tag: FrameworkTag[frameworkTag] },
        });

        if (isNil(frameworkTemplate)) {
            throw new Error(`Framework template not found for tag ${frameworkTag}`);
        }

        const requirementIndexTemplates = await this.requirementIndexTemplateRepository.find({
            where: { frameworkTemplate: { id: frameworkTemplate.id } },
            relations: ['requirementTemplate'],
        });
        const requirementIds = requirementIndexTemplates
            .map(reqIndex => reqIndex.requirementTemplate?.id)
            .filter(id => id !== undefined);
        const requirementTemplates = await this.requirementTemplateRepository.find({
            where: { id: In(requirementIds) },
            relations: ['controlTemplates'],
        });

        const controlTemplates = Array.from(
            new Map(
                requirementTemplates
                    .flatMap(req => req.controlTemplates || [])
                    .map(control => [control.code, control]),
            ).values(),
        );

        // FRAMEWORK -> EVIDENCE mappings not tracked in global DB
        const mappedEvidence = getFrameworkEvidenceTemplates(frameworkTag).map(
            evidence => evidence.evidenceTemplateCode,
        );
        const evidenceTemplates = await this.evidenceTemplateRepository.find({
            where: {
                evidenceTemplateCode: In(mappedEvidence),
            },
            relations: ['controlTemplates'],
        });

        // FRAMEWORK -> POLICY mappings not tracked in global DB
        const mappedPolicies = getFrameworkPolicyMappings(frameworkTag);
        const policies = await this.policyTemplateRepository.findBy({
            name: In([...mappedPolicies]),
        });

        return {
            template: new FrameworkData(
                null,
                controlTemplates,
                evidenceTemplates,
                frameworkTemplate,
                requirementTemplates,
                requirementIndexTemplates,
                policies,
                true,
            ),
        };
    }

    async getFrameworkUpdates(
        tenantFramework: Framework,
        frameworkTemplate: FrameworkTemplate,
        compare: string[],
    ): Promise<{ update: { framework: Framework; updates: Partial<Framework> } }> {
        const updates = await compareEntities(
            tenantFramework,
            frameworkTemplate,
            defaultPropertiesComparisonConfig(compare),
        );
        const update = { framework: tenantFramework, updates };

        return { update };
    }

    async saveFrameworkUpdates(
        updateFramework: { framework: Framework; updates: Partial<Framework> },
        logs: FrameworkLog,
    ): Promise<void> {
        const log = {
            action: 'Framework Updated',
            id: updateFramework.framework.id,
            name: updateFramework.framework.name,
            description: updateFramework.framework.description,
            updates: updateFramework.updates,
        };

        await this.frameworkRepository.update(
            updateFramework.framework.id,
            updateFramework.updates,
        );

        logs.framework.push(log);
    }

    async getEvidenceAdditions(
        frameworkContext: FrameworkContext,
        evidenceTemplates: EvidenceTemplate[],
    ): Promise<{
        add: LibraryDocument[];
    }> {
        const processedTemplates = evidenceTemplates.map(template => {
            const templateCopy = { ...template };
            templateCopy.controlTemplates = [...template.controlTemplates];
            return templateCopy;
        });

        const add: LibraryDocument[] = [];

        for (const evidence of processedTemplates) {
            const existingEvidence = frameworkContext.foundEvidenceMap.get(
                evidence.evidenceTemplateCode,
            );
            if (!existingEvidence) {
                const addEvidence = new LibraryDocument();
                // eslint-disable-next-line @typescript-eslint/no-unused-vars
                const { id, controlTemplates, ...otherProps } = evidence;
                Object.assign(addEvidence, otherProps);

                addEvidence.libraryDocumentControlsMap = controlTemplates
                    .map(controlTemplate => {
                        const foundControl = frameworkContext.foundControlsMap.get(
                            controlTemplate.code,
                        );
                        if (!foundControl) {
                            return undefined;
                        }
                        const addMap = new LibraryDocumentControlsMap();
                        addMap.libraryDocument = addEvidence;
                        addMap.createdAt = new Date();
                        addMap.control = foundControl;
                        return addMap;
                    })
                    .filter(
                        (controlMap): controlMap is LibraryDocumentControlsMap =>
                            controlMap !== undefined,
                    );
                add.push(addEvidence);
            }
        }

        return { add };
    }

    async saveNewEvidence(
        evidence: LibraryDocument[],
        frameworkContext: FrameworkContext,
        logs: FrameworkLog,
    ): Promise<void> {
        const evidenceProductMappings: LibraryDocumentProductsMap[] = [];
        const evidenceWorkflows: LibraryDocumentWorkflow[] = [];
        const evidenceVersions: LibraryDocumentVersion[] = [];
        const evidenceRenewals: LibraryDocumentRenewalSchema[] = [];

        evidence.forEach(document => {
            const evidenceProductMapping = new LibraryDocumentProductsMap();
            evidenceProductMapping.product = frameworkContext.product;
            evidenceProductMapping.libraryDocument = document;
            evidenceProductMapping.createdAt = new Date();
            evidenceProductMappings.push(evidenceProductMapping);

            const evidenceWorkflow = new LibraryDocumentWorkflow();
            evidenceWorkflow.type = LibraryDocumentWorkflowType.EVIDENCE;
            evidenceWorkflow.libraryDocument = document;
            evidenceWorkflow.createdAt = new Date();
            evidenceWorkflows.push(evidenceWorkflow);

            const evidenceVersion = new LibraryDocumentVersion();
            evidenceVersion.type = LibraryDocumentVersionType.NONE;
            evidenceVersion.libraryDocument = document;
            evidenceVersion.current = true;
            evidenceVersion.version = 1;
            evidenceVersion.createdAt = new Date();
            evidenceVersion.source = '';
            evidenceVersions.push(evidenceVersion);

            const evidenceRenewal = new LibraryDocumentRenewalSchema();
            evidenceRenewal.libraryDocumentWorkflow = evidenceWorkflow;
            evidenceRenewal.libraryDocumentVersion = evidenceVersion;
            evidenceRenewal.renewalDate = null;
            evidenceRenewal.renewalScheduleType = RenewalScheduleType.NONE;
            evidenceRenewals.push(evidenceRenewal);
        });

        frameworkContext.evidence = await this.libraryDocumentRepository.save(evidence);
        await this.libraryDocumentProductMapRepository.save(evidenceProductMappings);
        logs.evidence.push(
            ...frameworkContext.evidence.map(document => ({
                action: 'Evidence Added',
                id: document.id,
                name: document.name,
                code: document.evidenceTemplateCode,
            })),
        );

        const evidenceControlMappings = evidence.flatMap(
            doc => doc.libraryDocumentControlsMap || [],
        );
        if (evidenceControlMappings.length > 0) {
            const addedEvidenceControlMappings =
                await this.libraryDocumentControlsMapRepository.save(evidenceControlMappings);
            logs.evidence.push(
                ...addedEvidenceControlMappings.map(mapping => ({
                    action: 'Evidence Control Mapping Added',
                    id: mapping.id,
                    evidence: mapping.libraryDocument.evidenceTemplateCode,
                    control: mapping.control.code,
                })),
            );
        }

        if (evidenceWorkflows.length > 0) {
            await this.libraryDocumentWorkflowRepository.save(evidenceWorkflows);
        }
        if (evidenceVersions.length > 0) {
            await this.libraryDocumentVersionRepository.save(evidenceVersions);
        }
        if (evidenceRenewals.length > 0) {
            await this.libraryDocumentRenewalSchemaRepository.save(evidenceRenewals);
        }
    }

    async getRequirementAdditionsAndUpdates(
        frameworkContext: FrameworkContext,
        requirementTemplates: RequirementTemplate[],
        compare: string[],
    ): Promise<{
        add: Requirement[];
        update: { requirement: Requirement; updates: Partial<Requirement> }[];
    }> {
        const processedTemplates = requirementTemplates.map(template => {
            const templateCopy = { ...template };
            templateCopy.controlTemplates = [...template.controlTemplates];
            return templateCopy;
        });

        const add: Requirement[] = [];
        const update: {
            requirement: Requirement;
            updates: Partial<Requirement>;
        }[] = [];

        for (const req of processedTemplates) {
            const existingRequirement = frameworkContext.foundRequirementsMap.get(req.name);

            if (!existingRequirement) {
                const addRequirement = new Requirement();
                // eslint-disable-next-line @typescript-eslint/no-unused-vars
                const { id, controlTemplates, ...otherProps } = req;
                Object.assign(addRequirement, otherProps);

                addRequirement.controls = controlTemplates
                    .map(controlTemplate => {
                        const foundControl = frameworkContext.foundControlsMap.get(
                            controlTemplate.code,
                        );
                        if (!foundControl) {
                            return undefined;
                        }
                        return foundControl;
                    })
                    .filter((control): control is Control => control !== undefined);
                add.push(addRequirement);
            } else {
                // eslint-disable-next-line no-await-in-loop
                const updates = await compareEntities(
                    existingRequirement,
                    req,
                    requirementPropertiesComparisonConfig(compare),
                );

                if ('controlTemplates' in updates && updates.controlTemplates) {
                    const controlTemplates = updates.controlTemplates as ControlTemplate[];

                    const controls = controlTemplates
                        .map(controlTemplate => {
                            const foundControl = frameworkContext.foundControlsMap.get(
                                controlTemplate.code,
                            );
                            if (!foundControl) {
                                return undefined;
                            }
                            return foundControl;
                        })
                        .filter((control): control is Control => control !== undefined);

                    delete updates.controlTemplates;
                    updates['controls'] = controls;
                }

                if (Object.keys(updates).length > 0) {
                    update.push({
                        requirement: existingRequirement,
                        updates: updates,
                    });
                }
            }
        }

        return { add, update };
    }

    async saveNewRequirements(
        newRequirements: Requirement[],
        frameworkContext: FrameworkContext,
        logs: FrameworkLog,
    ): Promise<void> {
        const requirementNames = new Set(newRequirements.map(req => req.name));
        const orphanRequirements = await this.requirementRepository.find({
            where: {
                name: In([...requirementNames]),
            },
            relations: ['controls', 'requirementIndex'],
        });

        if (!isNil(orphanRequirements) && orphanRequirements.length > 0) {
            newRequirements.forEach(requirement => {
                const matchingOrphan = orphanRequirements.find(
                    orphan =>
                        orphan.name === requirement.name &&
                        orphan.description === requirement.description &&
                        isNil(orphan.requirementIndex),
                );
                if (matchingOrphan) {
                    requirement.id = matchingOrphan.id;
                }
            });
        }

        frameworkContext.requirements = await this.requirementRepository.save(newRequirements);
        logs.requirements.push(
            ...frameworkContext.requirements.map(requirement => ({
                action: 'Requirement Added',
                id: requirement.id,
                name: requirement.name,
                controls: requirement.controls?.map(control => control.code).join(', '),
            })),
        );
    }

    async saveRequirementUpdates(
        requirements: { requirement: Requirement; updates: Partial<Requirement> }[],
        logs: FrameworkLog,
    ): Promise<void> {
        for (const { requirement, updates } of requirements) {
            Object.assign(requirement, updates);
        }

        const log = requirements.map(({ requirement }) => ({
            action: 'Requirement Updated',
            id: requirement.id,
            name: requirement.name,
            controls: requirement.controls?.map(control => control.code).join(', '),
        }));

        await this.requirementRepository.save(requirements.map(req => req.requirement));
        logs.requirements.push(...log);
    }

    async saveNewRequirementIndexes(
        addedRequirements: Map<string, Requirement>,
        requirementIndexTemplates: RequirementIndexTemplate[],
        tenantFramework: Framework,
        logs: FrameworkLog,
    ): Promise<void> {
        const add: RequirementIndex[] = [];
        requirementIndexTemplates.forEach(reqIndex => {
            const existingReq = reqIndex.requirementTemplate[0];
            const matchedRequirement = addedRequirements.get(existingReq.name);

            if (matchedRequirement) {
                const newIndex = new RequirementIndex();
                newIndex.topic = reqIndex?.topic;
                newIndex.category = reqIndex?.category;
                newIndex.subCategory = reqIndex?.subCategory;
                newIndex.framework = tenantFramework;
                newIndex.requirement = matchedRequirement;
                add.push(newIndex);
            }
        });

        if (add.length > 0) {
            const newIndexes = await this.requirementIndexRepository.save(add);
            logs.requirements.push(`Saved ${newIndexes.length} new indexes for requirements`);
        }
    }

    async enableOrDisableFramework(
        frameworkToUpdate: FrameworkToUpdate,
        account: Account,
        isMpsEnabled: boolean,
        supportUser: User,
    ): Promise<void> {
        const logs = createEmptyFrameworkLog();
        const primaryProduct = await this.workspacesCoreService.getPrimaryProduct();

        const { template: frameworkSeed } = await this.buildFrameworkContextGlobal(
            FrameworkTag[frameworkToUpdate.tag],
        );

        const tenantFrameworks = await this.buildFrameworkContextTenant(frameworkSeed, logs);

        if (frameworkToUpdate.orphan) {
            tenantFrameworks.push({
                controls: [],
                evidence: [],
                framework: frameworkToUpdate.orphan,
                product: null,
                requirements: [],
                foundControlsMap: new Map(),
                foundRequirementsMap: new Map(),
                foundEvidenceMap: new Map(),
            });
        }

        if (tenantFrameworks.length === 0) {
            this.log(`No framework matching tag ${frameworkToUpdate.tag} found to update`);
            return;
        }

        const configs = FRAMEWORK_PROVISIONING_CONFIG;
        const frameworksToSave: Framework[] = [];
        const productFrameworksToSave: ProductFramework[] = [];

        for (const tenantFramework of tenantFrameworks) {
            /**
             * DISABLE FRAMEWORK
             */
            if (!frameworkToUpdate.enable) {
                //eslint-disable-next-line no-await-in-loop
                await this.controlService.disableControls(tenantFramework, logs);
                this._eventBus.publish(
                    new ControlsDisabledEvent(account, tenantFramework.controls, supportUser),
                );

                tenantFramework.framework.enabledAt = null;
                tenantFramework.framework.controlsEnabledAt = null;
                tenantFramework.framework.updatedAt = new Date();

                frameworksToSave.push(tenantFramework.framework);
                continue;
            }

            /**
             * ENABLE FRAMEWORK
             */
            tenantFramework.framework.enabledAt ??= new Date();
            tenantFramework.framework.controlsEnabledAt ??= new Date();
            tenantFramework.framework.updatedAt ??= new Date();

            frameworksToSave.push(tenantFramework.framework);

            const hasEnabledProductMapping = tenantFramework.framework.products?.some(
                productFramework => !isNil(productFramework.enabledAt),
            );
            const updateFrameworkData = !isMpsEnabled || hasEnabledProductMapping;

            if (!isMpsEnabled) {
                if (isNil(tenantFramework.product)) {
                    const productFramework = Object.assign(new ProductFramework(), {
                        framework: tenantFramework.framework,
                        product: primaryProduct,
                        enabledAt: new Date(),
                    });
                    productFrameworksToSave.push(productFramework);
                    tenantFramework.product = primaryProduct;
                } else {
                    tenantFramework.framework.products[0].enabledAt ??= new Date();
                    productFrameworksToSave.push(tenantFramework.framework.products[0]);
                }
            }

            if (!updateFrameworkData) {
                continue;
            }
            const hasPolicyUpdates = configs.framework.updates.includes('policyTemplates');
            const frameworkUpdates = [...(configs.framework.updates || [])].filter(
                item => item !== 'policyTemplates',
            );

            // -------- PROCESS FRAMEWORK UPDATES --------
            if (configs.framework.add || configs.framework.updates.length > 0) {
                //eslint-disable-next-line no-await-in-loop
                const { update: updateFramework } = await this.getFrameworkUpdates(
                    tenantFramework.framework,
                    frameworkSeed.framework,
                    frameworkUpdates,
                );

                if (updateFramework.updates && configs.framework.updates.length > 0) {
                    //eslint-disable-next-line no-await-in-loop
                    await this.saveFrameworkUpdates(updateFramework, logs);
                }
            }

            // -------- PROCESS POLICY UPDATES --------
            if (hasPolicyUpdates) {
                //eslint-disable-next-line no-await-in-loop
                await this.policiesCoreService.updateFrameworkPolicyMappings(
                    tenantFramework.framework,
                    frameworkSeed.policies,
                    logs,
                );
            }

            // -------- PROCESS CONTROL ADDITIONS & UPDATES --------
            if (configs.controls.add || configs.controls.updates.length > 0) {
                const { add: addControls, update: updateControls } =
                    //eslint-disable-next-line no-await-in-loop
                    await this.controlService.getControlAdditionsAndUpdates(
                        tenantFramework.foundControlsMap,
                        frameworkSeed.controls,
                        configs.controls.updates,
                    );

                if (configs.controls.add && addControls.length > 0) {
                    //eslint-disable-next-line no-await-in-loop
                    await this.controlService.saveNewControls(addControls, tenantFramework, logs);
                    tenantFramework.controls.forEach(control =>
                        tenantFramework.foundControlsMap.set(control.code, control),
                    );
                }

                if (configs.controls.updates.length > 0 && updateControls.length > 0) {
                    //eslint-disable-next-line no-await-in-loop
                    await this.controlService.saveControlUpdates(
                        updateControls,
                        logs,
                        account,
                        tenantFramework.product?.id,
                    );
                }
            }

            // -------- PROCESS REQUIREMENT/INDEX ADDITIONS & UPDATES --------
            if (configs.requirements.add || configs.requirements.updates.length > 0) {
                const { add: addRequirements, update: updateRequirements } =
                    //eslint-disable-next-line no-await-in-loop
                    await this.getRequirementAdditionsAndUpdates(
                        tenantFramework,
                        frameworkSeed.requirements,
                        configs.requirements.updates,
                    );

                if (configs.requirements.add && addRequirements.length > 0) {
                    //eslint-disable-next-line no-await-in-loop
                    await this.saveNewRequirements(addRequirements, tenantFramework, logs);

                    const addedRequirements = new Map(
                        tenantFramework.requirements.map(req => [req.name, req]),
                    );

                    if (configs.requirementIndexes.add) {
                        //eslint-disable-next-line no-await-in-loop
                        await this.saveNewRequirementIndexes(
                            addedRequirements,
                            frameworkSeed.requirementIndexes,
                            tenantFramework.framework,
                            logs,
                        );
                    }
                }

                if (configs.requirements.updates.length > 0 && updateRequirements.length > 0) {
                    //eslint-disable-next-line no-await-in-loop
                    await this.saveRequirementUpdates(updateRequirements, logs);
                }
            }

            // -------- PROCESS EVIDENCE ADDITIONS & UPDATES --------
            if (configs.evidence.add) {
                const { add: addEvidence } =
                    //eslint-disable-next-line no-await-in-loop
                    await this.getEvidenceAdditions(tenantFramework, frameworkSeed.evidence);

                if (addEvidence.length > 0) {
                    //eslint-disable-next-line no-await-in-loop
                    await this.saveNewEvidence(addEvidence, tenantFramework, logs);

                    this._eventBus.publish(
                        new EvidenceTemplateUpdatedEvent(
                            this._tenancyContext.getConnection() as DrataDataSource,
                            account,
                            undefined,
                            [tenantFramework.framework],
                        ),
                    );
                }
            }
        }

        if (frameworksToSave.length > 0) {
            await this.frameworkRepository.save(frameworksToSave);
            logs.framework.push(
                ...frameworksToSave.map(framework => ({
                    action: frameworkToUpdate.enable ? 'Framework Enabled' : 'Framework Disabled',
                    id: framework.id,
                    name: framework.name,
                    enabledAt: framework.enabledAt,
                    controlsEnabledAt: framework.controlsEnabledAt,
                })),
            );
        }
        if (productFrameworksToSave.length > 0) {
            await this.workspacesFrameworkCoreService.saveProductFrameworks(
                productFrameworksToSave,
            );
            logs.framework.push(
                ...productFrameworksToSave.map(productFramework => ({
                    action: frameworkToUpdate.enable
                        ? 'Product Framework Enabled'
                        : 'Product Framework Disabled',
                    id: productFramework.id,
                    product: productFramework.productId,
                    framework: productFramework.frameworkId,
                    enabledAt: productFramework.enabledAt,
                })),
            );
        }

        if (frameworkToUpdate.enable) {
            this._eventBus.publish(
                new FrameworkEnabledEvent(account, frameworkToUpdate.tag, supportUser),
            );
        } else {
            this._eventBus.publish(
                new FrameworkDisabledEvent(account, frameworkToUpdate.tag, supportUser),
            );
        }

        const allRequirements = await this.requirementRepository.find({
            where: {
                requirementIndex: { framework: { tag: frameworkToUpdate.tag } },
            },
            relations: [
                'controls.requirements',
                'controls.requirements.requirementIndex',
                'controls.requirements.requirementIndex.framework',
            ],
        });

        let controlsToUpdate: Control[] = [];

        if (!isEmpty(allRequirements)) {
            controlsToUpdate =
                (await this.controlService.syncControlScopeOnFrameworkUpdate(allRequirements)) ||
                [];
        }

        const archivedControls = controlsToUpdate.filter(control => !isNil(control.archivedAt));
        const unarchivedControls = controlsToUpdate.filter(control => isNil(control.archivedAt));

        if (!isEmpty(archivedControls)) {
            this._eventBus.publish(
                new ControlsArchivedEvent(account, archivedControls, supportUser),
            );
        }

        if (!isEmpty(unarchivedControls)) {
            this._eventBus.publish(
                new ControlsUnarchivedEvent(account, unarchivedControls, supportUser),
            );
        }

        this.log(
            `Framework enablement operations performed on tenant:\n${JSON.stringify(
                logs,
                null,
                2,
            )}`,
        );
    }

    private async getFrameworkFeatureFlags(
        account: Account,
        user: User,
    ): Promise<{
        releaseFedRamp20x: boolean;
        releaseHiTrust: boolean;
        releaseMssspaV11: boolean;
    }> {
        const [releaseFedRamp20x, releaseHiTrust, releaseMssspaV11] = await Promise.all([
            this.featureFlagService.evaluate(
                {
                    category: FeatureFlagCategory.NONE,
                    name: FeatureFlag.RELEASE_FED_RAMP_20_X,
                    defaultValue: false,
                },
                user,
                account,
            ),
            this.featureFlagService.evaluate(
                {
                    category: FeatureFlagCategory.NONE,
                    name: FeatureFlag.RELEASE_HITRUST,
                    defaultValue: false,
                },
                user,
                account,
            ),
            this.featureFlagService.evaluate(
                {
                    category: FeatureFlagCategory.NONE,
                    name: FeatureFlag.RELEASE_MSSSPA_V11,
                    defaultValue: false,
                },
                user,
                account,
            ),
        ]);
        return { releaseFedRamp20x, releaseHiTrust, releaseMssspaV11 };
    }
    private get frameworkRepository(): FrameworkRepository {
        return this.getCustomTenantRepository(FrameworkRepository);
    }

    private get controlRepository(): ControlRepository {
        return this.getCustomTenantRepository(ControlRepository);
    }

    private get requirementRepository(): RequirementRepository {
        return this.getCustomTenantRepository(RequirementRepository);
    }

    private get index(): RequirementIndexViewRepository {
        return this.getCustomTenantRepository(RequirementIndexViewRepository);
    }

    private get requirementIndexTagRepository(): RequirementIndexTagRepository {
        return this.getCustomTenantRepository(RequirementIndexTagRepository);
    }

    private get requirementIndexRepository(): RequirementIndexRepository {
        return this.getCustomTenantRepository(RequirementIndexRepository);
    }

    private get frameworkIsReadyIndexViewRepository(): FrameworkIsReadyIndexViewRepository {
        return this.getCustomTenantRepository(FrameworkIsReadyIndexViewRepository);
    }

    private get frameworkReducedRepository(): FrameworkReducedRepository {
        return this.getCustomTenantRepository(FrameworkReducedRepository);
    }

    private get profileRequirementsToControlsMapRepository(): ProfileRequirementsToControlsMapRepository {
        return this.getCustomTenantRepository(ProfileRequirementsToControlsMapRepository);
    }

    private get profileSelectionRepository(): ProfileSelectionRepository {
        return this.getCustomTenantRepository(ProfileSelectionRepository);
    }

    private get requirementReadyRepository(): Repository<RequirementIsReadyView> {
        return this.getTenantRepository(RequirementIsReadyView);
    }

    private get controlReadyRepository(): Repository<ControlIsReadyView> {
        return this.getTenantRepository(ControlIsReadyView);
    }

    private get auditorFrameworkTypeRepository(): Repository<AuditorFrameworkType> {
        return this.getTenantRepository(AuditorFrameworkType);
    }

    private get libraryDocumentRepository(): LibraryDocumentRepository {
        return this.getCustomTenantRepository(LibraryDocumentRepository);
    }

    private get libraryDocumentControlsMapRepository(): LibraryDocumentControlsMapRepository {
        return this.getCustomTenantRepository(LibraryDocumentControlsMapRepository);
    }

    private get libraryDocumentProductMapRepository(): Repository<LibraryDocumentProductsMap> {
        return this.getTenantRepository(LibraryDocumentProductsMap);
    }

    private get libraryDocumentWorkflowRepository(): Repository<LibraryDocumentWorkflow> {
        return this.getTenantRepository(LibraryDocumentWorkflow);
    }
    private get libraryDocumentVersionRepository(): Repository<LibraryDocumentVersion> {
        return this.getTenantRepository(LibraryDocumentVersion);
    }
    private get libraryDocumentRenewalSchemaRepository(): Repository<LibraryDocumentRenewalSchema> {
        return this.getTenantRepository(LibraryDocumentRenewalSchema);
    }

    private get productRepository(): ProductRepository {
        return this.getCustomTenantRepository(ProductRepository);
    }
}
