import { AuditType } from '@drata/enums';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsOptional, IsString } from 'class-validator';
import { IsPositiveInt } from 'commons/decorators/is-positive-int.decorator';
import { IsValidEnum } from 'commons/decorators/is-valid-enum.decorator';
import { RequestDto } from 'commons/dtos/request.dto';

export class FrameworksForNewAuditRequestDto extends RequestDto {
    @ApiProperty({
        type: 'string',
        description: 'Filter data by searching for framework names',
        required: false,
    })
    @IsOptional()
    @IsString()
    q?: string;

    @ApiProperty({
        type: 'number',
        example: 1,
        description: 'ID of the Workspace.',
        required: true,
    })
    @IsPositiveInt()
    @Type(() => Number)
    workspaceId: number;

    @ApiProperty({
        nullable: true,
        default: AuditType[AuditType.FULL_AUDIT],
        description: 'Which audit type to filter by. If not specified returns all types.',
        required: false,
        enum: AuditType,
    })
    @IsValidEnum(AuditType, { required: false })
    @IsOptional()
    auditType?: AuditType | null;
}
