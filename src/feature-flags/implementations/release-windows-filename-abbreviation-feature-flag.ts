import { Injectable } from '@nestjs/common';
import { Account } from 'auth/entities/account.entity';
import { FeatureFlagCategory } from 'feature-flags/enums/feature-flag-category.enum';
import { FeatureFlag } from 'feature-flags/feature-flag.enum';
import { FeatureFlagService } from 'feature-flags/feature-flag.service';
import { FeatureFlagType } from 'feature-flags/types/feature-flag.type';

@Injectable()
export class ReleaseFilenameAbbreviation {
    private readonly flag: FeatureFlagType<boolean>;

    constructor(private readonly featureFlagService: FeatureFlagService) {
        this.flag = {
            category: FeatureFlagCategory.NONE,
            defaultValue: false,
            name: FeatureFlag.RELEASE_FILENAME_ABBREVIATION,
        };
    }

    async isFeatureEnabled(account: Account): Promise<boolean> {
        return this.featureFlagService.evaluateAsTenant(this.flag, account);
    }
}
