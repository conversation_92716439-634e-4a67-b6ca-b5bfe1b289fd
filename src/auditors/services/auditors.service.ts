// @spell-checker: words personnels audithub bufers
/* eslint-disable no-await-in-loop */
import {
    AgentPlatform,
    AuditorFrameworkType as AuditorFrameworkTypeEnum,
    AuditType,
    AuthModes,
    EmploymentStatus,
    ErrorCode,
    EventCategory,
    EventType,
    FrameworkTag,
} from '@drata/enums';
import { GoneException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { FULFILLED, REJECTED } from 'app/audit-hub/constants/audit-packages.constants';
import { ControlEvidenceWaasService } from 'app/audit-hub/evidence-waas-service/audithub-control-evidence-waas.service';
import { getEvidenceDirectory } from 'app/audit-hub/helpers/audit-packages.helper';
import { getAuditPeriodByFramework } from 'app/audit-hub/helpers/audit.helper';
import { AuditHubAuditPackagesOrchestrationService } from 'app/audit-hub/services/audithub-audit-packages/audit-hub-audit-packages-orchestration.service';
import { AuditAuditorPersonnelRequestAuditorApiDto } from 'app/auditor/auditor-api/dtos/audit-auditor-personnel-request.auditor-api.dto';
import { AuditorControlEvidencePackageRequestAuditorApiDto } from 'app/auditor/auditor-api/dtos/auditor-control-evidence-package-request.auditor-api.dto';
import { AuditorSampleSetupRequestAuditorApiDto } from 'app/auditor/auditor-api/dtos/auditor-sample-setup-request.auditor-api.dto';
import { CompanyArchive } from 'app/companies/entities/company-archive.entity';
import { Company } from 'app/companies/entities/company.entity';
import { CompanyOrchestrationService } from 'app/companies/orchestration/company-orchestration.service';
import { ProductFrameworkRepository } from 'app/companies/products/repositories/product-framework.repository';
import { WorkspacesCoreService } from 'app/companies/products/services/workspaces-core.service';
import { CompaniesCoreService } from 'app/companies/services/companies-core.service';
import { CompaniesOrchestrationService } from 'app/companies/services/companies-orchestration.service';
import { CompanyArchivesCoreService } from 'app/companies/services/company-archives-core.service';
import { CreateEventCoreService } from 'app/create-event/create-event-core.service';
import { CreateEvent } from 'app/create-event/decorators/create-event.decorator';
import { AuditorFrameworkListView } from 'app/customer-request/entities/auditor-framework-list-view.entity';
import { AuditorFrameworkRequestsSummaryView } from 'app/customer-request/entities/auditor-framework-requests-summary-view.entity';
import { AuditorFrameworkSampleArchiveMap } from 'app/customer-request/entities/auditor-framework-sample-archive-map.entity';
import { AuditorFrameworkSamplePersonnelMap } from 'app/customer-request/entities/auditor-framework-sample-personnel-map.entity';
import { AuditorFrameworkSample } from 'app/customer-request/entities/auditor-framework-sample.entity';
import { CustomerRequestListViewRepository } from 'app/customer-request/repositories/customer-request-list-view.repository';
import { CustomerRequestRepository } from 'app/customer-request/repositories/customer-request.repository';
import { CustomerRequestOrchestrationService } from 'app/customer-request/services/customer-request-orchestration.service';
import { AuditorFrameworkSummaryResponseType } from 'app/customer-request/types/auditor-framework-requests-summary-view-response.type';
import { FrameworksCoreService } from 'app/frameworks/services/frameworks-core.service';
import { RequirementsCoreService } from 'app/frameworks/services/requirements-core.service';
import { Control } from 'app/grc/entities/control.entity';
import { GrcEvidenceDownloadOrchestrationService } from 'app/grc/orchestration/grc-evidence-download-orchestration.service';
import { ControlRepository } from 'app/grc/repositories/control.repository';
import { TrustCenterRequestRepository } from 'app/trust-center/repositories/trust-center-request.repository';
import { UserRole } from 'app/users/entities/user-role.entity';
import { User } from 'app/users/entities/user.entity';
import { Personnel } from 'app/users/personnel/entities/personnel.entity';
import { PersonnelOrchestrationService } from 'app/users/personnel/orchestration/personnel/personnel-orchestration.service';
import { PersonnelRepository } from 'app/users/personnel/repositories/personnel.repository';
import { PersonnelCoreService } from 'app/users/personnel/services/personnel-core.service';
import { UserRoleRepository } from 'app/users/repositories/user-role.repository';
import { UserRepository } from 'app/users/repositories/user.repository';
import { UsersCoreService } from 'app/users/services/users-core.service';
import { AuditFrameworkControlsRequestDto } from 'auditors/dtos/audit-framework-controls-request.dto';
import { AuditFrameworkDownloadOnlyUpdateRequestDto } from 'auditors/dtos/audit-framework-download-only-update-request.dto';
import { AuditOwnersRequestDto } from 'auditors/dtos/audit-owners-request.dto';
import { AuditorAuditsRequestDto } from 'auditors/dtos/auditor-audits-request.dto';
import { OldAuditorsClientsRequestDto } from 'auditors/dtos/auditor-clients-request.dto';
import { AuditorControlEvidencePackageRequestDto } from 'auditors/dtos/auditor-control-evidence-package-request.dto';
import { AuditorFirmsRequestDto } from 'auditors/dtos/auditor-firms-request.dto';
import { AuditorFrameworkTimeframeRequestDto } from 'auditors/dtos/auditor-framework-timeframe-request.dto';
import { DeleteAuditorFrameworkRequestDto } from 'auditors/dtos/auditor-frameworkId-delete.dto';
import { AuditorListRequestDto } from 'auditors/dtos/auditor-list-request.dto';
import { AuditorPersonnelRequestDto } from 'auditors/dtos/auditor-personnel-request.dto';
import { AuditorRequestControlEvidencePackageRequestDto } from 'auditors/dtos/auditor-request-control-evidence-package-request.dto';
import { OldAuditorRequestDto } from 'auditors/dtos/auditor-request.dto';
import { AuditorToggleRequestDto } from 'auditors/dtos/auditor-toggle-request.dto';
import { AuditFirm } from 'auditors/entities/audit-firm.entity';
import { AuditorClient } from 'auditors/entities/auditor-client.entity';
import { AuditorFrameworkAuditors } from 'auditors/entities/auditor-framework-auditors.entity';
import { AuditorFrameworkCompanyArchiveTypeMap } from 'auditors/entities/auditor-framework-company-archive-type.map';
import { AuditorFrameworkTypeNames } from 'auditors/entities/auditor-framework-type-names.map';
import { Auditor } from 'auditors/entities/auditor.entity';
import { FrameworkTypeTags } from 'auditors/entities/framework-type-tags.map';
import {
    areSampleDatesWithinAuditorFrameworkPeriod,
    throwInvalidAuditorsException,
    validateAuditor,
    validateAuditorFramework,
    validateControlEvidencePackagePersonnel,
} from 'auditors/helpers/auditor-framework-validator.helper';
import { AuditorAddedToAuditEvent } from 'auditors/observables/events/auditor-added-to-audit.event';
import { AuditorAuthenticatedOauthEvent } from 'auditors/observables/events/auditor-authenticated-oauth.event';
import { AuditorControlEvidencePackageEmailEvent } from 'auditors/observables/events/auditor-control-evidence-package-email.event';
import { AuditorCreatedEvent } from 'auditors/observables/events/auditor-created.event';
import { AuditorFrameworkUpdatedEvent } from 'auditors/observables/events/auditor-framework-updated.event';
import { AuditorHasEnteredToTenantDetailsEvent } from 'auditors/observables/events/auditor-has-entered-to-tenant-details.event';
import { AuditorRemovedFromAuditEvent } from 'auditors/observables/events/auditor-removed-from-audit.event';
import { AuditorRevokedEvent } from 'auditors/observables/events/auditor-revoked.event';
import { AuditorSampleOutOfPeriodEvent } from 'auditors/observables/events/auditor-sample-out-of-period.event';
import { AuditorToggleUpdatedEvent } from 'auditors/observables/events/auditor-toggle-updated.event';
import { DownloadOnlyAuditCreatedEvent } from 'auditors/observables/events/download-only-audit-created.event';
import { GenerateControlEvidencePackageEvent } from 'auditors/observables/events/generate-control-evidence-package.event';
import { GenerateRequestControlEvidencePackageEvent } from 'auditors/observables/events/generate-request-control-evidence-package.event';
import { InviteMagicLinkAuditorEvent } from 'auditors/observables/events/invite-magic-link-auditor.event';
import { AuditFirmRepository } from 'auditors/repositories/audit-firm.repository';
import { AuditorClientRepository } from 'auditors/repositories/auditor-client.repository';
import { AuditorFrameworkRepository } from 'auditors/repositories/auditor-framework.repository';
import { AuditorRepository } from 'auditors/repositories/auditor.repository';
import { AuditInfoService } from 'auditors/services/audit-info.service';
import { AuditorClientListType } from 'auditors/types/auditor-client-list.type';
import { AuditorClientPaginatedListType } from 'auditors/types/auditor-client-paginated-list.type';
import { AuditorFrameworkAuditorsType } from 'auditors/types/auditor-framework-auditors.type';
import { AuditorFrameworkSampleData } from 'auditors/types/auditor-framework-sample-data.type';
import { AuditorFrameworksUnreadMessagesType } from 'auditors/types/auditor-framework-unread-messages.type';
import { AuditorFrameworksListType } from 'auditors/types/auditor-frameworks-list.type';
import { AuditorFrameworksType } from 'auditors/types/auditor-frameworks.type';
import { AuditorPersonnelTypeIds } from 'auditors/types/auditor-personnel-ids.type';
import { AuditorPersonnelType } from 'auditors/types/auditor-personnel.type';
import { AuditorWithSignatureType } from 'auditors/types/auditor-with-signature.type';
import { ControlEvidencePackageRelatedData } from 'auditors/types/control-evidence-package-related-data.type';
import { ControlEvidencePingInformation } from 'auditors/types/control-evidence-ping-information.type';
import { NotificationsStats } from 'auditors/types/notifications-stats.type';
import { PendingEvidencePackageCompanyArchive } from 'auditors/types/pending-evidence-package-company-archive.type';
import { Audit } from 'audits/entities/audit.entity';
import { Account } from 'auth/entities/account.entity';
import { Entry } from 'auth/entities/entry.entity';
import { AccountsService } from 'auth/services/accounts.service';
import { AuthSigningService } from 'auth/services/auth-signing.service';
import { EntryCoreService } from 'auth/services/entry-core.service';
import { AuthResponseType } from 'auth/types/auth-response-type';
import { PolloAdapter } from 'commons/adapters/pollo.adapter';
import { SignedUrlResponseDto } from 'commons/dtos/signed-url-response.dto';
import { ApiResponse } from 'commons/enums/api-response.enum';
import { AuditorFrameworkArchiveType } from 'commons/enums/auditor-framework-archive-type.enum';
import { AuditorPersonnelHandler } from 'commons/enums/auditors/auditor-personnel-handlers.enum';
import { AuditorPersonnelStatus } from 'commons/enums/auditors/auditor-personnel-status.enum';
import { AccountStatus } from 'commons/enums/auth/account-status.enum';
import { CompanyArchiveCategory } from 'commons/enums/company-archive-category.enum';
import { CompanyArchiveStatus } from 'commons/enums/company-archive-status.enum';
import { ControlEvidencePackageGenerationType } from 'commons/enums/control-evidence-package-generation-type.enum';
import { SamplePersonnelType } from 'commons/enums/sample-personnel-type.enum';
import { UploadType } from 'commons/enums/upload-type.enum';
import { Role } from 'commons/enums/users/role.enum';
import { ConflictException } from 'commons/exceptions/conflict.exception';
import { ForbiddenException } from 'commons/exceptions/forbidden.exception';
import { NotFoundException as OurNotFoundException } from 'commons/exceptions/not-found.exception';
import { PreconditionFailedException } from 'commons/exceptions/precondition-failed.exception';
import { UnauthorizedException } from 'commons/exceptions/unauthorized.exception';
import { validateAndGetAuditorFrameworkByAuditFirm } from 'commons/helpers/audit-api.helper';
import { createZipBufferWithPassThrough } from 'commons/helpers/buffer.helper';
import { fileNameDate } from 'commons/helpers/date.helper';
import { getDomainFromEmail, isExcludedDomain, isSameDomain } from 'commons/helpers/domain.helper';
import { getProductId, isMultiProductEnabled } from 'commons/helpers/products.helper';
import { promiseAllInBatches } from 'commons/helpers/promise.helper';
import { frameworkToFrameworkTemplateEntity } from 'commons/helpers/publish-frameworks.helper';
import { intercomIdentityVerification } from 'commons/helpers/security.helper';
import { hasRole, isSupportUser, verifyUserIsActive } from 'commons/helpers/user.helper';
import { AppService } from 'commons/services/app.service';
import { AccountIdType } from 'commons/types/account-id.type';
import { AuditApiAuditorParamsType } from 'commons/types/audit-api-auditor-params.type';
import { FileBufferType } from 'commons/types/file-buffer.type';
import { PaginationType } from 'commons/types/pagination.type';
import config from 'config';
import { Downloader } from 'dependencies/downloader/downloader';
import { DownloaderPayloadType } from 'dependencies/downloader/types/downloader-payload.interface';
import { Uploader } from 'dependencies/uploader/uploader';
import { FeatureFlagCategory } from 'feature-flags/enums/feature-flag-category.enum';
import { FeatureFlag } from 'feature-flags/feature-flag.enum';
import { FeatureFlagService } from 'feature-flags/feature-flag.service';
import { chain, compact, flatten, get, head, isEmpty, isNil, keyBy, omit, uniqBy } from 'lodash';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { ServiceUserClientRepository } from 'service-user/repositories/service-user-client.repository';
import { ServiceUserRepository } from 'service-user/repositories/service-user.repository';
import { tenantWrapper } from 'tenancy/contexts/tenant-wrapper';
import { In, IsNull, Not, Repository, UpdateResult } from 'typeorm';

@Injectable()
export class AuditorsService extends AppService {
    private readonly CONTROL_EVIDENCE_ZIP_FILENAME = 'ControlEvidence.zip';

    constructor(
        private readonly auditInfoService: AuditInfoService,
        private readonly controlEvidenceWaasService: ControlEvidenceWaasService,
        private readonly auditorRepository: AuditorRepository,
        private readonly auditorClientRepository: AuditorClientRepository,
        private readonly entryCoreService: EntryCoreService,
        private readonly auditorFrameworkRepository: AuditorFrameworkRepository,
        @InjectRepository(AuditorFrameworkAuditors)
        private readonly auditorFrameworkAuditorsRepository: Repository<AuditorFrameworkAuditors>,
        private readonly accountsService: AccountsService,
        private readonly uploader: Uploader,
        private readonly downloader: Downloader,
        private readonly serviceUserClientRepository: ServiceUserClientRepository,
        private readonly serviceUserRepository: ServiceUserRepository,
        private readonly auditFirmRepository: AuditFirmRepository,
        private readonly createEventCoreService: CreateEventCoreService, // needed by the @CreateEvent decorator
        private readonly usersCoreService: UsersCoreService,
        private readonly personnelCoreService: PersonnelCoreService,
        private readonly companiesOrchestrationService: CompaniesOrchestrationService,
        private readonly companyArchivesCoreService: CompanyArchivesCoreService,
        private readonly workspacesCoreService: WorkspacesCoreService,
        private readonly frameworksCoreService: FrameworksCoreService,
        private readonly customerRequestOrchestrationService: CustomerRequestOrchestrationService,
        private readonly companiesCoreService: CompaniesCoreService,
        private readonly requirementsCoreService: RequirementsCoreService,
        private readonly auditHubAuditPackagesOrchestrationService: AuditHubAuditPackagesOrchestrationService,
        private readonly companyOrchestrationService: CompanyOrchestrationService,
        private readonly grcEvidenceDownloadOrchestrationService: GrcEvidenceDownloadOrchestrationService,
        private readonly personnelOrchestrationService: PersonnelOrchestrationService,
        private readonly authSigningService: AuthSigningService,
        private readonly featureFlagService: FeatureFlagService,
    ) {
        super();
    }

    async auditorTenantAuth(auditor: Auditor, clientId: string): Promise<AuthResponseType> {
        const account = await this.getAccountByAuditorClientId(clientId);

        const isReleaseOptimizedAuditorAuthEndpoint =
            await this.featureFlagService.evaluateAsTenant(
                {
                    category: FeatureFlagCategory.NONE,
                    name: FeatureFlag.RELEASE_AUDITOR_AUTH_ENDPOINT,
                    defaultValue: false,
                },
                account,
            );

        // this validates the auditor has permissions to access this tenant
        const entry = await this.getTenantAuthEntry(account, auditor, {
            isReleaseOptimizedAuditorAuthEndpoint,
        });

        return {
            accessToken: this.authSigningService.signAuditorTenantUserAccessToken(
                auditor.id,
                account.id,
                entry.id,
                AuthModes.NORMAL,
            ),
            mode: AuthModes.NORMAL, // the user is now acting as a tenant user, so the auth mode matches a NORMAL user
        };
    }

    /**
     * Optimized version that directly returns the account for an auditor client
     * @param auditorClientId
     */
    async getAccountByAuditorClientId(auditorClientId: string): Promise<Account> {
        const client = await this.auditorClientRepository.findOne({
            select: ['account'],
            where: { id: auditorClientId },
            relations: ['account'],
            loadEagerRelations: false,
        });

        if (!client) {
            throw new NotFoundException(`Auditor client with ID ${auditorClientId} not found`);
        }

        return client.account;
    }

    /**
     * @deprecated Use AuditorsOrchestrationService.toggleAuditorReadOnly
     *
     * @param requestDto
     * @param account
     */
    async toggleAuditorReadOnly(
        account: Account,
        user: User,
        entryId: string,
        requestDto: AuditorToggleRequestDto,
    ): Promise<AuditorClient> {
        const entry = await this.entryCoreService.getEntryById(entryId);
        const auditorClient = await this.getAuditorClientByAccountAndEntry(account, entry);

        const auditor = await this.auditorRepository.findOne({
            where: { entry: { id: entry.id } },
        });

        if (isNil(auditorClient) || isNil(auditor)) {
            throw new NotFoundException(ApiResponse.NOT_FOUND);
        }

        const { hasAccess, canDownload } = requestDto;

        let save = false;

        if (hasAccess && isNil(auditorClient.readOnly)) {
            auditorClient.readOnly = new Date();
            save = true;
        } else if (!hasAccess && !isNil(auditorClient.readOnly)) {
            auditorClient.readOnly = null;
            auditorClient.allowDownloads = false;
            save = true;
        }

        if (
            !isNil(auditorClient.readOnly) &&
            !isNil(canDownload) &&
            canDownload !== auditorClient.allowDownloads
        ) {
            save = true;
            auditorClient.allowDownloads = canDownload;
        }

        if (save) {
            await this.auditorClientRepository.save(auditorClient);

            // trigger event
            this._eventBus.publish(
                new AuditorToggleUpdatedEvent(account, user, requestDto, auditor),
            );
        }

        return auditorClient;
    }

    /**
     * @deprecated Use AuditorsOrchestrationService.getAuditorFromAccount
     *
     * @param id
     */
    async getAuditorFromAccount(id: AccountIdType, auditor: Auditor): Promise<User> {
        const account = await this.accountsService.getAccountById(id);

        if (account.status !== AccountStatus.ACTIVE) {
            throw new GoneException();
        }

        const user = await this.getAccountUserByEntry(account, auditor.entry);

        if (isNil(user)) {
            throw new NotFoundException(ErrorCode.USER_NOT_FOUND);
        }
        return user;
    }

    /**
     *
     * @param email
     * @param account
     */
    private async createEntry(email: string, account: Account): Promise<Entry> {
        // passing parameter as true, in this context getEntryByEmailNoFail should look for auditor entries
        const isAuditor = true;
        let entry = await this.entryCoreService.getEntryByEmailNoFail(email, isAuditor);
        if (isNil(entry)) {
            entry = new Entry();
            entry.email = email;
            entry.accounts = [account];
            entry = await this.entryCoreService.saveEntry(entry);
        } else {
            if (isEmpty(entry.account(account.id))) {
                entry.setEntryAccount(account);
                await this.entryCoreService.saveEntry(entry);
            }
        }
        return entry;
    }

    /**
     *
     * @param email
     * @param account
     */
    private async createUser(
        userData: OldAuditorRequestDto,
        account: Account,
        entry: Entry,
    ): Promise<User> {
        const { email } = userData;

        if (isSameDomain(account.domain, email)) {
            throw new ConflictException(
                "Only users with an email address outside of our company's domain can be added into the Auditor Role",
                ErrorCode.INVALID_DOMAIN,
            );
        }

        if (isExcludedDomain(getDomainFromEmail(email))) {
            throw new ConflictException(
                // 'This domain is in the list of public domains to exclude',
                'Only corporate email addresses are allowed',
                ErrorCode.SERVICE_PROVIDER_NOT_ALLOWED_DOMAIN,
            );
        }

        let createdUser = await this.usersCoreService.findOneByInsensitiveEmail(email);
        const auditor = await this.getAuditorByEntry(entry);
        if (isNil(createdUser)) {
            createdUser = new User();
            createdUser.entryId = entry.id;
            createdUser.email = userData.email;
            createdUser.firstName = userData.firstName;
            createdUser.lastName = userData.lastName;
            createdUser.language = account.language;
            createdUser.drataTermsAgreedAt = !isNil(auditor) ? auditor.drataTermsAgreedAt : null;
            createdUser = await this.usersCoreService.saveUser(createdUser, account.id);
        } else {
            if (hasRole(createdUser, [Role.AUDITOR])) {
                throw new ConflictException(
                    `${createdUser.firstName}`,
                    ErrorCode.CONFLICT_DUPLICATED_AUDITOR,
                );
            }

            const hasDisabledAuditorRole = await this.userRepository.hasDisabledAuditorRole(
                createdUser.id,
            );

            const serviceUser = await this.serviceUserRepository.findOne({
                where: {
                    entry: {
                        id: entry.id,
                    },
                },
            });

            // if the user does not have a role of auditor it can't be restored
            if (!hasDisabledAuditorRole && isNil(serviceUser)) {
                throw new ConflictException(
                    'The email for the Auditor you are trying to add is already in use in the System. Existing Users cannot be set as Auditors.',
                    ErrorCode.EMPLOYEE_AUDITOR_CONFLICT,
                );
            }
        }

        if (!hasRole(createdUser, [Role.AUDITOR])) {
            // wait for this to finish to update the role on the DB
            await this.createAuditorRoleForUser(createdUser, account);
            // reset the user to get the latest hotness
            createdUser = await this.usersCoreService.getUserById(createdUser.id);
        }

        return createdUser;
    }

    /**
     *
     * @param user
     * @param account
     */
    private async createAuditorRoleForUser(user: User, account: Account): Promise<UserRole> {
        const auditorRole = new UserRole();
        auditorRole.role = Role.AUDITOR;
        auditorRole.user = user;

        return this.userRoleRepository.save(auditorRole);
    }

    /**
     * @deprecated Use AuditorsOrchestrationService.revokeAuditorsForCustomFramework
     **/
    async revokeAuditorsForCustomFramework(
        account: Account,
        user: User,
        customFrameworkId: string,
    ): Promise<void> {
        // find auditors with custom framework id = customFrameworkId
        const auditors = await this.auditorClientRepository.getAuditorsByCustomFrameworkId(
            account.id,
            customFrameworkId,
        );

        const auditorEntriesIds = auditors.map(client => client.auditor.entry.id);

        // revoke each auditor
        for (const entry of auditorEntriesIds) {
            await this.deleteAuditorFramework(account, user, entry, {
                auditorFrameworkId: customFrameworkId,
            });
        }
    }

    /**
     * @deprecated Use AuditorsOrchestrationService.revokeAllAuditorsForCustomFrameworks
     *
     * @param account
     */
    async revokeAllAuditorsForCustomFrameworks(account: Account): Promise<void> {
        const auditors =
            await this.auditorClientRepository.getAllAuditorsForCustomFrameworks(account);

        const auditorEntriesIds = auditors.map(au => au.auditor.entry.id);

        for (const entry of auditorEntriesIds) {
            await this.deleteAuditorFramework(account, null, entry);
        }
    }

    /**
     * @deprecated Use AuditorsOrchestrationService.deleteAuditorFramework
     *
     * @param account
     * @param user
     * @param entryId
     * @param auditorFrameworkIdDeleteDto
     */
    async deleteAuditorFramework(
        account: Account,
        user: User | null,
        entryId: string,
        auditorFrameworkIdDeleteDto?: DeleteAuditorFrameworkRequestDto,
    ): Promise<Auditor> {
        const auditor = await this.getAuditorByEntryIdOrFail(entryId);
        const auditorClient = await this.getAuditorClientByAccountAndEntry(account, auditor.entry);
        const auditorUser = await this.usersCoreService.getUserByEmail(auditor.entry.email);

        const frameworks =
            auditorClient?.auditorFrameworkAuditor
                ?.map(item => item.auditorFramework)
                ?.filter(Boolean) ?? [];

        const { auditorFrameworkId } = auditorFrameworkIdDeleteDto ?? {};

        /**
         * SoftRemove requires Framework to have auditorClient,
         * but it could be empty cause currently getAuditorFrameworks takes frameworks
         * from the AuditorClient
         */
        // auditorFrameworkId is not empty for specific delete, it could be framework id or custom framework id
        if (!isEmpty(auditorFrameworkId)) {
            let frameworkToDelete = frameworks.find(
                framework => framework.id === auditorFrameworkId,
            );

            if (isNil(frameworkToDelete)) {
                frameworkToDelete = frameworks.find(
                    framework => framework.customFrameworkId === auditorFrameworkId,
                );
            }

            if (!isNil(frameworkToDelete)) {
                await this.auditorFrameworkRepository.softRemove(frameworkToDelete);

                /**
                 * Remove from frameworks the framework deleted with softRemove since it's
                 * delete from others auditors too.
                 */
                const frameworkIndex = frameworks.indexOf(frameworkToDelete);
                frameworks.splice(frameworkIndex, 1);
            }
        } else {
            const productId = getProductId(account);
            const frameworksToDelete = frameworks.filter(
                framework => framework.productId === productId,
            );
            for (const framework of frameworksToDelete) {
                await this.auditorFrameworkRepository.softRemove(framework);
            }
        }

        if (isEmpty(auditorFrameworkId) || frameworks.length === 0) {
            await this.usersCoreService.softDeleteAuditorRole(auditorUser);
            if (auditorClient) {
                await this.auditorClientRepository.softRemove(auditorClient);
            } else {
                this.logger.error(
                    PolloAdapter.acct(
                        `Auditor client not found for auditor with entry id ${entryId}. Auditor client will not be soft-deleted.`,
                        account,
                    ),
                );
            }

            /**
             * When disabling Custom Framework for a Tenant, the auditors related to all
             * custom frameworks should be revoked but that action is made by the drata administration panel
             * so there is no a current tenant's user
             */
            if (!isNil(user)) {
                this._eventBus.publish(new AuditorRevokedEvent(account, user, auditor, frameworks));
            }
        }
        return auditor;
    }

    /**
     * Delete single auditor framework
     * @param account Account
     * @param user User
     * @param auditorFrameworkId Auditor framework ID
     */
    async deleteSingleAuditorFramework(
        account: Account,
        auditorFrameworkId: string,
    ): Promise<void> {
        try {
            await this.auditorFrameworkAuditorsRepository.softDelete({
                auditorFramework: { id: auditorFrameworkId },
            });
            await this.auditorFrameworkRepository.softDelete(auditorFrameworkId);
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(error.message, account)
                    .setError(error)
                    .setContext(this.constructor.name)
                    .setSubContext(this.deleteSingleAuditorFramework.name),
            );
            throw error;
        }
    }

    private async getAccountUserByEntryOptimized(
        account: Account,
        entry: Entry,
    ): Promise<User | null> {
        return tenantWrapper(account, () => {
            return this.usersCoreService.getUserByEmailNoFail(entry.email, {
                includePersonnel: false,
            });
        });
    }

    /**
     *
     * @param account
     * @param auditor
     */
    async getTenantAuthEntry(
        account: Account,
        auditor: Auditor,
        options?: { isReleaseOptimizedAuditorAuthEndpoint?: boolean },
    ): Promise<Entry> {
        const { isReleaseOptimizedAuditorAuthEndpoint = false } = options ?? {};
        // validate if Auditor have access to the given account
        if (!auditor.entry.accounts.find(acc => acc.id === account.id)) {
            throw new NotFoundException(ErrorCode.AUDITOR_CLIENT_FORBIDDEN);
        }

        // auditor must have Auditor role in tenant
        let tenantUser: User | null;

        if (isReleaseOptimizedAuditorAuthEndpoint) {
            tenantUser = await this.getAccountUserByEntryOptimized(account, auditor.entry);
        } else {
            tenantUser = await this.getAccountUserByEntry(account, auditor.entry);
        }

        if (isNil(tenantUser)) {
            throw new NotFoundException(ErrorCode.USER_NOT_FOUND);
        }

        let verifyResults = verifyUserIsActive(tenantUser);
        if (
            (verifyResults.reason === ErrorCode.USER_ROLES_NOT_FOUND ||
                !hasRole(tenantUser, [Role.AUDITOR])) &&
            (isReleaseOptimizedAuditorAuthEndpoint
                ? await this.isAuditorMappingValidOptimized(auditor, account)
                : await this.isAuditorMappingValid(auditor, account))
        ) {
            await this.addAuditorRoleToUser(tenantUser, account);
            verifyResults = verifyUserIsActive(tenantUser);
        }
        if (!verifyResults.active) {
            throw new UnauthorizedException(verifyResults.reason);
        }

        if (!hasRole(tenantUser, [Role.AUDITOR])) {
            throw new UnauthorizedException(ErrorCode.USER_ROLES_NOT_FOUND);
        }

        // sanity check for the entry and its linking accounts
        if (auditor.entry && auditor.entry.accounts.length) {
            this._eventBus.publish(
                new AuditorHasEnteredToTenantDetailsEvent(account, auditor, tenantUser),
            );
            return auditor.entry;
        } else {
            throw new NotFoundException(ErrorCode.ENTITY_NOT_FOUND);
        }
    }

    async isAuditorMappingValid(auditor: Auditor, account: Account) {
        const auditorClient = await this.auditorClientRepository.findOne({
            where: {
                account: {
                    id: account.id,
                },
                entry: {
                    id: auditor.entry.id,
                },
            },
            select: ['id'],
        });
        if (isNil(auditorClient)) {
            return false;
        }
        const auditorFrameworkAuditor = await this.auditorFrameworkAuditorsRepository.findOne({
            where: {
                auditorClient: {
                    id: auditorClient.id,
                },
            },
        });
        if (isNil(auditorFrameworkAuditor)) {
            return false;
        }
        return true;
    }

    isAuditorMappingValidOptimized(auditor: Auditor, account: Account): Promise<boolean> {
        return this.auditorFrameworkAuditorsRepository
            .createQueryBuilder('afa')
            .innerJoin('afa.auditorClient', 'ac', 'ac.deleted_at IS NULL')
            .where('ac.fk_account_id = :accountId', { accountId: account.id })
            .andWhere('ac.fk_entry_id = :entryId', { entryId: auditor.entry.id })
            .getExists();
    }

    addAuditorRoleToUser(tenantUser: User, account: Account) {
        return tenantWrapper(account, async () => {
            const auditorRole = new UserRole();
            auditorRole.role = Role.AUDITOR;
            auditorRole.user = tenantUser;
            await this.userRoleRepository.save(auditorRole);

            tenantUser.roles = tenantUser.roles || [];
            tenantUser.roles.push(auditorRole);

            return this.userRepository.save(tenantUser);
        });
    }

    /**
     *
     * @param requestDto
     * @param auditor
     */
    async listAuditorsClients(
        requestDto: OldAuditorsClientsRequestDto,
        auditor: Auditor,
    ): Promise<PaginationType<AuditorClientPaginatedListType>> {
        const clientList = await this.auditorClientRepository.getAuditorClients(
            requestDto,
            auditor.entry.id,
        );
        return this.formatAuditorClients(clientList);
    }

    private async formatAuditorClients(
        clientList: PaginationType<AuditorClient>,
    ): Promise<PaginationType<AuditorClientPaginatedListType>> {
        await this.buildCustomFrameworksClient(clientList);

        /**
         * this is to avoid the auditor view to be broken when an auditor has a custom framework deleted
         */
        const filteredClients = clientList.data.filter(client =>
            client.auditorFrameworkAuditor.some(afa => !isNil(afa.auditorFramework)),
        );

        //transform data from clients
        const auditorClientMap: AuditorClientPaginatedListType[] = filteredClients.map(client => {
            const validFrameworks = client.auditorFrameworkAuditor.filter(
                framework => !isNil(framework.auditorFramework),
            );
            return {
                id: client.id,
                name: client.name,
                contactFirstName: client.contactFirstName,
                contactLastName: client.contactLastName,
                contactEmail: client.contactEmail,
                logo: client.logo,
                notes: client.notes,
                deletedAt: client.deletedAt,
                createdAt: client.createdAt,
                updatedAt: client.updatedAt,
                readOnly: client.readOnly,
                allowDownloads: client.allowDownloads,
                entry: client.entry,
                account: client.account,
                auditorFrameworks: validFrameworks.map(afa => afa.auditorFramework),
                auditorFrameworkAuditor: validFrameworks,
                totalUnreadMessages: null,
            };
        });

        return {
            page: clientList.page,
            limit: clientList.limit,
            total: clientList.total,
            data: auditorClientMap,
        };
    }

    /**
     *
     * @param {PaginationType<AuditorClient>} clientList
     */
    async buildCustomFrameworksClient(clientList: PaginationType<AuditorClient>): Promise<void> {
        // Filter clients with custom framework
        const clientsWithCustomFrameworks = clientList.data.filter(client =>
            client.auditorFrameworkAuditor.some(
                auditor =>
                    auditor.auditorFramework &&
                    auditor.auditorFramework.frameworkType === AuditorFrameworkTypeEnum.CUSTOM,
            ),
        );
        const batchSize = 5;
        await promiseAllInBatches(clientsWithCustomFrameworks, batchSize, async client => {
            try {
                await tenantWrapper(client.account, () =>
                    this.getAuditorFrameworksWithRelatedCustomFrameworks(
                        client.account,
                        client.auditorFrameworkAuditor.map(auditor => auditor.auditorFramework),
                    ),
                );
            } catch (error) {
                this.logger.error(
                    PolloMessage.msg(error.message)
                        .setDomain(client.account.domain)
                        .setError(error),
                );
            }
        });
    }

    /**
     * @deprecated Use AuditorsCoreService.getAuditorByEntry
     *
     * @param entry
     */
    getAuditorByEntry(entry: Entry): Promise<Auditor> {
        return this.auditorRepository.findOne({
            where: { entry: { id: entry.id } },
        });
    }

    /**
     * @deprecated Use AuditorsCoreService.getAuditorByEntryIdOrFail
     *
     * @param entryId
     */
    getAuditorByEntryIdOrFail(entryId: string): Promise<Auditor> {
        return this.auditorRepository.findOneOrFail({
            where: { entry: { id: entryId } },
        });
    }

    /**
     *
     * @param auditorClientId
     */
    async getAuditorClientById(auditorClientId: string): Promise<AuditorClient> {
        return this.auditorClientRepository.findOneByOrFail({
            id: auditorClientId,
        });
    }

    /**
     * @deprecated Use AuditorsCoreService.getAuditorClientByAccountAndEntry
     *
     * @param account
     * @param entry
     */
    async getAuditorClientByAccountAndEntry(
        account: Account,
        entry: Entry,
    ): Promise<AuditorClient | null> {
        const auditorClient = await this.auditorClientRepository
            .createQueryBuilder('AuditorClient')
            .leftJoinAndSelect('AuditorClient.auditorFrameworkAuditor', 'Auditor')
            .leftJoinAndSelect('Auditor.auditorFramework', 'AuditorFramework')
            .leftJoinAndSelect('AuditorFramework.auditorFrameworkType', 'AuditorFrameworkType')
            .where('AuditorClient.fk_account_id = :accountId', { accountId: account.id })
            .andWhere('AuditorClient.fk_entry_id = :entryId', { entryId: entry.id })
            .getOne();

        if (auditorClient) {
            // to avoid an additional join, we can simply re map the account and entry to this auditorClient entity
            auditorClient.account = account;
            auditorClient.entry = entry;
        }

        return auditorClient;
    }

    /**
     *
     * @param account
     * @param entryId
     */
    async getAuditorClientByAccountAndEntryId(
        account: Account,
        entryId: string,
    ): Promise<AuditorClient> {
        const entry = await this.entryCoreService.getEntryById(entryId);
        return this.getAuditorClientByAccountAndEntry(account, entry);
    }

    /**
     * @deprecated Use AuditorsOrchestrationService.getAuditorByEmail
     *
     * @param email
     */
    async getAuditorByEmail(email: string): Promise<Auditor | null> {
        const entry = await this.entryCoreService.getEntryByEmailNoFail(email, true);

        if (isNil(entry)) {
            return null;
        }

        return this.auditorRepository.findOne({
            where: { entry: { id: entry.id } },
        });
    }

    /**
     * @deprecated Use AuditorsCoreService.getOathAuditorByEmail
     *
     * @param entry
     */
    async getOathAuditorByEmail(entry: Entry): Promise<Auditor> {
        if (isNil(entry)) {
            // this is a NO FAIL function - so just return undefined
            return undefined;
        }

        const auditor = await this.auditorRepository.findOne({
            where: { entry: { id: entry.id } },
        });

        if (!isNil(auditor)) {
            this._eventBus.publish(
                new AuditorAuthenticatedOauthEvent(entry.primaryAccount(), auditor.id),
            );
        }

        return auditor;
    }

    /**
     * @deprecated Use AuditorsOrchestrationService.getAuditorWithEmail
     *
     * @param entry
     */
    async getAuditorWithEmail(email: string): Promise<Auditor> {
        const entry = await this.entryCoreService.getEntryByEmailNoFail(email, true);

        if (isNil(entry)) {
            return undefined;
        }

        return this.auditorRepository.findOne({
            where: { entry: { id: entry.id } },
        });
    }

    /**
     *
     * @param entryId
     */
    async getAuditorClientsByEntryId(entryId: string): Promise<AuditorClient[]> {
        return this.auditorClientRepository.getAuditorNonDeletedClientsByEntryId(entryId);
    }

    /**
     * @param auditor
     */
    getMyAuditor(auditor: Auditor): AuditorWithSignatureType {
        return {
            auditor,
            signature: intercomIdentityVerification(auditor.entry.id),
        };
    }

    /**
     *
     * @param auditor
     */
    async agreeToTerms({ id, drataTermsAgreedAt }: Auditor): Promise<void> {
        if (isNil(drataTermsAgreedAt)) {
            const auditor = await this.getAuditorById(id);
            const agreedAt = new Date();
            auditor.drataTermsAgreedAt = agreedAt;
            await this.auditorRepository.save(auditor);

            const clients = await this.getAuditorClientsByEntryId(auditor.entry.id);

            for (const client of clients) {
                const { account, entry } = client;
                await tenantWrapper(account, async () => {
                    const user = await this.usersCoreService.getUserByEmail(entry.email);

                    if (isNil(user.drataTermsAgreedAt)) {
                        user.drataTermsAgreedAt = agreedAt;
                        await this.usersCoreService.saveUser(user, account.id);
                    }
                });
            }
        }
    }

    /**
     *
     * @param originalAuditor
     * @param requestDto
     */
    async updateAuditor(
        originalAuditor: Auditor,
        requestDto: OldAuditorRequestDto,
    ): Promise<Auditor> {
        const { firmName, firstName, lastName, language, email, customFirmName } = requestDto;

        const auditor = await this.getAuditorById(originalAuditor.id);
        auditor.firstName = firstName;
        auditor.lastName = lastName;
        auditor.language = language;
        auditor.auditFirm = await this.getAuditFirm(getDomainFromEmail(email), firmName);

        const haveCustomFirmName =
            !isEmpty(auditor.firmName) && auditor.auditFirm?.name !== auditor.firmName;
        const shouldUpdateCustomFirmName = auditor.firmName !== customFirmName;

        auditor.auditFirm = await this.getAuditFirm(getDomainFromEmail(email), firmName);

        if (shouldUpdateCustomFirmName || haveCustomFirmName) {
            auditor.firmName = customFirmName;
        }

        const updatedAuditor = await this.auditorRepository.save(auditor);

        const clients = await this.getAuditorClientsByEntryId(auditor.entry.id);

        for (const client of clients) {
            const { account, entry } = client;
            await tenantWrapper(account, async () => {
                const user = await this.usersCoreService.getUserByEmail(entry.email);
                user.firstName = firstName;
                user.lastName = lastName;
                user.language = language;

                await this.usersCoreService.saveUser(user, account.id);
            });
        }
        return updatedAuditor;
    }

    getAuditorFrameworkByIdAndAccountId(
        id: string,
        accountId: AccountIdType,
    ): Promise<Audit | null> {
        return this.auditorFrameworkRepository.findOneBy({
            id,
            account: {
                id: accountId,
            },
        });
    }

    /**
     * @deprecated Use AuditorsOrchestrationService.saveSampleData
     *
     * @param account
     * @param auditorFramework
     * @param dates
     * @param platform
     * @param currentPersonnelIds
     * @param hiredPersonnelIds
     * @param formerPersonnelIds
     * @returns
     */
    async saveSampleData(
        account: Account,
        // @AUDIT-REFACTOR: TODO rename to  audit
        auditorFramework: Audit,
        dates: string[],
        platform: AgentPlatform,
        currentPersonnelIds: number[],
        hiredPersonnelIds: number[],
        formerPersonnelIds: number[],
    ): Promise<AuditorFrameworkSample> {
        const newSampleData = new AuditorFrameworkSample();
        newSampleData.auditorFrameworkId = auditorFramework.id;
        newSampleData.platform = platform;
        newSampleData.dates = dates.join(',');

        let personnelMapped = [];

        if (!isEmpty(currentPersonnelIds)) {
            const currentPersonnel =
                await this.personnelCoreService.findAllByIds(currentPersonnelIds);

            const currentPersonnelToPersonnelMap = currentPersonnel.map(personnel => {
                const samplePersonnelMap = new AuditorFrameworkSamplePersonnelMap();
                samplePersonnelMap.personnel = personnel;
                samplePersonnelMap.type = SamplePersonnelType.EMPLOYED_DURING_TIMEFRAME;
                return samplePersonnelMap;
            });

            personnelMapped = [...personnelMapped, ...currentPersonnelToPersonnelMap];
        }

        if (!isEmpty(hiredPersonnelIds)) {
            const hiredPersonnel = await this.personnelCoreService.findAllByIds(hiredPersonnelIds);

            const hiredPersonnelToPersonnelMap = hiredPersonnel.map(personnel => {
                const samplePersonnelMap = new AuditorFrameworkSamplePersonnelMap();
                samplePersonnelMap.personnel = personnel;
                samplePersonnelMap.type = SamplePersonnelType.HIRED;
                return samplePersonnelMap;
            });

            personnelMapped = [...personnelMapped, ...hiredPersonnelToPersonnelMap];
        }

        if (!isEmpty(formerPersonnelIds)) {
            const formerPersonnel =
                await this.personnelCoreService.findAllByIds(formerPersonnelIds);

            const formerPersonnelToPersonnelMap = formerPersonnel.map(personnel => {
                const samplePersonnelMap = new AuditorFrameworkSamplePersonnelMap();
                samplePersonnelMap.personnel = personnel;
                samplePersonnelMap.type = SamplePersonnelType.FORMER;
                return samplePersonnelMap;
            });

            personnelMapped = [...personnelMapped, ...formerPersonnelToPersonnelMap];
        }

        const auditorFrameworkSample =
            await this.auditorFrameworkSampleRepository.save(newSampleData);

        for (const personnel of personnelMapped) {
            personnel.auditorFrameworkSample = auditorFrameworkSample;
        }

        auditorFrameworkSample.personnel =
            await this.auditorFrameworkSamplePersonnelMapRepository.save(personnelMapped);

        return auditorFrameworkSample;
    }

    /**
     *
     * @param {Account} account
     * @param {User} user
     * @param {Audit} auditorFramework
     * @param {AuditorControlEvidencePackageRequestAuditorApiDto} requestDto
     * @returns {Promise<CompanyArchive>}
     */
    async generateControlEvidenceCompanyArchive(
        account: Account,
        user: User,
        // @AUDIT-REFACTOR: TODO rename to  audit
        auditorFramework: Audit,
        requestDto: AuditorControlEvidencePackageRequestAuditorApiDto,
        frameworkTypeName: string,
        companyName: string,
        pendingCompanyArchive: CompanyArchive,
    ): Promise<CompanyArchive> {
        const { dates, currentPersonnelIds, hiredPersonnelIds, formerPersonnelIds, platform } =
            requestDto;

        const personnelIds: AuditorPersonnelTypeIds = {
            [AuditorPersonnelStatus.CURRENT]: currentPersonnelIds,
            [AuditorPersonnelStatus.HIRED]: hiredPersonnelIds,
            [AuditorPersonnelStatus.FORMER]: formerPersonnelIds,
        };

        try {
            const auditorFrameworkSample = await this.saveSampleData(
                account,
                auditorFramework,
                dates,
                platform,
                currentPersonnelIds,
                hiredPersonnelIds,
                formerPersonnelIds,
            );

            const sampleArchiveMap = new AuditorFrameworkSampleArchiveMap();
            sampleArchiveMap.auditorFrameworkSample = auditorFrameworkSample;
            sampleArchiveMap.type = AuditorFrameworkArchiveType.FRAMEWORK_ARCHIVE;
            sampleArchiveMap.companyArchive = pendingCompanyArchive;
            await this.auditorFrameworkSampleArchiveMapRepository.save(sampleArchiveMap);

            // Empty zip file for now
            // Get control evidence files and add them to the zip here
            this.logger.log(
                PolloAdapter.acct('Starting company control evidence package', account),
            );

            const { evidence, symlinks } =
                await this.auditHubAuditPackagesOrchestrationService.getAllControlEvidence(
                    auditorFramework,
                    account,
                    user,
                    dates,
                    personnelIds,
                    this.grcEvidenceDownloadOrchestrationService,
                    this.personnelOrchestrationService,
                    this.companyOrchestrationService,
                );

            this.logger.log(
                PolloAdapter.acct('Company control evidence archive generated', account),
            );

            // get the passthrough and the uploader

            const { passThrough, upload } = this.uploader.getPassThroughUploader({
                bucket: config.get('aws.s3.appBucket'),
                contentType: config.get('archive.contentType'),
                uploadType: UploadType.CONTROL_EVIDENCE_PACKAGE,
                originalName: this.CONTROL_EVIDENCE_ZIP_FILENAME,
                accountId: account.id,
                subFolder: config.get('archive.subFolder'),
            });

            const uploadPromise = upload.done();

            this.log('Creating and Uploading zip file...', account);

            const currentFrameworkProduct = await this.workspacesCoreService.getProductById(
                auditorFramework.productId,
            );
            // Required to have workspace name included in evidence package
            account.setCurrentProduct(currentFrameworkProduct);

            this.log('Got products and framework for upload...', account);

            this.log('Getting personnel', account);

            const currentPersonnelNames =
                await this.personnelCoreService.getPersonnelNamesFromAuditSampleDataByIds(
                    currentPersonnelIds,
                );

            this.log('got current personnel', account);

            const hiredPersonnelNames =
                await this.personnelCoreService.getPersonnelNamesFromAuditSampleDataByIds(
                    hiredPersonnelIds,
                );

            this.log('got hired personnel', account);

            const formerPersonnelNames =
                await this.personnelCoreService.getPersonnelNamesFromAuditSampleDataByIds(
                    formerPersonnelIds,
                );

            this.log('get former personnel', account);

            const auditInfoFile = this.auditInfoService.createAuditInfoFile(
                account,
                companyName,
                frameworkTypeName,
                auditorFramework,
                dates,
                currentPersonnelNames,
                hiredPersonnelNames,
                formerPersonnelNames,
            );
            evidence.push(auditInfoFile);

            this.log('Got audit info file', account);

            const auditReadMeFile = this.auditInfoService.createAuditPackageReadMeFile();
            evidence.push(auditReadMeFile);

            this.log('Got audit README file', account);

            await createZipBufferWithPassThrough(
                flatten(evidence),
                symlinks,
                passThrough,
                platform,
                progress => {
                    this.log('zip file progress', account, progress);
                },
                warningError => {
                    this.logger.warn(
                        PolloAdapter.acct('Zip file progress warning', account).setError(
                            warningError,
                        ),
                    );
                },
            );

            const data = await uploadPromise;

            this.logger.log(
                PolloAdapter.acct(
                    'Company control evidence archive uploaded',
                    account,
                ).setIdentifier({
                    filename: pendingCompanyArchive.name,
                }),
            );

            pendingCompanyArchive.status = CompanyArchiveStatus.SUCCESS;
            pendingCompanyArchive.file = data.Key;
            await this.companyArchivesCoreService.saveCompanyArchive(pendingCompanyArchive);

            return pendingCompanyArchive;
        } catch (error) {
            pendingCompanyArchive.status = CompanyArchiveStatus.FAILED;
            await this.companyArchivesCoreService.saveCompanyArchive(pendingCompanyArchive);
            this.logger.error(
                PolloAdapter.acct(
                    `${this.constructor.name}: Error on building package/archive ${error.message}`,
                    account,
                ).setError(error),
            );

            return pendingCompanyArchive;
        }
    }

    /**
     *
     * @param {Account} account
     * @param {User} user
     * @param {string} entryId
     * @param {AuditorControlEvidencePackageRequestAuditorApiDto} requestDto
     */
    async generateControlEvidencePackageAsWaaS(
        account: Account,
        user: User,
        entryId: string,
        requestDto: AuditorControlEvidencePackageRequestAuditorApiDto,
    ): Promise<void> {
        const { audit } = await this.getControlEvidencePackageRelatedData(
            entryId,
            account,
            requestDto,
        );

        const company = await this.getAuditedCompanyByAccount(account);

        const controlEvidencePendingCompanyArchive =
            await this.generateControlEvidencePendingCompanyArchive(
                account,
                user,
                audit,
                requestDto,
                company,
            );

        void this.controlEvidenceWaasService.triggerControlEvidencePackageGeneration(
            account,
            user,
            company,
            audit.id,
            {
                companyArchiveId: controlEvidencePendingCompanyArchive.pendingCompanyArchive.id,
                type: ControlEvidencePackageGenerationType.COMPLETE_PACKAGE,
            },
        );
    }

    /**
     * @deprecated Use AuditorOrchestrationService.generateControlEvidencePackage
     *
     * @param {Account} account
     * @param {User} user
     * @param {string} entryId
     * @param {AuditorControlEvidencePackageRequestAuditorApiDto} requestDto
     */
    async generateControlEvidencePackage(
        account: Account,
        user: User,
        entryId: string,
        requestDto: AuditorControlEvidencePackageRequestAuditorApiDto,
    ): Promise<void> {
        try {
            this.log('Control Evidence requested', account, requestDto);

            const { auditor, audit } = await this.getControlEvidencePackageRelatedData(
                entryId,
                account,
                requestDto,
            );

            const company = await this.getAuditedCompanyByAccount(account);

            await this.validateControlEvidencePackageData(auditor, account, requestDto, audit);

            //analytics event for download only audit created
            if (audit.auditType === AuditType.DOWNLOAD_ONLY_AUDIT) {
                this._eventBus.publish(
                    new DownloadOnlyAuditCreatedEvent(
                        account,
                        account.companyName,
                        user,
                        AuditorFrameworkTypeEnum[audit.frameworkType],
                    ),
                );
            }

            await this.generatePendingControlEvidencePackageAndPublishEvent(
                account,
                company,
                user,
                audit,
                requestDto,
            );
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(error.message, account)
                    .setError(error)
                    .setContext(this.constructor.name)
                    .setSubContext(this.generateControlEvidencePackage.name),
            );
            throw error;
        }
    }

    /**
     * @deprecated Use AuditorsOrchestrationService.generatePendingControlEvidencePackageAndPublishEvent
     *
     * @param account
     * @param company
     * @param user
     * @param audit
     * @param requestDto
     */
    async generatePendingControlEvidencePackageAndPublishEvent(
        account: Account,
        company: Company,
        user: User,
        audit: Audit,
        requestDto: AuditorControlEvidencePackageRequestAuditorApiDto,
    ): Promise<void> {
        this.log('Generating the control evidence company archive with PENDING status.', account);

        const { pendingCompanyArchive } = await this.generateControlEvidencePendingCompanyArchive(
            account,
            user,
            audit,
            requestDto,
            company,
        );

        this.log('Publishing event to generate zip', account, requestDto);
        this._eventBus.publish(
            new GenerateControlEvidencePackageEvent(
                account,
                company,
                user,
                audit,
                requestDto,
                pendingCompanyArchive,
            ),
        );
    }

    /**
     * @deprecated Use AuditorOrchestrationService.getControlEvidencePackageRelatedData
     *
     * @param {string} entryId
     * @param {Account} account
     * @param {AuditorControlEvidencePackageRequestAuditorApiDto} requestDto
     *
     * Retrieves the data needed to generate the control evidence package.
     * This data includes the auditor framework for all audit types and
     * the auditor data for the FULL_AUDIT audit type.
     * For the FULL_AUDIT it uses the entry id of the auditor to retrieve
     * the auditor framework.
     * For the DOWNLOAD_ONLY AUDIT it uses directly the framework
     */
    async getControlEvidencePackageRelatedData(
        entryId: string,
        account: Account,
        requestDto: AuditorControlEvidencePackageRequestAuditorApiDto,
    ): Promise<ControlEvidencePackageRelatedData> {
        const { auditorFrameworkId } = requestDto;

        const entry = await this.entryCoreService.getEntryById(entryId);
        const auditor = await this.getAuditorByEntry(entry);

        const product = account.getCurrentProduct();
        const productId = get(product, 'id');

        const auditorClient = await this.getAuditorClientByAccountAndEntry(account, entry);

        // @AUDIT-REFACTOR: TODO rename to  audits
        const auditorFrameworks: Audit[] = [];

        if (!isNil(auditorClient)) {
            const { auditorFrameworkAuditor } = auditorClient;
            const auditorAuditorFrameworks = auditorFrameworkAuditor.map(
                mapItem => mapItem.auditorFramework,
            );
            auditorFrameworks.push(...auditorAuditorFrameworks);
        } else {
            const externalAuditAuditorFramework =
                await this.auditorFrameworkRepository.getAuditorFrameworkByIdOrFail(
                    auditorFrameworkId,
                    productId,
                );
            auditorFrameworks.push(externalAuditAuditorFramework);
        }

        const auditorFrameworksWithRelatedCustomFrameworks =
            await this.getAuditorFrameworksWithRelatedCustomFrameworks(account, auditorFrameworks);

        const audit = auditorFrameworksWithRelatedCustomFrameworks.find(
            auditorFw => auditorFw.id === auditorFrameworkId,
        );

        if (isNil(audit)) {
            throw new NotFoundException(ErrorCode.AUDIT_NOT_FOUND);
        }

        return { auditor, audit };
    }

    /**
     * @deprecated Use AuditorsOrchestrationService.validateControlEvidencePackageData
     *
     * @param {Auditor} auditor
     * @param {Account} account
     * @param {AuditorControlEvidencePackageRequestAuditorApiDto} requestDto
     * @param {Audit} auditorFramework
     *
     * Validates that the data sent to generate the control evidence package.
     * For the FULL_AUDIT audit type it also validates that the auditor information
     * is correct; it does not occur for DOWNLOAD_ONLY as there won't be any auditor
     * associated to it.
     * Validates all the personnel data as well. Validations are based on start date
     * and end date of the auditor framework.
     */
    async validateControlEvidencePackageData(
        auditor: Auditor | undefined,
        account: Account,
        requestDto: AuditorControlEvidencePackageRequestAuditorApiDto,
        // @AUDIT-REFACTOR: TODO rename to  audit
        auditorFramework: Audit,
    ): Promise<void> {
        this.log('Validating Auditor information', account, requestDto);
        // Perform validations
        if (auditorFramework.auditType === AuditType.FULL_AUDIT) {
            validateAuditor(auditor);
        }

        validateAuditorFramework(
            auditorFramework,
            auditorFramework?.frameworkType,
            requestDto.dates,
        );
        this.log('Validating requested personnel', account, requestDto);

        await validateControlEvidencePackagePersonnel(
            this.personnelCoreService,
            auditorFramework,
            requestDto.hiredPersonnelIds,
            requestDto.currentPersonnelIds,
            requestDto.formerPersonnelIds,
        );
    }

    /**
     * @deprecated Use AuditorsOrchestrationService.generateControlEvidencePendingCompanyArchive
     *
     * @param {Account} account
     * @param {User} user
     * @param {Audit} auditorFramework
     * @param {AuditorControlEvidencePackageRequestAuditorApiDto} requestDto
     * @param {Company} company
     *
     * It starts the process of generating the company archive entry for the
     * control evidence package. It will create an entry for the company archive
     * in the pending state and the needed associated sample data. After this method
     * is called the GenerateControlEvidencePackageEvent event must be triggered. This
     * will take care of generating the zip file and updating the status of the pending
     * company archive to either success of failed.
     */
    async generateControlEvidencePendingCompanyArchive(
        account: Account,
        user: User,
        // @AUDIT-REFACTOR: TODO rename to  audit
        auditorFramework: Audit,
        requestDto: AuditorControlEvidencePackageRequestAuditorApiDto,
        company: Company,
    ): Promise<PendingEvidencePackageCompanyArchive> {
        const { dates, currentPersonnelIds, hiredPersonnelIds, formerPersonnelIds, platform } =
            requestDto;

        let frameworkTypeName: string;

        if (auditorFramework.frameworkType === AuditorFrameworkTypeEnum.CUSTOM) {
            const customFramework = await this.frameworksCoreService.getEnabledFrameworkByTag(
                FrameworkTag.CUSTOM,
                null,
                auditorFramework.customFrameworkId,
            );
            if (isNil(customFramework)) {
                throw new ConflictException(
                    `Custom Framework with ID ${auditorFramework.customFrameworkId} does not exist or is not enabled`,
                    ErrorCode.FRAMEWORK_NOT_FOUND,
                );
            }
            frameworkTypeName = customFramework.pill;
        } else {
            frameworkTypeName = AuditorFrameworkTypeNames.get(auditorFramework.frameworkType);
        }

        const baseFileName = `${frameworkTypeName} - Control Evidence - ${fileNameDate()}.zip`;

        const fileName = await this.createControlEvidenceFilename(
            account,
            auditorFramework.productId,
            company.name,
            baseFileName,
        );

        const companyArchive = await this.companyArchivesCoreService.createCompanyDocument(
            account,
            fileName,
            null,
            user,
            CompanyArchiveCategory.CONTROL_EVIDENCE,
            AuditorFrameworkCompanyArchiveTypeMap.get(auditorFramework.frameworkType),
            CompanyArchiveStatus.PENDING,
        );

        const auditorFrameworkSample = await this.saveSampleData(
            account,
            auditorFramework,
            dates,
            platform,
            currentPersonnelIds,
            hiredPersonnelIds,
            formerPersonnelIds,
        );

        const sampleArchiveMap = new AuditorFrameworkSampleArchiveMap();
        sampleArchiveMap.auditorFrameworkSample = auditorFrameworkSample;
        sampleArchiveMap.type = AuditorFrameworkArchiveType.FRAMEWORK_ARCHIVE;
        sampleArchiveMap.companyArchive = companyArchive;
        await this.auditorFrameworkSampleArchiveMapRepository.save(sampleArchiveMap);

        return { pendingCompanyArchive: companyArchive, frameworkTypeName };
    }

    /**
     * @deprecated Use AuditorsOrchestrationService.setupAuditorFrameworkSample
     *
     * Setup audit sample by generating control evidence package.
     * @param account Tenant's account
     * @param user Auditor user
     * @param auditFirm Audit firm
     * @param params Request parameters
     * @param requestDto Request DTO
     */
    async setupAuditorFrameworkSample(
        account: Account,
        user: User,
        auditId: string,
        requestDto: AuditorSampleSetupRequestAuditorApiDto,
    ): Promise<void> {
        await this.generateControlEvidencePackage(account, user, user.entryId, {
            auditorFrameworkId: auditId,
            ...requestDto,
        });

        if (requestDto.generateAuditRequests) {
            const auditorFrameworkId = auditId;
            await this.customerRequestOrchestrationService.createAuditRequestsRequirements(
                account,
                {
                    auditorFrameworkId,
                },
            );
        }
    }

    async generateRequestControlEvidencePackageEvent(
        account: Account,
        user: User,
        requestDto: AuditorRequestControlEvidencePackageRequestDto,
    ): Promise<void> {
        const { selectedControlIds } = requestDto;

        /**
         * When downloading specific control evidence, validate controls are active
         */
        if (!isEmpty(selectedControlIds)) {
            const controls = await this.controlRepository.find({
                where: {
                    id: In(selectedControlIds),
                    enabledAt: Not(IsNull()),
                },
            });

            if (isEmpty(controls)) {
                throw new NotFoundException(ErrorCode.CONTROL_NOT_FOUND);
            }
        }

        this._eventBus.publish(
            new GenerateRequestControlEvidencePackageEvent(account, user, requestDto),
        );
    }

    /**
     * Build a .zip file containing the evidence by customer request
     * @param account Account
     * @param user User
     * @param requestDto Request DTO
     * @returns Signed URL
     */
    async generateRequestControlEvidencePackage(
        account: Account,
        user: User,
        requestDto: AuditorRequestControlEvidencePackageRequestDto,
    ): Promise<string | undefined> {
        this.log('Request Control Evidence requested', account, requestDto);
        const { selectedControlIds: controlIds } = requestDto;

        const customerRequest = await this.customerRequestRepository.findOneOrFail({
            where: { id: requestDto.requestId },
            relations: ['controls', 'controls.controlTestInstances'],
        });
        const company = await this.companiesCoreService.getCompanyByAccountId(account.id);

        const auditorFramework = await this.auditorFrameworkRepository.findOneOrFail({
            where: { id: customerRequest.auditorFrameworkId },
            join: {
                alias: 'auditorFramework',
                leftJoinAndSelect: {
                    auditorFrameworkAuditors: 'auditorFramework.auditorFrameworkAuditors',
                    auditorClient: 'auditorFrameworkAuditors.auditorClient',
                    entry: 'auditorClient.entry',
                },
            },
        });

        const sampleData = await this.getAuditorFrameworkSampleData(account, auditorFramework);
        const auditProduct = await this.workspacesCoreService.getProductById(
            auditorFramework.productId,
        );

        // Required by validations through the download evidence package process
        account.setCurrentProduct(auditProduct);

        this.log('Validating Auditor information', account, requestDto);

        const auditorEntryIds = auditorFramework.auditorFrameworkAuditors.map(
            a => a.auditorClient.entry.id,
        );
        const auditors = await this.auditorRepository.getAuditorsByEntryIds(auditorEntryIds);
        if (auditors?.length !== auditorEntryIds.length) {
            throwInvalidAuditorsException();
        }

        validateAuditorFramework(
            auditorFramework,
            auditorFramework?.frameworkType,
            sampleData.dates,
        );

        this.log('Validating requested personnel', account, requestDto);
        await validateControlEvidencePackagePersonnel(
            this.personnelCoreService,
            auditorFramework,
            sampleData.hiredPersonnelIds,
            sampleData.currentPersonnelIds,
            sampleData.formerPersonnelIds,
        );

        const personnelIds: AuditorPersonnelTypeIds = {
            [AuditorPersonnelStatus.CURRENT]: sampleData.currentPersonnelIds,
            [AuditorPersonnelStatus.HIRED]: sampleData.hiredPersonnelIds,
            [AuditorPersonnelStatus.FORMER]: sampleData.formerPersonnelIds,
        };

        this.logger.log(
            PolloAdapter.acct('Starting company request control evidence package', account),
        );

        let controlsToUse: Control[] = customerRequest.controls;
        if (!isNil(controlIds)) {
            this.log('Filter with selected controlIds');
            const controlsFiltered = customerRequest.controls.filter(control =>
                controlIds.includes(control.id),
            );
            controlsToUse = controlsFiltered;
        }

        const evidence = await this.getRequestControlEvidence(
            auditorFramework,
            account,
            user,
            controlsToUse,
            sampleData.dates,
            personnelIds,
        );

        this.logger.log(
            PolloAdapter.acct('Company request control evidence archive generated', account),
        );

        const baseFileName = `${customerRequest.code} - Control Evidence - ${fileNameDate()}.zip`;

        const fileName = await this.createControlEvidenceFilename(
            account,
            auditorFramework.productId,
            company.name,
            baseFileName,
        );

        const { passThrough, upload } = this.uploader.getPassThroughUploader({
            bucket: config.get('aws.s3.appBucket'),
            contentType: config.get('archive.contentType'),
            uploadType: UploadType.CONTROL_EVIDENCE_PACKAGE,
            originalName: this.CONTROL_EVIDENCE_ZIP_FILENAME,
            accountId: account.id,
            subFolder: config.get('archive.subFolder'),
        });

        const uploadPromise = upload.done();
        const currentPersonnelNames =
            await this.personnelCoreService.getPersonnelNamesFromAuditSampleDataByIds(
                sampleData.currentPersonnelIds,
            );

        const hiredPersonnelNames =
            await this.personnelCoreService.getPersonnelNamesFromAuditSampleDataByIds(
                sampleData.hiredPersonnelIds,
            );

        const formerPersonnelNames =
            await this.personnelCoreService.getPersonnelNamesFromAuditSampleDataByIds(
                sampleData.formerPersonnelIds,
            );

        const auditInfoFile = this.auditInfoService.createAuditInfoFile(
            account,
            company.name,
            auditorFramework.auditorFrameworkType.label,
            auditorFramework,
            sampleData.dates,
            currentPersonnelNames,
            hiredPersonnelNames,
            formerPersonnelNames,
        );
        evidence.push(auditInfoFile);

        const auditReadMeFile = this.auditInfoService.createAuditPackageReadMeFile();
        evidence.push(auditReadMeFile);

        this.log('Creating and Uploading zip file...', account);

        await createZipBufferWithPassThrough(
            flatten(evidence),
            null,
            passThrough,
            sampleData.platform,
        );

        this.logger.log(
            PolloAdapter.acct('zip buffer resolved', account).setSubContext(
                this.generateRequestControlEvidencePackage.name,
            ),
        );

        const data = await uploadPromise;
        this.logger.log(
            PolloAdapter.acct(
                'Company request control evidence archive uploaded',
                account,
            ).setIdentifier({
                fileName,
            }),
        );

        return (await this.downloader.getDownloadUrl(data.Key)).signedUrl;
    }

    /**
     * get latest auditor framework sample data
     * @param account
     * @param audit
     * @returns
     */
    async getAuditorFrameworkSampleData(
        account: Account,
        audit: Audit,
    ): Promise<AuditorControlEvidencePackageRequestDto> {
        const [auditorFrameworkSample, auditSamplePersonnel] = await Promise.all([
            this.getAuditorFrameworkSample(audit.id),
            this.getPersonnelIdListByAuditId(audit.id),
        ]);

        const hiredPersonnelIds = auditSamplePersonnel.hiredPersonnel.map(
            personnel => personnel.id,
        );
        const currentPersonnelIds = auditSamplePersonnel.currentPersonnel.map(
            personnel => personnel.id,
        );
        const formerPersonnelIds = auditSamplePersonnel.formerPersonnel.map(
            personnel => personnel.id,
        );

        const dates = !isEmpty(auditorFrameworkSample.dates)
            ? auditorFrameworkSample.dates.split(',')
            : [];
        const platform = auditorFrameworkSample.platform;

        return {
            auditorFrameworkId: audit.id,
            frameworkType: audit.frameworkType,
            customFrameworkId: audit.customFrameworkId,
            hiredPersonnelIds,
            currentPersonnelIds,
            formerPersonnelIds,
            dates,
            platform,
        };
    }

    /**
     * Refresh the .zip file containing the evidence by customer request
     * @param account Account
     * @param user User
     * @param requestDto Request DTO
     * @returns Signed URL
     */
    async refreshControlEvidencePackage(account: Account, user: User, auditorFrameworkId: string) {
        const audit = await this.auditorFrameworkRepository.findOneOrFail({
            where: { id: auditorFrameworkId },
            join: {
                alias: 'auditorFramework',
                leftJoinAndSelect: {
                    auditorFrameworkAuditors: 'auditorFramework.auditorFrameworkAuditors',
                    auditorClient: 'auditorFrameworkAuditors.auditorClient',
                    entry: 'auditorClient.entry',
                },
            },
        });

        if (isEmpty(audit.auditorFrameworkAuditors)) {
            throw new ConflictException(
                'Audit does not have any auditors',
                ErrorCode.AUDIT_WITH_NO_AUDITORS,
            );
        }

        const dto = await this.getAuditorFrameworkSampleData(account, audit);

        /**
         * If the user is an admin, we need to find an AUDITOR entry id to rebuild the package
         */
        const entryId = hasRole(user, [Role.AUDITOR])
            ? user.entryId
            : head(audit.auditorFrameworkAuditors).auditorClient.entry.id;

        await this.generateControlEvidencePackage(account, user, entryId, dto);
    }

    async refreshControlEvidencePackageViaWaaS(
        account: Account,
        user: User,
        auditId: string,
    ): Promise<void> {
        const audit = await this.auditorFrameworkRepository.findOneOrFail({
            where: { id: auditId },
            join: {
                alias: 'auditorFramework',
                leftJoinAndSelect: {
                    auditorFrameworkAuditors: 'auditorFramework.auditorFrameworkAuditors',
                    auditorClient: 'auditorFrameworkAuditors.auditorClient',
                    entry: 'auditorClient.entry',
                },
            },
        });

        if (isEmpty(audit.auditorFrameworkAuditors)) {
            throw new ConflictException(
                'Audit does not have any auditors',
                ErrorCode.AUDIT_WITH_NO_AUDITORS,
            );
        }

        const dto = await this.getAuditorFrameworkSampleData(account, audit);

        /**
         * If the user is an admin, we need to find an AUDITOR entry id to rebuild the package
         */
        const entryId = hasRole(user, [Role.AUDITOR])
            ? user.entryId
            : head(audit.auditorFrameworkAuditors).auditorClient.entry.id;

        return this.generateControlEvidencePackageAsWaaS(account, user, entryId, dto);
    }

    /**
     * Returns file buffer data for customer requests
     * @param audit Auditor framework
     * @param account Account
     * @param user User
     * @param controls Controls
     * @param dates Dates
     * @param personnelIds Personnel IDs
     * @returns .zip file buffer
     */
    private async getRequestControlEvidence(
        audit: Audit,
        account: Account,
        user: User,
        controls: Control[],
        dates: string[],
        personnelIds: AuditorPersonnelTypeIds,
    ): Promise<FileBufferType[]> {
        const doLog = (message: string) => {
            this.logger.log(PolloAdapter.acct(message, account));
        };

        const { frameworkType, customFrameworkId, productId } = audit;

        // Get the requirements for the requested framework type
        const frameworkTag = FrameworkTypeTags.get(frameworkType);

        const requirements = await this.requirementsCoreService.getRequirementsByFrameworkForAudit(
            frameworkTag,
            productId,
            customFrameworkId,
        );

        // Create a unique control array by control code and separate out of scope controls
        const outOfScopeControls: Control[] = [];
        const inScopeControls = uniqBy(controls, 'code').filter(control => {
            if (!isNil(control.archivedAt)) {
                outOfScopeControls.push(control);
                return false;
            }
            return true;
        });

        doLog('Getting Test Evidence, Regular Evidence and Out of Scope Controls CSV...');

        const combinedEvidenceResults = await Promise.allSettled([
            this.auditHubAuditPackagesOrchestrationService.getTestsEvidenceFileBuffersOrFail(
                inScopeControls,
                account,
                user,
                dates,
            ),
            this.grcEvidenceDownloadOrchestrationService.getRegularEvidenceBuffersOrFail(
                inScopeControls,
                account,
                audit,
            ),
            this.auditHubAuditPackagesOrchestrationService.getOutOfScopeControlsCsvFileBuffersOrFail(
                account,
                audit,
                outOfScopeControls,
            ),
        ]);

        const rejectedCombinedEvidencePromises = combinedEvidenceResults.filter(
            ({ status }) => status === REJECTED,
        );
        const combinedEvidence = combinedEvidenceResults
            .filter(item => item.status === FULFILLED && !isNil(item?.value))
            .flatMap<FileBufferType>(
                (item: PromiseFulfilledResult<FileBufferType[]>) => item.value,
            );

        doLog(
            `Test & Regular Evidence and Out of Scope Controls CSV combined count: ${combinedEvidence.length}`,
        );
        doLog(
            `Test & Regular Evidence and Out of Scope Controls CSV combined fail count: ${rejectedCombinedEvidencePromises.length}`,
        );

        const company = await this.companiesCoreService.getCompanyByAccountId(account.id);

        doLog('Getting Special Evidence...');
        const specialEvidenceResults = await Promise.allSettled([
            this.auditHubAuditPackagesOrchestrationService.getControlsPersonnelEvidenceFileBuffers(
                account,
                inScopeControls,
                personnelIds,
                user,
                this.personnelOrchestrationService,
            ),
            this.auditHubAuditPackagesOrchestrationService.getBoardOfDirectorsCsvFileBuffersOrFail(
                account,
                inScopeControls,
            ),
            this.auditHubAuditPackagesOrchestrationService.getTermsUrlEvidenceCsvFileBuffersOrFail(
                account,
                company,
                inScopeControls,
            ),
            this.auditHubAuditPackagesOrchestrationService.getPrivacyPolicyUrlCsvEvidenceFileBufersOrFail(
                account,
                company,
                inScopeControls,
            ),
            this.auditHubAuditPackagesOrchestrationService.getEmploymentAgreementsFileBuffersOrFail(
                account,
                inScopeControls,
            ),
            this.auditHubAuditPackagesOrchestrationService.getAssetsEvidenceLinksCsvFileBuffersOrFail(
                account,
                inScopeControls,
            ),
            this.auditHubAuditPackagesOrchestrationService.getControlsVendorsEvidenceFileBuffers(
                account,
                inScopeControls,
            ),
            this.auditHubAuditPackagesOrchestrationService.getJobDescriptionEvidenceFileBufferOrFail(
                account,
                inScopeControls,
            ),
            this.auditHubAuditPackagesOrchestrationService.getMSAEvidenceFileBufferOrFail(
                account,
                inScopeControls,
            ),
        ]);

        const rejectedSpecialEvidencePromises = specialEvidenceResults.filter(
            ({ status }) => status === REJECTED,
        );
        const specialEvidence = specialEvidenceResults
            .filter(item => item.status === FULFILLED && !isNil(item?.value))
            .flatMap<FileBufferType>(
                (item: PromiseFulfilledResult<FileBufferType[]>) => item.value,
            );

        doLog(`Special Evidence count: ${specialEvidence.length}`);
        doLog(`Special Evidence fail count: ${rejectedSpecialEvidencePromises.length}`);

        const allEvidence = flatten([...combinedEvidence, ...specialEvidence]);

        // Add a No Evidence.txt file to missing evidence directories
        const hasEvidence = {};
        for (const evidence of allEvidence) {
            const { filename } = evidence;
            const controlDir = this.getControlDir(filename);
            hasEvidence[controlDir] = true;
        }

        for (const control of inScopeControls) {
            const directory = getEvidenceDirectory(control.code, control.name);
            const controlDir = this.getControlDir(directory);
            if (!hasEvidence[controlDir]) {
                const noEvidenceDirectory = `${directory}No evidence from selected samples.txt`;
                allEvidence.push({
                    stream: Buffer.from(
                        // eslint-disable-next-line max-len
                        "There wasn't any evidence collected for the date and personnel samples you selected. Consider changing our evidence samples if you need to review evidence for this control.",
                    ),
                    filename: noEvidenceDirectory,
                });
            }
        }

        // Add a Business Rationale.txt file to requirement directories
        const rationaleFiles =
            await this.auditHubAuditPackagesOrchestrationService.getBusinessRationalesFileBuffers(
                audit,
                requirements.filter(requirement => !isNil(requirement)),
            );

        if (!isEmpty(rationaleFiles)) {
            allEvidence.push(...rationaleFiles.filter(r => !isNil(r)));
        }

        doLog(`Evidence count: ${allEvidence.length}`);
        return allEvidence;
    }

    /**
     * @deprecated Use AuditorsOrchestrationService.listAuditors
     *
     * @param {AuditorListRequestDto} dto
     * @param {Account} account
     */
    async listAuditors(
        dto: AuditorListRequestDto,
        account: Account,
        user: User,
    ): Promise<PaginationType<AuditorFrameworksUnreadMessagesType>> {
        // auditors have the restriction to only see their own firm members.
        const entry = await this.entryCoreService.getEntryWithoutRelationsByEmailOrFail(user.email);
        const auditor = await this.auditorRepository.findOneBy({
            firstName: user.firstName,
            lastName: user.lastName,
            entry: { id: entry.id },
        });
        const isAuditorReadOnly = hasRole(user, [Role.ACT_AS_READ_ONLY]) && !isNil(auditor);

        if (isAuditorReadOnly) {
            /**
             * TODO ENG-39556: firmName property should be removed once item ENG-39555 is closed
             */
            dto.firmName = auditor.auditFirm?.name ?? auditor.firmName;
        }
        const auditors = await this.auditorClientRepository.getAuditors(dto, account);

        this.filterDeletedCustomFrameworks(auditors);

        return auditors;
    }

    /**
     * @deprecated Use AuditorsCoreService.listAuditorFirms
     *
     * @param {AuditorFirmsRequestDto} reqObj
     * @param {Account} account
     */
    listAuditorFirms(reqObj: AuditorFirmsRequestDto, account: Account): Promise<string[]> {
        return this.auditorRepository.getAuditorFirms(reqObj, account);
    }

    /**
     * @deprecated Use AuditorsOrchestrationService.getAuditor
     *
     * @param {string} auditorId
     * @param {Account} account
     */
    async getAuditor(auditorId: string, account: Account): Promise<AuditorFrameworksType> {
        const auditor = await this.auditorClientRepository.getAuditor(auditorId, account);
        auditor.frameworks = await this.getAuditorFrameworksWithRelatedCustomFrameworks(
            account,
            auditor.frameworks,
        );
        return auditor;
    }

    /**
     * @deprecated use AuditorsCoreService.updateAuditorClient instead
     **/
    updateAuditorClient(accountId: AccountIdType, companyName: string): Promise<UpdateResult> {
        return this.auditorClientRepository.update(
            {
                account: {
                    id: accountId,
                },
            },
            {
                name: companyName,
            },
        );
    }

    /**
     * @deprecated use AuditorsCoreService.updateCompanyLogo instead
     **/
    async updateCompanyLogo(account: Account, logo: string): Promise<void> {
        await this.auditorClientRepository.update(
            {
                account: {
                    id: account.id,
                },
            },
            {
                logo,
            },
        );
    }

    async getAuditRequestOwners(
        requestDto: AuditOwnersRequestDto,
        auditId: string,
        account: Account,
    ): Promise<PaginationType<User>> {
        return this.usersCoreService.getAuditRequestOwnersByAuditId(requestDto, auditId);
    }

    /**
     *
     * @param id
     */
    private getAuditorById(id: string): Promise<Auditor> {
        return this.auditorRepository.findOneByOrFail({ id });
    }

    public async validateAccountAndAuditOrFail(account: Account, auditId: string): Promise<void> {
        if (isEmpty(account) || isNil(auditId)) {
            //Nothing to validate
            return;
        }
        const auditorFramework = await this.auditorFrameworkRepository.findOne({
            where: {
                id: auditId,
            },
            relations: ['account'],
        });

        if (isEmpty(auditorFramework)) {
            this.logger.log(PolloAdapter.acct(`Auditor with id ${auditId} not found`, account));
            return;
        }

        if (account.id === auditorFramework?.account.id) {
            this.logger.log(
                PolloAdapter.acct(
                    `Account with id ${account.id} properly validated to access audit with id ${auditId}`,
                    account,
                ),
            );
        } else {
            this.logger.error(
                PolloAdapter.acct(
                    `Account with id ${account.id} unauthorized access to audit with ${auditId}`,
                    account,
                ),
            );

            throw new UnauthorizedException(ErrorCode.AUDIT_ACCOUNT_MISMATCH);
        }
    }

    /**
     * @deprecated Use AuditorsOrchestrationService.getAuditorFrameworkRequestsSummary
     *
     * @param account
     * @param auditorFrameworkId
     * @returns
     */
    public async getAuditorFrameworkRequestsSummary(
        account: Account,
        auditorFrameworkId: string,
    ): Promise<AuditorFrameworkSummaryResponseType> {
        const [auditorFrameworkRequestsSummaryResponse, auditorFramework] = await Promise.all([
            this.auditorFrameworkRequestsSummaryRepository.findOne({
                where: {
                    auditorFrameworkId,
                },
            }),
            this.auditorFrameworkRepository.findOne({
                where: {
                    id: auditorFrameworkId,
                },
            }),
        ]);

        if (isNil(auditorFramework)) {
            throw new NotFoundException(
                `Auditor framework with ID ${auditorFrameworkId} was not found`,
            );
        }
        if (auditorFramework.frameworkType === AuditorFrameworkTypeEnum.CUSTOM) {
            const customFramework = await this.frameworksCoreService.getEnabledFrameworkByTag(
                FrameworkTag.CUSTOM,
                null,
                auditorFramework.customFrameworkId,
            );
            //this validation is just in case auditor relation with customFramework is not found
            if (!isNil(customFramework)) {
                auditorFramework.auditorFrameworkType.label = customFramework.name;
                auditorFramework.auditorFrameworkType.relatedFramework =
                    frameworkToFrameworkTemplateEntity(customFramework);
            }
        }
        const auditorFrameworkRequestsType: AuditorFrameworkSummaryResponseType = {
            auditorFramework,
            auditorFrameworkRequestsSummaryView: isNil(auditorFrameworkRequestsSummaryResponse)
                ? {
                      auditorFrameworkId,
                      outstandingRequests: 0,
                      inReviewRequests: 0,
                      acceptedRequests: 0,
                      totalRequests: 0,
                      acceptedPercentage: 0,
                  }
                : auditorFrameworkRequestsSummaryResponse,
        };

        return auditorFrameworkRequestsType;
    }

    /**
     *
     * @param email
     * @returns
     */
    async validateAuditorUser(email: string, user: User, account: Account): Promise<void> {
        if (isSupportUser(email)) {
            throw new PreconditionFailedException(ErrorCode.SUPPORT_USER_NOT_SUPPORTED);
        }

        if (isExcludedDomain(getDomainFromEmail(email))) {
            throw new ConflictException(
                // 'This domain is in the list of public domains to exclude',
                'Only corporate email addresses are allowed',
                ErrorCode.SERVICE_PROVIDER_NOT_ALLOWED_DOMAIN,
            );
        }

        const entry = await this.entryCoreService.getEntryByEmailNoFail(email, true);

        const userFound = await this.userRepository.findOne({
            where: {
                email,
            },
        });

        if (isNil(userFound) && isSameDomain(account.domain, email)) {
            throw new ConflictException(
                'Users within your company’s domain cannot be auditors',
                ErrorCode.EMPLOYEE_AUDITOR_CONFLICT,
            );
        }

        if (isNil(entry)) {
            // new user, all ok!
            return;
        }
        const auditor = await this.auditorRepository.findOne({
            where: { entry: { id: entry.id } },
        });

        const serviceUserInThisAccount = await this.serviceUserClientRepository.findOne({
            where: {
                entry: { id: entry.id },
                account: { id: account.id },
            },
        });

        const serviceUser = await this.serviceUserRepository.findOne({
            where: {
                entry: {
                    id: entry.id,
                },
            },
        });

        if (!isNil(serviceUser) && isNil(serviceUserInThisAccount)) {
            // user is able to be service user and auditor at the same time
            return;
        }
        if (!isNil(auditor) && isNil(serviceUserInThisAccount)) {
            // user is an auditor already
            return;
        }
        if (isSameDomain(account.domain, entry.email))
            // user is not an auditor already, can't cross role
            throw new ConflictException(
                'User cannot be auditor',
                ErrorCode.EMPLOYEE_AUDITOR_CONFLICT,
            );
    }

    /**
     * Update the timeframe for a given auditor framework
     * @param account Account
     * @param user User
     * @param auditorFrameworkId Auditor framework ID
     * @param requestDto Request DTO
     * @returns Updated auditor framework
     */
    public async putAuditorFrameworkTimeframe(
        account: Account,
        user: User,
        auditorFrameworkId: string,
        requestDto: AuditorFrameworkTimeframeRequestDto,
    ): Promise<Audit> {
        try {
            const auditorFramework = await this.auditorFrameworkRepository.findOne({
                where: { id: auditorFrameworkId },
                join: {
                    alias: 'auditorFramework',
                    leftJoinAndSelect: {
                        auditorFrameworkAuditors: 'auditorFramework.auditorFrameworkAuditors',
                        auditorClient: 'auditorFrameworkAuditors.auditorClient',
                        entry: 'auditorClient.entry',
                    },
                },
            });

            if (isNil(auditorFramework)) {
                throw new NotFoundException(
                    `Auditor framework with ID ${auditorFrameworkId} was not found`,
                );
            }
            const previousAuditorFrameworkStartDate = auditorFramework.startDate;
            const previousAuditorFrameworkEndDate = auditorFramework.endDate;

            auditorFramework.startDate = requestDto.startDate;
            auditorFramework.endDate = requestDto.endDate;

            const auditorsNotifiedCount = await this.checkAuditSampleDatesOutOfPeriod(
                account,
                auditorFramework,
                previousAuditorFrameworkStartDate,
                previousAuditorFrameworkEndDate,
            );

            this.log(
                `${auditorsNotifiedCount} auditor(s) notified about the auditor framework period update.`,
                account,
            );

            const [product, hasMultipleProducts] = await Promise.all([
                this.workspacesCoreService.getProductById(auditorFramework.productId),
                this.workspacesCoreService.hasMultipleProducts(account),
            ]);

            this._eventBus.publish(
                new AuditorFrameworkUpdatedEvent(
                    account,
                    user,
                    [auditorFramework],
                    requestDto,
                    product,
                    hasMultipleProducts,
                ),
            );

            return await this.auditorFrameworkRepository.save(auditorFramework);
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(`Auditor: ${error.message}`, account)
                    .setError(error)
                    .setContext(this.constructor.name)
                    .setSubContext('putAuditorFrameworkTimeframe'),
            );
        }
    }

    private async checkAuditSampleDatesOutOfPeriod(
        account: Account,
        // @AUDIT-REFACTOR: TODO rename to  audit
        auditorFramework: Audit,
        auditStartDate: string,
        auditEndDate: string,
    ): Promise<number> {
        let auditorUsersInfo: {
            auditorUser: User;
            auditorClientId: string;
        }[] = [];

        const sampleDates = await this.getAuditorFrameworkSampleDates(account, auditorFramework.id);

        if (
            !areSampleDatesWithinAuditorFrameworkPeriod(
                auditorFramework.startDate,
                auditorFramework.endDate,
                sampleDates,
            )
        ) {
            auditorUsersInfo = await Promise.all(
                auditorFramework.auditorFrameworkAuditors.map(async auditor => {
                    const userByEntryId = await this.usersCoreService.getUserByEntryId(
                        auditor.auditorClient.entry.id,
                    );
                    return {
                        auditorUser: userByEntryId,
                        auditorClientId: auditor.auditorClient.id,
                    };
                }),
            );

            auditorUsersInfo.forEach(userInfo => {
                this._eventBus.publish(
                    new AuditorSampleOutOfPeriodEvent(
                        account,
                        userInfo.auditorUser,
                        userInfo.auditorClientId,
                        auditorFramework,
                        auditStartDate,
                        auditEndDate,
                    ),
                );
            });
        }

        return auditorUsersInfo.length;
    }

    /**
     *
     * @param {Audit} auditorFramework
     * @param {Account} account
     * @returns {Promise<AuditorPersonnelType>}
     *
     * This returns the personnel auditor framework's personnel data
     */
    async getAuditorFrameworkPersonnelData(
        account: Account,
        // @AUDIT-REFACTOR: TODO rename to  audit
        auditorFramework?: Audit,
    ): Promise<AuditorPersonnelType> {
        if (isNil(auditorFramework)) {
            throw new ConflictException(
                'Requested framework not available ',
                ErrorCode.FRAMEWORK_NOT_RELATED,
            );
        }
        const formattedDates = getAuditPeriodByFramework(auditorFramework);
        return this.getPersonnelListByTimeRange(account, formattedDates);
    }

    /**
     *
     * @param {Account} account
     * @param {string} entryId
     * @param {AuditorPersonnelRequestDto} dto
     * @returns {Promise<AuditorPersonnelType>}
     */
    async getAuditorPersonnel(
        account: Account,
        entryId: string,
        dto: AuditorPersonnelRequestDto,
    ): Promise<AuditorPersonnelType> {
        const entry = await this.entryCoreService.getEntryById(entryId);
        const { auditorFrameworkAuditor } =
            (await this.getAuditorClientByAccountAndEntry(account, entry)) || {};
        const auditorFrameworks =
            auditorFrameworkAuditor?.map(mapItem => mapItem.auditorFramework) || [];

        // @AUDIT-REFACTOR: TODO rename to audit
        let auditorFramework: Audit;
        if (dto.frameworkType === AuditorFrameworkTypeEnum.CUSTOM) {
            const auditorFrameworksWithRelatedCustomFrameworks =
                await this.getAuditorFrameworksWithRelatedCustomFrameworks(
                    account,
                    auditorFrameworks,
                );
            auditorFramework = auditorFrameworksWithRelatedCustomFrameworks.find(
                framework => framework.id === dto.auditorFrameworkId,
            );
        } else {
            auditorFramework = auditorFrameworks.find(
                framework => framework && framework.id === dto.auditorFrameworkId,
            );
        }

        return this.getAuditorFrameworkPersonnelData(account, auditorFramework);
    }

    /**
     *
     * @param {Account} account
     * @param {string} auditorFrameworkId
     * @returns {Promise<AuditorPersonnelType>}
     *
     * This retrieves the list of personnel that can be selected in the ui when
     * creating an audit. These personnel lists are based on the auditor framework
     * start and end dates.
     */
    async getAuditorFrameworkAvailablePersonnel(
        account: Account,
        auditorFrameworkId: string,
    ): Promise<AuditorPersonnelType> {
        const workspace = account.getCurrentProduct();
        const workspaceId = get(workspace, 'id');

        let auditorFramework = await this.auditorFrameworkRepository.getAuditorFrameworkByIdOrFail(
            auditorFrameworkId,
            workspaceId,
        );

        if (auditorFramework.frameworkType === AuditorFrameworkTypeEnum.CUSTOM) {
            const auditorFrameworksWithRelatedCustomFrameworks =
                await this.getAuditorFrameworksWithRelatedCustomFrameworks(account, [
                    auditorFramework,
                ]);
            auditorFramework = auditorFrameworksWithRelatedCustomFrameworks.find(
                framework => framework.id === auditorFrameworkId,
            );
        }

        return this.getAuditorFrameworkPersonnelData(account, auditorFramework);
    }

    /**
     *
     * @param {Account} account
     * @param {string} auditorFrameworkId
     * @returns {Promise<AuditorFrameworkSampleData>}
     *
     * This method is used for the DOWNLOAD_ONLY_AUDIT to return
     * the list of personnel and platform associated to the auditor framework id
     * received as a parameter.
     */
    async getAuditSampleData(
        account: Account,
        auditorFrameworkId: string,
    ): Promise<AuditorFrameworkSampleData> {
        const auditorFrameworkSample = await this.auditorFrameworkSampleRepository.findOneOrFail({
            where: {
                auditorFrameworkId,
            },
            order: {
                createdAt: 'DESC',
            },
            relations: ['personnel', 'personnel.personnel'],
        });

        const hiredPersonnel: Personnel[] =
            auditorFrameworkSample?.personnel?.flatMap(personnelSample =>
                personnelSample.type === SamplePersonnelType.HIRED ? personnelSample.personnel : [],
            ) ?? [];
        const currentPersonnel: Personnel[] =
            auditorFrameworkSample?.personnel?.flatMap(personnelSample =>
                personnelSample.type === SamplePersonnelType.EMPLOYED_DURING_TIMEFRAME
                    ? personnelSample.personnel
                    : [],
            ) ?? [];
        const formerPersonnel: Personnel[] =
            auditorFrameworkSample?.personnel?.flatMap(personnelSample =>
                personnelSample.type === SamplePersonnelType.FORMER
                    ? personnelSample.personnel
                    : [],
            ) ?? [];

        const { platform, dates } = auditorFrameworkSample;

        return {
            platform,
            hiredPersonnel,
            currentPersonnel,
            formerPersonnel,
            dates,
        };
    }

    /**
     * @deprecated Use AuditorsOrchestrationService.getAuditorFrameworksWithRelatedCustomFrameworks
     *
     * @param account
     * @param audits
     * @returns
     */
    async getAuditorFrameworksWithRelatedCustomFrameworks(
        account: Account,
        audits: Audit[],
    ): Promise<Audit[]> {
        const completeAudits: Audit[] = [];
        if (isEmpty(audits)) {
            return completeAudits;
        }
        const customFrameworkIds = audits
            .filter(
                (auditFramework): auditFramework is Audit & { customFrameworkId: string } =>
                    auditFramework?.frameworkType === AuditorFrameworkTypeEnum.CUSTOM &&
                    typeof auditFramework.customFrameworkId === 'string',
            )
            .map(auditFramework => auditFramework.customFrameworkId);

        const customFrameworks = await this.frameworksCoreService.getEnabledFrameworksByTags(
            FrameworkTag.CUSTOM,
            null,
            customFrameworkIds,
        );

        const customFrameworkMap = new Map(
            customFrameworks.map(customFramework => [
                customFramework.customFrameworkId,
                customFramework,
            ]),
        );

        for (const audit of audits) {
            if (!audit) {
                continue;
            }

            const { frameworkType, customFrameworkId } = audit;

            if (frameworkType !== AuditorFrameworkTypeEnum.CUSTOM) {
                completeAudits.push(audit);
                continue;
            }

            const customFramework = customFrameworkMap.get(customFrameworkId);

            if (!customFramework) {
                // Framework related not found, jump this auditor framework
                continue;
            }

            audit.auditorFrameworkType.relatedFramework =
                frameworkToFrameworkTemplateEntity(customFramework);
            completeAudits.push(audit);
        }
        return completeAudits;
    }

    private getControlDir(path: string): string {
        const paths = path.split('/');
        const evidenceIndex = paths.findIndex((dir: string) => dir === 'Evidence');
        return paths[evidenceIndex + 1];
    }

    /**
     *
     * @param account
     * @param dto
     * @returns
     */
    private async getAuditorFrameworkListByAccount(
        account: Account,
        dto: AuditorAuditsRequestDto,
    ): Promise<PaginationType<Audit>> {
        return this.auditorFrameworkRepository.getAuditorFrameworksByAccount(account, dto);
    }

    /**
     *
     * @param auditorFrameworks
     * @returns
     */
    async buildAuditsWithAuditors(
        // @AUDIT-REFACTOR: TODO rename to audits
        auditorFrameworks: Audit[],
    ): Promise<AuditorFrameworksListType[]> {
        const auditList = [];
        for (const af of auditorFrameworks) {
            const entryIds = af.auditorFrameworkAuditors
                .filter(aFa => !isNil(aFa.auditorClient))
                .map(aFA => aFA.auditorClient.entry.id);
            const auditors = await this.auditorRepository.find({
                where: {
                    entry: {
                        id: In(entryIds),
                    },
                },
            });

            auditList.push({
                ...af,
                auditors,
            });
        }
        return auditList;
    }

    async getNotificationsStats(account: Account, user: User): Promise<NotificationsStats> {
        const hasOnlyWorkspaceAdminRole =
            hasRole(user, [Role.WORKSPACE_ADMINISTRATOR]) && user.roles.length === 2;

        const [
            unreadMessagesCount,
            pendingTrustCenterRequests,
            auditorFrameworks,
            customFrameworks = [],
        ] = await Promise.all([
            this.customerRequestListViewRepository.getTotalUnreadMessagesByFramework(false),
            hasOnlyWorkspaceAdminRole
                ? 0
                : this.trustCenterRequestRepository.getPendingRequest(account),
            this.auditorFrameworkRepository.findWithQuery({
                where: {
                    ['account.id']: account.id,
                    productId: account.getCurrentProduct().id,
                    completedAt: null,
                },
                select: {
                    id: true,
                    frameworkType: true,
                    customFrameworkId: true,
                },
            }),
            this.productFrameworkRepository.findWithQuery({
                relations: {
                    framework: true,
                },
                where: {
                    productId: account.getCurrentProduct().id,
                    enabledAt: { not$: null },
                    framework: {
                        enabledAt: { not$: null },
                        tag: FrameworkTag.CUSTOM,
                    },
                },
                select: {
                    id: true,
                    framework: {
                        name: true,
                        customFrameworkId: true,
                    },
                },
            }),
        ]);

        const mappedCustomFrameworks = keyBy(customFrameworks, 'framework.customFrameworkId');

        const audits = chain(auditorFrameworks)
            .map(({ frameworkType, id: auditorFrameworkId, customFrameworkId }) => ({
                auditorFrameworkId,
                frameworkType,
                unreadMessages: unreadMessagesCount[auditorFrameworkId] ?? 0,
                frameworkName: get(
                    mappedCustomFrameworks[customFrameworkId],
                    'framework.name',
                    null,
                ),
            }))
            .filter(audit => audit.unreadMessages > 0)
            .orderBy('unreadMessages', 'desc')
            .value();

        return {
            audits,
            pendingTrustCenterRequests,
        };
    }

    /**
     *
     * @param requestDto
     * @param auditor
     */
    async getAuditorFrameworkListData(
        entryId: string,
        requestDto: OldAuditorsClientsRequestDto,
        accountId: AccountIdType,
    ): Promise<PaginationType<AuditorClientListType>> {
        const entry = await this.entryCoreService.getEntryById(entryId);
        const auditor = await this.getAuditorByEntry(entry);

        /**
         * this is to prevent an admin from accessing any client list not in their account
         **/
        const hasAccess = await this.auditorClientRepository.findOne({
            where: {
                entry: { id: entryId },
                account: { id: accountId },
            },
        });

        if (!hasAccess) {
            throw new ForbiddenException(
                'Auditor cannot access audit.',
                ErrorCode.TENANT_AUDITOR_FORBIDDEN,
            );
        }

        const clientList = await this.auditorClientRepository.getAuditorClients(
            requestDto,
            auditor.entry.id,
        );

        await this.buildCustomFrameworksClient(clientList);

        await this.addExtraInformationToFrameworks(clientList, auditor);

        /**
         * this is to avoid the auditor view to be broken when an auditor has a custom framework deleted
         */
        clientList.data = clientList.data.filter(
            client =>
                !isEmpty(client.auditorFrameworkAuditor.map(mapItem => mapItem.auditorFramework)),
        );

        return clientList;
    }

    /**
     *
     * @param {Account} account
     * @param {Audit[]  | AuditorFrameworksListType[]} auditorFrameworks
     * @param {Auditor} auditor
     * @returns
     */
    private async addExtraInformationToFrameworksByAccount(
        account: Account,
        // @AUDIT-REFACTOR: TODO rename to audits
        auditorFrameworks: Audit[] | AuditorFrameworksListType[],
        auditor?: Auditor,
    ): Promise<AuditorFrameworksListType[]> {
        const auditorFrameworkListData = await this.auditorFrameworkListView.find();

        let isAuditor = false;

        if (!isNil(auditor)) {
            const userAuditor = await this.usersCoreService.getUserByEmail(auditor.entry.email);
            isAuditor = hasRole(userAuditor, [Role.AUDITOR]);
        }

        const pathToOmit = ['customerUnreadMessages', 'auditorUnreadMessages'];

        const pathToUnreadMessages = isAuditor ? 'auditorUnreadMessages' : 'customerUnreadMessages';

        const auditorFrameworkListDataFiltered = auditorFrameworkListData.map(listFramework => {
            const filteredFramework = omit(listFramework, pathToOmit);
            return {
                ...filteredFramework,
                ['unreadMessages']: !isNil(listFramework[pathToUnreadMessages])
                    ? listFramework[pathToUnreadMessages]
                    : 0,
            };
        });

        return auditorFrameworks.map(framework => ({
            ...framework,
            ...auditorFrameworkListDataFiltered.find(
                afExtraInfo => afExtraInfo.auditorFrameworkId === framework.id,
            ),
        }));
    }

    /**
     *
     * @param {PaginationType<any>} clientList
     * @param {Auditor} auditor
     */
    async addExtraInformationToFrameworks(
        clientList: PaginationType<any>,
        auditor: Auditor,
    ): Promise<void> {
        for (const client of clientList.data) {
            client.auditorFrameworks = await tenantWrapper(client.account, async () =>
                this.addExtraInformationToFrameworksByAccount(
                    client.account,
                    client.auditorFrameworks || [],
                    auditor,
                ),
            );
        }
    }

    /**
     *
     * @param {Account} account
     * @param {string} auditorFrameworkId
     * @returns {Promise<AuditorFrameworkSampleArchiveMap>}
     */
    private async getLatestAuditorFrameworkEvidenceMap(
        account: Account,
        auditorFrameworkId: string,
    ): Promise<AuditorFrameworkSampleArchiveMap> {
        try {
            const auditorFrameworkSample =
                await this.auditorFrameworkSampleRepository.findOneOrFail({
                    where: {
                        auditorFrameworkId,
                    },
                    relations: [
                        'companyArchives',
                        'companyArchives.companyArchive',
                        'companyArchives.companyArchive.product',
                    ],
                    order: {
                        id: 'DESC',
                    },
                });

            return auditorFrameworkSample.companyArchives?.find(
                companyArchiveMap =>
                    companyArchiveMap.type == AuditorFrameworkArchiveType.FRAMEWORK_ARCHIVE,
            );
        } catch (error) {
            this.logger.warn(
                PolloAdapter.acct(`Audit is not started by the Auditor`, account)
                    .setContext(this.constructor.name)
                    .setSubContext('getLatestsAuditorFrameworkEvidenceMap'),
            );
            return null;
        }
    }

    /**
     * Get latest auditor framework evidence archive download data
     * @param account Account
     * @param user User
     * @param auditorFrameworkId Auditor framework id
     * @returns Download data
     */
    @CreateEvent<DownloaderPayloadType>(
        EventType.COMPANY_PACKAGE_DOWNLOADED,
        EventCategory.COMPANY,
        '${user.firstName} ${user.lastName} has downloaded a package from the Download Center',
    )
    async getLastAuditorFrameworkEvidenceArchive(
        account: Account,
        user: User,
        auditorFrameworkId: string,
    ): Promise<DownloaderPayloadType> {
        return this.getLastControlsPackage(account, auditorFrameworkId);
    }

    /**
     * get latest controls package
     * @param account
     * @param auditorFrameworkId
     * @returns
     */
    async getLastControlsPackage(
        account: Account,
        auditorFrameworkId: string,
    ): Promise<DownloaderPayloadType> {
        const lastControlEvidenceArchiveMap = await this.getLatestAuditorFrameworkEvidenceMap(
            account,
            auditorFrameworkId,
        );

        if (!lastControlEvidenceArchiveMap) {
            return null;
        }

        return this.downloader.getDownloadUrl(lastControlEvidenceArchiveMap.companyArchive.file);
    }

    /**
     * Checks if the latest auditor framework evidence exists
     * @param account Account
     * @param auditorFrameworkId Auditor framework id
     * @returns true if the latest auditor framework evidence is not null
     */
    async pingLastAuditorFrameworkEvidence(
        account: Account,
        auditorFrameworkId: string,
    ): Promise<ControlEvidencePingInformation> {
        const lastControlEvidenceArchiveMap = await this.getLatestAuditorFrameworkEvidenceMap(
            account,
            auditorFrameworkId,
        );

        return {
            found: !isNil(lastControlEvidenceArchiveMap),
            companyArchiveStatus: lastControlEvidenceArchiveMap?.companyArchive?.status,
            companyArchiveLastUpdatedAt: lastControlEvidenceArchiveMap?.companyArchive?.updatedAt,
        };
    }

    async sendLatestAuditorFrameworkEvidenceArchiveEmail(
        account: Account,
        user: User,
        auditorFrameworkId: string,
    ): Promise<void> {
        const lastControlEvidenceArchiveMap = await this.getLatestAuditorFrameworkEvidenceMap(
            account,
            auditorFrameworkId,
        );

        if (isNil(lastControlEvidenceArchiveMap)) {
            throw new OurNotFoundException(ErrorCode.EVIDENCE_NOT_FOUND);
        }

        const { companyArchive } = lastControlEvidenceArchiveMap;

        const { product } = companyArchive;
        const productName = get(product, 'name', '');

        const auditorFramework = await this.auditorFrameworkRepository.findOneBy({
            id: auditorFrameworkId,
        });

        if (isNil(auditorFramework)) {
            throw new NotFoundException(
                `Auditor framework with ID ${auditorFrameworkId} does not exist`,
            );
        }

        const frameworkTypeName = AuditorFrameworkTypeNames.get(auditorFramework.frameworkType);
        const hasMultipleProducts = await this.workspacesCoreService.hasMultipleProducts(account);
        const isResend = true;

        this._eventBus.publish(
            new AuditorControlEvidencePackageEmailEvent(
                account,
                user,
                companyArchive.status,
                frameworkTypeName,
                productName,
                hasMultipleProducts,
                isResend,
            ),
        );
    }

    /**
     * @deprecated Use AuditorsOrchestrationService.createControlEvidenceFilename
     *
     * @param {Account} account
     * @param {number} productId
     * @param {string} companyName
     * @param {string} baseFileName
     * @returns
     */
    async createControlEvidenceFilename(
        account: Account,
        productId: number,
        companyName: string,
        baseFileName: string,
    ): Promise<string> {
        const product = await this.workspacesCoreService.getProductById(productId);
        const productName = get(product, 'name');

        return isMultiProductEnabled(account)
            ? `${productName} - ${baseFileName}`
            : `${companyName} - ${baseFileName}`;
    }

    /**
     *
     * @param userData
     * @param entry
     * @param account
     * @param newAuditorUser
     * @param adminUser
     * @returns
     */
    private async createNewAuditor(
        userData: OldAuditorRequestDto,
        entry: Entry,
        account: Account,
        newAuditorUser: User,
        adminUser: User,
    ): Promise<AuditorFrameworkAuditorsType> {
        const { firmName, firstName, lastName, email } = userData;

        let auditor = await this.auditorRepository.findOne({
            where: { entry: { id: entry.id } },
        });

        if (isNil(auditor)) {
            auditor = new Auditor();
            auditor.firstName = firstName;
            auditor.lastName = lastName;
            auditor.language = account.language;
            auditor.drataTermsAgreedAt = newAuditorUser.drataTermsAgreedAt;
            auditor.entry = entry;
            auditor.auditFirm = await this.getAuditFirm(getDomainFromEmail(email), firmName);
            auditor.firmName = auditor.auditFirm.name;
            auditor = await this.auditorRepository.save(auditor);
        }

        let auditorClient = await this.getAuditorClientByAccountAndEntry(account, entry);

        if (isNil(auditorClient)) {
            const company = await this.companiesCoreService.getCompanyByAccountId(account.id);

            auditorClient = new AuditorClient();
            auditorClient.name = account.companyName;
            auditorClient.contactFirstName = adminUser.firstName;
            auditorClient.contactLastName = adminUser.lastName;
            auditorClient.contactEmail = adminUser.email;
            auditorClient.logo = company.logo;
            auditorClient.entry = entry;
            auditorClient.account = account;
            auditorClient.allowDownloads = false;
            auditorClient.allowAuditKeyAccess = true;
            auditorClient = await this.auditorClientRepository.save(auditorClient);
        }

        this._eventBus.publish(
            new AuditorCreatedEvent(
                account,
                adminUser,
                { ...userData, frameworks: [] },
                newAuditorUser,
                auditor,
                [],
            ),
        );

        return { auditor, auditorClient };
    }

    /**
     *
     * @param account
     * @param auditorFramework
     * @param auditorClient
     * @returns
     */
    private async createAuditorFrameworkAuditors(
        account: Account,
        // @AUDIT-REFACTOR: TODO rename to audit
        auditorFramework: Audit,
        auditorClient: AuditorClient,
    ): Promise<AuditorFrameworkAuditors> {
        try {
            const newAuditorFrameworkAuditors = new AuditorFrameworkAuditors();
            newAuditorFrameworkAuditors.auditorFramework = auditorFramework;
            newAuditorFrameworkAuditors.auditorClient = auditorClient;
            return await this.auditorFrameworkAuditorsRepository.save(newAuditorFrameworkAuditors);
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(error.message, account)
                    .setError(error)
                    .setContext(this.constructor.name)
                    .setSubContext(this.createAuditorFrameworkAuditors.name),
            );
            throw error;
        }
    }

    /**
     *
     * @param account
     * @param user
     * @param dto
     * @returns
     */
    async postAuditor(
        account: Account,
        user: User,
        dto: OldAuditorRequestDto,
    ): Promise<AuditorFrameworkAuditorsType> {
        try {
            const { firstName, lastName, email, firmName } = dto;
            if (isEmpty(firstName) || isEmpty(lastName) || isEmpty(firmName)) {
                throw new ConflictException(
                    'Auditor Information is wrong',
                    ErrorCode.AUDITOR_INVALID_DATA,
                );
            }
            await this.validateAuditorUser(email, user, account);
            const entry = await this.createEntry(email, account);
            const newAuditorUser = await this.createUser(dto, account, entry);

            return await this.createNewAuditor(dto, entry, account, newAuditorUser, user);
        } catch (error) {
            this.logger.logConditionalWarningOrError(
                PolloAdapter.acct(error.message, account)
                    .setError(error)
                    .setContext(this.constructor.name)
                    .setSubContext(this.postAuditor.name),
            );
            throw error;
        }
    }

    async validateSamplePersonnelInAuditPeriodDateRange(auditId: string): Promise<boolean> {
        const auditorFramework = await this.auditorFrameworkRepository.findOneOrFailWithQuery({
            where: { id: auditId },
        });
        const auditorFrameworkSample = await this.auditorFrameworkSampleRepository.findOne({
            where: {
                auditorFrameworkId: auditId,
            },
        });
        if (!auditorFrameworkSample) {
            // Auditor sample is not yet set
            return true;
        }
        const { hiredPersonnel, currentPersonnel, formerPersonnel } =
            await this.getPersonnelIdListByAuditId(auditId);

        const { startDate, endDate } = getAuditPeriodByFramework(auditorFramework);

        return this.isAllPersonnelValid(
            hiredPersonnel,
            currentPersonnel,
            formerPersonnel,
            startDate,
            endDate,
        );
    }

    private async isAllPersonnelValid(
        hiredPersonnels: Personnel[],
        currentPersonnels: Personnel[],
        formerPersonnels: Personnel[],
        startDate: string,
        endDate: string,
    ) {
        const [isHiredValid, isFormerValid, isCurrentValid] = await Promise.all([
            this.isHiredPersonnelsValidForAuditPeriod(
                this.personnelRepository,
                hiredPersonnels,
                startDate,
                endDate,
            ),
            this.isFormerPersonnelsValidForAuditPeriod(
                this.personnelRepository,
                formerPersonnels,
                startDate,
                endDate,
            ),
            this.isCurrentPersonnelsValidForAuditPeriod(
                this.personnelRepository,
                currentPersonnels,
                startDate,
                endDate,
            ),
        ]);

        return isCurrentValid && isFormerValid && isHiredValid;
    }

    private async isHiredPersonnelsValidForAuditPeriod(
        personnelRepository: PersonnelRepository,
        hiredPersonnels: Personnel[],
        auditPeriodStartDate: string,
        auditPeriodEndDate: string,
    ): Promise<boolean> {
        const actualHiredPersonnelsCount =
            await personnelRepository.countSampledHiredPersonnelInDateRange(
                hiredPersonnels.map(personnel => personnel.id),
                auditPeriodStartDate,
                auditPeriodEndDate,
            );
        return actualHiredPersonnelsCount === hiredPersonnels.length;
    }

    private async isCurrentPersonnelsValidForAuditPeriod(
        personnelRepository: PersonnelRepository,
        currentPersonnels: Personnel[],
        auditPeriodStartDate: string,
        auditPeriodEndDate: string,
    ): Promise<boolean> {
        const actualCurrentPersonnelsCount =
            await personnelRepository.countSampledCurrentPersonnelInDateRange(
                currentPersonnels.map(personnel => personnel.id),
                auditPeriodStartDate,
                auditPeriodEndDate,
            );
        return actualCurrentPersonnelsCount === currentPersonnels.length;
    }

    private async isFormerPersonnelsValidForAuditPeriod(
        personnelRepository: PersonnelRepository,
        formerPersonnels: Personnel[],
        auditPeriodStartDate: string,
        auditPeriodEndDate: string,
    ): Promise<boolean> {
        const actualFormerPersonnelsCount =
            await personnelRepository.countSampledFormerPersonnelInDateRange(
                formerPersonnels.map(personnel => personnel.id),
                auditPeriodStartDate,
                auditPeriodEndDate,
            );
        return actualFormerPersonnelsCount === formerPersonnels.length;
    }

    /**
     * Assign an auditor to an auditor framework
     * @param account Account
     * @param entryId Auditor's Entry ID
     * @param auditorFrameworkId Auditor Framework ID
     * @returns Auditor Framework Data
     */
    async assignAuditorToFramework(
        account: Account,
        entryId: string,
        auditorFrameworkId: string,
        user?: User,
    ): Promise<Audit> {
        try {
            const auditorClient = await this.getAuditorClientByEntryId(entryId, account.id);

            if (isNil(auditorClient)) {
                throw new NotFoundException(`Auditor client was not found for entry ID ${entryId}`);
            }

            const auditorFramework = await this.auditorFrameworkRepository.findOneBy({
                id: auditorFrameworkId,
            });

            if (isNil(auditorFramework)) {
                throw new NotFoundException(
                    `Auditor framework with ID ${auditorFrameworkId} does not exist`,
                );
            }

            const result = await this.createAuditorFrameworkAuditors(
                account,
                auditorFramework,
                auditorClient,
            );

            const newAuditorUser = await this.usersCoreService.getUserByEntryId(entryId);
            this._eventBus.publish(new InviteMagicLinkAuditorEvent(newAuditorUser, account));

            if (!isNil(user)) {
                void this.publishAuditorAddedToAuditEvent(
                    account,
                    auditorFramework,
                    user,
                    newAuditorUser,
                    result.updatedAt,
                );
            }
            return result.auditorFramework;
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(error.message, account)
                    .setError(error)
                    .setContext(this.constructor.name)
                    .setSubContext(this.assignAuditorToFramework.name),
            );
            throw error;
        }
    }

    async updateDownloadOnlyAudit(
        account: Account,
        auditorFrameworkId: string,
        requestDto: AuditFrameworkDownloadOnlyUpdateRequestDto,
    ): Promise<Audit> {
        try {
            const auditorFramework =
                await this.auditorFrameworkRepository.getDownloadOnlyAuditWithAccount(
                    auditorFrameworkId,
                );

            if (auditorFramework?.frameworkType === AuditorFrameworkTypeEnum.CUSTOM) {
                const auditorFrameworkWithCustom =
                    await this.getAuditorFrameworksWithRelatedCustomFrameworks(account, [
                        auditorFramework,
                    ]);
                auditorFramework.frameworkType = auditorFrameworkWithCustom[0].frameworkType;
            }

            if (isEmpty(auditorFramework)) {
                throw new NotFoundException(`Could not find audit`);
            }

            if (account.id !== auditorFramework?.account.id) {
                throw new ForbiddenException(
                    'User cannot access audit.',
                    ErrorCode.AUDIT_AUDITOR_FORBIDDEN,
                );
            }

            auditorFramework.startDate = requestDto.startDate;
            auditorFramework.endDate = requestDto.endDate;

            return await this.auditorFrameworkRepository.save(auditorFramework);
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(error.message, account)
                    .setError(error)
                    .setContext(this.constructor.name)
                    .setSubContext(this.updateDownloadOnlyAudit.name),
            );
            throw error;
        }
    }

    /**
     *
     * @param accountId
     * @param entryId
     * @returns
     */
    async getAuditorClientByEntryId(
        entryId: string,
        accountId: AccountIdType,
    ): Promise<AuditorClient> {
        return this.auditorClientRepository.findOne({
            where: { entry: { id: entryId }, account: { id: accountId } },
            join: {
                alias: 'auditorClient',
                leftJoinAndSelect: {
                    account: 'auditorClient.account',
                    entry: 'auditorClient.entry',
                },
            },
        });
    }

    /**
     *
     * @param {AuditorListRequestDto} dto
     * @param {Account} account
     */
    async getAuditorsList(
        account: Account,
        dto: AuditorListRequestDto,
    ): Promise<PaginationType<Auditor>> {
        return this.auditorRepository.getAuditorsList(account.id, dto);
    }

    /**
     * Delete auditor
     * @param account Account
     * @param user User
     * @param entryId Auditor's Entry ID
     */
    async deleteAuditor(account: Account, user: User, entryId: string): Promise<void> {
        try {
            const auditor = await this.getAuditorByEntryIdOrFail(entryId);
            const auditorClient = await this.getAuditorClientByEntryId(entryId, account.id);
            const auditorUser = await this.usersCoreService.getUserByEmail(auditor.entry.email);

            //Unlink auditor from audits
            const deletedAudits = await this.deleteAuditFrameworkAuditor(account, entryId);
            //SoftDelete Auditor
            await this.usersCoreService.softDeleteAuditorRole(auditorUser);
            await this.auditorClientRepository.softRemove(auditorClient);

            const auditIds = deletedAudits.map(audit => audit.id);
            const audits = !isEmpty(auditIds)
                ? await this.auditorFrameworkAuditorsRepository.find({
                      where: { id: In(auditIds) },
                      withDeleted: true,
                      relations: ['auditorFramework'],
                  })
                : [];
            const frameworks = audits.map(audit => audit.auditorFramework);
            this._eventBus.publish(new AuditorRevokedEvent(account, user, auditor, frameworks));
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(error.message, account)
                    .setError(error)
                    .setContext(this.constructor.name)
                    .setSubContext(this.deleteAuditor.name),
            );
            throw error;
        }
    }

    /**
     *
     * @param account
     * @param entryId
     * @param auditId
     * @returns
     */
    async deleteAuditFrameworkAuditor(
        account: Account,
        entryId: string,
        auditId?: string,
        user?: User,
    ): Promise<AuditorFrameworkAuditors[]> {
        try {
            const auditorClient = await this.getAuditorClientByEntryId(entryId, account.id);

            const auditorFrameworkAuditors = !isEmpty(auditId)
                ? await this.auditorFrameworkAuditorsRepository.find({
                      where: {
                          auditorClient: { id: auditorClient.id },
                          auditorFramework: { id: auditId },
                      },
                  })
                : await this.auditorFrameworkAuditorsRepository.find({
                      where: {
                          auditorClient: { id: auditorClient.id },
                      },
                  });

            let deletedAuditorFrameworkAuditors: AuditorFrameworkAuditors[] = [];
            if (!isEmpty(auditorFrameworkAuditors)) {
                auditorFrameworkAuditors.forEach(auditAuditor => {
                    auditAuditor.deletedAt = new Date();
                });

                deletedAuditorFrameworkAuditors =
                    await this.auditorFrameworkAuditorsRepository.save(auditorFrameworkAuditors);
            }

            if (!isNil(auditId) && !isNil(user)) {
                void this.publishAuditorRemovedFromAuditEvent(
                    account,
                    auditId,
                    entryId,
                    user,
                    deletedAuditorFrameworkAuditors[0].updatedAt,
                );
            }

            return deletedAuditorFrameworkAuditors;
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(error.message, account)
                    .setError(error)
                    .setContext(this.constructor.name)
                    .setSubContext(this.deleteAuditFrameworkAuditor.name),
            );
            throw error;
        }
    }

    /**
     * Get controls related by framework
     * @param account Account
     * @param auditFrameworkId Audit framework ID
     * @param dto Request DTO
     * @returns List of controls related by framework
     */
    async getFrameworkRelatedControls(
        account: Account,
        auditFrameworkId: string,
        dto: AuditFrameworkControlsRequestDto,
    ): Promise<PaginationType<Control>> {
        try {
            const auditorFramework = await this.auditorFrameworkRepository.findOneBy({
                id: auditFrameworkId,
            });

            if (isNil(auditorFramework)) {
                throw new NotFoundException(
                    `Auditor framework with ID ${auditFrameworkId} does not exist.`,
                );
            }

            const frameworkTag = FrameworkTypeTags.get(auditorFramework.frameworkType);
            const framework = await this.frameworksCoreService.getEnabledFrameworkByTag(
                frameworkTag,
                auditorFramework.productId,
                auditorFramework.customFrameworkId,
            );

            return await this.listFrameworkRelatedControls(account, framework.id, dto);
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(error.message, account)
                    .setError(error)
                    .setContext(this.constructor.name)
                    .setSubContext(this.getFrameworkRelatedControls.name),
            );
            throw error;
        }
    }

    private async publishAuditorRemovedFromAuditEvent(
        account: Account,
        auditId: string,
        entryId: string,
        user: User,
        updatedAt: Date,
    ): Promise<void> {
        const auditorFramework = await this.auditorFrameworkRepository.findOneBy({ id: auditId });

        const currentProduct = await this.workspacesCoreService.getProductById(
            auditorFramework.productId,
        );
        const removedAuditorUser = await this.usersCoreService.getUserByEntryId(entryId);

        this._eventBus.publish(
            new AuditorRemovedFromAuditEvent(
                auditorFramework,
                user,
                removedAuditorUser,
                account,
                currentProduct,
                updatedAt,
            ),
        );
    }

    private async publishAuditorAddedToAuditEvent(
        account: Account,
        auditorFramework: Audit,
        user: User,
        newAuditorUser: User,
        updatedAt: Date,
    ): Promise<void> {
        const currentProduct = await this.workspacesCoreService.getProductById(
            auditorFramework.productId,
        );
        this._eventBus.publish(
            new AuditorAddedToAuditEvent(
                auditorFramework,
                user,
                newAuditorUser,
                account,
                currentProduct,
                updatedAt,
            ),
        );
    }

    /**
     * Gets audit firm by searching by firm name
     * @param firmDomain Audit firm domain
     * @param firmName Audit firm name
     * @returns Audit firm entity
     */
    private async getAuditFirm(firmDomain: string, firmName?: string): Promise<AuditFirm> {
        const existingAuditFirm = await this.auditFirmRepository.findOneWithQuery({
            select: {
                '...': true,
                auditors: {
                    '...': true,
                    auditFirm: false,
                },
            },
            where: { domain: firmDomain },
            relations: { auditors: true },
        });

        if (!isNil(existingAuditFirm)) {
            if (existingAuditFirm?.name != firmName) {
                // update auditor.firmName (custom firm name) for the other auditors
                await this.updateAuditorsCustomFirm(existingAuditFirm, firmName);
                existingAuditFirm.name = firmName;
                return this.auditFirmRepository.save(existingAuditFirm);
            } else {
                return existingAuditFirm;
            }
        }

        const newAuditFirm = new AuditFirm();
        if (!isNil(firmName)) {
            newAuditFirm.name = firmName;
        }
        newAuditFirm.domain = firmDomain;

        return this.auditFirmRepository.save(newAuditFirm);
    }

    async updateAuditorsCustomFirm(auditFirm: AuditFirm, newAuditFirmName?: string) {
        const { auditors, name: oldAuditFirmName } = auditFirm;
        const auditorsToUpdate: (Auditor | null)[] = [];
        for (const auditor of auditors) {
            const auditorToUpdate = this.updateAuditorCustomFirm(
                auditor,
                newAuditFirmName,
                oldAuditFirmName,
            );
            auditorsToUpdate.push(auditorToUpdate);
        }
        await this.auditorRepository.save(compact(auditorsToUpdate));
    }

    updateAuditorCustomFirm(
        auditor: Auditor,
        newAuditFirmName?: string,
        oldAuditFirmName?: string | null,
    ): Auditor | null {
        const haveCustomFirmName =
            !isEmpty(auditor.firmName) && oldAuditFirmName !== auditor.firmName;

        if (!haveCustomFirmName) {
            auditor.firmName = newAuditFirmName;
            return auditor;
        }
        return null;
    }

    /**
     * get auditor personnel list by audit id
     * @param account
     * @param auditId
     * @returns
     */
    async getPersonnelIdListByAuditId(auditId: string): Promise<AuditorPersonnelType> {
        const auditorFrameworkSample = await this.getAuditorFrameworkSample(auditId);
        const hiredPersonnel = !isEmpty(auditorFrameworkSample.personnel)
            ? compact(
                  auditorFrameworkSample.personnel
                      .filter(personnel => personnel.type === SamplePersonnelType.HIRED)
                      .map(personnel => personnel.personnel),
              )
            : [];
        const currentPersonnel = !isEmpty(auditorFrameworkSample.personnel)
            ? compact(
                  auditorFrameworkSample.personnel
                      .filter(
                          personnel =>
                              personnel.type === SamplePersonnelType.EMPLOYED_DURING_TIMEFRAME,
                      )
                      .map(personnel => personnel.personnel),
              )
            : [];
        const formerPersonnel = !isEmpty(auditorFrameworkSample.personnel)
            ? compact(
                  auditorFrameworkSample.personnel
                      .filter(personnel => personnel.type === SamplePersonnelType.FORMER)
                      .map(personnel => personnel.personnel),
              )
            : [];

        return {
            hiredPersonnel,
            currentPersonnel,
            formerPersonnel,
        };
    }

    /**
     * get auditor framework sample with personnel
     * @param account
     * @param auditorFrameworkId
     * @returns
     */
    async getAuditorFrameworkSample(auditorFrameworkId: string): Promise<AuditorFrameworkSample> {
        return this.auditorFrameworkSampleRepository.findOneOrFail({
            where: {
                auditorFrameworkId: auditorFrameworkId,
            },
            join: {
                alias: 'auditorFramework',
                leftJoinAndSelect: {
                    samplePersonnel: 'auditorFramework.personnel',
                    personnel: 'samplePersonnel.personnel',
                    user: 'personnel.user',
                },
            },
            order: {
                id: 'DESC',
            },
        });
    }

    async getAuditorFrameworkSampleDates(
        account: Account,
        auditorFrameworkId: string,
    ): Promise<string[]> {
        const auditorFrameworkSample = await this.auditorFrameworkSampleRepository.findOne({
            where: {
                auditorFrameworkId,
            },
            order: {
                id: 'DESC',
            },
        });

        if (isNil(auditorFrameworkSample)) {
            return [];
        }

        const { dates } = auditorFrameworkSample;

        return dates.split(',');
    }

    /**
     * get latests audit controls evidence package
     * @param entryId
     * @param accountId
     * @param auditId
     * @param auditFirm
     * @returns
     */
    async getLatestAuditControlsEvidencePackage(
        account: Account,
        params: AuditApiAuditorParamsType,
        auditFirm: AuditFirm,
    ): Promise<DownloaderPayloadType> {
        await validateAndGetAuditorFrameworkByAuditFirm(
            this.auditorFrameworkRepository,
            params.auditId,
            params.entryId,
            auditFirm,
        );

        return this.getLastControlsPackage(account, params.auditId);
    }

    /**
     * Get pre audit package.
     * @param account Tenant's account
     * @param user Auditor user
     * @param params Request parameters
     * @param requestDto Request DTO
     * @returns Downloader payload
     */
    async getPreAuditPackage(
        account: Account,
        user: User,
        params: AuditApiAuditorParamsType,
        auditFirm: AuditFirm,
    ): Promise<DownloaderPayloadType> {
        await validateAndGetAuditorFrameworkByAuditFirm(
            this.auditorFrameworkRepository,
            params.auditId,
            params.entryId,
            auditFirm,
        );

        const currentProduct = account.getCurrentProduct();

        return this.companiesOrchestrationService.getLastCompanyDownloadArchiveByCategory(
            account,
            user,
            CompanyArchiveCategory.PRE_AUDIT,
            currentProduct,
        );
    }
    /**
     * get request evidence package
     * @param entryId
     * @param accountId
     * @param auditId
     * @param requestId
     * @param auditFirm
     * @returns
     */
    async getRequestEvidencePackage(
        account: Account,
        user: User,
        params: AuditApiAuditorParamsType,
        requestId: number,
        auditFirm: AuditFirm,
    ): Promise<SignedUrlResponseDto> {
        await validateAndGetAuditorFrameworkByAuditFirm(
            this.auditorFrameworkRepository,
            params.auditId,
            params.entryId,
            auditFirm,
        );

        const requestDto = { requestId };

        const signedUrl = await this.generateRequestControlEvidencePackage(
            account,
            user,
            requestDto,
        );
        const responseDto = { signedUrl };

        return new SignedUrlResponseDto().build(responseDto);
    }

    async getAuditPersonnelList(
        params: AuditApiAuditorParamsType,
        account: Account,
        auditFirm: AuditFirm,
        dto: AuditAuditorPersonnelRequestAuditorApiDto,
    ): Promise<AuditorPersonnelType> {
        const auditorFramework = await validateAndGetAuditorFrameworkByAuditFirm(
            this.auditorFrameworkRepository,
            dto.auditorFrameworkId,
            params.entryId,
            auditFirm,
        );

        const formattedDates = getAuditPeriodByFramework(auditorFramework);
        return this.getPersonnelListByTimeRange(account, formattedDates);
    }

    async getPersonnelListByTimeRange(
        account: Account,
        dates: {
            startDate: string;
            endDate: string;
            isSingleDateFramework: boolean;
        },
    ): Promise<any> {
        const [hiredPersonnel, formerPersonnel, currentPersonnel] = await Promise.all([
            await this.personnelRepository.findPersonnelByTimeRange(
                dates,
                AuditorPersonnelHandler.HIRED,
                [
                    EmploymentStatus.FORMER_EMPLOYEE,
                    EmploymentStatus.CURRENT_EMPLOYEE,
                    EmploymentStatus.FORMER_CONTRACTOR,
                    EmploymentStatus.CURRENT_CONTRACTOR,
                    EmploymentStatus.SPECIAL_FORMER_EMPLOYEE,
                    EmploymentStatus.SPECIAL_FORMER_CONTRACTOR,
                ],
            ),
            await this.personnelRepository.findPersonnelByTimeRange(
                dates,
                AuditorPersonnelHandler.SEPARATION,
                [
                    EmploymentStatus.FORMER_EMPLOYEE,
                    EmploymentStatus.SPECIAL_FORMER_EMPLOYEE,
                    EmploymentStatus.FORMER_CONTRACTOR,
                    EmploymentStatus.SPECIAL_FORMER_CONTRACTOR,
                ],
            ),
            await this.personnelRepository.findPersonnelByTimeRangeForCurrent(
                {
                    startDate: dates.startDate,
                    endDate: !dates.isSingleDateFramework ? dates.endDate : null,
                },
                [
                    EmploymentStatus.FORMER_EMPLOYEE,
                    EmploymentStatus.CURRENT_EMPLOYEE,
                    EmploymentStatus.FORMER_CONTRACTOR,
                    EmploymentStatus.CURRENT_CONTRACTOR,
                    EmploymentStatus.SPECIAL_FORMER_EMPLOYEE,
                    EmploymentStatus.SPECIAL_FORMER_CONTRACTOR,
                ],
            ),
        ]);
        return {
            hiredPersonnel,
            formerPersonnel,
            currentPersonnel,
        };
    }

    getAuditorClientByAccountAndClientId(
        clientId: string,
        account: Account,
    ): Promise<AuditorClient> {
        return this.auditorClientRepository.findOne({
            where: {
                id: clientId,
                account: {
                    id: account.id,
                },
            },
            relations: ['account'],
        });
    }

    /**
     * @deprecated Use AuditorsCoreService.filterDeletedCustomFrameworks
     *
     * @param clientList
     */
    private filterDeletedCustomFrameworks(
        clientList: PaginationType<AuditorFrameworksUnreadMessagesType>,
    ): void {
        clientList.data?.forEach(client => {
            client.frameworks = client.frameworks?.filter(framework => !isNil(framework));
        });
    }

    /**
     * @deprecated Use CompaniesCoreService.getCompanyByAccountId
     *
     * @param account
     * @returns
     */
    async getAuditedCompanyByAccount(account: Account): Promise<Company> {
        return this.companiesCoreService.getCompanyByAccountId(account.id);
    }

    async saveEvidencePackageCompanyArchive(
        account: Account,
        audit: Audit,
        companyArchive: CompanyArchive,
    ): Promise<CompanyArchive> {
        this.logger.log(
            PolloAdapter.acct(
                `Saving evidence package company archive for audit with id ${audit.id}`,
                account,
            ),
        );

        return this.companyArchivesCoreService.saveCompanyArchive(companyArchive);
    }

    /**
     * Get controls related by framework
     * @param frameworkId Framework ID
     * @param dto Request DTO
     * @returns List of controls related by framework
     */
    private async listFrameworkRelatedControls(
        account: Account,
        frameworkId: number,
        dto: AuditFrameworkControlsRequestDto,
    ): Promise<PaginationType<Control>> {
        const controlIds = !isNil(dto.excludeRequestId)
            ? await this.controlRepository.getControlIdsByCustomerRequest(dto.excludeRequestId)
            : [];

        const list = await this.controlRepository.listFrameworkRelatedControls(
            frameworkId,
            controlIds,
            dto,
        );

        list.data = list.data.map(item => {
            item.description = item.description.replace(/%s/g, account.companyName);
            return item;
        });

        return list;
    }

    /**
     * @deprecated Use AuditorsOrchestrationService.getAccountUserByEntry
     *
     * @param account
     * @param entry
     * @returns
     */
    private async getAccountUserByEntry(account: Account, entry: Entry): Promise<User | null> {
        return tenantWrapper(account, () => {
            return this.usersCoreService.getUserByEmailNoFail(entry.email);
        });
    }

    private get userRepository(): UserRepository {
        return this.getCustomTenantRepository(UserRepository);
    }

    private get userRoleRepository(): UserRoleRepository {
        return this.getCustomTenantRepository(UserRoleRepository);
    }

    private get auditorFrameworkSampleRepository(): Repository<AuditorFrameworkSample> {
        return this.getTenantRepository(AuditorFrameworkSample);
    }

    private get auditorFrameworkSamplePersonnelMapRepository(): Repository<AuditorFrameworkSamplePersonnelMap> {
        return this.getTenantRepository(AuditorFrameworkSamplePersonnelMap);
    }

    private get auditorFrameworkSampleArchiveMapRepository(): Repository<AuditorFrameworkSampleArchiveMap> {
        return this.getTenantRepository(AuditorFrameworkSampleArchiveMap);
    }

    private get customerRequestRepository(): CustomerRequestRepository {
        return this.getCustomTenantRepository(CustomerRequestRepository);
    }

    private get auditorFrameworkRequestsSummaryRepository(): Repository<AuditorFrameworkRequestsSummaryView> {
        return this.getTenantRepository(AuditorFrameworkRequestsSummaryView);
    }

    private get trustCenterRequestRepository(): TrustCenterRequestRepository {
        return this.getCustomTenantRepository(TrustCenterRequestRepository);
    }

    private get customerRequestListViewRepository(): CustomerRequestListViewRepository {
        return this.getCustomTenantRepository(CustomerRequestListViewRepository);
    }

    private get productFrameworkRepository(): ProductFrameworkRepository {
        return this.getCustomTenantRepository(ProductFrameworkRepository);
    }

    private get personnelRepository(): PersonnelRepository {
        return this.getCustomTenantRepository(PersonnelRepository);
    }

    private get auditorFrameworkListView(): Repository<AuditorFrameworkListView> {
        return this.getTenantRepository(AuditorFrameworkListView);
    }

    private get controlRepository(): ControlRepository {
        return this.getCustomTenantRepository(ControlRepository);
    }
}
