import {
    AgentPlatform,
    AuditorFrameworkType as AuditorFrameworkTypeEnum,
    AuditType,
    ErrorCode,
    FrameworkTag,
} from '@drata/enums';
import { GoneException, Injectable, NotFoundException } from '@nestjs/common';
import { AuditorControlEvidencePackageRequestAuditorApiDto } from 'app/auditor/auditor-api/dtos/auditor-control-evidence-package-request.auditor-api.dto';
import { AuditorSampleSetupRequestAuditorApiDto } from 'app/auditor/auditor-api/dtos/auditor-sample-setup-request.auditor-api.dto';
import { Company } from 'app/companies/entities/company.entity';
import { WorkspacesCoreService } from 'app/companies/products/services/workspaces-core.service';
import { CompaniesCoreService } from 'app/companies/services/companies-core.service';
import { CompanyArchivesCoreService } from 'app/companies/services/company-archives-core.service';
import { AuditorFrameworkRequestsSummaryView } from 'app/customer-request/entities/auditor-framework-requests-summary-view.entity';
import { AuditorFrameworkSampleArchiveMap } from 'app/customer-request/entities/auditor-framework-sample-archive-map.entity';
import { AuditorFrameworkSamplePersonnelMap } from 'app/customer-request/entities/auditor-framework-sample-personnel-map.entity';
import { AuditorFrameworkSample } from 'app/customer-request/entities/auditor-framework-sample.entity';
import { CustomerRequestOrchestrationService } from 'app/customer-request/services/customer-request-orchestration.service';
import { AuditorFrameworkSummaryResponseType } from 'app/customer-request/types/auditor-framework-requests-summary-view-response.type';
import { FrameworksCoreService } from 'app/frameworks/services/frameworks-core.service';
import { User } from 'app/users/entities/user.entity';
import { PersonnelCoreService } from 'app/users/personnel/services/personnel-core.service';
import { UsersCoreService } from 'app/users/services/users-core.service';
import { DeleteAuditorFrameworkRequestDto } from 'auditors/dtos/auditor-frameworkId-delete.dto';
import { AuditorListRequestDto } from 'auditors/dtos/auditor-list-request.dto';
import { AuditorToggleRequestDto } from 'auditors/dtos/auditor-toggle-request.dto';
import { AuditorClient } from 'auditors/entities/auditor-client.entity';
import { AuditorFrameworkCompanyArchiveTypeMap } from 'auditors/entities/auditor-framework-company-archive-type.map';
import { AuditorFrameworkTypeNames } from 'auditors/entities/auditor-framework-type-names.map';
import { Auditor } from 'auditors/entities/auditor.entity';
import {
    validateAuditor,
    validateAuditorFramework,
    validateControlEvidencePackagePersonnel,
} from 'auditors/helpers/auditor-framework-validator.helper';
import { AuditorRevokedEvent } from 'auditors/observables/events/auditor-revoked.event';
import { AuditorToggleUpdatedEvent } from 'auditors/observables/events/auditor-toggle-updated.event';
import { DownloadOnlyAuditCreatedEvent } from 'auditors/observables/events/download-only-audit-created.event';
import { GenerateControlEvidencePackageEvent } from 'auditors/observables/events/generate-control-evidence-package.event';
import { AuditorClientRepository } from 'auditors/repositories/auditor-client.repository';
import { AuditorFrameworkRepository } from 'auditors/repositories/auditor-framework.repository';
import { AuditorRepository } from 'auditors/repositories/auditor.repository';
import { AuditorsCoreService } from 'auditors/services/auditors-core.service';
import { AuditorFrameworksUnreadMessagesType } from 'auditors/types/auditor-framework-unread-messages.type';
import { AuditorFrameworksType } from 'auditors/types/auditor-frameworks.type';
import { ControlEvidencePackageRelatedData } from 'auditors/types/control-evidence-package-related-data.type';
import { PendingEvidencePackageCompanyArchive } from 'auditors/types/pending-evidence-package-company-archive.type';
import { Audit } from 'audits/entities/audit.entity';
import { Account } from 'auth/entities/account.entity';
import { Entry } from 'auth/entities/entry.entity';
import { AccountsCoreService } from 'auth/services/accounts-core.service';
import { EntryCoreService } from 'auth/services/entry-core.service';
import { PolloAdapter } from 'commons/adapters/pollo.adapter';
import { ApiResponse } from 'commons/enums/api-response.enum';
import { AuditorFrameworkArchiveType } from 'commons/enums/auditor-framework-archive-type.enum';
import { AccountStatus } from 'commons/enums/auth/account-status.enum';
import { CompanyArchiveCategory } from 'commons/enums/company-archive-category.enum';
import { CompanyArchiveStatus } from 'commons/enums/company-archive-status.enum';
import { SamplePersonnelType } from 'commons/enums/sample-personnel-type.enum';
import { Role } from 'commons/enums/users/role.enum';
import { ConflictException } from 'commons/exceptions/conflict.exception';
import { fileNameDate } from 'commons/helpers/date.helper';
import { getProductId, isMultiProductEnabled } from 'commons/helpers/products.helper';
import { frameworkToFrameworkTemplateEntity } from 'commons/helpers/publish-frameworks.helper';
import { hasRole } from 'commons/helpers/user.helper';
import { AppService } from 'commons/services/app.service';
import { AccountIdType } from 'commons/types/account-id.type';
import { PaginationType } from 'commons/types/pagination.type';
import { get, isEmpty, isNil } from 'lodash';
import { tenantWrapper } from 'tenancy/contexts/tenant-wrapper';
import { Repository } from 'typeorm';

@Injectable()
export class AuditorsOrchestrationService extends AppService {
    constructor(
        private readonly auditorClientRepository: AuditorClientRepository,
        private readonly auditorsCoreService: AuditorsCoreService,
        private readonly usersCoreService: UsersCoreService,
        private readonly auditorRepository: AuditorRepository,
        private readonly entryCoreService: EntryCoreService,
        private readonly accountsCoreService: AccountsCoreService,
        private readonly auditorFrameworkRepository: AuditorFrameworkRepository,
        private readonly frameworksCoreService: FrameworksCoreService,
        private readonly customerRequestOrchestrationService: CustomerRequestOrchestrationService,
        private readonly companiesCoreService: CompaniesCoreService,
        private readonly personnelCoreService: PersonnelCoreService,
        private readonly workspacesCoreService: WorkspacesCoreService,
        private readonly companyArchivesCoreService: CompanyArchivesCoreService,
    ) {
        super();
    }

    async revokeAuditorsForCustomFramework(
        account: Account,
        user: User,
        customFrameworkId: string,
    ): Promise<void> {
        // find auditors with custom framework id = customFrameworkId
        const auditors = await this.auditorClientRepository.getAuditorsByCustomFrameworkId(
            account.id,
            customFrameworkId,
        );

        const auditorEntriesIds = auditors.map(client => client.auditor.entry.id);

        // revoke each auditor
        for (const entry of auditorEntriesIds) {
            // eslint-disable-next-line no-await-in-loop
            await this.deleteAuditorFramework(account, user, entry, {
                auditorFrameworkId: customFrameworkId,
            });
        }
    }
    async revokeAllAuditorsForCustomFrameworks(account: Account): Promise<void> {
        const auditors =
            await this.auditorClientRepository.getAllAuditorsForCustomFrameworks(account);

        const auditorEntriesIds = auditors.map(au => au.auditor.entry.id);

        for (const entry of auditorEntriesIds) {
            // eslint-disable-next-line no-await-in-loop
            await this.deleteAuditorFramework(account, null, entry);
        }
    }

    async deleteAuditorFramework(
        account: Account,
        user: User | null,
        entryId: string,
        auditorFrameworkIdDeleteDto?: DeleteAuditorFrameworkRequestDto,
    ): Promise<Auditor> {
        const auditor = await this.auditorsCoreService.getAuditorByEntryIdOrFail(entryId);

        const auditorClient = await this.auditorsCoreService.getAuditorClientByAccountAndEntry(
            account,
            auditor.entry,
        );

        const auditorUser = await this.usersCoreService.getUserByEmail(auditor.entry.email);

        const frameworks =
            auditorClient?.auditorFrameworkAuditor
                ?.map(item => item.auditorFramework)
                ?.filter(Boolean) ?? [];

        const { auditorFrameworkId } = auditorFrameworkIdDeleteDto ?? {};

        /**
         * SoftRemove requires Framework to have auditorClient,
         * but it could be empty cause currently getAuditorFrameworks takes frameworks
         * from the AuditorClient
         */
        // auditorFrameworkId is not empty for specific delete, it could be framework id or custom framework id
        if (!isEmpty(auditorFrameworkId)) {
            let frameworkToDelete = frameworks.find(
                framework => framework.id === auditorFrameworkId,
            );

            if (isNil(frameworkToDelete)) {
                frameworkToDelete = frameworks.find(
                    framework => framework.customFrameworkId === auditorFrameworkId,
                );
            }

            if (!isNil(frameworkToDelete)) {
                await this.auditorFrameworkRepository.softRemove(frameworkToDelete);

                /**
                 * Remove from frameworks the framework deleted with softRemove since it's
                 * delete from others auditors too.
                 */
                const frameworkIndex = frameworks.indexOf(frameworkToDelete);
                frameworks.splice(frameworkIndex, 1);
            }
        } else {
            const productId = getProductId(account);
            const frameworksToDelete = frameworks.filter(
                framework => framework.productId === productId,
            );
            for (const framework of frameworksToDelete) {
                // eslint-disable-next-line no-await-in-loop
                await this.auditorFrameworkRepository.softRemove(framework);
            }
        }

        if (isEmpty(auditorFrameworkId) || frameworks.length === 0) {
            await this.usersCoreService.softDeleteAuditorRole(auditorUser);
            if (auditorClient) {
                await this.auditorClientRepository.softRemove(auditorClient);
            } else {
                this.logger.error(
                    PolloAdapter.acct(
                        `Auditor client not found for auditor with entry id ${entryId}. Auditor client will not be soft-deleted.`,
                        account,
                    ),
                );
            }

            /**
             * When disabling Custom Framework for a Tenant, the auditors related to all
             * custom frameworks should be revoked but that action is made by the drata administration panel
             * so there is no a current tenant's user
             */
            if (!isNil(user)) {
                this._eventBus.publish(new AuditorRevokedEvent(account, user, auditor, frameworks));
            }
        }
        return auditor;
    }

    async getAuditorWithEmail(email: string): Promise<Auditor> {
        const entry = await this.entryCoreService.getEntryByEmailNoFail(email, true);

        if (isNil(entry)) {
            return undefined;
        }

        return this.auditorRepository.findOne({
            where: { entry: { id: entry.id } },
        });
    }

    async getAuditorFromAccount(id: AccountIdType, auditor: Auditor): Promise<User> {
        const account = await this.accountsCoreService.getAccountById(id);

        if (account.status !== AccountStatus.ACTIVE) {
            throw new GoneException();
        }

        const user = await this.getAccountUserByEntry(account, auditor.entry);

        if (isNil(user)) {
            throw new NotFoundException(ErrorCode.USER_NOT_FOUND);
        }

        return user;
    }

    async getAuditorByEmail(email: string): Promise<Auditor | null> {
        const entry = await this.entryCoreService.getEntryByEmailNoFail(email, true);

        if (isNil(entry)) {
            return null;
        }

        return this.auditorRepository.findOne({
            where: { entry: { id: entry.id } },
        });
    }

    public async getAuditorFrameworkRequestsSummary(
        account: Account,
        auditorFrameworkId: string,
    ): Promise<AuditorFrameworkSummaryResponseType> {
        const [auditorFrameworkRequestsSummaryResponse, auditorFramework] = await Promise.all([
            this.auditorFrameworkRequestsSummaryViewRepository.findOne({
                where: {
                    auditorFrameworkId,
                },
            }),

            this.auditorFrameworkRepository.findOne({
                where: {
                    id: auditorFrameworkId,
                },
            }),
        ]);

        if (isNil(auditorFramework)) {
            throw new NotFoundException(
                `Auditor framework with ID ${auditorFrameworkId} was not found`,
            );
        }

        if (auditorFramework.frameworkType === AuditorFrameworkTypeEnum.CUSTOM) {
            const customFramework = await this.frameworksCoreService.getEnabledFrameworkByTag(
                FrameworkTag.CUSTOM,
                null,
                auditorFramework.customFrameworkId,
            );

            //this validation is just in case auditor relation with customFramework is not found
            if (!isNil(customFramework)) {
                auditorFramework.auditorFrameworkType.label = customFramework.name;
                auditorFramework.auditorFrameworkType.relatedFramework =
                    frameworkToFrameworkTemplateEntity(customFramework);
            }
        }

        const auditorFrameworkRequestsType: AuditorFrameworkSummaryResponseType = {
            auditorFramework,
            auditorFrameworkRequestsSummaryView: isNil(auditorFrameworkRequestsSummaryResponse)
                ? {
                      auditorFrameworkId,
                      outstandingRequests: 0,
                      inReviewRequests: 0,
                      acceptedRequests: 0,
                      totalRequests: 0,
                      acceptedPercentage: 0,
                  }
                : auditorFrameworkRequestsSummaryResponse,
        };

        return auditorFrameworkRequestsType;
    }

    async toggleAuditorReadOnly(
        account: Account,
        user: User,
        entryId: string,
        requestDto: AuditorToggleRequestDto,
    ): Promise<AuditorClient> {
        const entry = await this.entryCoreService.getEntryById(entryId);
        const auditorClient = await this.auditorsCoreService.getAuditorClientByAccountAndEntry(
            account,
            entry,
        );

        const auditor = await this.auditorRepository.findOne({
            where: { entry: { id: entry.id } },
        });

        if (isNil(auditorClient) || isNil(auditor)) {
            throw new NotFoundException(ApiResponse.NOT_FOUND);
        }

        const { hasAccess, canDownload } = requestDto;

        let save = false;

        if (hasAccess && isNil(auditorClient.readOnly)) {
            auditorClient.readOnly = new Date();
            save = true;
        } else if (!hasAccess && !isNil(auditorClient.readOnly)) {
            auditorClient.readOnly = null;
            auditorClient.allowDownloads = false;
            save = true;
        }

        if (
            !isNil(auditorClient.readOnly) &&
            !isNil(canDownload) &&
            canDownload !== auditorClient.allowDownloads
        ) {
            save = true;
            auditorClient.allowDownloads = canDownload;
        }

        if (save) {
            await this.auditorClientRepository.save(auditorClient);

            // trigger event
            this._eventBus.publish(
                new AuditorToggleUpdatedEvent(account, user, requestDto, auditor),
            );
        }

        return auditorClient;
    }

    async listAuditors(
        dto: AuditorListRequestDto,
        account: Account,
        user: User,
    ): Promise<PaginationType<AuditorFrameworksUnreadMessagesType>> {
        // auditors have the restriction to only see their own firm members.
        const entry = await this.entryCoreService.getEntryWithoutRelationsByEmailOrFail(user.email);

        const auditor = await this.auditorRepository.findOneBy({
            firstName: user.firstName,
            lastName: user.lastName,
            entry: { id: entry.id },
        });

        const isAuditorReadOnly = hasRole(user, [Role.ACT_AS_READ_ONLY]) && !isNil(auditor);

        if (isAuditorReadOnly) {
            /**
             * TODO ENG-39556: firmName property should be removed once item ENG-39555 is closed
             */
            dto.firmName = auditor.auditFirm?.name ?? auditor.firmName;
        }
        const auditors = await this.auditorClientRepository.getAuditors(dto, account);

        this.auditorsCoreService.filterDeletedCustomFrameworks(auditors);

        return auditors;
    }

    async getAuditor(auditorId: string, account: Account): Promise<AuditorFrameworksType> {
        const auditor = await this.auditorClientRepository.getAuditor(auditorId, account);

        auditor.frameworks = await this.getAuditorFrameworksWithRelatedCustomFrameworks(
            account,
            auditor.frameworks,
        );

        return auditor;
    }

    async getAuditorFrameworksWithRelatedCustomFrameworks(
        account: Account,
        audits: Audit[],
    ): Promise<Audit[]> {
        const completeAudits: Audit[] = [];
        if (isEmpty(audits)) {
            return completeAudits;
        }
        const customFrameworkIds = audits
            .filter(
                (auditFramework): auditFramework is Audit & { customFrameworkId: string } =>
                    auditFramework?.frameworkType === AuditorFrameworkTypeEnum.CUSTOM &&
                    typeof auditFramework.customFrameworkId === 'string',
            )
            .map(auditFramework => auditFramework.customFrameworkId);

        const customFrameworks = await this.frameworksCoreService.getEnabledFrameworksByTags(
            FrameworkTag.CUSTOM,
            null,
            customFrameworkIds,
        );

        const customFrameworkMap = new Map(
            customFrameworks.map(customFramework => [
                customFramework.customFrameworkId,
                customFramework,
            ]),
        );

        for (const audit of audits) {
            if (!audit) {
                continue;
            }

            const { frameworkType, customFrameworkId } = audit;

            if (frameworkType !== AuditorFrameworkTypeEnum.CUSTOM) {
                completeAudits.push(audit);
                continue;
            }

            const customFramework = customFrameworkMap.get(customFrameworkId);

            if (!customFramework) {
                // Framework related not found, jump this auditor framework
                continue;
            }

            audit.auditorFrameworkType.relatedFramework =
                frameworkToFrameworkTemplateEntity(customFramework);
            completeAudits.push(audit);
        }
        return completeAudits;
    }

    async setupAuditorFrameworkSample(
        account: Account,
        user: User,
        auditId: string,
        requestDto: AuditorSampleSetupRequestAuditorApiDto,
    ): Promise<void> {
        await this.generateControlEvidencePackage(account, user, user.entryId, {
            auditorFrameworkId: auditId,
            ...requestDto,
        });

        if (requestDto.generateAuditRequests) {
            const auditorFrameworkId = auditId;
            await this.customerRequestOrchestrationService.createAuditRequestsRequirements(
                account,
                {
                    auditorFrameworkId,
                },
            );
        }
    }

    async generateControlEvidencePackage(
        account: Account,
        user: User,
        entryId: string,
        requestDto: AuditorControlEvidencePackageRequestAuditorApiDto,
    ): Promise<void> {
        try {
            this.log('Control Evidence requested', account, requestDto);

            const { auditor, audit } = await this.getControlEvidencePackageRelatedData(
                entryId,
                account,
                requestDto,
            );

            const company = await this.companiesCoreService.getCompanyByAccountId(account.id);

            await this.validateControlEvidencePackageData(auditor, account, requestDto, audit);

            //analytics event for download only audit created
            if (audit.auditType === AuditType.DOWNLOAD_ONLY_AUDIT) {
                this._eventBus.publish(
                    new DownloadOnlyAuditCreatedEvent(
                        account,
                        account.companyName,
                        user,
                        AuditorFrameworkTypeEnum[audit.frameworkType],
                    ),
                );
            }

            await this.generatePendingControlEvidencePackageAndPublishEvent(
                account,
                company,
                user,
                audit,
                requestDto,
            );
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(error.message, account)
                    .setError(error)
                    .setContext(this.constructor.name)
                    .setSubContext(this.generateControlEvidencePackage.name),
            );
            throw error;
        }
    }

    async generatePendingControlEvidencePackageAndPublishEvent(
        account: Account,
        company: Company,
        user: User,
        audit: Audit,
        requestDto: AuditorControlEvidencePackageRequestAuditorApiDto,
    ): Promise<void> {
        this.log('Generating the control evidence company archive with PENDING status.', account);

        const { pendingCompanyArchive } = await this.generateControlEvidencePendingCompanyArchive(
            account,
            user,
            audit,
            requestDto,
            company,
        );

        this.log('Publishing event to generate zip', account, requestDto);
        this._eventBus.publish(
            new GenerateControlEvidencePackageEvent(
                account,
                company,
                user,
                audit,
                requestDto,
                pendingCompanyArchive,
            ),
        );
    }

    async generateControlEvidencePendingCompanyArchive(
        account: Account,
        user: User,
        // @AUDIT-REFACTOR: TODO rename to  audit
        auditorFramework: Audit,
        requestDto: AuditorControlEvidencePackageRequestAuditorApiDto,
        company: Company,
    ): Promise<PendingEvidencePackageCompanyArchive> {
        const { dates, currentPersonnelIds, hiredPersonnelIds, formerPersonnelIds, platform } =
            requestDto;

        let frameworkTypeName: string;

        if (auditorFramework.frameworkType === AuditorFrameworkTypeEnum.CUSTOM) {
            const customFramework = await this.frameworksCoreService.getEnabledFrameworkByTag(
                FrameworkTag.CUSTOM,
                null,
                auditorFramework.customFrameworkId,
            );
            if (isNil(customFramework)) {
                throw new ConflictException(
                    `Custom Framework with ID ${auditorFramework.customFrameworkId} does not exist or is not enabled`,
                    ErrorCode.FRAMEWORK_NOT_FOUND,
                );
            }
            frameworkTypeName = customFramework.pill;
        } else {
            frameworkTypeName = AuditorFrameworkTypeNames.get(auditorFramework.frameworkType);
        }

        const baseFileName = `${frameworkTypeName} - Control Evidence - ${fileNameDate()}.zip`;

        const fileName = await this.createControlEvidenceFilename(
            account,
            auditorFramework.productId,
            company.name,
            baseFileName,
        );

        const companyArchive = await this.companyArchivesCoreService.createCompanyDocument(
            account,
            fileName,
            null,
            user,
            CompanyArchiveCategory.CONTROL_EVIDENCE,
            AuditorFrameworkCompanyArchiveTypeMap.get(auditorFramework.frameworkType),
            CompanyArchiveStatus.PENDING,
        );

        const auditorFrameworkSample = await this.saveSampleData(
            account,
            auditorFramework,
            dates,
            platform,
            currentPersonnelIds,
            hiredPersonnelIds,
            formerPersonnelIds,
        );

        const sampleArchiveMap = new AuditorFrameworkSampleArchiveMap();
        sampleArchiveMap.auditorFrameworkSample = auditorFrameworkSample;
        sampleArchiveMap.type = AuditorFrameworkArchiveType.FRAMEWORK_ARCHIVE;
        sampleArchiveMap.companyArchive = companyArchive;
        await this.auditorFrameworkSampleArchiveMapRepository.save(sampleArchiveMap);

        return { pendingCompanyArchive: companyArchive, frameworkTypeName };
    }

    async saveSampleData(
        account: Account,
        // @AUDIT-REFACTOR: TODO rename to  audit
        auditorFramework: Audit,
        dates: string[],
        platform: AgentPlatform,
        currentPersonnelIds: number[],
        hiredPersonnelIds: number[],
        formerPersonnelIds: number[],
    ): Promise<AuditorFrameworkSample> {
        const newSampleData = new AuditorFrameworkSample();
        newSampleData.auditorFrameworkId = auditorFramework.id;
        newSampleData.platform = platform;
        newSampleData.dates = dates.join(',');

        let personnelMapped = [];

        if (!isEmpty(currentPersonnelIds)) {
            const currentPersonnel =
                await this.personnelCoreService.findAllByIds(currentPersonnelIds);

            const currentPersonnelToPersonnelMap = currentPersonnel.map(personnel => {
                const samplePersonnelMap = new AuditorFrameworkSamplePersonnelMap();
                samplePersonnelMap.personnel = personnel;
                samplePersonnelMap.type = SamplePersonnelType.EMPLOYED_DURING_TIMEFRAME;
                return samplePersonnelMap;
            });

            personnelMapped = [...personnelMapped, ...currentPersonnelToPersonnelMap];
        }

        if (!isEmpty(hiredPersonnelIds)) {
            const hiredPersonnel = await this.personnelCoreService.findAllByIds(hiredPersonnelIds);

            const hiredPersonnelToPersonnelMap = hiredPersonnel.map(personnel => {
                const samplePersonnelMap = new AuditorFrameworkSamplePersonnelMap();
                samplePersonnelMap.personnel = personnel;
                samplePersonnelMap.type = SamplePersonnelType.HIRED;
                return samplePersonnelMap;
            });

            personnelMapped = [...personnelMapped, ...hiredPersonnelToPersonnelMap];
        }

        if (!isEmpty(formerPersonnelIds)) {
            const formerPersonnel =
                await this.personnelCoreService.findAllByIds(formerPersonnelIds);

            const formerPersonnelToPersonnelMap = formerPersonnel.map(personnel => {
                const samplePersonnelMap = new AuditorFrameworkSamplePersonnelMap();
                samplePersonnelMap.personnel = personnel;
                samplePersonnelMap.type = SamplePersonnelType.FORMER;
                return samplePersonnelMap;
            });

            personnelMapped = [...personnelMapped, ...formerPersonnelToPersonnelMap];
        }

        const auditorFrameworkSample =
            await this.auditorFrameworkSampleRepository.save(newSampleData);

        for (const personnel of personnelMapped) {
            personnel.auditorFrameworkSample = auditorFrameworkSample;
        }

        auditorFrameworkSample.personnel =
            await this.auditorFrameworkSamplePersonnelMapRepository.save(personnelMapped);

        return auditorFrameworkSample;
    }

    async createControlEvidenceFilename(
        account: Account,
        productId: number,
        companyName: string,
        baseFileName: string,
    ): Promise<string> {
        const product = await this.workspacesCoreService.getProductById(productId);
        const productName = get(product, 'name');

        return isMultiProductEnabled(account)
            ? `${productName} - ${baseFileName}`
            : `${companyName} - ${baseFileName}`;
    }

    async validateControlEvidencePackageData(
        auditor: Auditor | undefined,
        account: Account,
        requestDto: AuditorControlEvidencePackageRequestAuditorApiDto,
        // @AUDIT-REFACTOR: TODO rename to  audit
        auditorFramework: Audit,
    ): Promise<void> {
        this.log('Validating Auditor information', account, requestDto);
        // Perform validations
        if (auditorFramework.auditType === AuditType.FULL_AUDIT) {
            validateAuditor(auditor);
        }

        validateAuditorFramework(
            auditorFramework,
            auditorFramework?.frameworkType,
            requestDto.dates,
        );

        this.log('Validating requested personnel', account, requestDto);

        await validateControlEvidencePackagePersonnel(
            this.personnelCoreService,
            auditorFramework,
            requestDto.hiredPersonnelIds,
            requestDto.currentPersonnelIds,
            requestDto.formerPersonnelIds,
        );
    }

    async getControlEvidencePackageRelatedData(
        entryId: string,
        account: Account,
        requestDto: AuditorControlEvidencePackageRequestAuditorApiDto,
    ): Promise<ControlEvidencePackageRelatedData> {
        const { auditorFrameworkId } = requestDto;

        const entry = await this.entryCoreService.getEntryById(entryId);
        const auditor = await this.auditorsCoreService.getAuditorByEntry(entry);

        const product = account.getCurrentProduct();
        const productId = get(product, 'id');

        const auditorClient = await this.auditorsCoreService.getAuditorClientByAccountAndEntry(
            account,
            entry,
        );

        // @AUDIT-REFACTOR: TODO rename to  audits
        const auditorFrameworks: Audit[] = [];

        if (!isNil(auditorClient)) {
            const { auditorFrameworkAuditor } = auditorClient;
            const auditorAuditorFrameworks = auditorFrameworkAuditor.map(
                mapItem => mapItem.auditorFramework,
            );
            auditorFrameworks.push(...auditorAuditorFrameworks);
        } else {
            const externalAuditAuditorFramework =
                await this.auditorFrameworkRepository.getAuditorFrameworkByIdOrFail(
                    auditorFrameworkId,
                    productId,
                );
            auditorFrameworks.push(externalAuditAuditorFramework);
        }

        const auditorFrameworksWithRelatedCustomFrameworks =
            await this.getAuditorFrameworksWithRelatedCustomFrameworks(account, auditorFrameworks);

        const audit = auditorFrameworksWithRelatedCustomFrameworks.find(
            auditorFw => auditorFw.id === auditorFrameworkId,
        );

        if (isNil(audit)) {
            throw new NotFoundException(ErrorCode.AUDIT_NOT_FOUND);
        }

        return { auditor, audit };
    }

    private async getAccountUserByEntry(account: Account, entry: Entry): Promise<User | null> {
        return tenantWrapper(account, () => {
            return this.usersCoreService.getUserByEmailNoFail(entry.email);
        });
    }

    private get auditorFrameworkRequestsSummaryViewRepository(): Repository<AuditorFrameworkRequestsSummaryView> {
        return this.getTenantRepository(AuditorFrameworkRequestsSummaryView);
    }

    private get auditorFrameworkSampleRepository(): Repository<AuditorFrameworkSample> {
        return this.getTenantRepository(AuditorFrameworkSample);
    }

    private get auditorFrameworkSamplePersonnelMapRepository(): Repository<AuditorFrameworkSamplePersonnelMap> {
        return this.getTenantRepository(AuditorFrameworkSamplePersonnelMap);
    }

    private get auditorFrameworkSampleArchiveMapRepository(): Repository<AuditorFrameworkSampleArchiveMap> {
        return this.getTenantRepository(AuditorFrameworkSampleArchiveMap);
    }
}
