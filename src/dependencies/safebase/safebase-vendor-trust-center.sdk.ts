import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import { logAxiosError } from 'commons/helpers/logger.helper';
import config from 'config';
import * as crypto from 'crypto';
import { SBCategoryType } from 'dependencies/safebase/category-type.enum';
import { ITEM_CATEGORY_TYPES } from 'dependencies/safebase/item-category-types.const';
import {
    SBAccessRequestRequirements,
    SBBaseItem,
    SBProduct,
    SBTrustCenterAccessRequestPayload,
    SBTrustCenterAccessRequestResponse,
    SBTrustCenterDocument,
    SBTrustCenterDocumentDownload,
    SBTrustCenterInfo,
    SBTrustCenterLegalItem,
    SBTrustCenterOverviewItem,
} from 'dependencies/safebase/safebase-vendor-trust-center.types';
import { HeadersType } from 'dependencies/safebase/types/safebase-buld-header-type';
import { isNil } from 'lodash';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { PolloLogger } from 'pollo-logger/pollo.logger';
import { firstValueFrom } from 'rxjs';
import { format } from 'util';

@Injectable()
export class SafeBaseVendorTrustCenterSdk {
    private readonly baseUrl: string;
    private readonly keyId: string;
    private readonly secret: string;
    private readonly accessClientSecret: string;
    private readonly accessClientId: string;
    private readonly logger: PolloLogger<PolloMessage>;

    constructor(private readonly httpService: HttpService) {
        this.logger = PolloLogger.logger(this.constructor.name);

        const baseUrl = config.get('safebase.api.baseUrl');
        const keyId = config.get('safebase.api.vrm.keyId');
        const secret = config.get('safebase.api.vrm.secret');

        const cfAccessClientSecret = config.get('safebase.api.headers.cfAccessClientSecret');
        const cfAccessClientId = config.get('safebase.api.headers.cfAccessClientId');

        if (isNil(baseUrl) || isNil(keyId) || isNil(secret)) {
            throw new Error(
                'SafeBase configuration is missing. Please check safebase.api.vrm config values.',
            );
        }

        this.baseUrl = baseUrl;
        this.keyId = keyId;
        this.secret = secret;
        this.accessClientSecret = cfAccessClientSecret;
        this.accessClientId = cfAccessClientId;
    }

    /**
     * Get trust center information from SafeBase API
     * @param organizationId - The organization ID
     * @returns Promise<SBTrustCenterInfo>
     */
    async getTrustCenterInfo(organizationId: string): Promise<SBTrustCenterInfo> {
        const endpoint = format(config.get('safebase.api.vrm.endpoints.info'), organizationId);

        try {
            const headers = this.buildHeaders({ method: 'GET', path: endpoint });
            const url = `${this.baseUrl}${endpoint}`;

            const response = await firstValueFrom(this.httpService.get(url, { headers }));

            return response.data.result;
        } catch (error) {
            logAxiosError(
                this.logger,
                `SafeBase SDK: getTrustCenterInfo for ${organizationId} FAILED`,
                error,
            );
            throw error;
        }
    }

    /**
     * Get trust center compliance items from SafeBase API
     * @param organizationId - The organization ID
     * @param products - The list of products
     * @returns Promise<SBBaseItem[]>
     */
    async getTrustCenterComplianceItems(
        organizationId: string,
        products: string[],
        accessToken: string,
    ): Promise<SBBaseItem[]> {
        const endpoint = format(config.get('safebase.api.vrm.endpoints.items'), organizationId);

        const queryString = this.buildQueryParams({
            products,
            categoryTypes: SBCategoryType.COMPLIANCE,
        });

        try {
            const path = `${endpoint}${queryString}`;
            const url = `${this.baseUrl}${path}`;

            const headers = this.buildHeaders({ method: 'GET', path, accessToken });

            const response = await firstValueFrom(this.httpService.get(url, { headers }));

            return response.data.result.items;
        } catch (error) {
            logAxiosError(
                this.logger,
                `SafeBase SDK: getTrustCenterComplianceItems for ${organizationId} FAILED`,
                error,
            );
            throw error;
        }
    }

    /**
     * Get trust center compliance items from SafeBase API
     * @param organizationId - The organization ID
     * @param products - The list of products
     * @returns Promise<SBTrustCenterLegalItem[]>
     */
    async getTrustCenterLegalItems(
        organizationId: string,
        products: string[],
        accessToken: string,
    ): Promise<SBTrustCenterLegalItem[]> {
        const endpoint = format(config.get('safebase.api.vrm.endpoints.items'), organizationId);

        const queryString = this.buildQueryParams({
            products,
            categoryTypes: SBCategoryType.LEGAL,
        });
        try {
            const path = `${endpoint}${queryString}`;
            const url = `${this.baseUrl}${path}`;

            const headers = this.buildHeaders({ method: 'GET', path, accessToken });

            const response = await firstValueFrom(this.httpService.get(url, { headers }));

            return response.data.result.items;
        } catch (error) {
            logAxiosError(
                this.logger,
                `SafeBase SDK: getTrustCenterLegalItems for ${organizationId} FAILED`,
                error,
            );
            throw error;
        }
    }

    async getTrustCenterItems(
        organizationId: string,
        products: string[],
        accessToken: string,
    ): Promise<SBBaseItem[]> {
        const endpoint = format(config.get('safebase.api.vrm.endpoints.items'), organizationId);

        const queryString = this.buildQueryParams({
            products,
            categoryTypes: ITEM_CATEGORY_TYPES,
        });

        try {
            const path = `${endpoint}${queryString}`;
            const url = `${this.baseUrl}${path}`;

            const headers = this.buildHeaders({ method: 'GET', path, accessToken });

            const response = await firstValueFrom(this.httpService.get(url, { headers }));

            return response.data.result.items;
        } catch (error) {
            logAxiosError(
                this.logger,
                `SafeBase SDK: getTrustCenterItems for ${organizationId} FAILED`,
                error,
            );
            throw error;
        }
    }

    /**
     * Get trust center documents from SafeBase API
     * @param organizationId - The organization ID
     * @param products - The list of products
     * @returns Promise<SBTrustCenterDocument[]>
     */
    async getTrustCenterDocuments(
        organizationId: string,
        products: string[],
        accessToken: string,
    ): Promise<SBTrustCenterDocument[]> {
        const endpoint = format(config.get('safebase.api.vrm.endpoints.documents'), organizationId);

        try {
            const queryString = this.buildQueryParams({
                products,
            });
            const path = `${endpoint}${queryString}`;
            const url = `${this.baseUrl}${path}`;

            const headers = this.buildHeaders({ method: 'GET', path, accessToken });

            const response = await firstValueFrom(this.httpService.get(url, { headers }));

            return response.data.result.documents.items;
        } catch (error) {
            logAxiosError(
                this.logger,
                `SafeBase SDK: getTrustCenterDocuments for ${organizationId} FAILED`,
                error,
            );
            throw error;
        }
    }

    /**
     * Get trust center documents from SafeBase API
     * @param organizationId - The organization ID
     * @param documentId - Document id
     * @param products - The list of products
     * @returns Promise<any>
     */
    async getTrustCenterDocumentsById(
        organizationId: string,
        documentId: string,
        products: string[],
        accessToken: string,
    ): Promise<SBTrustCenterDocumentDownload[]> {
        const endpoint = format(
            config.get('safebase.api.vrm.endpoints.documentsDownload'),
            organizationId,
        );

        try {
            const queryString = this.buildQueryParams({
                products,
                documentIds: documentId,
            });
            const path = `${endpoint}${queryString}`;
            const url = `${this.baseUrl}${path}`;

            const headers = this.buildHeaders({ method: 'GET', path, accessToken });

            const response = await firstValueFrom(this.httpService.get(url, { headers }));

            return response.data.result;
        } catch (error) {
            logAxiosError(
                this.logger,
                `SafeBase SDK: getTrustCenterDocumentsById for ${organizationId} FAILED`,
                error,
            );
            throw error;
        }
    }

    /**
     * Get access request requirements from SafeBase API
     * @param organizationId - The organization ID
     * @param email - The user email
     * @param product - The product slug
     * @returns Promise<SBAccessRequestRequirements> - Access request requirements data
     */
    async getAccessRequestRequirements(
        organizationId: string,
        email: string,
        product: string,
        accessToken: string,
    ): Promise<SBAccessRequestRequirements> {
        const endpoint = format(
            config.get('safebase.api.vrm.endpoints.accessRequestRequirements'),
            organizationId,
        );

        try {
            const queryString = this.buildQueryParams({
                email,
                product,
            });
            const path = `${endpoint}${queryString}`;
            const headers = this.buildHeaders({ method: 'GET', path, accessToken });
            const url = `${this.baseUrl}${path}`;

            const response = await firstValueFrom(this.httpService.get(url, { headers }));

            return response.data.result;
        } catch (error) {
            logAxiosError(
                this.logger,
                `SafeBase SDK: getAccessRequestRequirements for ${organizationId} with email ${email} and product ${product} FAILED`,
                error,
            );
            throw error;
        }
    }

    /**
     * Get the list of products name from SafeBase products
     * @param products - Products object from SafeBase API
     * @returns List of products name
     */
    getProductsName(products: Record<string, SBProduct>): string[] {
        if (!products) return [];

        const productEntries = Object.entries(products);
        return productEntries.map(([, value]) => value.name);
    }

    /**
     * Get trust center overview items from SafeBase API
     * @param organizationId - The organization ID
     * @param products - The list of products
     * @returns Promise<SBTrustCenterOverviewItem[]>
     */
    async getTrustCenterOverviewItems(
        organizationId: string,
        products: string[],
    ): Promise<SBTrustCenterOverviewItem[]> {
        const endpoint = format(config.get('safebase.api.vrm.endpoints.items'), organizationId);

        try {
            const queryString = this.buildQueryParams({
                products,
                categoryTypes: SBCategoryType.OVERVIEW,
            });

            const path = `${endpoint}${queryString}`;
            const url = `${this.baseUrl}${path}`;

            const headers = this.buildHeaders({ method: 'GET', path });

            const response = await firstValueFrom(this.httpService.get(url, { headers }));

            return response.data.result.items;
        } catch (error) {
            logAxiosError(
                this.logger,
                `SafeBase SDK: getTrustCenterOverviewItems for ${organizationId} FAILED`,
                error,
            );
            throw error;
        }
    }

    /**
     * Post trust center access request from SafeBase API
     * @param organizationId - The organization ID
     * @param product - The product string
     * @param fields - The list of fields
     * @returns Promise<SBTrustCenterAccessRequestRequirements>
     */
    async postVendorTrustCenterAccessRequest(
        organizationId: string,
        body: SBTrustCenterAccessRequestPayload,
    ): Promise<SBTrustCenterAccessRequestResponse> {
        const endpoint = format(
            config.get('safebase.api.vrm.endpoints.accessRequest'),
            organizationId,
        );

        try {
            const url = `${this.baseUrl}${endpoint}`;

            const headers = this.buildHeaders({ method: 'POST', path: endpoint, body: JSON.stringify(body) });

            const response = await firstValueFrom(
                this.httpService.post(url.toString(), body, { headers }),
            );

            return response.data;
        } catch (error) {
            logAxiosError(
                this.logger,
                `SafeBase SDK: postVendorTrustCenterAccessRequest for ${organizationId} FAILED`,
                error,
            );
            throw error;
        }
    }

    /**
     * Generate authentication headers for SafeBase API
     * @param method - HTTP method
     * @param path - API endpoint path
     * @param accessToken - Access token
     * @param body - Request body (empty for GET requests)
     * @returns Headers object with authentication
     */
    private buildHeaders({
        method,
        path,
        accessToken = '',
        body = '',
    }: HeadersType): Record<string, string> {
        const timestamp = new Date().toISOString();

        // Generate HMAC signature
        const hmacData = `${timestamp}.${method}.${path}.${body}`;
        const hmacSignature = crypto
            .createHmac('sha256', this.secret)
            .update(hmacData)
            .digest('base64');

        const fullSignature = `HMAC-SHA256 ${hmacSignature}`;

        return {
            'x-sb-key-id': this.keyId,
            'x-sb-timestamp': timestamp,
            'x-sb-signature': fullSignature,
            'CF-Access-Client-Secret': this.accessClientSecret,
            'CF-Access-Client-Id': this.accessClientId,
            'x-sb-tprm-token': accessToken,
        };
    }

    /**
     * Generate query params for any API
     * @param params - params for query to build
     * @returns params built for endpoint: data=string&data2=string
     */
    private buildQueryParams(
        params: Record<string, string | number | (string | number)[]>,
    ): string {
        const searchParams = new URLSearchParams();

        for (const [key, value] of Object.entries(params)) {
            if (Array.isArray(value)) {
                searchParams.append(key, value.join(','));
            } else {
                searchParams.append(key, value.toString());
            }
        }

        const queryString = searchParams.toString();
        return queryString ? `?${queryString}` : '';
    }
}
