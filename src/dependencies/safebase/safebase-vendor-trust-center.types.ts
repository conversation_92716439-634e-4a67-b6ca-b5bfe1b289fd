import { VendorTrustCenterAccessRequestPayloadFieldsDto } from 'app/users/vendors/dtos/vendor-trust-center-access-request-payload-fields.dto';
import { SBVendorTrustCenterAccessRequestStatus } from 'app/users/vendors/enums/vendor-trust-center-access-request-status.enum';
import { SBAccessRequestFieldType } from 'dependencies/safebase/access-request-field-type.enum';
import { SBCategoryType } from 'dependencies/safebase/category-type.enum';
import { SBItemType } from 'dependencies/safebase/item-type.enum';
import { SBOrganizationDocumentAccessType } from 'dependencies/safebase/organization-document-access-type.enum';
import { SBPartnerRequestAccessStatusCode } from 'dependencies/safebase/partner-request-access-status-code.enum';
import { SafebaseVendorTrustCenterMaturity } from 'dependencies/safebase/safebase-vendor-trust-center-maturity.enum';

export interface SBProduct {
    name: string;
    slug: string;
}

export interface SBTrustCenterInfo {
    name: string;
    organizationId: string;
    trustCenterUrl: string;
    products: Record<string, SBProduct>;
}

export interface SBBaseItem {
    id: string;
    title: string;
    itemType: SBItemType;
    categoryType: SBCategoryType;
    categoryTitle: string;
    explanation?: string;
    href?: string;
    question?: string;
    maturity?: SafebaseVendorTrustCenterMaturity;
    [key: string]: unknown;
}

export interface SBTrustCenterOverviewItem extends SBBaseItem {
    explanation: string;
}

export interface SBCompany {
    name?: string;
    logo?: string;
    domain?: string;
}

interface SBTrustCenterLegalItemEntry {
    company?: SBCompany;
    location?: string;
    additionalDetails?: string;
}

export interface SBTrustCenterLegalItem extends SBBaseItem {
    listEntries?: SBTrustCenterLegalItemEntry[];
}

export interface SBTrustCenterDocument {
    id: string;
    name: string;
    itemName: string;
    publicAccess: boolean;
    documentAccessType: SBOrganizationDocumentAccessType;
}

export interface SBTrustCenterDocumentDownload {
    url: string;
    fileName?: string;
}

export interface SBTrustCenterAccessRequestPayload {
    fields: VendorTrustCenterAccessRequestPayloadFieldsDto;
    callbackUrl: string;
    redirectLocationUrl: string;
}

export interface SBTrustCenterAccessRequestResult {
    id: string;
    code: SBVendorTrustCenterAccessRequestStatus;
}

export interface SBTrustCenterAccessRequestResponse {
    ok: boolean;
    result: SBTrustCenterAccessRequestResult[];
}

export interface SBSelectFieldOption {
    value: string;
    label: string;
}

export interface SBUrlFieldOption {
    url: string;
    label: string;
}

export interface SBAccessRequestRequirementsField {
    name: string;
    type: SBAccessRequestFieldType;
    label: string;
    required: boolean;
    minLength?: number;
    maxLength?: number;
    urls?: SBUrlFieldOption[];
    options?: SBSelectFieldOption[];
}

export interface SBAccessRequestRequirements {
    memberLoginStatusCode: SBPartnerRequestAccessStatusCode;
    fields: SBAccessRequestRequirementsField[];
}
