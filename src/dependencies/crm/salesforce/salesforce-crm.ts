import { HttpService } from '@nestjs/axios';
import { Injectable, InternalServerErrorException, NotFoundException } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import config from 'config';
import { Crm } from 'dependencies/crm/crm';
import { SalesforceConnectionFactory } from 'dependencies/crm/salesforce/factories/salesforce-connection.factory';
import { CrmAccount } from 'dependencies/crm/types/crm-account';
import { CrmContractEndDateAccount } from 'dependencies/crm/types/crm-contract-end-date-account';
import { CrmContractObj } from 'dependencies/crm/types/crm-contract-object';
import { CrmDrataObj } from 'dependencies/crm/types/crm-drata-obj';
import { CrmOpportunityStageName } from 'dependencies/crm/types/crm-opportunity-stage-name';
import { Connection, SaveResult, SfDate } from 'jsforce';
import { isNil } from 'lodash';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { URLSearchParams } from 'url';

@Injectable()
export class SalesforceCrm extends Crm {
    constructor(
        private readonly httpService: HttpService,
        private readonly salesforceConnectionFactory: SalesforceConnectionFactory,
    ) {
        super();
    }

    /**
     * Any errors should be handled in the higher scope
     * @param crmAccountId
     * @param accountId
     */
    async setAccountIdOnCrm(crmAccountId: string, accountId: string): Promise<void> {
        if (config.get('salesforce.enabled')) {
            const form = new URLSearchParams();
            form.append('drataAccountId', accountId);
            form.append('crmAccountId', crmAccountId);

            await this.httpService
                .post(config.get('salesforce.setAccountIdUrl'), form, {
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                })
                .toPromise();

            this.logger.log(
                PolloMessage.msg(
                    `Just sent off the Zap to save the Drata account ID {${accountId}} to CRM Account ID {${crmAccountId}}`,
                ),
            );
        }
    }

    async getAccountByCrmAccountId(crmAccountId: string): Promise<CrmAccount> {
        const conn = await this.salesforceConnectionFactory.connect();
        const query = `SELECT Id, Name, Company_Legal_Name__c, Region_Tenancy__c FROM Account where Id='${crmAccountId}'`;

        try {
            const queryResult = await conn.query<CrmAccount>(query);

            if (queryResult && queryResult.totalSize === 0) {
                throw new NotFoundException(`The CRM Account ID ${crmAccountId} was not found`);
            }

            return plainToInstance(CrmAccount, queryResult.records[0]);
        } catch (error) {
            this.logError(error);
            throw error;
        }
    }

    async getAccountContractEndDate(crmAccountId: string): Promise<CrmContractEndDateAccount> {
        const conn = await this.salesforceConnectionFactory.connect();
        const query = `SELECT Contract_End_Date__c FROM Account where Id='${crmAccountId}'`;

        try {
            const queryResult = await conn.query<CrmContractEndDateAccount>(query);

            if (queryResult && queryResult.totalSize === 0) {
                throw new NotFoundException(`The CRM Account ID ${crmAccountId} was not found`);
            }

            return plainToInstance(CrmContractEndDateAccount, queryResult.records[0]);
        } catch (error) {
            this.logError(error);
            throw error;
        }
    }

    async getAccountCRMDataToDelete(
        crmAccountId: string,
        tenantId: string,
    ): Promise<{
        drataObj: CrmDrataObj;
        contractObj: CrmContractObj | null;
        opportunities: CrmOpportunityStageName[] | null;
    }> {
        const connection = await this.salesforceConnectionFactory.connect();
        const drataObj = await this.getOrCreateDrataObjSF(connection, crmAccountId, tenantId);
        const contractObj = await this.getContractObj(connection, crmAccountId);
        const opportunities = await this.getOpportunities(connection, crmAccountId);

        return { drataObj, contractObj, opportunities };
    }

    async updateDeleteDateByCrmAccountId(crmAccountId: string, date: Date): Promise<void> {
        const conn = await this.salesforceConnectionFactory.connect();
        const formattedDate = SfDate.toDateTimeLiteral(date);

        try {
            const results = await conn
                .sobject('Drata_Tenant__c')
                .findOne({ Account__c: crmAccountId })
                .update({ Tenant_Delete_Date__c: formattedDate });

            this.handleRecordResultsErrors(results);
        } catch (error) {
            this.logError(error);
            throw new InternalServerErrorException(error);
        }
    }

    async sendAccountNote(crmAccountId: string, note: string): Promise<void> {
        const conn = await this.salesforceConnectionFactory.connect();
        const drataObj = await this.getDrataObjSF(conn, crmAccountId);

        const noteToSend = this.stringToJsonFormat(note);

        const originalJson = JSON.parse(drataObj.JSON__c ?? '{}');

        const newJson = {
            ...originalJson,
            log: [...(originalJson?.log ?? []), noteToSend],
        };

        try {
            const results = await conn
                .sobject('Drata_Tenant__c')
                .findOne({ Account__c: crmAccountId })
                .update({ JSON__c: JSON.stringify(newJson) });

            this.handleRecordResultsErrors(results);
        } catch (error) {
            this.logError(error);
            throw new InternalServerErrorException(error);
        }
    }

    async healthCheck(): Promise<any> {
        if (!config.get('salesforce.enabled')) {
            this.logger.log(
                PolloMessage.msg('Salesforce is disabled, skipping health check').setContext(
                    this.constructor.name,
                ),
            );
            return null;
        }
        this.logger.log(
            PolloMessage.msg(
                'Performing Salesforce health check by selecting 1 opportunity',
            ).setContext(this.constructor.name),
        );

        try {
            const conn = await this.salesforceConnectionFactory.connect();
            const query = 'SELECT Id, Name, StageName FROM Opportunity LIMIT 1';

            const queryResult = await conn.query(query);

            this.logger.log(
                PolloMessage.msg('Salesforce health check completed successfully')
                    .setMetadata({
                        totalSize: queryResult.totalSize,
                        opportunityFound: queryResult.totalSize > 0,
                    })
                    .setContext(this.constructor.name),
            );

            return queryResult.records[0] || null;
        } catch (error) {
            this.logger.error(
                PolloMessage.msg('Salesforce health check failed')
                    .setContext(this.constructor.name)
                    .setError(error),
            );
            throw error;
        }
    }

    private async getContractObj(
        connection: Connection,
        crmAccountId: string,
    ): Promise<CrmContractObj | null> {
        let contractObj: CrmContractObj | null = null;

        try {
            contractObj = await connection.sobject('Contract').findOne({ AccountId: crmAccountId });
        } catch (error) {
            // In case the object doesn't exists
            return null;
        }

        return plainToInstance(CrmContractObj, contractObj);
    }

    private async getOpportunities(connection: Connection, crmAccountId: string) {
        let opportunities: CrmOpportunityStageName[] | null = null;

        try {
            opportunities = await connection
                .sobject('Opportunity')
                .find<CrmOpportunityStageName>({ AccountId: crmAccountId });
        } catch (error) {
            // In case the object doesn't exists
            return null;
        }

        return plainToInstance(CrmOpportunityStageName, opportunities);
    }

    private async getDrataObjSF(
        connection: Connection,
        crmAccountId: string,
    ): Promise<CrmDrataObj> {
        const drataObj = await connection
            .sobject('Drata_Tenant__c')
            .findOne({ Account__c: crmAccountId });

        return plainToInstance(CrmDrataObj, drataObj);
    }

    private async getOrCreateDrataObjSF(
        connection: Connection,
        crmAccountId: string,
        tenantId: string,
    ): Promise<CrmDrataObj> {
        let drataObj: CrmDrataObj | null = await this.getDrataObjSF(connection, crmAccountId);

        if (isNil(drataObj)) {
            // If the Drata Obj doesn't exist, create one and link it to the account
            await connection.sobject('Drata_Tenant__c').create({
                Account__c: crmAccountId,
                Drata_Tenant_ID__c: tenantId,
            });

            drataObj = await connection
                .sobject('Drata_Tenant__c')
                .findOne({ Account__c: crmAccountId });
        }

        return plainToInstance(CrmDrataObj, drataObj);
    }

    // Parsing Information
    private stringToJsonFormat(text: string) {
        return { msg: text, added: new Date().toISOString() };
    }

    // Error handling
    private handleRecordResultsErrors(results?: SaveResult[]) {
        const result = results?.[0];
        if (result && !result?.success) {
            const errorObj = {
                ...result.errors[0],
                name: 'Unknown Error',
            };
            this.logError(errorObj);
            throw new InternalServerErrorException(errorObj);
        }
    }

    private logError(errorObj: Error) {
        this.logger.error(
            PolloMessage.msg(errorObj.message).setContext(this.constructor.name).setError(errorObj),
        );
    }
}
