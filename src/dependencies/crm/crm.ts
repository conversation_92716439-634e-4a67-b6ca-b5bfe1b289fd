import { ICrm } from 'dependencies/crm/interfaces/crm.interface';
import { CrmAccount } from 'dependencies/crm/types/crm-account';
import { CrmContractEndDateAccount } from 'dependencies/crm/types/crm-contract-end-date-account';
import { CrmContractObj } from 'dependencies/crm/types/crm-contract-object';
import { CrmDrataObj } from 'dependencies/crm/types/crm-drata-obj';
import { CrmOpportunityStageName } from 'dependencies/crm/types/crm-opportunity-stage-name';
import { PolloLogger } from 'pollo-logger/pollo.logger';

export abstract class Crm implements ICrm {
    /**
     *
     */
    protected logger = PolloLogger.logger(this.constructor.name);

    /**
     *
     * @param crmAccountId
     * @param accountId
     * @returns
     */
    abstract setAccountIdOnCrm(crmAccountId: string, accountId: string): Promise<void>;

    /**
     *
     * Get crm account by crmAccountId
     *
     * @param {string} crmAccountId
     * @returns {CrmAccount}
     */
    abstract getAccountByCrmAccountId(crmAccountId: string): Promise<CrmAccount>;

    /**
     *
     * Get crm account with contract end date by crmAccountId
     *
     * @param {string} crmAccountId
     * @returns {CrmContractEndDateAccount}
     */
    abstract getAccountContractEndDate(crmAccountId: string): Promise<CrmContractEndDateAccount>;

    /**
     *
     * Update the delete date by crmAccountId
     *
     * @param {string} crmAccountId
     * @param {Date} date
     * @returns {void}
     */
    abstract updateDeleteDateByCrmAccountId(crmAccountId: string, date: Date): Promise<void>;

    /**
     *
     * Send a note in the with the crmAccountId
     *
     * @param {string} crmAccountId
     * @param {string} note
     * @returns {void}
     */
    abstract sendAccountNote(crmAccountId: string, note: string): Promise<void>;

    /**
     *
     * Get all the CRM data required to validated if the account should be removed
     *
     * @param {string} crmAccountId
     * @param {string} tenantId
     * @returns { drataObj: CrmDrataObj; contractObj: CrmContractObj | null }
     */
    abstract getAccountCRMDataToDelete(
        crmAccountId: string,
        tenantId: string,
    ): Promise<{
        drataObj: CrmDrataObj;
        contractObj: CrmContractObj | null;
        opportunities: CrmOpportunityStageName[] | null;
    }>;

    /**
     *
     * Perform a health check on the CRM connection
     *
     * @returns {Promise<any>}
     */
    abstract healthCheck(): Promise<any>;
}
