import { CrmAccount } from 'dependencies/crm/types/crm-account';

export interface ICrm {
    /**
     *
     * @param crmAccountId
     * @param accountId
     * @returns
     */
    setAccountIdOnCrm(crmAccountId: string, accountId: string): Promise<void>;

    /**
     *
     * Get crm account by crmAccountId
     *
     * @param {string} crmAccountId
     * @returns {CrmAccountType}
     */
    getAccountByCrmAccountId(crmAccountId: string): Promise<CrmAccount>;

    /**
     *
     * Perform a health check on the CRM connection
     *
     * @returns {Promise<any>}
     */
    healthCheck(): Promise<any>;
}
