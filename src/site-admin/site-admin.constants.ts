import { FrameworkTag } from '@drata/enums';
import { FeatureFlagCategory } from 'feature-flags/enums/feature-flag-category.enum';
import { FeatureFlag } from 'feature-flags/feature-flag.enum';

export const FRAMEWORKS_WITHOUT_CONTROLS = [
    FrameworkTag.COBIT,
    FrameworkTag.ISO27701,
    FrameworkTag.CMMC,
    FrameworkTag.FFIEC,
    FrameworkTag.MSSSPA,
    FrameworkTag.NIST800171,
    FrameworkTag.SOX_ITGC,
];

export const ONBOARDING_TABLES_TO_VALIDATE = [
    'admin_onboarding',
    'auditor_framework_type',
    'autopilot_recipe_instance',
    'company',
    'control',
    'control_test_instance',
    'framework',
    'migration',
    'monitor_instance',
    'monitor_instance_check_type',
    'policy',
    'policy_grace_period_sla',
    'policy_p3_matrix_sla',
    'policy_week_time_frame_sla',
    'product',
    'products_control_test_instances_map',
    'products_controls_map',
    'products_frameworks_map',
    'recipe_control_test',
    'requirement',
    'role',
    'roles_permissions_map',
    'trust_center_company_info',
    'typeorm_metadata',
    'user',
    'user_role',
    'users_roles_map',
];

export const TABLES_EXCEPTION_WITHOUT_FRAMEWORKS = [
    'auditor_framework_type',
    'control',
    'control_test_instance',
    'framework',
    'monitor_instance',
    'monitor_instance_check_type',
    'products_control_test_instances_map',
    'products_controls_map',
    'products_frameworks_map',
    'recipe_control_test',
    'requirement',
];
export const TABLES_EXCEPTION_WITHOUT_CONTROLS = [
    'control',
    'control_test_instance',
    'monitor_instance',
    'monitor_instance_check_type',
    'products_control_test_instances_map',
    'products_controls_map',
    'recipe_control_test',
];

/**
 * In Salesforce (SFDC), account IDs typically come in two formats: 15-character case-sensitive or 18-character case-insensitive. Here's a breakdown of both formats:
 *
 * 1. 15-Character Case-Sensitive ID**:
 *    - This ID is composed of alphanumeric characters (A-Z, a-z, 0-9).
 *    - Example: `001A000000BcdEf`
 *
 * 2. 18-Character Case-Insensitive ID**:
 *    - This is an extended version of the 15-character ID, which includes an additional 3 characters used as a checksum to ensure case insensitivity.
 *    - Example: `001A000000BcdEfIAK`
 *
 * Structure
 * - **15-Character ID**: The first 3 characters denote the object type, with "001" typically indicating an Account.
 * - **18-Character ID**: The first 15 characters remain the same as the 15-character ID, while the last 3 characters are a checksum.
 *
 * Regular Expression for SFDC Account ID
 * If you want to validate an SFDC Account ID, you can use the following regular expression:
 *
 * - For 15-character IDs: `/^001[A-Za-z0-9]{12}$/`
 * - For 18-character IDs: `/^001[A-Za-z0-9]{12}[A-Za-z0-9]{3}$/`
 *
 * Combining both into one regex, considering that the 3-character checksum at the end is optional:
 *
 * ```regex
 * /^001[A-Za-z0-9]{12}(?:[A-Za-z0-9]{3})?$/
 * ```
 *
 * This regex matches strings that:
 * - Start with "001"
 * - Are followed by exactly 12 alphanumeric characters
 * - Optionally end with 3 more alphanumeric characters, making the total length either 15 or 18 characters
 */
export const CRM_COMPANY_EXPRESSION = /^001[A-Za-z0-9]{12}(?:[A-Za-z0-9]{3})?$/;

export const POLICY_MULTI_APPROVERS_FF = {
    category: FeatureFlagCategory.NONE,
    name: FeatureFlag.POLICY_MULTI_APPROVERS,
    defaultValue: false,
};

type SiteAdminAuthGuard =
    | 'SITE_ADMIN_JWT_GUARD'
    | 'SITE_ADMIN_OKTA_GUARD'
    | 'SITE_ADMIN_JWT_ROLES_GUARD'
    | 'SITE_ADMIN_OKTA_SCOPES_GUARD';

export const SITE_ADMIN_JWT_GUARD: SiteAdminAuthGuard = 'SITE_ADMIN_JWT_GUARD';
export const SITE_ADMIN_OKTA_GUARD: SiteAdminAuthGuard = 'SITE_ADMIN_OKTA_GUARD';
export const SITE_ADMIN_JWT_ROLES_GUARD: SiteAdminAuthGuard = 'SITE_ADMIN_JWT_ROLES_GUARD';
export const SITE_ADMIN_OKTA_SCOPES_GUARD: SiteAdminAuthGuard = 'SITE_ADMIN_OKTA_SCOPES_GUARD';
