import {
    Body,
    Controller,
    Get,
    HttpCode,
    HttpStatus,
    Param,
    ParseUUIDPipe,
    Post,
    Put,
    Query,
} from '@nestjs/common';
import { EventBus } from '@nestjs/cqrs';
import {
    ApiBadRequestResponse,
    ApiConflictResponse,
    ApiCreatedResponse,
    ApiNotFoundResponse,
    ApiOkResponse,
    ApiOperation,
    ApiParam,
    ApiPreconditionFailedResponse,
    ApiTags,
} from '@nestjs/swagger';
import { SupportUserOrchestrationService } from 'app/users/services/support-user-orchestration.service';
import { Account } from 'auth/entities/account.entity';
import { Token } from 'auth/entities/token.entity';
import { AccountsCoreService } from 'auth/services/accounts-core.service';
import { AuthService } from 'auth/services/auth.service';
import { TenantRouterService } from 'auth/services/tenant-router.service';
import { AccountInfoType } from 'auth/types/account-info-type';
import { AccountLimitsType } from 'auth/types/account-limits.type';
import { AccountTrustCenterCnameType } from 'auth/types/account-trust-center-cname.type';
import { BaseController } from 'commons/controllers/base.controller';
import { Dto } from 'commons/decorators/dto.decorator';
import { Area, ProductArea } from 'commons/decorators/product-area.decorator';
import { Roles } from 'commons/decorators/roles.decorator';
import { ExceptionResponseDto } from 'commons/dtos/exception-response.dto';
import { ApiResponse } from 'commons/enums/api-response.enum';
import { TokenType } from 'commons/enums/auth/token-type.enum';
import { AdminRole } from 'commons/enums/site-admin/admin-role.enum';
import { AuditLogEventType } from 'commons/enums/site-admin/audit-log-event-type.enum';
import { AuditLogTargetType } from 'commons/enums/site-admin/audit-log-target-type.enum';
import { AccountIdType } from 'commons/types/account-id.type';
import { PaginationType } from 'commons/types/pagination.type';
import { GetSiteAdmin } from 'site-admin/decorators/get-site-admin.decorator';
import { SiteAdminAuth } from 'site-admin/decorators/site-admin-auth.decorator';
import { AccountActAsResponseDto } from 'site-admin/dtos/account-act-as-response.dto';
import { AccountAdminRequestDto } from 'site-admin/dtos/account-admin-request.dto';
import { AccountEntitlementSettingsRequestDto } from 'site-admin/dtos/account-entitlement-settings-request.dto';
import { AccountFrameworksRequestDto } from 'site-admin/dtos/account-frameworks-request.dto';
import { AccountInfoResponseDto } from 'site-admin/dtos/account-info-response.dto';
import { AccountInfoUpdateRequestDto } from 'site-admin/dtos/account-info-update-request.dto';
import { AccountInfoUpdatedResponseDto } from 'site-admin/dtos/account-info-updated-response.dto';
import { AccountLimitsRequestDto } from 'site-admin/dtos/account-limits-request.dto';
import { AccountLimitsResponseDto } from 'site-admin/dtos/account-limits-response.dto';
import { AccountRequestDto } from 'site-admin/dtos/account-request.dto';
import { AccountResponseDto } from 'site-admin/dtos/account-response.dto';
import { AccountTrustCenterCnameResponseDto } from 'site-admin/dtos/account-trust-center-cname-response.dto';
import { AccountTrustCenterCnameRequestDto } from 'site-admin/dtos/account-trust-center-cname.request.dto';
import { AccountsRequestDto } from 'site-admin/dtos/accounts-request.dto';
import { AccountsResponseDto } from 'site-admin/dtos/accounts-response.dto';
import { ActAsRequestDto } from 'site-admin/dtos/act-as-request.dto';
import { AdminAccountRequestDto } from 'site-admin/dtos/admin-account-request.dto';
import { CreateAccountRequestDto } from 'site-admin/dtos/create-account-request.dto';
import { SiteAdminTenantDatabaseHostResponseDto } from 'site-admin/dtos/site-admin-tenant-database-host-response.dto';
import { SiteAdmin } from 'site-admin/entities/site-admin.entity';
import { TenantDatabaseHost } from 'site-admin/entities/tenant-database-host.entity';
import { AuditLogEvent } from 'site-admin/observables/events/audit-log.event';
import { SiteAdminAccountLimitsService } from 'site-admin/services/site-admin-account-limits.service';
import { SiteAdminTenantDatabaseHostService } from 'site-admin/services/site-admin-tenant-database-host.service';
import { SiteAdminService } from 'site-admin/services/site-admin.service';
import { SiteAdminRoute } from 'site-admin/site-admin.routes';
import { tenantWrapper } from 'tenancy/contexts/tenant-wrapper';

@ApiTags('Site Admin')
@SiteAdminAuth()
@Controller()
@ProductArea(Area.TBD)
export class SiteAdminAccountsController extends BaseController {
    constructor(
        private readonly siteAdminService: SiteAdminService,
        private readonly tenantDatabaseHostService: SiteAdminTenantDatabaseHostService,
        private readonly siteAdminAccountLimitsService: SiteAdminAccountLimitsService,
        private readonly authService: AuthService,
        private readonly eventBus: EventBus,
        private readonly tenantRouterService: TenantRouterService,
        private readonly accountsCoreService: AccountsCoreService,
        private readonly supportUserOrchestrationService: SupportUserOrchestrationService,
    ) {
        super();
    }

    @ApiOperation({
        description: `Get account records with pagination
        <br><br>
        Allowed Roles: [
            ${AdminRole[AdminRole.EMPLOYEE]}
        ]`,
    })
    @ApiOkResponse({
        type: AccountsResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @Roles(AdminRole.EMPLOYEE)
    @Dto(AccountsResponseDto)
    @Get(SiteAdminRoute.GET_ACCOUNTS)
    getAccounts(@Query() requestDto: AccountsRequestDto): Promise<PaginationType<Account>> {
        // get the paginated accounts
        // does not need tenancy
        return this.accountsCoreService.getAccounts(requestDto);
    }

    @ApiOperation({
        description: `Get account record
        <br><br>
        Allowed Roles: [
            ${AdminRole[AdminRole.EMPLOYEE]}
        ]`,
    })
    @ApiOkResponse({
        type: AccountInfoResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponseDto,
    })
    @ApiParam({
        name: 'id',
        description: 'Account ID',
        type: 'string',
        format: 'uuid',
    })
    @Roles(AdminRole.EMPLOYEE)
    @Dto(AccountInfoResponseDto)
    @Get(SiteAdminRoute.GET_ACCOUNT)
    async getAccount(
        @Param('id', ParseUUIDPipe) id: AccountIdType,
        @Query() requestDto: AdminAccountRequestDto,
    ): Promise<AccountInfoType> {
        // get the account
        const account = await this.accountsCoreService.getAccountById(id);

        return tenantWrapper(account, () =>
            this.siteAdminService.getAccountInfo(id, requestDto.includeDeleted),
        );
    }

    @ApiOperation({
        description: `List Tenant Database Hosts
        <br><br>
        Allowed Roles: [
            ${AdminRole[AdminRole.EMPLOYEE]}
        ]`,
    })
    @ApiOkResponse({
        description: ApiResponse.OK,
        type: SiteAdminTenantDatabaseHostResponseDto,
    })
    @Roles(AdminRole.EMPLOYEE)
    @Dto(SiteAdminTenantDatabaseHostResponseDto)
    @Get(SiteAdminRoute.GET_TENANT_DATABASE_HOSTS)
    getTenantDatabaseHosts(): Promise<PaginationType<TenantDatabaseHost>> {
        // does not need tenancy
        return this.tenantDatabaseHostService.getTenantDatabaseHosts();
    }

    @ApiOperation({
        description: `Get Magic Link token to auth into ADMIN in a tenant account
        <br><br>
        Allowed Roles: [
            ${AdminRole[AdminRole.ACT_AS]},
            ${AdminRole[AdminRole.ACT_AS_READ_ONLY]}
        ]`,
    })
    @ApiCreatedResponse({
        description: ApiResponse.CREATED,
        type: AccountActAsResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @ApiPreconditionFailedResponse({
        description: ApiResponse.PRECONDITION_FAILED_ACT_AS,
        type: ExceptionResponseDto,
    })
    @ApiParam({
        name: 'id',
        description: 'Account ID',
        type: 'string',
        format: 'uuid',
    })
    @Roles(AdminRole.ACT_AS, AdminRole.ACT_AS_READ_ONLY)
    @Dto(AccountActAsResponseDto)
    @Post(SiteAdminRoute.POST_ACCOUNT_ACT_AS)
    async accountActAs(
        @GetSiteAdmin() currentAdmin: SiteAdmin,
        @Body() requestDto: ActAsRequestDto,
        @Param('id', ParseUUIDPipe) id: AccountIdType,
    ): Promise<Token> {
        const account = await this.accountsCoreService.getAccountById(id);

        const { supportUser, tokenType } = await tenantWrapper(account, async () =>
            this.siteAdminService.getSupportUserAndTokenType(account, requestDto, currentAdmin),
        );

        const entry =
            await this.siteAdminService.validateAccountAndSupportUserEntryMapping(account);

        // does not need tenancy below

        const token = await this.authService.generateMagicLinkTokenV2({
            email: supportUser.email,
            tokenType: tokenType,
            siteAdmin: currentAdmin,
            entry, // this is set here so as not to fetch from DB again
        });

        await this.tenantRouterService.addRoute(account, token.id);

        let eventType = AuditLogEventType.USER_IMPERSONATED_READ;

        if (tokenType === TokenType.ACT_AS) {
            eventType = AuditLogEventType.USER_IMPERSONATED_WRITE;
        }

        // site-admin audit log
        this.eventBus.publish(
            new AuditLogEvent(
                currentAdmin,
                eventType,
                AuditLogTargetType.ACCOUNT,
                account.id,
                account,
            ),
        );

        return token;
    }

    @ApiOperation({
        description: `Create a new account by a site admin
        <br><br>
        Allowed Roles: [
            ${AdminRole[AdminRole.ADMIN]},
            ${AdminRole[AdminRole.CSG_TECH]},
            ${AdminRole[AdminRole.CSG]},
            ${AdminRole[AdminRole.BUS_DEV]}
        ]`,
    })
    @ApiOkResponse({
        description: ApiResponse.OK,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @ApiConflictResponse({
        description: [ApiResponse.CONFLICT_EMAIL, ApiResponse.CONFLICT_DATABASE].toString(),
        type: ExceptionResponseDto,
    })
    @ApiPreconditionFailedResponse({
        description: ApiResponse.PRECONDITION_FAILED_DOMAIN,
        type: ExceptionResponseDto,
    })
    @Roles(AdminRole.ADMIN, AdminRole.CSG_TECH, AdminRole.CSG, AdminRole.BUS_DEV)
    @Dto(AccountResponseDto)
    @Post(SiteAdminRoute.POST_ADD_ACCOUNT)
    @HttpCode(HttpStatus.OK)
    async createAccount(
        @GetSiteAdmin() currentAdmin: SiteAdmin,
        @Body() requestDto: CreateAccountRequestDto,
    ): Promise<Account> {
        // TODO: need to figure out what to do with auth service
        // uses tenantConnection to add tables to the tenant database after creation
        const account = await this.authService.createAccount(currentAdmin, requestDto);
        // create supportUser for every new account
        await tenantWrapper(account, () =>
            this.supportUserOrchestrationService.createSupportUser(account.id),
        );

        return account;
    }

    @ApiOperation({
        description: `Send onboarding email to the account owner
        <br><br>
        Allowed Roles: [
            ${AdminRole[AdminRole.ADMIN]},
            ${AdminRole[AdminRole.CSG_TECH]},
            ${AdminRole[AdminRole.CSG]},
            ${AdminRole[AdminRole.BUS_DEV]}
        ]`,
    })
    @ApiOkResponse({
        description: ApiResponse.OK,
        type: AccountResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponseDto,
    })
    @ApiConflictResponse({
        description: ApiResponse.CONFLICT,
        type: ExceptionResponseDto,
    })
    @ApiParam({
        name: 'id',
        description: 'Account ID',
        type: 'string',
        format: 'uuid',
    })
    @Roles(AdminRole.ADMIN, AdminRole.CSG_TECH, AdminRole.CSG, AdminRole.BUS_DEV)
    @Dto(AccountResponseDto)
    @Put(SiteAdminRoute.PUT_READY_ACCOUNT)
    async sendOnboardingEmail(
        @GetSiteAdmin() currentAdmin: SiteAdmin,
        @Param('id', ParseUUIDPipe) id: AccountIdType,
    ): Promise<Account> {
        // delegate to the service
        const account = await this.accountsCoreService.getAccountById(id);
        return tenantWrapper(account, () =>
            this.siteAdminService.sendOnboardingEmail(currentAdmin, id),
        );
    }

    @ApiOperation({
        description: `Update an account
        <br><br>
        Allowed Roles: [
            ${AdminRole[AdminRole.CSG_TECH]},
            ${AdminRole[AdminRole.CSG]},
            ${AdminRole[AdminRole.ENGINEER]},
            ${AdminRole[AdminRole.ADMIN]},
            ${AdminRole[AdminRole.BUS_DEV]}
        ]`,
    })
    @ApiOkResponse({
        type: AccountResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponseDto,
    })
    @ApiConflictResponse({
        description: ApiResponse.CONFLICT_ACCOUNT_STATUS,
        type: ExceptionResponseDto,
    })
    @ApiParam({
        name: 'id',
        description: 'Account ID',
        type: 'string',
        format: 'uuid',
    })
    @Roles(
        AdminRole.CSG_TECH,
        AdminRole.CSG,
        AdminRole.ENGINEER,
        AdminRole.ADMIN,
        AdminRole.BUS_DEV,
    )
    @Dto(AccountInfoResponseDto)
    @Put(SiteAdminRoute.PUT_UPDATE_ACCOUNT)
    async updateAccount(
        @GetSiteAdmin() currentAdmin: SiteAdmin,
        @Body() requestDto: AccountRequestDto,
        @Param('id', ParseUUIDPipe) id: AccountIdType,
    ): Promise<AccountInfoType> {
        // call the service to update the account and return the data
        const account = await this.accountsCoreService.getAccountById(id);
        return tenantWrapper(account, () =>
            this.siteAdminService.updateAccount(currentAdmin, requestDto, id),
        );
    }

    @ApiOperation({
        description: `Update an account info (status, language, CRM Id, DB host and port, tenant databse and multi domain support)
        <br><br>
        Allowed Roles: [
             ${AdminRole[AdminRole.CSG_TECH]},
            ${AdminRole[AdminRole.CSG]},
            ${AdminRole[AdminRole.ENGINEER]},
            ${AdminRole[AdminRole.ADMIN]},
            ${AdminRole[AdminRole.BUS_DEV]}
        ]`,
    })
    @ApiOkResponse({
        type: AccountInfoUpdatedResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponseDto,
    })
    @ApiConflictResponse({
        description: ApiResponse.CONFLICT_ACCOUNT_STATUS,
        type: ExceptionResponseDto,
    })
    @ApiParam({
        name: 'id',
        description: 'Account ID',
        type: 'string',
        format: 'uuid',
    })
    @Roles(
        AdminRole.CSG_TECH,
        AdminRole.CSG,
        AdminRole.ENGINEER,
        AdminRole.ADMIN,
        AdminRole.BUS_DEV,
    )
    @Dto(AccountInfoUpdatedResponseDto)
    @Put(SiteAdminRoute.PUT_UPDATE_INFO_ACCOUNT)
    async updateAccountInfo(
        @GetSiteAdmin() currentAdmin: SiteAdmin,
        @Body() requestDto: AccountInfoUpdateRequestDto,
        @Param('id', ParseUUIDPipe) id: AccountIdType,
    ): Promise<Account> {
        const account = await this.accountsCoreService.getAccountById(id);
        return tenantWrapper(account, () =>
            this.siteAdminService.updateAccountInfo(currentAdmin, id, requestDto),
        );
    }

    @ApiOperation({
        description: `Update account limits(workspace, custom framework and security questionnaire)
        <br><br>
        Allowed Roles: [
            ${AdminRole[AdminRole.ADMIN]},
            ${AdminRole[AdminRole.ENGINEER]},
            ${AdminRole[AdminRole.CSG]},
            ${AdminRole[AdminRole.CSG_TECH]},
            ${AdminRole[AdminRole.BUS_DEV]}
        ]`,
    })
    @ApiOkResponse({
        type: AccountLimitsResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponseDto,
    })
    @ApiParam({
        name: 'id',
        description: 'Account ID',
        type: 'string',
        format: 'uuid',
    })
    @Roles(
        AdminRole.ADMIN,
        AdminRole.ENGINEER,
        AdminRole.CSG,
        AdminRole.CSG_TECH,
        AdminRole.BUS_DEV,
    )
    @Dto(AccountLimitsResponseDto)
    @Put(SiteAdminRoute.PUT_UPDATE_ACCOUNT_LIMITS)
    async updateAccountLimits(
        @GetSiteAdmin() currentAdmin: SiteAdmin,
        @Body() requestDto: AccountLimitsRequestDto,
        @Param('id', ParseUUIDPipe) id: AccountIdType,
    ): Promise<AccountLimitsType> {
        const account = await this.accountsCoreService.getAccountById(id);
        return tenantWrapper(account, () =>
            this.siteAdminAccountLimitsService.updateLimitsSetting(currentAdmin, id, requestDto),
        );
    }

    @ApiOperation({
        description: `Update trust center cname'
        <br><br>
        Allowed Roles: [
            ${AdminRole[AdminRole.ADMIN]},
            ${AdminRole[AdminRole.ENGINEER]},
            ${AdminRole[AdminRole.CSG]},
            ${AdminRole[AdminRole.CSG_TECH]},
            ${AdminRole[AdminRole.BUS_DEV]}
        ]`,
    })
    @ApiOkResponse({
        type: AccountTrustCenterCnameResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponseDto,
    })
    @ApiParam({
        name: 'id',
        description: 'Account ID',
        type: 'string',
        format: 'uuid',
    })
    @Roles(
        AdminRole.ADMIN,
        AdminRole.ENGINEER,
        AdminRole.CSG,
        AdminRole.CSG_TECH,
        AdminRole.BUS_DEV,
    )
    @Dto(AccountTrustCenterCnameResponseDto)
    @Put(SiteAdminRoute.PUT_UPDATE_ACCOUNT_TRUST_CENTER_CNAME)
    async updateTrustCenterCname(
        @GetSiteAdmin() currentAdmin: SiteAdmin,
        @Body() requestDto: AccountTrustCenterCnameRequestDto,
        @Param('id', ParseUUIDPipe) id: AccountIdType,
    ): Promise<AccountTrustCenterCnameType> {
        const account = await this.accountsCoreService.getAccountById(id);
        return tenantWrapper(account, () =>
            this.siteAdminService.updateTrustCenterCname(currentAdmin, id, requestDto),
        );
    }

    @ApiOperation({
        description: `Update account entitlements settings'
        <br><br>
        Allowed Roles: [
            ${AdminRole[AdminRole.ADMIN]},
            ${AdminRole[AdminRole.ENGINEER]},
            ${AdminRole[AdminRole.CSG]},
            ${AdminRole[AdminRole.CSG_TECH]},
            ${AdminRole[AdminRole.BUS_DEV]}
        ]`,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponseDto,
    })
    @ApiOkResponse()
    @ApiParam({
        name: 'id',
        description: 'Account ID',
        type: 'string',
        format: 'uuid',
    })
    @Roles(
        AdminRole.ADMIN,
        AdminRole.ENGINEER,
        AdminRole.CSG,
        AdminRole.CSG_TECH,
        AdminRole.BUS_DEV,
    )
    @Put(SiteAdminRoute.PUT_UPDATE_ACCOUNT_ENTITLEMENTS)
    async updateEntitlements(
        @GetSiteAdmin() currentAdmin: SiteAdmin,
        @Body() requestDto: AccountEntitlementSettingsRequestDto,
        @Param('id', ParseUUIDPipe) id: AccountIdType,
    ): Promise<void> {
        const account = await this.accountsCoreService.getAccountById(id);
        return tenantWrapper(account, () =>
            this.siteAdminService.updateEntitlementSettings(currentAdmin, id, requestDto),
        );
    }

    @ApiOperation({
        description: `Update account frameworks'
        <br><br>
        Allowed Roles: [
            ${AdminRole[AdminRole.ADMIN]},
            ${AdminRole[AdminRole.ENGINEER]},
            ${AdminRole[AdminRole.CSG]},
            ${AdminRole[AdminRole.CSG_TECH]},
            ${AdminRole[AdminRole.BUS_DEV]}
        ]`,
    })
    @ApiOkResponse()
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponseDto,
    })
    @ApiParam({
        name: 'id',
        description: 'Account ID',
        type: 'string',
        format: 'uuid',
    })
    @Roles(
        AdminRole.ADMIN,
        AdminRole.ENGINEER,
        AdminRole.CSG,
        AdminRole.CSG_TECH,
        AdminRole.BUS_DEV,
    )
    @Put(SiteAdminRoute.PUT_UPDATE_ACCOUNT_FRAMEWORKS)
    async updateFrameworks(
        @GetSiteAdmin() currentAdmin: SiteAdmin,
        @Body() requestDto: AccountFrameworksRequestDto,
        @Param('id', ParseUUIDPipe) id: AccountIdType,
    ): Promise<void> {
        const account = await this.accountsCoreService.getAccountById(id);
        await tenantWrapper(account, () =>
            this.siteAdminService.updateFrameworks(currentAdmin, id, requestDto),
        );
    }

    @ApiOperation({
        description: `Add new admin to the tenant database'
        <br><br>
        Allowed Roles: [
            ${AdminRole[AdminRole.ADMIN]},
            ${AdminRole[AdminRole.ENGINEER]},
            ${AdminRole[AdminRole.CSG]},
            ${AdminRole[AdminRole.CSG_TECH]},
            ${AdminRole[AdminRole.BUS_DEV]}
        ]`,
    })
    @ApiCreatedResponse()
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponseDto,
    })
    @ApiParam({
        name: 'id',
        description: 'Account ID',
        type: 'string',
        format: 'uuid',
    })
    @Roles(
        AdminRole.ADMIN,
        AdminRole.ENGINEER,
        AdminRole.CSG,
        AdminRole.CSG_TECH,
        AdminRole.BUS_DEV,
    )
    @Post(SiteAdminRoute.POST_ADD_ADMIN_ACCOUNT)
    async addNewAdmin(
        @GetSiteAdmin() currentAdmin: SiteAdmin,
        @Body() requestDto: AccountAdminRequestDto,
        @Param('id', ParseUUIDPipe) id: AccountIdType,
    ): Promise<void> {
        const account = await this.accountsCoreService.getAccountById(id);
        return tenantWrapper(account, () =>
            this.siteAdminService.addNewAdmin(currentAdmin, id, requestDto.newAdmin),
        );
    }
}
