import { ErrorCode } from '@drata/enums';
import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { UnauthorizedException } from 'commons/exceptions/unauthorized.exception';
import { Nullable } from 'commons/types/nullable.type';
import config from 'config';
import tracer from 'dd-trace';
import { FeatureFlagService } from 'feature-flags/feature-flag.service';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { PolloLogger } from 'pollo-logger/pollo.logger';
import { SiteAdmin } from 'site-admin/entities/site-admin.entity';
import { SiteAdminRoleRepository } from 'site-admin/repositories/site-admin-role.repository';
import { SiteAdminRepository } from 'site-admin/repositories/site-admin.respository';
import { SiteAdminJwtPayloadType } from 'site-admin/site-admin-jwt-payload.type';

@Injectable()
export class SiteAdminJwtStrategy extends PassportStrategy(
    Strategy,
    config.get('jwt.siteAdminStrategy'),
) {
    // set a logger
    private logger = PolloLogger.logger(this.constructor.name);

    constructor(
        private readonly siteAdminRepository: SiteAdminRepository,
        private readonly siteAdminRoleRepository: SiteAdminRoleRepository,
        private readonly featureFlagService: FeatureFlagService,
    ) {
        // setup PassportStrategy parent constructor
        super({
            jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
            secretOrKey: config.get('jwt.secret'),
        });
    }

    async validate(payload: SiteAdminJwtPayloadType): Promise<SiteAdmin> {
        this.logger.debug(PolloMessage.msg('Validating Site Admin JWT Strategy'));
        const { id } = payload;

        tracer.setUser({
            id: id,
        });
        // look it up in the database and remove soft-deleted roles
        const siteAdmin = await this.getSiteAdmin(id);
        // sanity check for a site admin
        if (!siteAdmin) {
            // log error here
            this.logger.error(PolloMessage.msg(`Site Admin does not exist: ${id}`));
            // reject - throw 401
            throw new UnauthorizedException(ErrorCode.JWT_EMPTY_SITE_ADMIN);
        }
        // return site admin
        return siteAdmin;
    }

    async getSiteAdmin(id: string): Promise<Nullable<SiteAdmin>> {
        const [siteAdmin, roles] = await Promise.all([
            this.siteAdminRepository.findOne({
                where: { id },
                loadEagerRelations: false,
            }),
            this.siteAdminRoleRepository.getBySiteAdminId(id),
        ]);
        if (siteAdmin) {
            siteAdmin.roles = roles;
        }
        return siteAdmin;
    }
}
