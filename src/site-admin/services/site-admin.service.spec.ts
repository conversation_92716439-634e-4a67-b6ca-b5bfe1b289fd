import { InternalServerErrorException } from '@nestjs/common';
import { TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { AutopilotScheduleInstanceCoreService } from 'app/autopilot/services/autopilot-schedule-instance-core.service';
import { AutopilotScheduleInstanceService } from 'app/autopilot/services/autopilot-schedule-instance.service';
import { ConnectionsCoreService } from 'app/companies/connections/services/connections-core.service';
import { AdminOnboardingOrchestrationService } from 'app/companies/services/admin-onboarding-orchestration.service';
import { AdminOnboardingService } from 'app/companies/services/admin-onboarding.service';
import { CompaniesCoreService } from 'app/companies/services/companies-core.service';
import { ComplianceCheckExclusionsService } from 'app/compliance-check-exclusions/services/compliance-check-exclusions.service';
import { FeatureService } from 'app/feature-toggling/services/feature.service';
import { FrameworkService } from 'app/frameworks/services/framework.service';
import { PersonnelOrchestrationService } from 'app/users/personnel/orchestration/personnel/personnel-orchestration.service';
import { ComplianceChecksOrchestrationService } from 'app/users/personnel/services/compliance-checks-orchestration.service';
import { PersonnelCoreService } from 'app/users/personnel/services/personnel-core.service';
import { PersonnelService } from 'app/users/personnel/services/personnel.service';
import { SupportUserOrchestrationService } from 'app/users/services/support-user-orchestration.service';
import { SupportUserService } from 'app/users/services/support-user.service';
import { UsersCoreService } from 'app/users/services/users-core.service';
import { UsersService } from 'app/users/services/users.service';
import { AuditorClientRepository } from 'auditors/repositories/auditor-client.repository';
import { AccountSafeBaseSettings } from 'auth/entities/account-safebase-settings.entity';
import { Account } from 'auth/entities/account.entity';
import { Entry } from 'auth/entities/entry.entity';
import { AccountsCoreService } from 'auth/services/accounts-core.service';
import { AccountsService } from 'auth/services/accounts.service';
import { EntryCoreService } from 'auth/services/entry-core.service';
import { EntryService } from 'auth/services/entry.service';
import { TenantRouterService } from 'auth/services/tenant-router.service';
import { CacheService } from 'cache/cache.service';
import { ConnectionFactory } from 'commons/factories/connection.factory';
import { createAppTestingModule } from 'commons/helpers/app-testing-module.helper';
import { MockFactory } from 'commons/mocks/factories/mock.factory';
import { AccountEntitlementService } from 'entitlements/entitlements.service';
import { AdaptiveAutomationMetadataService } from 'entitlements/services/adaptive-automation-metadata.service';
import { TrustCenterEntitlementService } from 'entitlements/trust-center/services/trust-center-entitlement.service';
import { FeatureFlagService } from 'feature-flags/feature-flag.service';
import { mock } from 'jest-mock-extended';
import { SelfServiceInviteService } from 'self-service-invite/services/self-service-invite.service';
import { ServiceUserClientRepository } from 'service-user/repositories/service-user-client.repository';
import { ServiceUsersCoreService } from 'service-user/service-users-core.service';
import { ServiceUsersOrchestrationService } from 'service-user/service-users-orchestration.service';
import { ServiceGroupRepository } from 'site-admin/repositories/service-group.repository';
import { siteAdminConnectionMock } from 'site-admin/services/mocks/connections/site-admin-connection.mock';
import { SiteAdminAccountLimitsService } from 'site-admin/services/site-admin-account-limits.service';
import { SiteAdminServiceGroupService } from 'site-admin/services/site-admin-service-group.service';
import { SiteAdminServiceUsersService } from 'site-admin/services/site-admin-service-users.service';
import { SiteAdminService } from 'site-admin/services/site-admin.service';
import { TenancyContextMockType } from 'tenancy/contexts/__mocks__/tenancy.context';
import { TenancyContext } from 'tenancy/contexts/tenancy.context';
import { factory, useSeeding } from 'typeorm-seeding';

describe('SiteAdminService', () => {
    let siteAdminService: SiteAdminService;
    const getAccountByIdSpy = jest.fn();

    const entryCoreServiceMock = MockFactory.getMock(EntryCoreService);
    const entryServiceMock = MockFactory.getMock(EntryService);
    const supportUserServiceMock = {
        getSupportUserEmail: jest.fn().mockReturnValue('<EMAIL>'),
        getOrCreateSupportUser: jest.fn(),
    };

    describe('SiteAdminService Testing Module', () => {
        beforeEach(async () => {
            const module: TestingModule = await createAppTestingModule({
                providers: [
                    SiteAdminService,
                    {
                        provide: getRepositoryToken(AccountSafeBaseSettings),
                        useValue: {},
                    },
                    {
                        provide: EntryCoreService,
                        useValue: entryCoreServiceMock,
                    },
                    {
                        provide: FeatureService,
                        useValue: {},
                    },

                    {
                        provide: EntryService,
                        useValue: entryServiceMock,
                    },
                    {
                        provide: AccountEntitlementService,
                        useValue: {},
                    },
                    {
                        provide: TrustCenterEntitlementService,
                        useValue: {},
                    },
                    {
                        provide: AccountsService,
                        useValue: {},
                    },
                    {
                        provide: AccountsCoreService,
                        useValue: {
                            getAccountById: getAccountByIdSpy,
                        },
                    },
                    {
                        provide: CacheService,
                        useValue: {},
                    },
                    {
                        provide: AuditorClientRepository,
                        useValue: {},
                    },
                    {
                        provide: SiteAdminServiceGroupService,
                        useValue: {},
                    },
                    {
                        provide: ServiceUsersCoreService,
                        useValue: {},
                    },
                    {
                        provide: TenantRouterService,
                        useValue: {},
                    },
                    {
                        provide: SelfServiceInviteService,
                        useValue: {},
                    },
                    {
                        provide: AdaptiveAutomationMetadataService,
                        useValue: {},
                    },
                    {
                        provide: SiteAdminAccountLimitsService,
                        useValue: mock<SiteAdminAccountLimitsService>(),
                    },
                    { provide: UsersService, useValue: {} },
                    { provide: FrameworkService, useValue: {} },
                    { provide: CompaniesCoreService, useValue: {} },
                    { provide: PersonnelService, useValue: {} },
                    { provide: AutopilotScheduleInstanceService, useValue: {} },
                    { provide: AutopilotScheduleInstanceService, useValue: {} },
                    { provide: ConnectionsCoreService, useValue: {} },
                    { provide: ComplianceCheckExclusionsService, useValue: {} },
                    { provide: AdminOnboardingService, useValue: {} },
                    {
                        provide: SupportUserService,
                        useValue: supportUserServiceMock,
                    },
                    {
                        provide: AutopilotScheduleInstanceCoreService,
                        useValue: {
                            updateSchedulesAccountData: jest.fn(),
                        },
                    },
                    {
                        provide: ComplianceChecksOrchestrationService,
                        useValue: {},
                    },
                    {
                        provide: AdminOnboardingOrchestrationService,
                        useValue: {},
                    },
                    {
                        provide: UsersCoreService,
                        useValue: {
                            getSupportUserEmail: jest.fn(),
                        },
                    },
                    {
                        provide: PersonnelCoreService,
                        useValue: {},
                    },
                    {
                        provide: PersonnelOrchestrationService,
                        useValue: {},
                    },
                    {
                        provide: FeatureFlagService,
                        useValue: { evaluateAsTenant: jest.fn().mockResolvedValue(true) },
                    },
                    {
                        provide: SupportUserOrchestrationService,
                        useValue: {},
                    },
                    {
                        provide: ServiceUserClientRepository,
                        useValue: {},
                    },
                    {
                        provide: ServiceGroupRepository,
                        useValue: {},
                    },
                    {
                        provide: SiteAdminServiceUsersService,
                        useValue: {},
                    },
                    {
                        provide: ServiceUsersOrchestrationService,
                        useValue: {},
                    },
                ],
            }).compile();

            siteAdminService = module.get<SiteAdminService>(SiteAdminService);
            const tenantContext = module.get<TenancyContextMockType>(TenancyContext);
            tenantContext.getRepository.mockReturnValue(siteAdminConnectionMock.getRepository());
        });

        afterEach(() => {
            jest.clearAllMocks();
        });

        it('should be defined', () => {
            expect(siteAdminService).toBeDefined();
        });
    });

    describe('validateTables', () => {
        let account: Account;

        beforeAll(async () => {
            await useSeeding({
                configName: './src/tests/ormconfig-unit-tests',
            });
            account = await factory(Account)().make({ id: '123er456ty7' });
        });

        it('Should throw error when does not have the min rows', () => {
            const tablesToValidate = ['frameworks'];

            jest.spyOn(ConnectionFactory, 'getConnection').mockReturnValue(
                Promise.resolve({
                    query: () => {
                        return {
                            length: 0,
                        };
                    },
                } as any),
            );

            return expect(
                siteAdminService.validateTables(account, tablesToValidate),
            ).rejects.toThrow();
        });

        it('Should not throw error when does have the min rows', () => {
            const tablesToValidate = ['frameworks'];

            jest.spyOn(ConnectionFactory, 'getConnection').mockReturnValue(
                Promise.resolve({
                    query: () => {
                        return {
                            length: 1,
                        };
                    },
                } as any),
            );

            return expect(
                siteAdminService.validateTables(account, tablesToValidate),
            ).resolves.not.toThrow();
        });
    });

    describe('validateAccountAndSupportUserEntryMapping', () => {
        beforeEach(() => {
            // Reset the mock implementation before each test
            supportUserServiceMock.getSupportUserEmail.mockReturnValue('<EMAIL>');
        });

        describe('Given an account that is not mapped to the entry of the support user', () => {
            let account: Account;
            let entry: Entry;

            beforeEach(() => {
                account = new Account();
                account.id = 'account-id';
                entry = new Entry();
                entry.accounts = [];
                entryCoreServiceMock.getEntryByEmailNoFail?.mockResolvedValue(entry);
                entryCoreServiceMock.getEntryByEmailNoFailV2?.mockResolvedValue(entry);
                entryCoreServiceMock.getEntryByEmailWithAccount?.mockResolvedValue(entry);
                entryCoreServiceMock.saveEntry?.mockImplementation(input => input);
            });

            it('Should return an entry containing the account', async () => {
                const result =
                    await siteAdminService.validateAccountAndSupportUserEntryMapping(account);
                expect(result.account(account.id)).toEqual(account);
            });
        });

        describe('Given an account that is mapped to the entry of the support user', () => {
            let account: Account;
            let entry: Entry;

            beforeEach(() => {
                account = new Account();
                account.id = 'account-id';
                entry = new Entry();
                entry.accounts = [account];
                entryCoreServiceMock.getEntryByEmailNoFail?.mockResolvedValue(entry);
                entryCoreServiceMock.getEntryByEmailWithAccount?.mockResolvedValue(entry);
                entryCoreServiceMock.saveEntry?.mockImplementation(input => input);
            });

            it('Should return an entry containing the account', async () => {
                const result =
                    await siteAdminService.validateAccountAndSupportUserEntryMapping(account);
                expect(result.account(account.id)).toEqual(account);
            });
        });

        describe('Given an account but no entry was found for the support user', () => {
            let account: Account;
            let entry: Entry;

            beforeEach(() => {
                account = new Account();
                account.id = 'account-id';
                entry = new Entry();
                entry.accounts = [account];
                entryCoreServiceMock.getEntryByEmailNoFail?.mockResolvedValue(null);
                entryCoreServiceMock.createEntryByEmail?.mockResolvedValue(entry);
                entryCoreServiceMock.saveEntry?.mockImplementation(input => input);
            });

            it('Should return an entry containing the account', async () => {
                const result =
                    await siteAdminService.validateAccountAndSupportUserEntryMapping(account);
                expect(result.account(account.id)).toEqual(account);
            });
        });

        describe('Given an account but no entry was found for the support user and the system failed to create the entry', () => {
            let account: Account;

            beforeEach(() => {
                account = new Account();
                account.id = 'account-id';

                entryCoreServiceMock.getEntryByEmailNoFail?.mockResolvedValue(null);
                entryCoreServiceMock.getEntryByEmailNoFailV2?.mockResolvedValue(null);
                entryCoreServiceMock.createEntryByEmail?.mockResolvedValue(null);
                entryCoreServiceMock.getEntryByEmailWithAccount?.mockResolvedValue(null);
                entryCoreServiceMock.saveEntry?.mockImplementation(input => input);
            });

            it('Should throw an internal server error', async () => {
                await expect(
                    siteAdminService.validateAccountAndSupportUserEntryMapping(account),
                ).rejects.toThrow(InternalServerErrorException);
            });
        });
    });
});
