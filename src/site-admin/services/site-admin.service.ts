/* eslint-disable no-await-in-loop */
import {
    AuthModes,
    ErrorCode,
    FrameworkTag,
    Language,
    SafeBaseMigrationStatus,
    SafeBaseMigrationType,
} from '@drata/enums';
import {
    ConflictException,
    ForbiddenException,
    Injectable,
    InternalServerErrorException,
    NotFoundException,
    PreconditionFailedException,
    UnauthorizedException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { AutopilotScheduleInstanceCoreService } from 'app/autopilot/services/autopilot-schedule-instance-core.service';
import { ConnectionsRepository } from 'app/companies/connections/repositories/connections.repository';
import { ConnectionsCoreService } from 'app/companies/connections/services/connections-core.service';
import { AdminOnboarding } from 'app/companies/entities/admin-onboarding.entity';
import { Company } from 'app/companies/entities/company.entity';
import { AdminOnboardingOrchestrationService } from 'app/companies/services/admin-onboarding-orchestration.service';
import { CompaniesCoreService } from 'app/companies/services/companies-core.service';
import { FrameworkEnabledEvent } from 'app/events/observables/events/framework-enabled.event';
import { FeatureService } from 'app/feature-toggling/services/feature.service';
import { Framework } from 'app/frameworks/entities/framework.entity';
import { FrameworkService } from 'app/frameworks/services/framework.service';
import { CompanyInfo } from 'app/trust-center/entities/company-info.entity';
import { UserIdentity } from 'app/users/entities/user-identity.entity';
import { User } from 'app/users/entities/user.entity';
import { UserRoleCreatedEvent } from 'app/users/observables/events/user-role-created.event';
import { PersonnelOrchestrationService } from 'app/users/personnel/orchestration/personnel/personnel-orchestration.service';
import { ComplianceChecksOrchestrationService } from 'app/users/personnel/services/compliance-checks-orchestration.service';
import { PersonnelCoreService } from 'app/users/personnel/services/personnel-core.service';
import { PolicyRepository } from 'app/users/policies/repositories/policy.repository';
import { UserIdentityRepository } from 'app/users/repositories/user-identity-repository';
import { UserRepository } from 'app/users/repositories/user.repository';
import { SupportUserOrchestrationService } from 'app/users/services/support-user-orchestration.service';
import { UsersCoreService } from 'app/users/services/users-core.service';
import { BaseUserDataType } from 'app/users/types/base-user-data.type';
import { frameworkDeactivationPolicyVersionWorkflowV1 } from 'app/worker/workflows/framework-deactivation/framework-deactivation-policy-version.v1.workflow';
import { AuditorClientRepository } from 'auditors/repositories/auditor-client.repository';
import { AccountSafeBaseSettings } from 'auth/entities/account-safebase-settings.entity';
import { Account } from 'auth/entities/account.entity';
import { Entry } from 'auth/entities/entry.entity';
import { updateDatabaseName } from 'auth/helpers/auth.sql.helper';
import { AccountsCoreService } from 'auth/services/accounts-core.service';
import { EntryCoreService } from 'auth/services/entry-core.service';
import { TenantRouterService } from 'auth/services/tenant-router.service';
import { AccountCheckStatusType } from 'auth/types/account-check-status.type';
import { AccountInfoType } from 'auth/types/account-info-type';
import { AccountTrustCenterCnameType } from 'auth/types/account-trust-center-cname.type';
import { AccountEntriesCacheBuster } from 'cache/account-entries-cache-buster.decorator';
import { SuperCacheAccountBuster } from 'cache/super-cache.decorator';
import { PolloAdapter } from 'commons/adapters/pollo.adapter';
import { getProvisionFrameworkLockKey } from 'commons/constants/cache-lock-keys.constants';
import { AccountEntitlementType } from 'commons/enums/account-entitlement-type.enum';
import { ActAsType } from 'commons/enums/act-as-type.enum';
import { AdminOnboardingStepType } from 'commons/enums/admin-onboarding-step-type.enum';
import { AccountStatus } from 'commons/enums/auth/account-status.enum';
import { ClientType } from 'commons/enums/auth/client-type.enum';
import { ProviderType } from 'commons/enums/auth/provider-type.enum';
import { TenantRouterCategory } from 'commons/enums/auth/tenant-router-category.enum';
import { TokenType } from 'commons/enums/auth/token-type.enum';
import { Caches } from 'commons/enums/cache.enum';
import { EmploymentStatusOptionsFilter } from 'commons/enums/personnel/employment-status-options-filter.enum';
import { AdminRole } from 'commons/enums/site-admin/admin-role.enum';
import { AuditLogEventType } from 'commons/enums/site-admin/audit-log-event-type.enum';
import { AuditLogTargetType } from 'commons/enums/site-admin/audit-log-target-type.enum';
import { AccessLengthType } from 'commons/enums/trust-center/access-length-type.enum';
import { Role } from 'commons/enums/users/role.enum';
import { ConflictException as OurConflictException } from 'commons/exceptions/conflict.exception';
import { ForbiddenException as OurForbiddenException } from 'commons/exceptions/forbidden.exception';
import { PreconditionFailedException as OurPreconditionFailedException } from 'commons/exceptions/precondition-failed.exception';
import { ValidationException } from 'commons/exceptions/validation.exception';
import { ConnectionFactory } from 'commons/factories/connection.factory';
import { asyncForEach } from 'commons/helpers/array.helper';
import { fqtn, like } from 'commons/helpers/database.helper';
import { hasExpired } from 'commons/helpers/date.helper';
import { getDomainFromEmail, toNewDomainEmail } from 'commons/helpers/domain.helper';
import { getLanguage } from 'commons/helpers/language.helper';
import { EmploymentStatusOptionsFilterMap } from 'commons/helpers/personnel.helper';
import { isMultiProductEnabled } from 'commons/helpers/products.helper';
import { getTemporalClient } from 'commons/helpers/temporal/client';
import { isTemporalEnabled } from 'commons/helpers/temporal/client-config.helper';
import { hasRole } from 'commons/helpers/user.helper';
import { AppService } from 'commons/services/app.service';
import { AccountIdType } from 'commons/types/account-id.type';
import config from 'config';
import { ToggleAccountEntitlementRequestDto } from 'entitlements/dtos/toggle-entitlement-request.dto';
import { AccountAdaptiveAutomationMetadata } from 'entitlements/entities/account-adaptive-automation-metadata.entity';
import { AccountEntitlementService } from 'entitlements/entitlements.service';
import { AdaptiveAutomationMetadataService } from 'entitlements/services/adaptive-automation-metadata.service';
import { TrustCenterEntitlementService } from 'entitlements/trust-center/services/trust-center-entitlement.service';
import { AccountEntitlementCompactType } from 'entitlements/types/account-entitlement-compact.type';
import { isFeatureFlagOn } from 'feature-flags/feature-flag.helper';
import { FeatureFlagService } from 'feature-flags/feature-flag.service';
import { cloneDeep, concat, difference, get, isEmpty, isNil, isUndefined, uniqBy } from 'lodash';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { SelfServiceInviteService } from 'self-service-invite/services/self-service-invite.service';
import { ServiceUserClientEntity } from 'service-user/entities/service-user-client.entity';
import { InviteWelcomeServiceProviderEvent } from 'service-user/observables/events/invite-welcome-service-provider.event';
import { ServiceUserClientRepository } from 'service-user/repositories/service-user-client.repository';
import { ServiceUsersCoreService } from 'service-user/service-users-core.service';
import { ServiceUsersOrchestrationService } from 'service-user/service-users-orchestration.service';
import { AccountEntitlementSettingsRequestDto } from 'site-admin/dtos/account-entitlement-settings-request.dto';
import { AccountFrameworksRequestDto } from 'site-admin/dtos/account-frameworks-request.dto';
import { AccountInfoUpdateRequestDto } from 'site-admin/dtos/account-info-update-request.dto';
import { AccountRequestDto } from 'site-admin/dtos/account-request.dto';
import { AccountTrustCenterCnameRequestDto } from 'site-admin/dtos/account-trust-center-cname.request.dto';
import { ActAsRequestDto } from 'site-admin/dtos/act-as-request.dto';
import { SiteAdmin } from 'site-admin/entities/site-admin.entity';
import { AccountEntitlementToggledEvent } from 'site-admin/observables/events/account-entitlement-toggled.event';
import { AccountFrameworkUpdatedEvent } from 'site-admin/observables/events/account-framework-updated.event';
import { AccountInfoUpdatedEvent } from 'site-admin/observables/events/account-info-updated.event';
import { AccountReadyEvent } from 'site-admin/observables/events/account-ready.event';
import { AccountUserUpdatedEvent } from 'site-admin/observables/events/account-user-updated.event';
import { AuditLogEvent } from 'site-admin/observables/events/audit-log.event';
import { ServiceGroupRepository } from 'site-admin/repositories/service-group.repository';
import { SiteAdminAccountLimitsService } from 'site-admin/services/site-admin-account-limits.service';
import { SiteAdminServiceGroupService } from 'site-admin/services/site-admin-service-group.service';
import { SiteAdminServiceUsersService } from 'site-admin/services/site-admin-service-users.service';
import {
    FRAMEWORKS_WITHOUT_CONTROLS,
    ONBOARDING_TABLES_TO_VALIDATE,
    POLICY_MULTI_APPROVERS_FF,
    TABLES_EXCEPTION_WITHOUT_CONTROLS,
    TABLES_EXCEPTION_WITHOUT_FRAMEWORKS,
} from 'site-admin/site-admin.constants';
import { ExternalUsers } from 'site-admin/types/externalUsers.type';
import { ToggleResult } from 'site-admin/types/toggle-result.type';
import { In, IsNull, Repository } from 'typeorm';

@Injectable()
export class SiteAdminService extends AppService {
    constructor(
        @InjectRepository(AccountSafeBaseSettings)
        private readonly accountSafeBaseSettingsRepository: Repository<AccountSafeBaseSettings>,
        private readonly featureService: FeatureService,
        private readonly entryCoreService: EntryCoreService,
        private readonly accountEntitlementService: AccountEntitlementService,
        private readonly trustCenterFeatureService: TrustCenterEntitlementService,
        private readonly accountsCoreService: AccountsCoreService,
        private readonly auditorClientRepository: AuditorClientRepository,
        private readonly serviceUserClientRepository: ServiceUserClientRepository,
        private readonly serviceGroupRepository: ServiceGroupRepository,
        private readonly siteAdminServiceGroupService: SiteAdminServiceGroupService,
        private readonly siteAdminServiceUsersService: SiteAdminServiceUsersService,
        private readonly serviceUsersCoreService: ServiceUsersCoreService,
        private readonly tenantRouterService: TenantRouterService,
        private readonly selfServiceService: SelfServiceInviteService,
        private readonly adaptiveAutomationMetadataService: AdaptiveAutomationMetadataService,
        private readonly siteAdminAccountLimitsService: SiteAdminAccountLimitsService,
        private readonly usersCoreService: UsersCoreService,
        private readonly frameworkService: FrameworkService,
        private readonly companiesCoreService: CompaniesCoreService,
        private readonly personnelCoreService: PersonnelCoreService,
        private readonly personnelOrchestrationService: PersonnelOrchestrationService,
        private readonly autopilotScheduleInstanceCoreService: AutopilotScheduleInstanceCoreService,
        private readonly connectionsCoreService: ConnectionsCoreService,
        private readonly complianceChecksOrchestrationService: ComplianceChecksOrchestrationService,
        private readonly adminOnboardingOrchestrationService: AdminOnboardingOrchestrationService,
        private readonly featureFlagService: FeatureFlagService,
        private readonly supportUserOrchestrationService: SupportUserOrchestrationService,
        private readonly serviceUsersOrchestrationService: ServiceUsersOrchestrationService,
    ) {
        super();
    }

    async getAccountInfo(id: AccountIdType, includeDeleted?: boolean): Promise<AccountInfoType> {
        // get the account
        const account = await this.accountsCoreService.getAccountWithEntitlementSettingsById(id);

        if (isNil(account) || isEmpty(account)) {
            throw new NotFoundException();
        }

        const accountAdaptiveAutomationMetadata =
            await this.getAccountAdaptiveAutomationMetadata(account);
        // get the hasMultiDomain value
        const company = await this.companiesCoreService.getCompanyWithFrameworksByAccountId(
            account.id,
        );
        const hasMultiDomain = !isNil(company.multiDomain);

        // Clean up workspaces by filtering out null frameworks
        const workspaces = company.products || [];
        for (const workspace of workspaces) {
            if (workspace.frameworks) {
                workspace.frameworks = workspace.frameworks.filter(
                    framework => !isNil(framework) && !isNil(framework.framework),
                );
            }
        }

        // get the admins
        const admins = await this.usersCoreService.getAdminUsers(true, includeDeleted);

        // get the frameworks
        const allFrameworks = await this.frameworkService.getAllDrataFrameworks();
        const frameworks = uniqBy(allFrameworks, 'tag');

        // get the cname
        const trustCenter = await this.trustCenterFeatureService.getTrustCenterByAccount(
            account.id,
        );
        const tenantRouter = await this.tenantRouterService.getRouteWithAccountAndCategory(
            account,
            TenantRouterCategory.TRUST_CENTER,
        );
        const trustId = tenantRouter.key;

        let invitedBy: string | null = null;
        const selfServiceAccountCreationInvite =
            await this.selfServiceService.getSelfServiceAccountCreationInviteByAccountId(
                account.id,
                true,
            );

        if (!isNil(selfServiceAccountCreationInvite?.siteAdmin)) {
            const { siteAdmin: { firstName, lastName } = {} } = selfServiceAccountCreationInvite;
            invitedBy = `${firstName} ${lastName}`;
        }

        const type = account.type;
        // return the data here

        return {
            account,
            frameworks,
            hasMultiDomain,
            type,
            admins,
            trustId,
            invitedBy,
            workspaces,
            trustCenter,
            accountAdaptiveAutomationMetadata,
        };
    }

    getAccount(id: AccountIdType): Promise<Account> {
        return this.accountsCoreService.getAccountById(id);
    }

    /**
     *
     * @param id
     * @param requestDto
     * @param siteAdmin
     * @returns
     *
     */
    async getSupportUserAndTokenType(
        account: Account,
        requestDto: ActAsRequestDto,
        siteAdmin: SiteAdmin,
    ): Promise<{ supportUser: User; tokenType: TokenType; account: Account }> {
        const { actAsType } = requestDto;

        const tokenType = await this.validateAndGetTokenType(account, actAsType, siteAdmin);

        const supportUser =
            await this.supportUserOrchestrationService.getOrCreateSupportUser(account);

        await this._cacheService.del(`${config.get('cache.globalPrefix')}:entry:${account.id}`);
        await this._cacheService.del(`${config.get('cache.globalPrefix')}:entry:${supportUser.id}`);

        return { supportUser, tokenType, account };
    }

    async validateAccountAndSupportUserEntryMapping(account: Account): Promise<Entry> {
        const email = this.usersCoreService.getSupportUserEmail();

        let entry = await this.entryCoreService.getEntryByEmailWithAccount(email, account.id);

        if (isNil(entry)) {
            this.log('No entry found for support user, creating entry', account);
            entry = await this.entryCoreService.createEntryByEmail(email, account);
            this.log('Support user entry created', account);
            if (!entry) {
                throw new InternalServerErrorException('No entry was returned');
            }
        } else if (isNil(entry.account(account.id))) {
            this.log(
                'An entry is found for support user but the account is not mapped. Mapping the account',
                account,
            );

            entry.setEntryAccount(account);
            await this.entryCoreService.saveEntry(entry);
        }
        return entry;
    }

    async toggleAccountTraining(
        account: Account,
        company: Company,
        firstAdmin: User,
        enabledFrameworkTags: FrameworkTag[],
        onboardingStep: AdminOnboarding,
        frameworkTags: FrameworkTag[], // Accepts an array of framework tags
        adminOnboardedCompleted: boolean,
        stepType: AdminOnboardingStepType,
    ): Promise<void> {
        for (const frameworkTag of frameworkTags) {
            if (isNil(onboardingStep)) {
                if (enabledFrameworkTags.includes(frameworkTag)) {
                    const step = new AdminOnboarding();
                    step.stepType = stepType;
                    step.company = company;
                    await this.adminOnboardingRepository.save(step);

                    if (adminOnboardedCompleted) {
                        company.adminOnboardedAt = null;
                        await this.companyRepository.save(company);
                    }
                }
            } else {
                if (
                    enabledFrameworkTags.includes(frameworkTag) &&
                    !isNil(onboardingStep.deletedAt)
                ) {
                    Object.assign(onboardingStep, { deletedAt: null });
                    await this.adminOnboardingRepository.save(onboardingStep);

                    if (adminOnboardedCompleted) {
                        company.adminOnboardedAt = null;
                        await this.companyRepository.save(company);
                    }
                } else if (
                    !enabledFrameworkTags.includes(frameworkTag) &&
                    isNil(onboardingStep.deletedAt)
                ) {
                    onboardingStep.deletedAt = new Date();
                    await this.adminOnboardingRepository.save(onboardingStep);

                    try {
                        await this.adminOnboardingOrchestrationService.finishOnboarding(
                            account,
                            firstAdmin,
                        );
                    } catch (e) {
                        this.logger.log(
                            PolloAdapter.acct(
                                'Admin Onboarding failed to complete, additional steps need to be completed',
                                account,
                            )
                                .setContext(this.constructor.name)
                                .setSubContext(this.toggleAccountTraining.name)
                                .setError(e),
                        );
                    }
                }
            }
        }
    }

    private async getAccountAdaptiveAutomationMetadata(
        account: Account,
    ): Promise<AccountAdaptiveAutomationMetadata | undefined> {
        let metadata: AccountAdaptiveAutomationMetadata | undefined;
        try {
            metadata = await this.adaptiveAutomationMetadataService.getMetadata(account);
            return metadata;
        } catch (e) {
            // I added this so it does not fail while migrations are still running
            this.warn(`Could not found metadata: ${e.message}`, account);
        }
    }

    /**
     *
     * @param currentAdmin
     * @param dto
     * @param id
     */
    async updateAccount(
        currentAdmin: SiteAdmin,
        dto: AccountRequestDto,
        accountId: AccountIdType,
    ): Promise<AccountInfoType> {
        const lockKey = getProvisionFrameworkLockKey(accountId);
        const isLocked = await this._cacheService.isLocked(lockKey);

        if (isLocked) {
            throw new OurConflictException(
                'Cannot update account, framework provisioning in progress',
                ErrorCode.FRAMEWORKS_NOT_PROVISIONED,
            );
        }

        await this.siteAdminAccountLimitsService.updateLimitsSetting(currentAdmin, accountId, {
            workspaceLimit: dto.workspaceLimit,
            customFrameworkLimit: dto.customFrameworkLimit,
            securityQuestionnairesLimit: dto.securityQuestionnairesLimit,
        });

        const account = await this.updateAccountInfo(currentAdmin, accountId, dto);

        /*
            If logic to create the new admin changes make sure to validate if the changes are necessary to
            add in the `createNewAdminUser` function from the `user-admin.service.ts` file.
            This will be refactored in the ticket ENG-29501
        */
        if (!isEmpty(dto.newAdmins)) {
            for (const newAdmin of dto.newAdmins) {
                await this.addNewAdmin(currentAdmin, accountId, newAdmin);
            }
        }

        await this.updateEntitlementSettings(currentAdmin, accountId, dto);

        const frameworks = await this.updateFrameworks(currentAdmin, accountId, {
            enableFrameworkTags: dto.enableFrameworkTags || [],
            enabledControlsForFrameworkTags: dto.enabledControlsForFrameworkTags || [],
        });

        await this.updateTrustCenterCname(currentAdmin, accountId, dto);

        const company = await this.companiesCoreService.getCompanyByAccountId(accountId);

        return {
            ...dto,
            workspaces: company.products,
            account,
            frameworks,
        };
    }

    async addNewAdmin(
        currentAdmin: SiteAdmin,
        accountId: AccountIdType,
        newAdmin: BaseUserDataType,
    ): Promise<void> {
        const originalAccount =
            await this.accountsCoreService.getAccountWithEntitlementSettingsById(accountId);

        if (isNil(originalAccount)) {
            throw new NotFoundException('Account was not found');
        }

        const lockKey = getProvisionFrameworkLockKey(accountId);
        const isLocked = await this._cacheService.isLocked(lockKey);

        if (isLocked) {
            throw new OurConflictException(
                'Cannot update account, framework provisioning in progress',
                ErrorCode.FRAMEWORKS_NOT_PROVISIONED,
            );
        }

        const company = await this.companiesCoreService.getCompanyByAccountId(accountId);

        originalAccount.entries = await this.entryCoreService.getEntriesForAccount(originalAccount);

        if (
            isNil(company.multiDomain) &&
            getDomainFromEmail(newAdmin.email) !== originalAccount.domain
        ) {
            throw new ValidationException(
                'Could not create a new admin. Multi domain is not enabled and domains do not match',
            );
        }

        const existingUser = await this.usersCoreService.getUserByEmailNoFail(newAdmin.email);

        if (!isNil(existingUser)) {
            // modifying existing user means we need to bust cache entry for that user
            await this._cacheService.del(`globalstore:entry:${existingUser.id}`);
            await this._cacheService.del(`globalstore:account:${originalAccount.id}`);
            await this.usersCoreService.addRole(originalAccount, existingUser, Role[Role.ADMIN]);

            // Send admin email notification
            await this.usersCoreService.sendAddedAsAdminOrTechGovEmail(
                originalAccount,
                existingUser,
                Role.ADMIN,
            );

            // Update compliance in background
            this.updateCompliance(originalAccount).catch(error => {
                this.error(
                    new Error('Unhandled promise rejection in `SiteAdminService.addNewAdmin`'),
                    originalAccount,
                    { error },
                );
            });

            // Publish audit log event
            this._eventBus.publish(
                new AuditLogEvent(
                    currentAdmin,
                    AuditLogEventType.ACCOUNT_ADMIN_ADDED,
                    AuditLogTargetType.ACCOUNT,
                    existingUser.entryId,
                    existingUser,
                ),
            );

            return;
        }

        const entry = await this.personnelOrchestrationService.createAdminEntryByEmail(
            newAdmin,
            originalAccount,
        );
        const user = await this.usersCoreService.createAdminUser(newAdmin, entry);

        await this.featureService.createUserFeatures(originalAccount, user);
        originalAccount.entries = [...originalAccount.entries, entry];

        await this.usersCoreService.sendAddedAsAdminOrTechGovEmail(
            originalAccount,
            user,
            Role.ADMIN,
        );

        // no await so it can compute in the background
        this.updateCompliance(originalAccount).catch(error => {
            this.error(
                new Error('Unhandled promise rejection in `SiteAdminService.updateAccount`'),
                originalAccount,
                { error },
            );
        });

        this._eventBus.publish(
            new AuditLogEvent(
                currentAdmin,
                AuditLogEventType.ACCOUNT_ADMIN_ADDED,
                AuditLogTargetType.ACCOUNT,
                entry.id,
                entry,
            ),
        );

        // need to pass in valid support user id when generating event for image render
        const supportUser =
            await this.supportUserOrchestrationService.getOrCreateSupportUser(originalAccount);

        await this._cacheService.del(
            `${config.get('cache.globalPrefix')}:entry:${originalAccount.id}`,
        );
        await this._cacheService.del(`${config.get('cache.globalPrefix')}:entry:${supportUser.id}`);

        this._eventBus.publish(
            new UserRoleCreatedEvent(originalAccount, supportUser, user, Role[Role.ADMIN]),
        );

        const firstAdmin = await this.usersCoreService.getUserByIdNoFail(
            config.get('api.firstAdmin'),
        );
        const frameworks = await this.frameworkService.getAllFrameworks();
        const multipleProductsEnabled = isMultiProductEnabled(originalAccount);

        this._eventBus.publish(
            new AccountUserUpdatedEvent(
                currentAdmin,
                originalAccount,
                user,
                frameworks,
                company,
                firstAdmin,
                multipleProductsEnabled,
            ),
        );

        // bust the cache for all data potentially modified by this function
        await this._cacheService.del(`globalstore:entry:${firstAdmin.id}`);
        await this._cacheService.del(`globalstore:entry:${currentAdmin.id}`);
        await this._cacheService.del(`globalstore:entry:${entry.id}`);
        await this._cacheService.del(`globalstore:entry:${entry.id},${originalAccount.id}`);
        await this._cacheService.del(`globalstore:account:${user.id}`);
        await this._cacheService.del(`globalstore:entry:${originalAccount.id}`);
        await this._cacheService.del(`globalstore:entry:${supportUser.id}`);
        await this._cacheService.del(
            `globalstore:entry:${accountId}:${Caches.USER_BY_ENTRY_ID}:${entry.id}`,
        );
    }

    async updateFrameworks(
        currentAdmin: SiteAdmin,
        accountId: AccountIdType,
        dto: AccountFrameworksRequestDto,
    ): Promise<Framework[]> {
        const lockKey = getProvisionFrameworkLockKey(accountId);
        try {
            const lockAcquired = await this._cacheService.acquireLock(
                lockKey,
                config.get('cache.ttl.10min'),
            );

            if (!lockAcquired) {
                throw new OurConflictException(
                    'Cannot update account, framework provisioning in progress',
                    ErrorCode.FRAMEWORKS_NOT_PROVISIONED,
                );
            }

            const originalAccount = await this.accountsCoreService.getAccountById(accountId);
            const originalAccountData = { ...originalAccount };

            // this is just used for audit log for now
            const allFrameworksBeforeUpdates = await this.frameworkService.getAllFrameworks();

            const firstAdmin = await this.usersCoreService.getUserByIdNoFail(
                config.get('api.firstAdmin'),
            );
            const supportUser = await this.usersCoreService.getUserByEmail(
                config.get('api.supportUser.email'),
            );

            const frameworks = await this.frameworkService.updateEnabledAt(
                dto,
                isMultiProductEnabled(originalAccount),
                originalAccount,
                supportUser,
            );
            const company = await this.companiesCoreService.getCompanyByAccountId(
                originalAccount.id,
            );

            const frameworkTags = frameworks.map(framework => framework.tag);
            const frameworkTagsToProvision = dto.enableFrameworkTags?.filter(
                tag => !frameworkTags.includes(tag),
            );
            const enabledControlsForFrameworkTags = dto.enabledControlsForFrameworkTags?.filter(
                tag => !frameworkTags.includes(tag),
            );
            if (!isNil(frameworkTagsToProvision)) {
                await this.frameworkService.provisionNewFrameworks(
                    frameworkTagsToProvision,
                    enabledControlsForFrameworkTags || [],
                    originalAccount,
                    firstAdmin,
                );
                for (const tag of frameworkTagsToProvision) {
                    if (tag != FrameworkTag.NONE) {
                        this._eventBus.publish(
                            new FrameworkEnabledEvent(originalAccount, tag, supportUser),
                        );
                    }
                }
            }

            const enabledFrameworkTags: FrameworkTag[] | null = get(dto, 'enableFrameworkTags', []);
            let allEnabledFrameworksTags: FrameworkTag[] = [];
            let disabledFrameworksTags: FrameworkTag[] = [];

            frameworks.forEach(framework => {
                if (!enabledFrameworkTags?.includes(framework.tag) && isNil(framework.enabledAt)) {
                    disabledFrameworksTags?.push(framework.tag);
                } else if (!isNil(framework.enabledAt)) {
                    allEnabledFrameworksTags.push(framework.tag);
                }
            });

            allEnabledFrameworksTags = concat(
                allEnabledFrameworksTags,
                frameworkTagsToProvision || [],
            );
            disabledFrameworksTags = difference(disabledFrameworksTags, allEnabledFrameworksTags);

            const onboardingSteps = await this.adminOnboardingRepository.find({
                where: {
                    stepType: In([
                        AdminOnboardingStepType.HIPAA_TRAINING,
                        AdminOnboardingStepType.NIST_AI_TRAINING,
                    ]),
                },
                withDeleted: true,
            });

            await this.frameworkService.sendAuditLog(
                currentAdmin,
                originalAccount,
                allFrameworksBeforeUpdates,
            );

            const adminOnboardedCompleted = !isNil(company.adminOnboardedAt);
            const hipaaOnboardingStep = onboardingSteps.find(
                step => step.stepType === AdminOnboardingStepType.HIPAA_TRAINING,
            );
            const nistAiOnboardingStep = onboardingSteps.find(
                step => step.stepType === AdminOnboardingStepType.NIST_AI_TRAINING,
            );

            if (!isNil(hipaaOnboardingStep)) {
                await this.toggleAccountTraining(
                    originalAccount,
                    company,
                    firstAdmin,
                    enabledFrameworkTags || [],
                    hipaaOnboardingStep,
                    [FrameworkTag.HIPAA],
                    adminOnboardedCompleted,
                    AdminOnboardingStepType.HIPAA_TRAINING,
                );
            }

            if (!isNil(nistAiOnboardingStep)) {
                await this.toggleAccountTraining(
                    originalAccount,
                    company,
                    firstAdmin,
                    enabledFrameworkTags || [],
                    nistAiOnboardingStep,
                    [FrameworkTag.NISTAI, FrameworkTag.ISO420012023],
                    adminOnboardedCompleted,
                    AdminOnboardingStepType.NIST_AI_TRAINING,
                );
            }

            originalAccountData.entries =
                await this.entryCoreService.getEntriesForAccount(originalAccount);

            this._eventBus.publish(
                new AccountFrameworkUpdatedEvent(
                    currentAdmin,
                    originalAccount,
                    allEnabledFrameworksTags,
                    disabledFrameworksTags,
                    frameworks,
                ),
            );

            const isPolicyMultiApproversEnabled = await isFeatureFlagOn({
                account: originalAccount,
                featureFlagService: this.featureFlagService,
                context: this.updateFrameworks.name,
                logger: this.logger,
                featureFlag: POLICY_MULTI_APPROVERS_FF,
            });

            if (isPolicyMultiApproversEnabled && isTemporalEnabled()) {
                const temporalClient = await getTemporalClient(originalAccount.domain);
                await temporalClient.startWorkflow(frameworkDeactivationPolicyVersionWorkflowV1, {
                    taskQueue: config.get('temporal.taskQueues.temporal-default'),
                    args: [
                        {
                            account: originalAccount,
                            user: supportUser,
                        },
                    ],
                    memo: { accountId: originalAccount.id, domain: originalAccount.domain },
                });
            }
            return frameworks;
        } finally {
            await this._cacheService.releaseLock(lockKey);
        }
    }

    async updateTrustCenterCname(
        currentAdmin: SiteAdmin,
        accountId: AccountIdType,
        dto: AccountTrustCenterCnameRequestDto,
    ): Promise<AccountTrustCenterCnameType> {
        const account = await this.accountsCoreService.getAccountById(accountId);
        const trustCenter = await this.trustCenterFeatureService.saveCnameByWorkspaceId(
            currentAdmin,
            account,
            dto,
        );

        if (isNil(trustCenter)) {
            const companyInfo = await this.companyInfoRepository.findOneBy({});
            if (!isNil(companyInfo)) {
                companyInfo.defaultAccessLength = config.get('trustCenter.defaultAccessLength');
                companyInfo.accessLengthType = AccessLengthType.DAYS;
                await this.companyInfoRepository.save(companyInfo);
            }
        }

        return dto;
    }

    async updateAccountInfo(
        currentAdmin: SiteAdmin,
        id: AccountIdType,
        dto: AccountInfoUpdateRequestDto,
    ): Promise<Account> {
        const originalAccount =
            await this.accountsCoreService.getAccountWithEntitlementSettingsById(id);
        if (isNil(originalAccount)) {
            throw new NotFoundException('Account was not found');
        }
        /**
         * Pass in the account as a typed object to trigger proper entries account busting
         */
        return this._updateAccountInfo(originalAccount, currentAdmin, dto);
    }

    @AccountEntriesCacheBuster()
    private async _updateAccountInfo(
        originalAccount: Account,
        currentAdmin: SiteAdmin,
        dto: AccountInfoUpdateRequestDto,
    ): Promise<Account> {
        const clonedAccount = cloneDeep(originalAccount);
        const originalAccountData = { ...originalAccount };
        // check if the account is in a state to update based on business rules
        this.checkUpdateStatus(currentAdmin, originalAccount, dto);

        originalAccount.status = dto.status;
        originalAccount.crmCompanyId = dto.crmCompanyId;
        originalAccount.language = dto.language ?? Language.ENGLISH_US;
        originalAccount.type = dto.type;
        originalAccount.otherReason = dto.otherReason;
        // TODO: ENG-73592
        originalAccount.safeBaseMigrationStatus = dto.safeBaseMigrationStatus;
        originalAccount.safeBaseOrgId = dto.safeBaseOrgId;

        // Update SafeBase settings in the AccountSafeBaseSettings table
        if (originalAccount.safeBaseSettings) {
            originalAccount.safeBaseSettings.migrationStatus = dto.safeBaseMigrationStatus;
            originalAccount.safeBaseSettings.orgId = dto.safeBaseOrgId;

            // if safeBaseSettings is provided, it will take precedence over the legacy fields
            if (!isNil(dto.safeBaseSettings?.migrationType)) {
                originalAccount.safeBaseSettings.migrationType = dto.safeBaseSettings
                    .migrationType as SafeBaseMigrationType;
            }
            if (!isNil(dto.safeBaseSettings?.migrationStatus)) {
                originalAccount.safeBaseSettings.migrationStatus = dto.safeBaseSettings
                    .migrationStatus as SafeBaseMigrationStatus;
                // TODO: ENG-73592
                originalAccount.safeBaseMigrationStatus = dto.safeBaseSettings
                    .migrationStatus as SafeBaseMigrationStatus;
            }
            if (!isNil(dto.safeBaseSettings?.orgId)) {
                originalAccount.safeBaseSettings.orgId = dto.safeBaseSettings.orgId;
                // TODO: ENG-73592
                originalAccount.safeBaseOrgId = dto.safeBaseSettings.orgId;
            }
            if (!isNil(dto.safeBaseSettings?.showDrataTrustCenter)) {
                originalAccount.safeBaseSettings.showDrataTrustCenter =
                    dto.safeBaseSettings.showDrataTrustCenter;
            }
            if (!isNil(dto.safeBaseSettings?.showSsoLinks)) {
                originalAccount.safeBaseSettings.showSsoLinks = dto.safeBaseSettings.showSsoLinks;
            }
            await this.accountSafeBaseSettingsRepository.save(originalAccount.safeBaseSettings);
        }

        if (!isUndefined(dto.pocExpiresAt)) {
            originalAccount.pocExpiresAt = dto.pocExpiresAt ?? null;
        }

        if (!isNil(dto.hasMultiDomain)) {
            await this.companiesCoreService.updateCompanyMultiDomain(
                originalAccount.id,
                dto.hasMultiDomain,
            );
        }

        // save the account and return the promise here
        const updatedAccount = await this.accountsCoreService.save(originalAccount);

        this._eventBus.publish(
            new AccountInfoUpdatedEvent(originalAccountData, originalAccount, currentAdmin),
        );

        this._eventBus.publish(
            new AuditLogEvent(
                currentAdmin,
                AuditLogEventType.ACCOUNT_UPDATED,
                AuditLogTargetType.ACCOUNT,
                updatedAccount.id,
                undefined, // target
                clonedAccount,
                updatedAccount,
            ),
        );

        return updatedAccount;
    }

    async updateEntitlementSettings(
        currentAdmin: SiteAdmin,
        accountId: AccountIdType,
        dto: AccountEntitlementSettingsRequestDto,
    ) {
        await asyncForEach(
            get(dto, 'entitlements', []),
            async (entitlement: AccountEntitlementCompactType) => {
                if (!isNil(get(entitlement, 'metadata', null))) {
                    const account: Account =
                        await this.accountsCoreService.getAccountById(accountId);
                    try {
                        const metadataBefore =
                            await this.adaptiveAutomationMetadataService.getMetadata(account);

                        const metadataUpdated = await this.adaptiveAutomationMetadataService.save(
                            account,
                            {
                                allotment: Number(get(entitlement, 'metadata.limit', 0)),
                            },
                        );

                        if (metadataBefore.allotment !== metadataUpdated.allotment) {
                            this._eventBus.publish(
                                new AuditLogEvent(
                                    currentAdmin,
                                    AuditLogEventType.ACCOUNT_ENTITLEMENT_SETTINGS_UPDATED,
                                    AuditLogTargetType.ACCOUNT,
                                    account.id,
                                    undefined, // target
                                    {
                                        entitlementSetting: `${entitlement.name}: ${metadataBefore.allotment}`,
                                    },
                                    {
                                        entitlementSetting: `${entitlement.name}: ${metadataUpdated.allotment}`,
                                    },
                                ),
                            );
                        }
                    } catch (e) {
                        this.logger.error(
                            PolloAdapter.acct(
                                `Account could not update ${
                                    AccountEntitlementType[AccountEntitlementType[entitlement.type]]
                                } metadata`,
                                account,
                            )
                                .setContext(this.constructor.name)
                                .setSubContext(this.updateEntitlementSettings.name)
                                .setError(e),
                        );
                    }
                }
            },
        );
    }

    /**
     * @param account
     * @param tablesToVerify
     */
    async validateTables(account: Account, tablesToVerify: string[]): Promise<void> {
        const minRows = 1;
        try {
            const connection = await ConnectionFactory.getConnection(account);

            for (const tableName of tablesToVerify) {
                const query = `SELECT COUNT(*) count FROM ${fqtn(
                    account.databaseName,
                    tableName,
                )};`;

                const { length: count } = await connection.query(query);

                this.logger.log(PolloAdapter.acct(`Account gut check ${tableName} rows`, account));

                if (count < minRows) {
                    const errorMessage =
                        `Account ${account.id} can't be onboarded, ` +
                        `${tableName} doesn't have ${minRows} ` +
                        `${minRows === 1 ? 'row' : 'rows'} at least.`;
                    this.logger.error(PolloAdapter.acct(errorMessage, account));
                    throw new OurConflictException(errorMessage, ErrorCode.ORM_VALIDATION);
                }
            }
        } catch (error) {
            this.logger.error(PolloAdapter.acct(error, account));
            throw new OurConflictException(error, ErrorCode.UNDEFINED);
        }
    }

    /**
     * @param account
     * @param allFrameworks
     */
    async validateOnboardingTables(account: Account, allFrameworks: number[]): Promise<void> {
        let tablesToValidate: string[] = [];

        if (isEmpty(allFrameworks)) {
            tablesToValidate = difference(
                ONBOARDING_TABLES_TO_VALIDATE,
                TABLES_EXCEPTION_WITHOUT_FRAMEWORKS,
            );
        } else {
            if (
                allFrameworks.every(framework => FRAMEWORKS_WITHOUT_CONTROLS.includes(framework)) &&
                (allFrameworks.length === FRAMEWORKS_WITHOUT_CONTROLS.length ||
                    allFrameworks.length < FRAMEWORKS_WITHOUT_CONTROLS.length)
            ) {
                tablesToValidate = difference(
                    ONBOARDING_TABLES_TO_VALIDATE,
                    TABLES_EXCEPTION_WITHOUT_CONTROLS,
                );
            } else {
                tablesToValidate = ONBOARDING_TABLES_TO_VALIDATE;
            }
        }

        if (isEmpty(tablesToValidate)) {
            return;
        }

        await this.validateTables(account, tablesToValidate);
    }

    /**
     * Add validation to make sure the account is created with the right number of
     * frameworks and policies
     * @param account
     */
    async validateFrameworkAndTablesProvisionCounts(account: Account): Promise<void> {
        const accountFrameworkCountKey = `${account.id}-provision-framework-tag-count`;
        const FRAMEWORK_SEED_COUNT = await this._cacheService.get<number>(accountFrameworkCountKey);
        const POLICY_SEED_COUNT = 34;

        const allFrameworks = await this.frameworkService.getAllFrameworks();
        const policyCount = await this.policyRepository.count();

        await this.validateOnboardingTables(
            account,
            allFrameworks.map(framework => framework.tag),
        );

        this.logger.log(
            PolloAdapter.acct(
                `Account gut check: framework count: ${allFrameworks.length}/${FRAMEWORK_SEED_COUNT}; ` +
                    `policy count: ${policyCount}/${POLICY_SEED_COUNT};`,
                account,
            )
                .setContext(this.constructor.name)
                .setSubContext(this.validateFrameworkAndTablesProvisionCounts.name),
        );

        if (allFrameworks.length < FRAMEWORK_SEED_COUNT) {
            this.logger.error(
                PolloAdapter.acct(
                    `Account cannot be onboarded, frameworks found ${allFrameworks.length} of ${FRAMEWORK_SEED_COUNT}`,
                    account,
                )
                    .setContext(this.constructor.name)
                    .setSubContext(this.validateFrameworkAndTablesProvisionCounts.name),
            );
            throw new OurConflictException(
                'Account cannot be onboarded, frameworks found less than ' + FRAMEWORK_SEED_COUNT,
                ErrorCode.FRAMEWORKS_NOT_PROVISIONED,
            );
        }

        if (policyCount < POLICY_SEED_COUNT) {
            this.logger.error(
                PolloAdapter.acct(
                    `Account cannot be onboarded, policies found ${policyCount} of ${POLICY_SEED_COUNT}`,
                    account,
                )
                    .setContext(this.constructor.name)
                    .setSubContext(this.validateFrameworkAndTablesProvisionCounts.name),
            );
            throw new OurConflictException(
                'Account cannot be onboarded, policies found less than ' + POLICY_SEED_COUNT,
                ErrorCode.FRAMEWORKS_NOT_PROVISIONED,
            );
        }
        await this._cacheService.del(accountFrameworkCountKey);
    }

    /**
     *
     * @param currentAdmin
     * @param accountId
     * @returns
     */
    async sendOnboardingEmail(currentAdmin: SiteAdmin, accountId: AccountIdType): Promise<Account> {
        const account = await this.accountsCoreService.getAccountById(accountId);

        if (account.status !== AccountStatus.PENDING) {
            throw new ConflictException('Account has already been onboarded');
        }

        const originalAccount = cloneDeep(account);

        // check account table for frameworks and policies
        await this.validateFrameworkAndTablesProvisionCounts(account);

        const entry = await this.entryCoreService.getOwnerEntryForPendingAccount(accountId);

        const serviceGroup =
            await this.siteAdminServiceGroupService.getServiceGroupByAccountId(accountId);

        account.status = AccountStatus.ACTIVE;

        await this.accountsCoreService.save(account);

        const user = await this.usersCoreService.getUserByEntryId(entry.id);
        const emailLanguage = getLanguage(user.language, account.language);

        /**
         * If a service user is associated to this account
         * we want the service user person to get the onboarding email
         * not the main OWNER of the account.
         */
        if (!isNil(serviceGroup)) {
            const serviceUser = await this.serviceUsersCoreService.getServiceUserByAccountId(
                account.id,
            );

            if (!isNil(serviceUser)) {
                this._eventBus.publish(
                    new InviteWelcomeServiceProviderEvent(
                        serviceUser.entry.email,
                        account,
                        emailLanguage,
                        serviceUser.firstName ?? '',
                    ),
                );
            } else {
                throw new NotFoundException(ErrorCode.SERVICE_USER_NOT_FOUND);
            }
        } else {
            this._eventBus.publish(new AccountReadyEvent(account, entry.email, emailLanguage));
        }

        // site-admin audit log
        this._eventBus.publish(
            new AuditLogEvent(
                currentAdmin,
                AuditLogEventType.ACCOUNT_ENABLED,
                AuditLogTargetType.ACCOUNT,
                account.id,
                undefined, // target
                originalAccount,
                account,
            ),
        );

        return account;
    }

    /**
     *
     * @param updatedDomain
     */
    private async validateDomainIsAvailable(updatedDomain: string): Promise<void> {
        try {
            const account = await this.accountsCoreService.getAccountByDomain(updatedDomain);

            if (!isNil(account)) {
                throw new ConflictException();
            }
        } catch (err) {
            if (err instanceof ConflictException) {
                throw new ConflictException('Unable to update tenant, tenant already exists.');
            } else {
                throw new InternalServerErrorException(err);
            }
        }
    }

    /**
     *
     * @param account
     * @param domain
     */
    private async validateAccountAndDomain(
        account: Account,
        domain: {
            originalDomain: string;
            updatedDomain: string;
        },
    ): Promise<any> {
        if (isNil(account)) {
            throw new UnauthorizedException(ErrorCode.ACCOUNT_NOT_FOUND);
        }

        account.isAccountActivePendingOrFail();

        const company = await this.companiesCoreService.getCompanyByAccountId(account.id);
        const hasMultiDomain = company.multiDomain !== null;

        if (hasMultiDomain) {
            throw new PreconditionFailedException(
                'Multi-Domain support is enabled for this tenant, unable to update domain.',
            );
        }

        const potentialEntryConflictCount = await this.entryCoreService.getEntriesCountForDomain(
            domain.updatedDomain,
        );

        if (potentialEntryConflictCount > 0) {
            throw new ConflictException(
                `There already is an existing entry with the domain ${domain.updatedDomain}`,
            );
        }
    }

    /**
     *
     * @param account
     * @param domain
     * @returns
     */
    private async updateGlobalEntries(
        currentAdmin: SiteAdmin,
        account: Account,
        domain: {
            originalDomain: string;
            updatedDomain: string;
        },
    ): Promise<ExternalUsers> {
        const updatedEntries: Entry[] = [];
        const auditorUsers: User[] = [];
        const serviceUsers: ServiceUserClientEntity[][] = [];

        // get the entries ONLY for the original domain as we're gonna swap out that domain
        const entries = await this.entryCoreService.getEntriesForAccount(
            account,
            domain.originalDomain,
        );

        if (!isEmpty(entries)) {
            this.logger.log(
                PolloAdapter.acct(
                    `Updating entries from ${account.domain} to ${domain.updatedDomain}`,
                    account,
                )
                    .setContext(this.constructor.name)
                    .setSubContext(this.updateGlobalEntries.name)
                    .setResult({ entries }),
            );
            for (const entry of entries) {
                this.logger.log(
                    PolloAdapter.acct(`Searching for auditor with entryId: ${entry.id}`, account)
                        .setContext(this.constructor.name)
                        .setSubContext(this.updateGlobalEntries.name),
                );
                const auditor = await this.auditorClientRepository.findOne({
                    where: {
                        account: { id: account.id },
                        entry: { id: entry.id },
                    },
                });

                const serviceUsersOfEntry = await this.serviceUserClientRepository.find({
                    where: {
                        entry: { id: entry.id },
                    },
                });

                if (!isNil(auditor)) {
                    this.logger.log(
                        PolloAdapter.acct(
                            `Found auditor with entryId: ${entry.id}. Retrieving auditor user`,
                            account,
                        )
                            .setContext(this.constructor.name)
                            .setSubContext(this.updateGlobalEntries.name)
                            .setResult({ auditor }),
                    );
                    const auditorUser = await this.usersCoreService.getUserByEntryId(entry.id);

                    auditorUsers.push(auditorUser);
                    this.logger.log(
                        PolloAdapter.acct(
                            `Auditor user with entryId ${entry.id} found, skipping...`,
                            account,
                        )
                            .setContext(this.constructor.name)
                            .setSubContext(this.updateGlobalEntries.name)
                            .setResult({ auditorUser, entry }),
                    );
                    continue;
                }

                if (!isEmpty(serviceUsersOfEntry)) {
                    serviceUsers.push(serviceUsersOfEntry);
                    this.logger.log(
                        PolloAdapter.acct(`Service user with entryId ${entry.id} found`, account)
                            .setContext(this.constructor.name)
                            .setSubContext(this.updateGlobalEntries.name)
                            .setResult({ serviceUsersOfEntry, entry }),
                    );
                    continue;
                }

                entry.email = toNewDomainEmail(entry.email, domain.updatedDomain);

                this.logger.log(
                    PolloAdapter.acct(
                        `Email for entry with id: ${entry.id}, is being updated to ${entry.email}`,
                        account,
                    )
                        .setContext(this.constructor.name)
                        .setSubContext(this.updateGlobalEntries.name)
                        .setResult({ entry }),
                );

                updatedEntries.push(entry);
            }

            const auditorClientsForAccount = await this.auditorClientRepository.find({
                where: { account: { id: account.id } },
            });

            // update auditor client contact email domain
            if (!isEmpty(auditorClientsForAccount)) {
                this.logger.log(
                    PolloAdapter.acct(`Found auditor clients associated to this account`, account)
                        .setContext(this.constructor.name)
                        .setSubContext(this.updateGlobalEntries.name)
                        .setResult({ auditorClientsForAccount }),
                );
                for (const auditorClient of auditorClientsForAccount) {
                    auditorClient.contactEmail = toNewDomainEmail(
                        auditorClient.contactEmail,
                        domain.updatedDomain,
                    );

                    this.logger.log(
                        PolloAdapter.acct(
                            `Updating auditor client contact email to ${auditorClient.contactEmail}`,
                            account,
                        )
                            .setContext(this.constructor.name)
                            .setSubContext(this.updateGlobalEntries.name)
                            .setResult({ auditorClient }),
                    );

                    await this.auditorClientRepository.save(auditorClient);
                }
            }

            for await (const entry of updatedEntries) {
                // busting the cache for all entries
                await this.entryCoreService.bustEntryCache(entry.id);
            }

            await this.entryCoreService.saveEntries(updatedEntries);
            this.logger.log(
                PolloAdapter.acct(`Updated entries`, account)
                    .setContext(this.constructor.name)
                    .setSubContext(this.updateGlobalEntries.name)
                    .setResult(updatedEntries),
            );
        }

        account.domain = domain.updatedDomain;

        await this.accountsCoreService.save(account);

        this.logger.log(
            PolloAdapter.acct(`Updated account domain to ${domain.updatedDomain}`, account)
                .setContext(this.constructor.name)
                .setSubContext(this.updateGlobalEntries.name),
        );

        return { auditorUsers, serviceUsers };
    }

    @SuperCacheAccountBuster<any>({
        stores: [
            Caches.ACCOUNT,
            Caches.ACCOUNT_CONTRACT_DETAILS,
            Caches.COMPANY_DATA,
            Caches.COMPANY_PERSONNEL,
            Caches.CONTROL_TEMPLATE,
            Caches.ENTRY,
            Caches.FRAMEWORK,
            Caches.LIST_CONTROLS,
            Caches.PUBLIC_API_KEY,
            Caches.REQUIREMENTS,
            Caches.USER_BY_ENTRY_ID,
            Caches.WORKSPACE_ID,
        ],
        useArgs: 1,
    })
    private async updateCompanyDomain(account: Account, updatedDomain: string): Promise<any> {
        const company = await this.companiesCoreService.getCompanyByAccountId(account.id);
        if (!isNil(company)) {
            // bust the cache for the company
            await this.companiesCoreService.bustCompanyCache(account.id);

            company.domain = updatedDomain;
            await this.companyRepository.save(company);

            this.logger.log(
                PolloAdapter.acct(`Updated company domain`, account)
                    .setContext(this.constructor.name)
                    .setSubContext(this.updateCompanyDomain.name),
            );
        }
    }

    @SuperCacheAccountBuster<void>({
        stores: [
            Caches.ACCOUNT,
            Caches.ACCOUNT_CONTRACT_DETAILS,
            Caches.COMPANY_DATA,
            Caches.COMPANY_PERSONNEL,
            Caches.CONTROL_TEMPLATE,
            Caches.ENTRY,
            Caches.FRAMEWORK,
            Caches.LIST_CONTROLS,
            Caches.PUBLIC_API_KEY,
            Caches.REQUIREMENTS,
            Caches.USER_BY_ENTRY_ID,
            Caches.WORKSPACE_ID,
        ],
        useArgs: 1,
    })
    private async updateAccountSchedulesDomain(
        account: Account,
        updatedDomain: string,
    ): Promise<void> {
        await this.autopilotScheduleInstanceCoreService.updateSchedulesAccountData({
            account,
            updatedDomain,
        });
    }

    @SuperCacheAccountBuster<void>({
        stores: [
            Caches.ACCOUNT,
            Caches.ACCOUNT_CONTRACT_DETAILS,
            Caches.COMPANY_DATA,
            Caches.COMPANY_PERSONNEL,
            Caches.CONTROL_TEMPLATE,
            Caches.ENTRY,
            Caches.FRAMEWORK,
            Caches.LIST_CONTROLS,
            Caches.PUBLIC_API_KEY,
            Caches.REQUIREMENTS,
            Caches.USER_BY_ENTRY_ID,
            Caches.WORKSPACE_ID,
        ],
        useArgs: 1,
    })
    private async updateUserDomains(
        account: Account,
        domain: { originalDomain: string; updatedDomain: string },
        auditorUsers: User[],
    ): Promise<any> {
        const users = await this.userRepository
            .createQueryBuilder('User')
            // required to later check if the user is admin
            .innerJoinAndSelect('User.roles', 'UserRole')
            .where(`User.email ${like()} :before`, {
                before: `%${domain.originalDomain}`,
            })
            .getMany();

        const updatedUsers: User[] = [];
        if (!isEmpty(users)) {
            this.logger.log(
                PolloAdapter.acct(
                    `Updating user email domains from ${domain.originalDomain} to ${domain.updatedDomain}`,
                    account,
                )
                    .setContext(this.constructor.name)
                    .setSubContext(this.updateUserDomains.name),
            );
            for (const user of users) {
                const isAuditor = auditorUsers.find(aUser => aUser.id === user.id);

                if (isAuditor) {
                    this.logger.log(
                        PolloAdapter.acct(
                            `Found auditor with user id ${isAuditor.id}, skipping..`,
                            account,
                        )
                            .setContext(this.constructor.name)
                            .setSubContext(this.updateUserDomains.name),
                    );
                    continue;
                }
                user.email = toNewDomainEmail(user.email, domain.updatedDomain);
                try {
                    await this.entryCoreService.bustEntryCache(user.entryId);
                } catch (error) {
                    this.logger.log(
                        PolloMessage.msg(
                            `Unable to bust cache for account ${account.id} while updating company domain`,
                        )
                            .setContext(this.constructor.name)
                            .setSubContext(this.updateCompanyDomain.name)
                            .setError(error),
                    );
                }
                updatedUsers.push(user);
            }
        }

        const userList = await this.userRepository.save(updatedUsers);

        this.logger.log(
            PolloAdapter.acct(`Finished updating ${updatedUsers.length} users`, account)
                .setContext(this.constructor.name)
                .setSubContext(this.updateUserDomains.name),
        );

        return userList;
    }

    @SuperCacheAccountBuster<any>({
        stores: [
            Caches.ACCOUNT,
            Caches.ACCOUNT_CONTRACT_DETAILS,
            Caches.COMPANY_DATA,
            Caches.COMPANY_PERSONNEL,
            Caches.CONTROL_TEMPLATE,
            Caches.ENTRY,
            Caches.FRAMEWORK,
            Caches.LIST_CONTROLS,
            Caches.PUBLIC_API_KEY,
            Caches.REQUIREMENTS,
            Caches.USER_BY_ENTRY_ID,
            Caches.WORKSPACE_ID,
        ],
        useArgs: 1,
    })
    private async updateIdpUserIdentities(
        account: Account,
        domain: { originalDomain: string; updatedDomain: string },
    ): Promise<any> {
        const clientTypes = [
            ClientType.GOOGLE,
            ClientType.MICROSOFT_365,
            ClientType.MICROSOFT_365_GCC_HIGH,
            ClientType.GOOGLE_OAUTH,
        ];
        const updatedUserIdentities: UserIdentity[] = [];
        const idpUserIdentities = await this.userIdentityRepository.find({
            join: {
                alias: 'userIdentity',
                leftJoinAndSelect: {
                    connection: 'userIdentity.connection',
                },
            },
            where: {
                connection: {
                    clientType: In(clientTypes),
                    deletedAt: IsNull(),
                },
            },
        });
        if (!isEmpty(idpUserIdentities)) {
            this.logger.log(
                PolloAdapter.acct(
                    `Updating user identities email and username domains from ${domain.originalDomain} to ${domain.updatedDomain}`,
                    account,
                )
                    .setContext(this.constructor.name)
                    .setSubContext(this.updateIdpUserIdentities.name),
            );
            for (const userIdentity of idpUserIdentities) {
                userIdentity.username = toNewDomainEmail(
                    userIdentity.username ?? '',
                    domain.updatedDomain,
                );

                userIdentity.email = toNewDomainEmail(userIdentity.username, domain.updatedDomain);

                updatedUserIdentities.push(userIdentity);
            }
        }

        await this.userIdentityRepository.save(updatedUserIdentities);
        this.logger.log(
            PolloAdapter.acct(`Updated ${updatedUserIdentities.length} user identities`, account)
                .setContext(this.constructor.name)
                .setSubContext(this.updateIdpUserIdentities.name),
        );
    }

    @SuperCacheAccountBuster<any>({
        stores: [
            Caches.ACCOUNT,
            Caches.ACCOUNT_CONTRACT_DETAILS,
            Caches.COMPANY_DATA,
            Caches.COMPANY_PERSONNEL,
            Caches.CONTROL_TEMPLATE,
            Caches.ENTRY,
            Caches.FRAMEWORK,
            Caches.LIST_CONTROLS,
            Caches.PUBLIC_API_KEY,
            Caches.REQUIREMENTS,
            Caches.USER_BY_ENTRY_ID,
            Caches.WORKSPACE_ID,
        ],
        useArgs: 1,
    })
    private async updateConnectionMetadata(account: Account, updatedDomain: string): Promise<any> {
        const idpConnections = await this.connectionsCoreService.getActiveConnectionsByClientTypes([
            ClientType.GOOGLE,
            ClientType.GOOGLE_OAUTH,
        ]);
        if (!isEmpty(idpConnections)) {
            for (const idpConnection of idpConnections) {
                const clientTypeFound = idpConnection.clientType;
                const metadata = idpConnection.getMetadata();

                metadata.key = toNewDomainEmail(metadata.key, updatedDomain);
                metadata.domain = updatedDomain;
                idpConnection.setMetadata(metadata);

                await this.connectionRepository.save(idpConnection, { reload: false });

                this.logger.log(
                    PolloAdapter.acct(
                        `Updated ${ClientType[clientTypeFound]} connection metadata`,
                        account,
                    )
                        .setContext(this.constructor.name)
                        .setSubContext(this.updateConnectionMetadata.name)
                        .setResult(idpConnection),
                );
            }
        } else {
            this.logger.log(
                PolloAdapter.acct(
                    `No ${ClientType[ClientType.GOOGLE]} idP connection found, skipping..`,
                    account,
                )
                    .setContext(this.constructor.name)
                    .setSubContext(this.updateConnectionMetadata.name),
            );
        }
    }

    @SuperCacheAccountBuster<void>({
        stores: [
            Caches.ACCOUNT,
            Caches.ACCOUNT_CONTRACT_DETAILS,
            Caches.COMPANY_DATA,
            Caches.COMPANY_PERSONNEL,
            Caches.CONTROL_TEMPLATE,
            Caches.ENTRY,
            Caches.FRAMEWORK,
            Caches.LIST_CONTROLS,
            Caches.PUBLIC_API_KEY,
            Caches.REQUIREMENTS,
            Caches.USER_BY_ENTRY_ID,
            Caches.WORKSPACE_ID,
        ],
        useArgs: 1,
    })
    async updateTenantDomain(
        account: Account,
        currentAdmin: SiteAdmin,
        email: string,
    ): Promise<void> {
        const originalAccount = cloneDeep(account);
        const originalAccountStatus = account.status;
        const originalDbName = account.databaseName;
        const originalDomain = account.domain;

        try {
            const updatedDomain = getDomainFromEmail(email);

            this.logger.log(
                PolloAdapter.acct(`Domain to update to ${updatedDomain}`, account)
                    .setContext(this.constructor.name)
                    .setSubContext(this.updateTenantDomain.name),
            );

            /**
             * Throws an error if the domain is not available
             */
            await this.validateDomainIsAvailable(updatedDomain);

            this.logger.log(
                PolloAdapter.acct(`Validated the domain`, account)
                    .setContext(this.constructor.name)
                    .setSubContext(this.updateTenantDomain.name),
            );

            /**
             * Throws an error if multi-domain support is enabled
             */
            await this.validateAccountAndDomain(account, {
                originalDomain,
                updatedDomain,
            });

            this.logger.log(
                PolloAdapter.acct(
                    `Validated the account and domain against the same domain globally`,
                    account,
                )
                    .setContext(this.constructor.name)
                    .setSubContext(this.updateTenantDomain.name),
            );

            /**
             * Updates the global entries with the exception of entries
             * that reference auditors. We also update the domain.
             */
            const { auditorUsers, serviceUsers } = await this.updateGlobalEntries(
                currentAdmin,
                account,
                {
                    originalDomain,
                    updatedDomain,
                },
            );

            this.logger.log(
                PolloAdapter.acct(`finished updateGlobalEntries`, account)
                    .setContext(this.constructor.name)
                    .setSubContext(this.updateTenantDomain.name),
            );

            if (account.id !== account.databaseName) {
                const updatedDbName = account.id;

                this.logger.log(
                    PolloAdapter.acct(`DB name to update to ${updatedDbName}`, account)
                        .setContext(this.constructor.name)
                        .setSubContext(this.updateTenantDomain.name),
                );

                account.status = AccountStatus.MAINTENANCE;
                account = await this.accountsCoreService.save(account);

                this.logger.log(
                    PolloAdapter.acct(`account in MAINTENANCE now`, account)
                        .setContext(this.constructor.name)
                        .setSubContext(this.updateTenantDomain.name),
                );

                /**
                 * Creates a new database with the new domain name,
                 * copies all of the tables over to the new database
                 * and then it drops the old database
                 */
                await updateDatabaseName(account, originalDbName, updatedDbName);

                this.logger.log(
                    PolloAdapter.acct(`updated the Database Name to ${updatedDbName}`, account)
                        .setContext(this.constructor.name)
                        .setSubContext(this.updateTenantDomain.name),
                );

                /**
                 * Set the new database name in the account
                 */
                account.databaseName = updatedDbName;
                await this.accountsCoreService.save(account);
            }

            /**
             * We have to pool the new connection for the following
             * proxy calls or we get an exception and no connection
             */
            await ConnectionFactory.getConnection(account).catch(error => {
                this.logger.error(
                    PolloAdapter.acct('Unable to Create Database Connection for tenant', account)
                        .setContext(this.constructor.name)
                        .setSubContext(this.updateTenantDomain.name)
                        .setError(error),
                );
            });

            /**
             * Updates company domain name
             */
            await this.updateCompanyDomain(account, updatedDomain);

            this.logger.log(
                PolloAdapter.acct(`updated the domain to ${updatedDomain}`, account)
                    .setContext(this.constructor.name)
                    .setSubContext(this.updateTenantDomain.name),
            );

            /**
             * Updates all user email domains with the exception
             * of auditor users.
             */
            const users = await this.updateUserDomains(
                account,
                { originalDomain, updatedDomain },
                auditorUsers,
            );

            this.logger.log(
                PolloAdapter.acct(`updated the user domains to ${updatedDomain}`, account)
                    .setContext(this.constructor.name)
                    .setSubContext(this.updateTenantDomain.name),
            );

            /**
             * Updates all user email domains with the exception
             * of auditor users.
             */
            await this.updateIdpUserIdentities(account, {
                originalDomain,
                updatedDomain,
            });

            this.logger.log(
                PolloAdapter.acct(`updated the IdP Identities`, account)
                    .setContext(this.constructor.name)
                    .setSubContext(this.updateTenantDomain.name),
            );

            /**
             * Update connection metadata with the new domain.
             * This only applies to the google idp connection.
             */
            await this.updateConnectionMetadata(account, updatedDomain);

            this.logger.log(
                PolloAdapter.acct(`updated the Connection Metadata`, account)
                    .setContext(this.constructor.name)
                    .setSubContext(this.updateTenantDomain.name),
            );

            /**
             * Updates custom schedules with updated domain name
             */
            await this.updateAccountSchedulesDomain(account, updatedDomain)
                .then(() =>
                    this.logger.log(
                        PolloAdapter.acct(
                            `updated account schedules domain to ${updatedDomain}`,
                            account,
                        )
                            .setContext(this.constructor.name)
                            .setSubContext(this.updateTenantDomain.name),
                    ),
                )
                .catch(e =>
                    this.logger.error(
                        PolloAdapter.acct(
                            'Failed to update account domain for Autopilot schedules',
                            account,
                        )
                            .setContext(this.constructor.name)
                            .setSubContext(this.updateTenantDomain.name)
                            .setError(e),
                    ),
                );

            // ONLY go into this flow if the original account was already set to ACTIVE
            if (originalAccountStatus === AccountStatus.ACTIVE) {
                const ssoConnection = await this.connectionsCoreService.getConnectionByProviderType(
                    ProviderType.ENTERPRISE_SSO,
                );
                const idpConnection = await this.connectionsCoreService.getConnectionByProviderType(
                    ProviderType.IDENTITY,
                );

                if (isEmpty(ssoConnection) && isEmpty(idpConnection)) {
                    const user = users.find(usr => hasRole(usr, [Role.ADMIN]));

                    /**
                     * Send a magic link email out to first admin user
                     * after we update the domain of the account.
                     */

                    if (!isNil(user)) {
                        this.logger.log(
                            PolloAdapter.acct(`Resend account ready email to first admin`, account)
                                .setContext(this.constructor.name)
                                .setSubContext(this.updateTenantDomain.name),
                        );

                        // get user language
                        const emailLanguage = getLanguage(user.Language, account.language);
                        this._eventBus.publish(
                            new AccountReadyEvent(account, user.email, emailLanguage),
                        );
                    }
                }
            }

            if (!isEmpty(serviceUsers)) {
                const serviceGroup = await this.serviceGroupRepository.findOne({
                    where: { domain: originalDomain },
                });
                if (!isNil(serviceGroup)) {
                    // Update service group
                    serviceGroup.domain = updatedDomain;
                    await this.serviceGroupRepository.save(serviceGroup);
                }
            }

            //Update service user emails in each tenant that the service user is linked to
            await asyncForEach(serviceUsers, async serviceUsersOfEntry => {
                await asyncForEach(serviceUsersOfEntry, async serviceUser => {
                    const newEmail = toNewDomainEmail(serviceUser.entry.email, updatedDomain);

                    try {
                        const userToUpdate =
                            await this.siteAdminServiceUsersService.updateServiceUserEmail(
                                currentAdmin,
                                serviceUsersOfEntry,
                                serviceUser.entry.id,
                                { email: newEmail },
                            );

                        this.logger.log(
                            PolloAdapter.acct(
                                `Service user with entryId ${serviceUser.entry.id}
                            email updated from ${serviceUser.entry.email} to ${newEmail}`,
                                account,
                            )
                                .setContext(this.constructor.name)
                                .setSubContext(this.updateGlobalEntries.name)
                                .setResult({ userToUpdate, serviceUser }),
                        );
                    } catch (error) {
                        this.logger.error(
                            PolloAdapter.acct(
                                `Error updating service user with entryId ${serviceUsersOfEntry.entry.id}`,
                                account,
                            )
                                .setContext(this.constructor.name)
                                .setSubContext(this.updateGlobalEntries.name)
                                .setError(error),
                        );
                        // Continue with the next entry even if this one fails
                    }
                });
            });

            // site-admin audit log
            this._eventBus.publish(
                new AuditLogEvent(
                    currentAdmin,
                    AuditLogEventType.ACCOUNT_DOMAIN_UPDATED,
                    AuditLogTargetType.ACCOUNT,
                    account.id,
                    undefined, // target
                    originalAccount,
                    account,
                ),
            );
        } catch (error) {
            this.logger.error(PolloAdapter.acct(error.message, account).setError(error));
            return await Promise.reject(new InternalServerErrorException(error));
        } finally {
            account.status = originalAccountStatus;
            await this.accountsCoreService.save(account);
        }
    }

    /**
     *
     * This should really only be called from the controller from the site admin
     *
     * @param currentAdmin
     * @param accountId
     * @param dto
     */
    @SuperCacheAccountBuster<any>({
        stores: [
            Caches.ACCOUNT,
            Caches.ACCOUNT_CONTRACT_DETAILS,
            Caches.COMPANY_DATA,
            Caches.COMPANY_PERSONNEL,
            Caches.CONTROL_TEMPLATE,
            Caches.ENTRY,
            Caches.FRAMEWORK,
            Caches.LIST_CONTROLS,
            Caches.PUBLIC_API_KEY,
            Caches.REQUIREMENTS,
            Caches.USER_BY_ENTRY_ID,
            Caches.WORKSPACE_ID,
        ],
        useArgs: 1,
    })
    async toggleAccountEntitlement(
        account: Account,
        currentAdmin: SiteAdmin,
        dto: ToggleAccountEntitlementRequestDto,
    ): Promise<void> {
        const { types } = dto;
        const { id: accountId } = account;
        const after: ToggleResult[] = [];

        for (const type of types) {
            const results = await this.accountEntitlementService.toggleEntitlement({
                accountId,
                type,
                typesToBeToggled: types,
                currentAdmin,
            });

            after.push({
                type: AccountEntitlementType[type],
                value: results.after,
            });
        }

        const updatedAccount = await this.accountsCoreService.getAccountById(accountId);

        this._eventBus.publish(
            new AccountEntitlementToggledEvent(
                updatedAccount,
                after.map(afterObject => afterObject.type),
            ),
        );

        // site-admin audit log
        this._eventBus.publish(
            new AuditLogEvent(
                currentAdmin,
                AuditLogEventType.ACCOUNT_ENTITLEMENT_TOGGLED,
                AuditLogTargetType.ACCOUNT,
                accountId,
                after,
            ),
        );
    }

    async removeSlackWelcomeMessageUserCache(slackUserId: string): Promise<void> {
        const cacheKey = `slack:app_home_opened:welcome:${slackUserId}`;
        try {
            const hasWelcomeMsg = await this._cacheService.get<boolean>(cacheKey);
            if (hasWelcomeMsg) {
                await this._cacheService.del(cacheKey);
                this.log(`Removed cache for slack id: ${slackUserId}`);
            } else {
                throw new NotFoundException(`Cache ${cacheKey} not found`);
            }
        } catch (error) {
            this.logger.error(
                PolloMessage.msg(
                    'Error trying to remove cache for slack id:' + slackUserId,
                ).setError(error),
            );
            throw error;
        }
    }

    /**
     *
     * @param currentAdmin
     * @param account
     * @param accountCheckStatusType
     */
    private checkUpdateStatus(
        currentAdmin: SiteAdmin,
        account: Account,
        accountCheckStatusType: AccountCheckStatusType,
    ): void {
        /**
         * make sure if the account was:
         * 1. set to NON PAYMENT
         * 2. and the user is updating that account status to something else
         * 3. and if the user does NOT have the ADMIN role
         *
         * Throw an exception
         */
        if (
            account.status === AccountStatus.NON_PAYMENT &&
            accountCheckStatusType.status !== AccountStatus.NON_PAYMENT &&
            !hasRole(currentAdmin, [AdminRole.ADMIN])
        ) {
            throw new OurForbiddenException(
                'You must be an ADMIN to move an account out of NON_PAYMENT',
                ErrorCode.SITE_ADMIN_MISSING_ADMIN_ROLE,
            );
        }
    }

    private async updateCompliance(account: Account): Promise<void> {
        const applicablePersonnel =
            await this.personnelCoreService.getPersonnelByEmploymentStatuses(
                EmploymentStatusOptionsFilterMap.get(EmploymentStatusOptionsFilter.ALL_PERSONNEL) ??
                    [],
            );

        await this.complianceChecksOrchestrationService.computeComplianceChecksForPersonnel(
            account,
            applicablePersonnel,
            false,
        );

        await this.complianceChecksOrchestrationService.updateAllPersonnelFullyComplianceCheck();
    }

    private async validateAndGetTokenType(
        account: Account,
        requestedActAsType: AuthModes,
        siteAdmin: SiteAdmin,
    ): Promise<TokenType> {
        let tokenType: TokenType;

        // Check if the account is active
        if (account.status !== AccountStatus.ACTIVE) {
            throw new PreconditionFailedException('Account status is not active');
        }

        if (
            config.get('api.usaRestrictedTenantImpersonation').includes(account.domain) &&
            !siteAdmin.roles.some(role => role.role === AdminRole.USA_PERSON)
        ) {
            /**
             * Restricts access to the "Act As" feature, limiting it to
             * USA-based employees only, as requested by the customer.
             */
            throw new OurPreconditionFailedException(ErrorCode.UNAUTHORIZED_USA_PERSON_RESTRICTION);
        }

        // check if the support access is expired at the company level
        const company = await this.companiesCoreService.getCompanyByAccountIdV2(account.id);

        const { actAs } = company;
        if (isNil(actAs)) {
            throw new PreconditionFailedException('Act as is not enabled for this account');
        }

        // the customer controls when the support access expires. If it's NULl they rejected it, or if time has expired
        const supportAccessExpired = isNil(actAs.expiresAt) || hasExpired(actAs.expiresAt);
        if (supportAccessExpired) {
            throw new PreconditionFailedException(
                'Act as has been disabled for this account or has expired',
            );
        }

        const hasActAsRole = siteAdmin.roles.some(
            role => role.role === AdminRole.ACT_AS_READ_ONLY || role.role === AdminRole.ACT_AS,
        );

        if (!hasActAsRole) {
            throw new OurForbiddenException(
                `You must have an Act-As permission to help this customer.
                        Please, submit a HALP ticket to IT`,
                ErrorCode.SITE_ADMIN_MISSING_ADMIN_ROLE,
            );
        }

        // make sure that the site admin has the correct role for the set act as type in the tenant
        switch (actAs.type) {
            case ActAsType.READ_ONLY:
                tokenType = TokenType.ACT_AS_READ_ONLY;
                break;
            case ActAsType.READ_AND_WRITE:
                // if you ONLY have read-only here, just give them RO access to the tenant
                if (
                    hasRole(siteAdmin, [AdminRole.ACT_AS_READ_ONLY]) &&
                    !hasRole(siteAdmin, [AdminRole.ACT_AS])
                ) {
                    tokenType = TokenType.ACT_AS_READ_ONLY;
                } else {
                    tokenType = TokenType.ACT_AS;
                }
                break;
            default:
                throw new ForbiddenException();
        }

        /**
         * From a security lens...
         * we have validated the site admin has the correct role to act as the customer
         * and at this point, the system set the correct token type
         * per what the customer set on their tenant
         */

        /**
         * One last check here, if the client requested read only (even if they have read/write access)
         * we override the token type to read only at this point
         */
        if (requestedActAsType === AuthModes.ACT_AS_READ_ONLY) {
            tokenType = TokenType.ACT_AS_READ_ONLY;
        }

        return tokenType;
    }

    private get adminOnboardingRepository(): Repository<AdminOnboarding> {
        return this.getTenantRepository(AdminOnboarding);
    }

    private get companyRepository(): Repository<Company> {
        return this.getTenantRepository(Company);
    }

    private get companyInfoRepository(): Repository<CompanyInfo> {
        return this.getTenantRepository(CompanyInfo);
    }

    private get policyRepository(): PolicyRepository {
        return this.getCustomTenantRepository(PolicyRepository);
    }
    private get userRepository(): UserRepository {
        return this.getCustomTenantRepository(UserRepository);
    }

    private get userIdentityRepository(): UserIdentityRepository {
        return this.getCustomTenantRepository(UserIdentityRepository);
    }

    private get connectionRepository(): ConnectionsRepository {
        return this.getCustomTenantRepository(ConnectionsRepository);
    }
}
