import { Global, Module } from '@nestjs/common';
import { ModuleType, ModuleTypes } from 'commons/decorators/module-type.decorator';
import config from 'config';
import { Cluster, Redis } from 'ioredis';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { PolloLogger } from 'pollo-logger/pollo.logger';

import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { CacheService } from 'cache/cache.service';

export let CACHE_IS_UP: boolean = false;
export const REDIS_OPERATION_TIMEOUT_MS = 2000;

@ModuleType(ModuleTypes.COMMON)
@Global()
@Module({
    providers: [
        {
            provide: CACHE_MANAGER,
            useFactory: async () => {
                const logger = PolloLogger.logger('RedisProvider');
                const host: string = config.get('redis.host');
                const port: number = config.get('redis.port');
                const enableSsl: boolean = config.get('redis.enable_ssl');

                const isCluster = host.includes('clustercfg');

                let client: Redis | Cluster;

                if (isCluster) {
                    // Use cluster mode
                    client = new Cluster([{ host, port }], {
                        dnsLookup: (address, callback) => callback(null, address),
                        redisOptions: {
                            tls: enableSsl ? {} : undefined,
                            commandTimeout: REDIS_OPERATION_TIMEOUT_MS,
                        },
                        enableAutoPipelining: false,
                    });

                    logger.log(
                        PolloMessage.msg(`Connected to Redis Cluster: ${host}:${port}`).setContext(
                            'RedisProvider',
                        ),
                    );
                } else {
                    // Use single node mode
                    client = new Redis({
                        host,
                        port,
                        tls: enableSsl ? {} : undefined,
                        commandTimeout: REDIS_OPERATION_TIMEOUT_MS,
                    });

                    logger.log(
                        PolloMessage.msg(
                            `Connected to Redis (single node): ${host}:${port}`,
                        ).setContext('RedisProvider'),
                    );
                }

                // Handle connection events
                client.on('error', (error: Error) => {
                    logger.error(
                        PolloMessage.msg(`Redis connection error: ${error.message}`)
                            .setContext('RedisProvider')
                            .setError(error),
                    );
                    CACHE_IS_UP = false;
                });

                client.on('connect', () => {
                    logger.log(
                        PolloMessage.msg('Redis connected successfully').setContext(
                            'RedisProvider',
                        ),
                    );
                });

                client.on('ready', () => {
                    logger.log(
                        PolloMessage.msg('Redis connection ready').setContext('RedisProvider'),
                    );
                    CACHE_IS_UP = true;
                });

                return client;
            },
        },
        CacheService,
    ],
    exports: [CacheService],
})
export class CacheModule {
    private logger = PolloLogger.logger(this.constructor.name);
    constructor() {
        this.logger.log(
            PolloMessage.msg('CacheModule initialized').setContext(this.constructor.name),
        );
    }
}
