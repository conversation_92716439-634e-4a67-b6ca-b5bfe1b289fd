import { decodeHTML } from 'entities';
import { isEmpty, isString } from 'lodash';
import { marked } from 'marked';

export interface ExtractedLink {
    text: string;
    url: string;
}

export interface MarkdownProcessingResult {
    plainText: string;
    links: ExtractedLink[];
}

/**
 * Validates if a URL is a well-formed HTTP or HTTPS URL
 * @param url The URL to validate
 * @returns True if the URL is a valid HTTP/HTTPS URL
 */
function isValidHttpUrl(url: string): boolean {
    try {
        const urlObj = new URL(url);
        return urlObj.protocol === 'http:' || urlObj.protocol === 'https:';
    } catch {
        return false;
    }
}

/**
 * Creates a marked renderer that captures links during processing
 * @param onLinkFound Callback to capture each link found
 * @returns Renderer that captures links and outputs plain text
 */
function createPlainTextRenderer(onLinkFound: (link: ExtractedLink) => void): marked.Renderer {
    const renderer = new marked.Renderer();

    // Text formatting methods
    renderer.heading = (text: string) => `${text}\n\n`;
    renderer.paragraph = (text: string) => `${text}\n`;
    renderer.strong = (text: string) => text;
    renderer.em = (text: string) => text;
    renderer.blockquote = (quote: string) => `"${quote.trim()}"\n`;

    renderer.br = () => '\n';
    renderer.hr = () => '';

    renderer.list = (body: string) => `${body}\n`;
    renderer.listitem = (text: string) => `- ${text.trim()}\n`;

    renderer.table = (header: string, body: string) => `${header}${body}`;
    renderer.tablerow = (content: string) => `${content}|\n`;
    renderer.tablecell = (content: string) => {
        return `| ${content} `;
    };

    renderer.image = (_href: string, _title: string, text: string) => text || '';
    renderer.code = (code: string) => code + '\n';
    renderer.codespan = (code: string) => code;
    renderer.checkbox = (checked: boolean) => (checked ? '☑ ' : '☐ ');
    renderer.del = (text: string) => text;

    // Link method that captures links and returns text
    renderer.link = (href: string, _title: string, text: string) => {
        const cleanedHref = href?.trim();
        const cleanedText = text?.trim();

        if (!isEmpty(cleanedHref) && !isEmpty(cleanedText) && isValidHttpUrl(cleanedHref)) {
            // Decode HTML entities in link text
            const decodedText = decodeHTML(cleanedText);
            onLinkFound({
                text: decodedText,
                url: cleanedHref,
            });
        }

        return cleanedText || '';
    };

    return renderer;
}

/**
 * Extracts links and converts markdown to plain text in a single operation
 * This is the most efficient option when you need both results
 * @param markdown The markdown content to process
 * @returns Object containing plain text and extracted links
 */
export function markdownToPlainText(markdown: string): MarkdownProcessingResult {
    if (!isString(markdown)) {
        return { plainText: '', links: [] };
    }

    const links: ExtractedLink[] = [];
    const renderer = createPlainTextRenderer(link => links.push(link));

    const plainText = marked(markdown, { renderer });

    // Decode HTML entities that marked might have encoded
    const decodedText = decodeHTML(plainText);

    return {
        plainText: decodedText.trim(),
        links,
    };
}
