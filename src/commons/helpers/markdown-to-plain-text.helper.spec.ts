import { markdownToPlainText } from 'commons/helpers/markdown-to-plain-text.helper';

describe('Markdown to Plain Text', () => {
    describe('markdownToPlainText', () => {
        it('should extract links and convert markdown to plain text', () => {
            const markdown = `
# Access Requirements

Please review our **security policy** at [Security Policy](https://example.com/security "Our Security Policy") 
and complete the [Vendor Assessment Form](https://forms.example.com/vendor-assessment).

Additional requirements:
- Review [Privacy Policy](https://example.com/privacy)
- Complete *background check*
- Sign the agreement

> Important: All vendors must comply with our standards.
            `.trim();

            const result = markdownToPlainText(markdown);

            expect(result.plainText).toContain('Access Requirements');
            expect(result.plainText).toContain(
                'Please review our security policy at Security Policy',
            );
            expect(result.plainText).toContain('Complete background check');
            expect(result.plainText).toContain(
                'Important: All vendors must comply with our standards',
            );

            expect(result.links).toEqual([
                {
                    text: 'Security Policy',
                    url: 'https://example.com/security',
                },
                {
                    text: 'Vendor Assessment Form',
                    url: 'https://forms.example.com/vendor-assessment',
                },
                {
                    text: 'Privacy Policy',
                    url: 'https://example.com/privacy',
                },
            ]);
        });

        it('should handle empty or null input', () => {
            const result1 = markdownToPlainText('');
            const result2 = markdownToPlainText(null as any);

            expect(result1).toEqual({ plainText: '', links: [] });
            expect(result2).toEqual({ plainText: '', links: [] });
        });

        it('should handle markdown without links', () => {
            const markdown = '**Bold text** and *italic text* with `code`.';
            const result = markdownToPlainText(markdown);

            expect(result.plainText).toBe('Bold text and italic text with code.');
            expect(result.links).toEqual([]);
        });

        it('should only extract valid HTTP/HTTPS URLs', () => {
            const markdown = `
Check out [Valid HTTPS](https://example.com) and [Valid HTTP](http://example.com).
Also see [Invalid FTP](ftp://example.com) and [Invalid mailto](mailto:<EMAIL>).
Visit [Relative Link](./page.html) and [Invalid URL](not-a-url).
            `.trim();

            const result = markdownToPlainText(markdown);

            expect(result.links).toEqual([
                {
                    text: 'Valid HTTPS',
                    url: 'https://example.com',
                },
                {
                    text: 'Valid HTTP',
                    url: 'http://example.com',
                },
            ]);
        });

        it('should handle complex nested markdown structures', () => {
            const markdown = `
# **Important** Security Requirements

Please complete the following steps:

1. **Review** our [Security Policy](https://example.com/security) *carefully*
2. Complete the [**Vendor Assessment**](https://forms.example.com/assessment) form
3. Sign the [*Legal Agreement*](https://docs.example.com/legal)

## Additional Requirements

- [ ] Read [Privacy Policy](https://example.com/privacy)
- [x] ~~Old requirement~~ (completed)
- [ ] Complete [Background Check](https://verify.example.com/bg-check)

> **Note**: All links must be reviewed by [Compliance Team](https://team.example.com/compliance).

\`\`\`
Code blocks should be ignored: [Not a Link](https://ignored.com)
\`\`\`

Inline \`code with [link](https://ignored.com)\` should also be ignored.
            `.trim();

            const result = markdownToPlainText(markdown);

            // Check plain text conversion
            expect(result.plainText).toContain('Important Security Requirements');
            expect(result.plainText).toContain('Review our Security Policy carefully');
            expect(result.plainText).toContain('Complete the Vendor Assessment form');
            expect(result.plainText).toContain('- ☐ Read Privacy Policy');
            expect(result.plainText).toContain(
                '"Note: All links must be reviewed by Compliance Team."',
            );
            expect(result.plainText).toContain(
                'Code blocks should be ignored: [Not a Link](https://ignored.com)',
            );
            expect(result.plainText).toContain(
                'code with [link](https://ignored.com) should also be ignored',
            );

            // Check extracted links (should ignore links in code blocks and inline code)
            expect(result.links).toEqual([
                {
                    text: 'Security Policy',
                    url: 'https://example.com/security',
                },
                {
                    text: 'Vendor Assessment',
                    url: 'https://forms.example.com/assessment',
                },
                {
                    text: 'Legal Agreement',
                    url: 'https://docs.example.com/legal',
                },
                {
                    text: 'Privacy Policy',
                    url: 'https://example.com/privacy',
                },
                {
                    text: 'Background Check',
                    url: 'https://verify.example.com/bg-check',
                },
                {
                    text: 'Compliance Team',
                    url: 'https://team.example.com/compliance',
                },
            ]);
        });

        it('should handle links with special characters and encoding', () => {
            const markdown = `
Visit [API Docs](https://api.example.com/docs?version=v1&format=json) for details.
Check [Search Results](https://search.example.com/q?query=test%20data&page=1).
See [Unicode Test](https://example.com/测试/path) and [Encoded Chars](https://example.com/path%20with%20spaces).
            `.trim();

            const result = markdownToPlainText(markdown);

            expect(result.links).toEqual([
                {
                    text: 'API Docs',
                    url: 'https://api.example.com/docs?version=v1&format=json',
                },
                {
                    text: 'Search Results',
                    url: 'https://search.example.com/q?query=test%20data&page=1',
                },
                {
                    text: 'Unicode Test',
                    url: 'https://example.com/测试/path',
                },
                {
                    text: 'Encoded Chars',
                    url: 'https://example.com/path%20with%20spaces',
                },
            ]);
        });

        it('should handle HTML entities in markdown', () => {
            const markdown = `
Please review our [Terms &amp; Conditions](https://example.com/terms) and [Privacy &lt;Policy&gt;](https://example.com/privacy).
Also check [Q&amp;A Section](https://example.com/qa) for &quot;frequently asked questions&quot;.
            `.trim();

            const result = markdownToPlainText(markdown);

            expect(result.plainText).toContain('Terms & Conditions');
            expect(result.plainText).toContain('Privacy <Policy>');
            expect(result.plainText).toContain('Q&A Section');
            expect(result.plainText).toContain('"frequently asked questions"');

            expect(result.links).toEqual([
                {
                    text: 'Terms & Conditions',
                    url: 'https://example.com/terms',
                },
                {
                    text: 'Privacy <Policy>',
                    url: 'https://example.com/privacy',
                },
                {
                    text: 'Q&A Section',
                    url: 'https://example.com/qa',
                },
            ]);
        });

        it('should handle malformed and edge case markdown', () => {
            const markdown = `
[Incomplete link](
[Missing URL]()
[Has text](https://example.com)
[](https://example.com/no-text)
[Valid Link](https://example.com/valid)
[Broken markdown [nested](https://example.com/nested) text
[Multiple](https://first.com) [links](https://second.com) in one line.
            `.trim();

            const result = markdownToPlainText(markdown);

            // Should only extract valid, complete links with both text and URL
            expect(result.links).toEqual([
                {
                    text: 'Has text',
                    url: 'https://example.com',
                },
                {
                    text: 'Valid Link',
                    url: 'https://example.com/valid',
                },
                {
                    text: 'nested',
                    url: 'https://example.com/nested',
                },
                {
                    text: 'Multiple',
                    url: 'https://first.com',
                },
                {
                    text: 'links',
                    url: 'https://second.com',
                },
            ]);
        });

        it('should handle tables and complex formatting', () => {
            const markdown = `
| Document | Link | Status |
|----------|------|--------|
| Security Policy | [View](https://example.com/security) | ✅ Required |
| Privacy Policy | [Download](https://example.com/privacy.pdf) | ⚠️ Optional |

---

**Important**: Please review all [documentation](https://docs.example.com) before proceeding.

1. First, read the [Getting Started](https://example.com/start) guide
2. Then, complete the [Assessment Form](https://forms.example.com/assess)
3. Finally, submit to [Review Portal](https://portal.example.com/submit)
            `.trim();

            const result = markdownToPlainText(markdown);

            // Tables should be converted to readable pipe-separated format
            expect(result.plainText).toContain('| Document | Link | Status |');
            expect(result.plainText).toContain('| Security Policy | View | ✅ Required |');
            expect(result.plainText).toContain('Important: Please review all documentation');
            expect(result.plainText).toContain('- First, read the Getting Started guide');

            expect(result.links).toEqual([
                {
                    text: 'View',
                    url: 'https://example.com/security',
                },
                {
                    text: 'Download',
                    url: 'https://example.com/privacy.pdf',
                },
                {
                    text: 'documentation',
                    url: 'https://docs.example.com',
                },
                {
                    text: 'Getting Started',
                    url: 'https://example.com/start',
                },
                {
                    text: 'Assessment Form',
                    url: 'https://forms.example.com/assess',
                },
                {
                    text: 'Review Portal',
                    url: 'https://portal.example.com/submit',
                },
            ]);
        });

        it('should handle complex tables with various content', () => {
            const markdown = `
## Complex Table Test

| **Document** | **Link** | **Status** | **Priority** | **Notes** |
|--------------|----------|------------|--------------|-----------|
| Security Policy | [View PDF](https://example.com/security.pdf) | ✅ **Required** | High | Must be reviewed *carefully* |
| Privacy Policy | [Download](https://example.com/privacy) | ⚠️ Optional | Medium | Contains \`sensitive\` info |
| Terms of Service | [Read Online](https://example.com/terms) | ❌ **Pending** | Low | ~~Old version~~ available |
| Data Processing Agreement | [Sign Here](https://forms.example.com/dpa?ref=vendor&id=123) | 🔄 In Progress | Critical | **Urgent**: Due by EOD |

### Additional Requirements

Please also complete the [Background Check](https://verify.example.com/bg) form.
            `.trim();

            const result = markdownToPlainText(markdown);

            // Complex table should be converted to readable pipe-separated format

            // Basic checks
            expect(result.plainText).toContain('Complex Table Test');
            expect(result.plainText).toContain('Security Policy');
            expect(result.plainText).toContain('Additional Requirements');

            // Should extract all links
            expect(result.links).toHaveLength(5);
            expect(result.links.map(l => l.text)).toEqual([
                'View PDF',
                'Download',
                'Read Online',
                'Sign Here',
                'Background Check',
            ]);
        });

        it('should handle real-world SafeBase-style content', () => {
            const markdown = `
## 🔒 Security Compliance Requirements

**Before proceeding, please ensure you have:**

✅ Reviewed our [**Security Framework**](https://security.safebase.io/framework)
✅ Completed the [Vendor Risk Assessment](https://forms.safebase.io/vendor-assessment?ref=onboarding)
✅ Signed the [Data Processing Agreement](https://legal.safebase.io/dpa.pdf)

### 📋 Additional Documentation

> **Important**: All vendors must maintain SOC 2 Type II certification.

Please also review:
- [Privacy Policy](https://safebase.io/privacy) (updated Jan 2024)
- [Terms of Service](https://safebase.io/terms)
- [Incident Response Plan](https://docs.safebase.io/security/incident-response)

**Questions?** Contact our [Compliance Team](https://support.safebase.io/compliance) or visit our [Help Center](https://help.safebase.io).

---
*This document was last updated on March 15, 2024.*
            `.trim();

            const result = markdownToPlainText(markdown);

            expect(result.plainText).toContain('🔒 Security Compliance Requirements');
            expect(result.plainText).toContain('Before proceeding, please ensure you have:');
            expect(result.plainText).toContain('✅ Reviewed our Security Framework');
            expect(result.plainText).toContain(
                '"Important: All vendors must maintain SOC 2 Type II certification."',
            );
            expect(result.plainText).toContain('- Privacy Policy (updated Jan 2024)');
            expect(result.plainText).toContain('Questions? Contact our Compliance Team');

            expect(result.links).toEqual([
                {
                    text: 'Security Framework',
                    url: 'https://security.safebase.io/framework',
                },
                {
                    text: 'Vendor Risk Assessment',
                    url: 'https://forms.safebase.io/vendor-assessment?ref=onboarding',
                },
                {
                    text: 'Data Processing Agreement',
                    url: 'https://legal.safebase.io/dpa.pdf',
                },
                {
                    text: 'Privacy Policy',
                    url: 'https://safebase.io/privacy',
                },
                {
                    text: 'Terms of Service',
                    url: 'https://safebase.io/terms',
                },
                {
                    text: 'Incident Response Plan',
                    url: 'https://docs.safebase.io/security/incident-response',
                },
                {
                    text: 'Compliance Team',
                    url: 'https://support.safebase.io/compliance',
                },
                {
                    text: 'Help Center',
                    url: 'https://help.safebase.io',
                },
            ]);
        });
    });
});
