import { ErrorCode } from '@drata/enums';
import type { ReviewGroupEntity } from 'app/approvals/v2/approval-review-groups-core/entities/review-group.entity';
import type { ReviewEntity } from 'app/approvals/v2/approval-review-groups-core/entities/review.entity';
import { ReviewStatus } from 'app/approvals/v2/approval-review-groups-core/types/review-status.enum';
import type { ApprovalEntity } from 'app/approvals/v2/approvals-core/entities/approval.entity';
import { ApprovalStatusType } from 'app/approvals/v2/approvals-core/types/approval-status.enum';
import type { PolicyApprovalsAssociationEntity } from 'app/policy-approvals/policy-approvals-commerce/entities/policy-approvals-associations.entity';
import type { User } from 'app/users/entities/user.entity';
import type { PolicyVersion } from 'app/users/policies/entities/policy-version.entity';
import type { Policy } from 'app/users/policies/entities/policy.entity';
import { PolicyStatus } from 'app/users/policies/enums/policy-status.enum';
import { PolicyVersionStatus } from 'commons/enums/users/policies/policy-version-status.enum';
import { Role } from 'commons/enums/users/role.enum';
import { BadRequestException } from 'commons/exceptions/bad-request.exception';
import { ForbiddenException } from 'commons/exceptions/forbidden.exception';
import { NotFoundException } from 'commons/exceptions/not-found.exception';
import { PreconditionFailedException } from 'commons/exceptions/precondition-failed.exception';
import { isPolicyOwner } from 'commons/helpers/policy.helper';
import { hasRole } from 'commons/helpers/user.helper';
import config from 'config';

/**
 * Checks if the approval is in a finalized state
 * @param approval - The approval entity
 * @returns True if approval is finalized, false otherwise
 */
export function approvalHasFinalizedStatus({ status }: { status: ApprovalStatusType }) {
    return (
        status === ApprovalStatusType.APPROVED ||
        status === ApprovalStatusType.CHANGES_REQUESTED ||
        status === ApprovalStatusType.CANCELLED
    );
}

/**
 * Validates that the user is authorized to approve the policy
 * @param reviewGroups - Array of review groups
 * @param userId - User ID attempting to approve
 * @throws ForbiddenException if user is not in active approval tier
 */
export function validateUserInActiveApprovalTier(
    reviewGroups: ReviewGroupEntity[],
    userId: number,
): void {
    const userReviewGroups = reviewGroups.filter(
        reviewGroup =>
            // Filter out review groups that have been completed
            reviewGroup.waasRunId &&
            !reviewGroup.hasReachedConsensus &&
            reviewGroup.reviews.some(review => review.userId === userId),
    );

    if (userReviewGroups.length === 0) {
        throw new ForbiddenException(
            'User is not part of the active approval tier',
            ErrorCode.ROLE_PERMISSION_FORBIDDEN,
        );
    }
}

/**
 * Validates that the review is in the correct state for approval
 * @param review - The review entity
 * @throws PreconditionFailedException if review is not ready for approval
 */
export function validateReviewReadyForApproval(review: ReviewEntity): void {
    if (review.status !== ReviewStatus.READY_FOR_REVIEW) {
        throw new PreconditionFailedException(
            ErrorCode.POLICY_VERSION_VERSION_INVALID_STATUS,
            `Review is not ${ReviewStatus.READY_FOR_REVIEW}.`,
        );
    }
}

/**
 * Validates that the review group has not reached consensus
 * @param reviewGroup - The review group entity
 * @throws PreconditionFailedException if consensus has already been reached
 */
export function validateReviewGroupConsensusNotReached(reviewGroup: ReviewGroupEntity): void {
    if (reviewGroup.hasReachedConsensus) {
        throw new PreconditionFailedException(
            ErrorCode.POLICY_INVALID_STATUS,
            'This review group has already reached consensus.',
        );
    }
}

/**
 * Validates that the policy is in the correct state for approval
 * @param policy - The policy entity
 * @throws PreconditionFailedException if policy is not in correct state
 */
export function validatePolicyStateForApproval(policy: Policy): void {
    if (
        policy.policyStatus !== PolicyStatus.ACTIVE &&
        policy.policyStatus !== PolicyStatus.OUTDATED
    ) {
        throw new PreconditionFailedException(
            ErrorCode.POLICY_INVALID_STATUS,
            `Policy must be in ACTIVE or OUTDATED status for approval. Current status: ${policy.policyStatus}.`,
        );
    }
}

/**
 * Validates that the policy version is awaiting approval
 * @param policyVersion - The policy version entity
 * @throws PreconditionFailedException if policy version is not awaiting approval
 */
export function validatePolicyVersionNeedsApproval({
    policyVersionStatus,
}: {
    policyVersionStatus: PolicyVersionStatus | null;
}): void {
    if (policyVersionStatus !== PolicyVersionStatus.NEEDS_APPROVAL) {
        throw new PreconditionFailedException(
            ErrorCode.POLICY_VERSION_VERSION_INVALID_STATUS,
            `Policy version must be in ${PolicyVersionStatus.NEEDS_APPROVAL}.`,
        );
    }
}

/**
 * Validates that the policy version is awaiting approval
 * @param policyVersion - The policy version entity
 * @throws PreconditionFailedException if policy version is not awaiting approval
 */
export function validatePolicyVersionAwaitingApproval(policyVersion: PolicyVersion): void {
    if (policyVersion.policyVersionStatus !== PolicyVersionStatus.NEEDS_APPROVAL) {
        throw new PreconditionFailedException(
            ErrorCode.POLICY_VERSION_VERSION_INVALID_STATUS,
            `Policy version must be in ${PolicyVersionStatus.NEEDS_APPROVAL}.`,
        );
    }
}

/**
 * Validates that the approval is not in a finalized state
 * @param approval - The approval entity
 * @throws PreconditionFailedException if approval is already finalized
 */
export function validateApprovalNotFinalized(approval: ApprovalEntity): void {
    if (approvalHasFinalizedStatus(approval)) {
        throw new PreconditionFailedException(
            ErrorCode.POLICY_INVALID_STATUS,
            `Cannot submit approval. Approval is already finalized with status: ${approval.status}.`,
        );
    }
}

/**
 * Validates that the review has not already been submitted with the same status
 * @param review - The review entity
 * @param newStatus - The new status being submitted
 * @throws BadRequestException if review already has the same status
 */
export function validateReviewStatusNotDuplicate(
    review: ReviewEntity,
    newStatus: ReviewStatus,
): void {
    if (review.status === newStatus) {
        throw new BadRequestException(
            `Review has already been submitted with status: ${newStatus}.`,
            ErrorCode.VALIDATION,
        );
    }
}

/**
 * Validates that the policy exists
 * @param policy - The policy entity
 * @throws NotFoundException if policy is not found
 */
export function validatePolicyExists(policy: Policy | null | undefined): asserts policy is Policy {
    if (!policy) {
        throw new NotFoundException(ErrorCode.POLICY_NOT_FOUND);
    }
}

/**
 * Validates that the policy version exists
 * @param policyVersion - The policy version entity
 * @throws NotFoundException if policy version is not found
 */
export function validatePolicyVersionExists(
    policyVersion: PolicyVersion | null | undefined,
): asserts policyVersion is PolicyVersion {
    if (!policyVersion) {
        throw new NotFoundException(ErrorCode.POLICY_VERSION_NOT_FOUND);
    }
}

/**
 * Validates that the approval exists
 * @param approval - The approval entity
 * @throws NotFoundException if approval is not found
 */
export function validateApprovalExists(
    approval: ApprovalEntity | null | undefined,
): asserts approval is ApprovalEntity {
    if (!approval) {
        throw new NotFoundException(ErrorCode.ENTITY_NOT_FOUND);
    }
}

/**
 * Validates that the review exists
 * @param review - The review entity
 * @throws NotFoundException if review is not found
 */
export function validateReviewExists(
    review: ReviewEntity | null | undefined,
): asserts review is ReviewEntity {
    if (!review) {
        throw new NotFoundException(ErrorCode.ENTITY_NOT_FOUND);
    }
}

/**
 * Validates that the review group exists
 * @param reviewGroup - The review group entity
 * @throws NotFoundException if review group is not found
 */
export function validateReviewGroupExists(
    reviewGroup: ReviewGroupEntity | null | undefined,
): asserts reviewGroup is ReviewGroupEntity {
    if (!reviewGroup) {
        throw new NotFoundException(ErrorCode.ENTITY_NOT_FOUND);
    }
}

/**
 * Validates that the workflow run ID exists
 * @param waasRunId - The workflow run ID
 * @throws PreconditionFailedException if workflow run ID is missing
 */
export function validateWorkflowRunIdExists(
    waasRunId: string | null | undefined,
): asserts waasRunId is string {
    if (!waasRunId) {
        throw new PreconditionFailedException(
            ErrorCode.VALIDATION,
            'Workflow run ID is required for approval processing.',
        );
    }
}

/**
 * Validates that the policy approval association exists
 * @param policyApprovalAssociation - The policy approval association entity
 * @throws NotFoundException if policy approval association is not found
 */
export function validatePolicyApprovalAssociationExists(
    policyApprovalAssociation: PolicyApprovalsAssociationEntity | null | undefined,
): asserts policyApprovalAssociation is PolicyApprovalsAssociationEntity {
    if (!policyApprovalAssociation) {
        throw new NotFoundException(ErrorCode.POLICY_NOT_FOUND);
    }

    validatePolicyExists(policyApprovalAssociation.policy);
    validatePolicyVersionExists(policyApprovalAssociation.policyVersion);
    validateApprovalExists(policyApprovalAssociation.approval);
}

/**
 * Validates that the user is a policy owner or has appropriate permissions to cancel approval
 * @param policy - The policy entity
 * @param user - The user attempting to cancel approval
 * @throws ForbiddenException if user is not authorized
 */
export function validatePolicyOwner(policy: Policy, user: User): void {
    if (!isPolicyOwner(policy, user) && !hasRole(user, [Role.ADMIN])) {
        throw new ForbiddenException(
            'User is not allowed to cancel policy approval',
            ErrorCode.USER_IS_NOT_POLICY_OWNER,
        );
    }
}

/**
 * Validates that there are active approval workflows to cancel
 * @param approval - The approval entity
 * @throws PreconditionFailedException if no active workflows exist
 */
export function validateApprovalReviewGroups(approval: ApprovalEntity): void {
    const activeWorkflowIds =
        approval.reviewGroups
            ?.filter(group => group.waasRunId && !group.hasReachedConsensus)
            ?.map(group => group.waasRunId) || [];

    if (activeWorkflowIds.length === 0) {
        throw new PreconditionFailedException(
            ErrorCode.POLICY_INVALID_STATUS,
            'No active approval workflows found to cancel.',
        );
    }
}

/**
 * Creates a Redis lock key for policy approval operations
 * @param accountId - Account ID
 * @param policyId - Policy ID
 * @param policyVersionId - Policy Version ID
 * @param approvalId - Approval ID (optional) - If not provided, lock is acquired at policy-version level (the starting approval workflow process)
 * @returns Redis lock key string
 */
export function createPolicyApprovalLockKey(
    accountId: string,
    policyId: number,
    policyVersionId: number,
    approvalId?: number,
): string {
    if (!approvalId) {
        return `${config.get('cache.globalPrefix')}:${accountId}:${policyId}:${policyVersionId}`;
    }

    return `${config.get('cache.globalPrefix')}:${accountId}:${policyId}:${policyVersionId}:${approvalId}`;
}
