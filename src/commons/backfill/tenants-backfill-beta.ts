import { AuditApi<PERSON>ey } from 'auditors/entities/audit-api-key.entity';
import { AuditFirm } from 'auditors/entities/audit-firm.entity';
import { AuditorClient } from 'auditors/entities/auditor-client.entity';
import { AuditorFrameworkAuditors } from 'auditors/entities/auditor-framework-auditors.entity';
import { Auditor } from 'auditors/entities/auditor.entity';
import { Audit } from 'audits/entities/audit.entity';
import { AccountSafeBaseSettings } from 'auth/entities/account-safebase-settings.entity';
import { Account } from 'auth/entities/account.entity';
import { Entry } from 'auth/entities/entry.entity';
import { TenantRouter } from 'auth/entities/tenant-router.entity';
import { Token } from 'auth/entities/token.entity';
import { program } from 'commander';
import { PolloAdapter } from 'commons/adapters/pollo.adapter';
import {
    type DrataDataSource,
    type DrataDataSourceOptions,
} from 'commons/classes/drata-data-source.class';
import { DrataEntityManager } from 'commons/classes/drata-entity-manager.class';
import { InFlightLatchQueue } from 'commons/classes/inflight-latch-queue.class';
import { databaseConfig, TypeOrmConfig } from 'commons/configs/typeorm.config';
import { AccountStatus } from 'commons/enums/auth/account-status.enum';
import { createDataSource, DataSourceManager } from 'commons/factories/data-source.manager';
import { createTenantConnection, stringProcess, subjectProcess } from 'commons/helpers/cli.helper';
import { createSelfManagedTenantConnection } from 'commons/helpers/database-connection.helper';
import { getEnvironmentName } from 'commons/helpers/environment.helper';
import { startLightRun } from 'commons/helpers/lightrun.helper';
import { createUploadMemorySnapshot } from 'commons/helpers/memory-snapshot.helper';
import { stringToNumber } from 'commons/helpers/string.helper';
import { getTenantBatchSize } from 'commons/helpers/tenant.helper';
import { AccountEntitlementSetting } from 'entitlements/entities/account-entitlement-setting.entity';
import { AccountEntitlement } from 'entitlements/entities/account-entitlement.entity';
import { TrustCenter } from 'entitlements/trust-center/entities/trust-center.entity';
import { ExternalClientRouter } from 'external-client-router/entities/external-client-router.entity';
import { get, isEmpty, isNil, uniq } from 'lodash';
import path from 'path';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { PolloLogger } from 'pollo-logger/pollo.logger';
import { PublicApiKey } from 'public-api-keys/entities/public-api-key.entity';
import { ServiceGroupEntity } from 'service-user/entities/service-group.entity';
import { ServiceUserClientEntity } from 'service-user/entities/service-user-client.entity';
import { ServiceUserEntity } from 'service-user/entities/service-user.entity';
import { AuditorFrameworkTypeTemplate } from 'site-admin/entities/auditor-framework-type-template.entity';
import { ControlTemplate } from 'site-admin/entities/control-template.entity';
import { ControlTestTemplate } from 'site-admin/entities/control-test-template.entity';
import { EvidenceTemplate } from 'site-admin/entities/evidence-template.entity';
import { FeatureTemplate } from 'site-admin/entities/feature-template.entity';
import { FrameworkTemplate } from 'site-admin/entities/framework-template.entity';
import { MonitorTemplateCheckType } from 'site-admin/entities/monitor-template-check-type.entity';
import { MonitorTemplate } from 'site-admin/entities/monitor-template.entity';
import { PolicyMetadataTemplate } from 'site-admin/entities/policy-metadata-template.entity';
import { PolicySLATemplate } from 'site-admin/entities/policy-sla-template.entity';
import { PolicyTemplateContent } from 'site-admin/entities/policy-template-content.entity';
import { PolicyTemplate } from 'site-admin/entities/policy-template.entity';
import { ProfileDetailsTemplate } from 'site-admin/entities/profile-details-template.entity';
import { RequirementIndexTagTemplate } from 'site-admin/entities/requirement-index-tag-template.entity';
import { RequirementIndexTemplate } from 'site-admin/entities/requirement-index-template.entity';
import { RequirementTemplate } from 'site-admin/entities/requirement-template.entity';
import { SiteAdminComment } from 'site-admin/entities/site-admin-comment.entity';
import { SiteAdminRefreshToken } from 'site-admin/entities/site-admin-refresh-token.entity';
import { SiteAdminRole } from 'site-admin/entities/site-admin-role.entity';
import { SiteAdmin } from 'site-admin/entities/site-admin.entity';
import { TenantDatabaseHost } from 'site-admin/entities/tenant-database-host.entity';
import { And, FindManyOptions, In, MoreThanOrEqual, Not } from 'typeorm';
import { MysqlConnectionOptions } from 'typeorm/driver/mysql/MysqlConnectionOptions';
import { v4 as uuidv4 } from 'uuid';

/**
 * @deprecated - Use RestrictedTenantBackfill instead
 */
export abstract class TenantsBackfillBeta {
    readonly correlationId = uuidv4();

    protected exitCode = 0;

    protected isDryRun = false;

    protected logger = PolloLogger.logger(path.basename(__filename));

    private isMemoryProfilingEnabled = false;

    private latch: InFlightLatchQueue = new InFlightLatchQueue();

    private initialQueueSize: number;

    private tenPercentInterval: { queueSize: number; started: boolean } = {
        queueSize: -1,
        started: false,
    };

    private twentyFivePercentInterval: { queueSize: number; started: boolean } = {
        queueSize: -1,
        started: false,
    };

    constructor() {
        const logError = this.logger.error;

        this.logger.error = (...args: Parameters<typeof logError>) => {
            this.exitCode = 1;
            return logError.call(this.logger, ...args);
        };
    }

    async runTenantsBackfill(): Promise<number> {
        let globalConnection: DrataDataSource | null = null;

        await startLightRun([`tenant_backfill_${getEnvironmentName()}`]);

        this.setScriptOptions();

        const {
            accountIds,
            accountDomains,
            notAccountDomains,
            beginFrom,
            memoryProfiling,
            dryRun,
        } = program;

        const tenantBatchSize = 1; // https://drata.atlassian.net/browse/ENG-51131

        this.printProcessType(accountIds, accountDomains, notAccountDomains, beginFrom);

        dryRun && this.setDryRun();

        memoryProfiling && this.setMemoryProfiling();

        try {
            if (this.isMemoryProfilingEnabled) {
                await createUploadMemorySnapshot(this.correlationId, 'start');
            }

            this.domainAccountCheck(accountDomains);
            this.accountIdCheck(accountIds);

            globalConnection = await createDataSource('global', {
                ...databaseConfig,
                name: 'global',
                entities: [
                    Account,
                    AccountEntitlement,
                    AccountEntitlementSetting,
                    AccountSafeBaseSettings,
                    Audit,
                    AuditApiKey,
                    AuditFirm,
                    Auditor,
                    AuditorClient,
                    AuditorFrameworkAuditors,
                    AuditorFrameworkTypeTemplate,
                    ControlTemplate,
                    ControlTestTemplate,
                    Entry,
                    EvidenceTemplate,
                    ExternalClientRouter,
                    FeatureTemplate,
                    FrameworkTemplate,
                    MonitorTemplate,
                    MonitorTemplateCheckType,
                    PolicyMetadataTemplate,
                    PolicySLATemplate,
                    PolicyTemplate,
                    PolicyTemplateContent,
                    ProfileDetailsTemplate,
                    PublicApiKey,
                    RequirementIndexTagTemplate,
                    RequirementIndexTemplate,
                    RequirementTemplate,
                    ServiceGroupEntity,
                    ServiceUserClientEntity,
                    ServiceUserEntity,
                    SiteAdmin,
                    SiteAdminComment,
                    SiteAdminRefreshToken,
                    SiteAdminRole,
                    TenantDatabaseHost,
                    TenantRouter,
                    Token,
                    TrustCenter,
                ],
            });

            const accountRepo = globalConnection.getRepository(Account);
            const query = this.getQuery(accountIds, accountDomains, notAccountDomains, beginFrom);

            const accounts = await accountRepo.find({
                where: {
                    ...query,
                    status: Not(AccountStatus.SITE_ADMIN_DELETED),
                },
                order: {
                    domain: 'ASC',
                },
            });

            const hosts = uniq(accounts.map(account => account.getDatabaseHost()));
            this.latch.setBatchSize(tenantBatchSize).setCountDown(hosts).setQueue(accounts);

            if (this.isMemoryProfilingEnabled) {
                this.initialQueueSize = this.latch.getQueue().length;
                this.tenPercentInterval.queueSize = Math.floor(this.initialQueueSize * 0.1);
                this.twentyFivePercentInterval.queueSize = Math.floor(this.initialQueueSize * 0.25);
                this.logger.log(
                    PolloMessage.msg(
                        `Snapshot interval queue sizes:: 10%: ${this.tenPercentInterval.queueSize}, 25%: ${this.twentyFivePercentInterval.queueSize}`,
                        this.getClassName(),
                    )
                        .setMetadata({
                            initialQueueSize: this.initialQueueSize,
                            tenPercentQueueSize: this.tenPercentInterval.queueSize,
                            twentyFivePercentQueueSize: this.twentyFivePercentInterval.queueSize,
                        })
                        .setCorrelationId(this.correlationId),
                );
            }

            await this.beforeAllTenants(globalConnection);

            for (const host of hosts) {
                const batchSize = getTenantBatchSize(this.latch.getBatchSize(), accounts, host);

                for (let i = 0; i < batchSize; i++) {
                    void this.backfillTenantDb(host, globalConnection);
                }
            }

            await this.latch.toDrain();
        } catch (e) {
            this.logger.error(
                PolloMessage.msg('Backfills script failed', this.getClassName())
                    .setError(e)
                    .setCorrelationId(this.correlationId),
            );
        } finally {
            this.onFinally();
            if (!isNil(globalConnection) && globalConnection.isInitialized) {
                await this.disconnect(globalConnection);
            }
        }

        return this.exitCode;
    }

    async backfillTenantDb(host: string, globalConnection: DrataDataSource): Promise<void> {
        const account = this.latch.getQueue<Account>().find(acc => acc.getDatabaseHost() === host);

        if (isNil(account)) {
            this.latch.countdown();
        } else {
            if (this.isMemoryProfilingEnabled) {
                const numProcessedAccounts = this.initialQueueSize - this.latch.getQueue().length;
                if (
                    numProcessedAccounts === this.tenPercentInterval.queueSize &&
                    !this.tenPercentInterval.started
                ) {
                    this.tenPercentInterval.started = true;
                    await createUploadMemorySnapshot(this.correlationId, 'interval_10%');
                } else if (
                    numProcessedAccounts === this.twentyFivePercentInterval.queueSize &&
                    !this.twentyFivePercentInterval.started
                ) {
                    this.twentyFivePercentInterval.started = true;
                    await createUploadMemorySnapshot(this.correlationId, 'interval_25%');
                }
            }
            this.latch.take(account);

            await this.backfillSingleAccount(
                account,
                globalConnection,
                async (completed: Account) => {
                    this.latch.landed(completed);

                    try {
                        await this.backfillTenantDb(completed.getDatabaseHost(), globalConnection);
                    } catch (error) {
                        this.logger.error(
                            PolloAdapter.acct(
                                'Failed to backfill tenant db',
                                account,
                                this.backfillTenantDb.name,
                            )
                                .setError(error)
                                .setMetadata({
                                    databaseHost: account.getDatabaseHost(),
                                })
                                .setCorrelationId(this.correlationId),
                        );
                    }
                },
            );
        }
    }

    async backfillSingleAccount(
        account: Account,
        globalConnection: DrataDataSource,
        callback: any,
    ): Promise<void> {
        this.logger.log(
            PolloAdapter.acct('Running backfills for account', account, this.getClassName())
                .setMetadata({ databaseHost: account.getDatabaseHost() })
                .setCorrelationId(this.correlationId),
        );

        try {
            await this.runTenantBackfill(account, globalConnection);
            this.logger.log(
                PolloAdapter.acct('Done with backfills for account', account, this.getClassName())
                    .setMetadata({ databaseHost: account.getDatabaseHost() })
                    .setCorrelationId(this.correlationId),
            );
        } catch (e) {
            this.logger.error(
                PolloAdapter.acct(
                    'Failed to run backfills on account',
                    account,
                    this.getClassName(),
                )
                    .setError(e)
                    .setMetadata({ databaseHost: account.getDatabaseHost() })
                    .setCorrelationId(this.correlationId),
            );
        } finally {
            callback(account);
        }
    }

    protected abstract runTenantBackfill(
        account: Account,
        globalConnection: DrataDataSource,
    ): Promise<void>;

    /**
     * This method uses a non-memory-leaky pattern to create TypeORM connections to
     * tenants. Use this method to create a tenant connection for your backfill.
     *
     * DO NOT USE ANY OTHER METHODS TO CREATE A TENANT CONNECTION.
     *
     * @param account The account you want to create a tenant connection for.
     * @param typeOrmConfig The {@link TypeOrmConfig} configuration you want to use for the tenant connection.
     * Only use this parameter if you're sure of what you're doing. In most cases the default will be enough.
     * @returns Promise of a tenant connection.
     */
    protected getTenantConnection(
        account: Account,
        typeOrmConfig?: DrataDataSourceOptions,
    ): Promise<DrataDataSource> {
        const config =
            typeOrmConfig ?? TypeOrmConfig.createTypeOrmConnectionOptionsForTenantCli(account);
        this.logger.log(
            PolloAdapter.acct(`Establishing tenant connection for account`, account)
                .setMetadata({
                    databaseHost: account.getDatabaseHost(),
                })
                .setCorrelationId(this.correlationId),
        );
        return createSelfManagedTenantConnection(account.databaseName, config);
    }

    /**
     * This is called when --dry-run is set on the backfill CLI
     * To use this in a child class, this method needs to be implemented
     * so it can set this.isDryRun to true, and then apply logic to any
     * place in the backfill that saves/modifies data
     *
     * protected setDryRun() {
     *   this.isDryRun = true;
     * }
     */
    protected setDryRun(): void {
        const err = new Error('Dry Run is not implemented');
        this.logger.error(
            PolloMessage.msg(err.message).setError(err).setCorrelationId(this.correlationId),
        );
        process.exit();
    }

    private setMemoryProfiling(): void {
        this.log('Running backfill with memory profiling enabled');
        this.isMemoryProfilingEnabled = true;
    }

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    protected async beforeAllTenants(globalConnection?: DrataDataSource): Promise<void> {
        this.log('beforeAllTenants');
    }

    protected onFinally(): void {
        this.log('onFinally');
    }

    protected async disconnect(connection: DrataDataSource, account?: Account);

    protected async disconnect(connection: DrataDataSource, account?: Account): Promise<void> {
        if (isNil(connection)) {
            return;
        }

        try {
            const dataSourceManager = DataSourceManager.getInstance();

            this.logger.log(
                PolloAdapter.acct(`Destroying connection ${connection.id}`, account)
                    .setMetadata({
                        databaseHost: (connection.options as MysqlConnectionOptions).host,
                    })
                    .setCorrelationId(this.correlationId),
            );

            if (!isNil(connection)) {
                await dataSourceManager.destroyDataSource(connection.id);
                this.logger.log(
                    PolloAdapter.acct(
                        `Destroyed connection ${connection.id}`,
                        account,
                        this.getClassName(),
                    )
                        .setMetadata({
                            connectionName: connection.id,
                        })
                        .setCorrelationId(this.correlationId),
                );
            } else {
                this.logger.warn(
                    PolloAdapter.acct(
                        `Tried to disconnect from a connection that was un-instantiated`,
                        account,
                        this.getClassName(),
                    ).setCorrelationId(this.correlationId),
                );
            }
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct('Could not close connection', account, this.getClassName())
                    .setError(error)
                    .setMetadata({
                        connectionName: connection.id,
                        databaseHost: (connection.options as MysqlConnectionOptions).host,
                    })
                    .setCorrelationId(this.correlationId),
            );
        }
    }

    protected log(msg: string, account?: Account, domain?: string, data?: unknown): void {
        const pMsg = PolloMessage.msg(msg, this.constructor.name);

        if (!isNil(account)) {
            pMsg.setDomain(account.domain);
            pMsg.setMetadata({ databaseHost: account.getDatabaseHost() });
        }

        if (!isEmpty(domain) && typeof domain === 'string') {
            pMsg.setDomain(domain);
        }

        if (!isEmpty(data)) {
            pMsg.setIdentifier(data);
        }

        pMsg.setCorrelationId(this.correlationId);

        this.logger.log(pMsg);
    }

    protected error(
        msg: string,
        account?: Account,
        domain?: string,
        data?: unknown,
        err?: Error,
    ): void {
        const pMsg = PolloMessage.msg(msg, this.constructor.name);

        if (!isNil(account)) {
            pMsg.setDomain(account.domain);
            pMsg.setMetadata({ databaseHost: account.getDatabaseHost() });
        }

        if (!isEmpty(domain) && typeof domain === 'string') {
            pMsg.setDomain(domain);
        }

        if (!isEmpty(data)) {
            pMsg.setIdentifier(data);
        }

        if (!isNil(err)) {
            pMsg.setError(err);
        }

        pMsg.setCorrelationId(this.correlationId);

        this.exitCode = 1;
        this.logger.error(pMsg);
    }

    protected warn(msg: string, account?: Account, domain?: string, data?: unknown): void {
        const pMsg = PolloMessage.msg(msg, this.constructor.name);

        if (!isNil(account)) {
            pMsg.setDomain(account.domain);
            pMsg.setMetadata({ databaseHost: account.getDatabaseHost() });
        }

        if (!isEmpty(domain) && typeof domain === 'string') {
            pMsg.setDomain(domain);
        }

        if (!isEmpty(data)) {
            pMsg.setIdentifier(data);
        }

        pMsg.setCorrelationId(this.correlationId);

        this.logger.warn(pMsg);
    }

    /**
     *
     * Override if CLI options are needed for backfill
     *
     * @returns
     */
    protected setScriptOptions(): void {
        program
            .option(
                '-i, --account-ids [account-ids...]',
                'One or more account ids to limit the accounts to process (space-delimited)',
                stringProcess,
                [],
            )
            .option(
                '-s --subjects  [subjects...]',
                'Run the backfill subjects making any changes to the databases',
                subjectProcess,
                [],
            )
            .option(
                '-d, --account-domains [account-domains...]',
                'One or more account domains to limit the tests to run for (space-delimited)',
                stringProcess,
                [],
            )
            .option(
                '-n, --not-account-domains [not-account-domains...]',
                'One or more account domains to NOT run against (space-delimited)',
                stringProcess,
                [],
            )
            .option(
                '-b, --begin-from [begin-from...]',
                'Domain to begin from (eg: eaaa.com will skip all a-d domains)',
                stringProcess,
                [],
            )
            .option(
                '--tenant-batch-size <num>',
                'How many accounts from one db host should be backfilled in parallel (DISABLED for now)',
                stringToNumber,
                1,
            )
            .option(
                '-ra --run-all [boolean]',
                'Override the account limit and run for all',
                (value: string) => {
                    if (value?.toLocaleLowerCase() === 'true') {
                        return true;
                    }
                    if (!value || value?.toLocaleLowerCase() === 'false') {
                        return false;
                    }
                    // commander by default sets the flag to true. We need to actively throw when command has typos.
                    console.error('Run All flag is neither true or false:', value);
                    program.help(); // Display help message and exit
                },
                false,
            )
            .option(
                '-mp, --memory-profiling',
                'Enable taking memory snapshots at the start of the backfill, and after 10% and 25% of accounts have been processed',
            )
            .option('--dry-run', 'Run the backfill without making any changes to the databases')
            .parse(process.argv);
    }

    protected getClassName(): string {
        return this.constructor.name;
    }

    private printProcessType(
        accountIds: string[],
        accountDomains: string[],
        notAccountDomains: string[],
        beginFrom: string,
    ) {
        if (!isEmpty(accountIds)) {
            this.logger.log(
                PolloMessage.msg('Running backfills for specified account ids', this.getClassName())
                    .setIdentifier({ accountIds: accountIds })
                    .setCorrelationId(this.correlationId),
            );
        } else if (!isEmpty(accountDomains)) {
            this.logger.log(
                PolloMessage.msg(
                    'Running backfills for specified account domains',
                    this.getClassName(),
                )
                    .setIdentifier({ domains: accountDomains })
                    .setCorrelationId(this.correlationId),
            );
        } else if (!isEmpty(notAccountDomains)) {
            this.logger.log(
                PolloMessage.msg(
                    'Running backfills for all account domains EXCEPT ',
                    this.getClassName(),
                )
                    .setIdentifier({ domains: notAccountDomains })
                    .setCorrelationId(this.correlationId),
            );
        } else {
            this.logger.log(
                PolloMessage.msg(
                    'Processing with no target accounts, processing all!',
                    this.getClassName(),
                ).setCorrelationId(this.correlationId),
            );
        }
        if (!isEmpty(beginFrom)) {
            this.logger.log(
                PolloMessage.msg(`Beginning from domain`, this.getClassName()).setIdentifier({
                    domain: beginFrom,
                }),
            );
        }
    }

    private getQuery(
        accountIds: string[],
        accountDomains: string[],
        notDomains: string[],
        beginFrom: string,
    ): FindManyOptions<Account> {
        let query = {};

        if (isEmpty(beginFrom)) {
            beginFrom = '';
        }

        if (!isEmpty(accountIds)) {
            query = {
                id: In(accountIds),
                domain: MoreThanOrEqual(beginFrom),
            };
        } else if (!isEmpty(accountDomains)) {
            query = {
                domain: And(In(accountDomains), MoreThanOrEqual(beginFrom)),
            };
        } else if (!isEmpty(notDomains)) {
            query = {
                domain: And(Not(In(notDomains)), MoreThanOrEqual(beginFrom)),
            };
        } else {
            query = { domain: MoreThanOrEqual(beginFrom) };
        }

        return query;
    }

    protected async runBackfillWithTransaction(
        account: Account,
        backfill: (entityManager: DrataEntityManager) => Promise<void>,
    ): Promise<void> {
        let tenantConnection: DrataDataSource | null = null;

        try {
            tenantConnection = await createTenantConnection(account);

            await tenantConnection.transaction(backfill);
        } catch (error) {
            this.error(error.message, account);
        } finally {
            if (!isNil(tenantConnection) && tenantConnection.isInitialized) {
                await this.disconnect(tenantConnection);
            }
        }
    }

    private domainAccountCheck(accountDomains: string[]): void {
        const args: string[] = get(process, 'argv', []);
        if (args.includes('-d') && isEmpty(accountDomains)) {
            throw new Error(
                'Trying to run a backfill for accounts without specifying an actual domain or array of domains',
            );
        }
    }

    private accountIdCheck(accountIds: string[]): void {
        const args: string[] = get(process, 'argv', []);
        if (args.includes('-i') && isEmpty(accountIds)) {
            throw new Error(
                'Trying to run a backfill for account without specifying an actual id or array of ids',
            );
        }
    }
}
