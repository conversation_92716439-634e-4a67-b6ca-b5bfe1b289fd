import { AuthModes, ErrorCode, RegionType } from '@drata/enums';
import {
    Body,
    Controller,
    Get,
    HttpCode,
    HttpStatus,
    NotFoundException,
    Param,
    ParseUUIDPipe,
    Post,
    Query,
    Redirect,
    Req,
    Res,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import {
    ApiBadRequestResponse,
    ApiCreatedResponse,
    ApiGoneResponse,
    ApiHeaders,
    ApiNoContentResponse,
    ApiNotFoundResponse,
    ApiOkResponse,
    ApiOperation,
    ApiPreconditionFailedResponse,
    ApiQuery,
    ApiTags,
} from '@nestjs/swagger';
import { IdentitySource } from 'app/users/personnel/entities/identity-source.enum';
import { WorkOsSsoCallbackRequestDto } from 'app/work-os/dtos/work-os-sso-callback-request.dto';
import { AuthRefreshRequestDto } from 'auditors/dtos/auth-refresh-request.dto';
import { AuditorsCoreService } from 'auditors/services/auditors-core.service';
import { AuthRoute } from 'auth/auth.routes';
import { SNAPON_TEST_DOMAIN } from 'auth/constants/domains';
import { AuthAccountTypeResponseDto } from 'auth/dtos/auth-account-type-response.dto';
import { AuthEmailRequestDto } from 'auth/dtos/auth-email-request.dto';
import { AuthIdentityProviderResourcesResponseDto } from 'auth/dtos/auth-identity-provider-resources-response.dto';
import { AuthIdentityProviderTypeRequestDto } from 'auth/dtos/auth-identity-provider-type-request.dto';
import { IdentityProviderTypeResponseDto } from 'auth/dtos/auth-identity-provider-type-response.dto';
import { AuthLogoutResponseDto } from 'auth/dtos/auth-logout-response.dto';
import { AuthOktaCodeRequestDto } from 'auth/dtos/auth-okta-code-request.dto';
import { AuthOktaUrlRequestDto } from 'auth/dtos/auth-okta-url-request.dto';
import { AuthOktaUrlResponseDto } from 'auth/dtos/auth-okta-url-response.dto';
import { AuthQueryEmailRequestDto } from 'auth/dtos/auth-query-email-request.dto';
import { AuthRegionRequestDto } from 'auth/dtos/auth-region-request.dto';
import { AuthRegionResponseDto } from 'auth/dtos/auth-region-response.dto';
import { AuthResponseDto } from 'auth/dtos/auth-response.dto';
import { AuthSsoConnectionIdResponseDto } from 'auth/dtos/auth-sso-connection-id-response.dto';
import { IdentityProviderAuthResourcesResponseDto } from 'auth/dtos/identity-provider-auth-resources-response.dto';
import { AuthCodeRequestDto } from 'auth/entities/auth-code-request.dto';
import { RefreshToken } from 'auth/entities/refresh-token.entity';
import { AccountAuthType } from 'auth/enums/account-auth-type.enum';
import {
    makeAccessTokenForMagicLink,
    makeAccessTokenForMagicLinkActAs,
} from 'auth/helpers/auth.helper';
import { AccountsCoreService } from 'auth/services/accounts-core.service';
import { AuthSigningService } from 'auth/services/auth-signing.service';
import { AuthService } from 'auth/services/auth.service';
import { EntryCoreService } from 'auth/services/entry-core.service';
import { RefreshTokenService } from 'auth/services/refresh-token.service';
import { TenantRouterService } from 'auth/services/tenant-router.service';
import { AuthResponseType } from 'auth/types/auth-response-type';
import {
    AuthIdentityProviderResources,
    IdentityProviderAuthResources,
} from 'auth/types/identity-provider-auth-resources.types';
import { RefreshTokenPayloadType } from 'auth/types/refresh-token-payload.type';
import { BaseController } from 'commons/controllers/base.controller';
import { Dto } from 'commons/decorators/dto.decorator';
import { Area, ProductArea } from 'commons/decorators/product-area.decorator';
import { ExceptionResponseDto } from 'commons/dtos/exception-response.dto';
import { ApiResponse } from 'commons/enums/api-response.enum';
import { AccountStatus } from 'commons/enums/auth/account-status.enum';
import { IdentityProviderType } from 'commons/enums/auth/identity-provider-type.enum';
import { RefreshTokenType } from 'commons/enums/auth/refresh-token-type.enum';
import { TokenType } from 'commons/enums/auth/token-type.enum';
import { UnauthorizedException } from 'commons/exceptions/unauthorized.exception';
import { checkAccountAuthenticationState } from 'commons/helpers/account.helper';
import { getAuthCookieOptions } from 'commons/helpers/cookie.helper';
import {
    getServerRegion,
    isAuthTempEnvironment,
    isLocal,
    isTest,
} from 'commons/helpers/environment.helper';
import config from 'config';
import { Request, Response } from 'express';
import { get, isNil } from 'lodash';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { PolloLogger } from 'pollo-logger/pollo.logger';
import { ServiceUsersCoreService } from 'service-user/service-users-core.service';

@ApiTags('Auth')
@Controller()
@ProductArea(Area.GENERAL_AUTHENTICATION)
export class AuthController extends BaseController {
    constructor(
        private readonly authService: AuthService,
        private readonly entryCoreService: EntryCoreService,
        private readonly authSigningService: AuthSigningService,
        private readonly jwtService: JwtService,
        private readonly refreshTokenService: RefreshTokenService,
        private readonly auditorsCoreService: AuditorsCoreService,
        private readonly serviceUsersCoreService: ServiceUsersCoreService,
        private readonly tenantRouterService: TenantRouterService,
        private readonly accountsCoreService: AccountsCoreService,
    ) {
        super();
    }

    @ApiOperation({
        description: 'Sends magic link email for authorized users',
    })
    @ApiNoContentResponse({
        description: ApiResponse.NO_CONTENT,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @Post(AuthRoute.POST_GENERATE_MAGIC_LINK)
    @HttpCode(HttpStatus.NO_CONTENT)
    generateMagicLink(@Body() requestDto: AuthEmailRequestDto): Promise<void> {
        return this.authService.generateMagicLink(requestDto);
    }

    @ApiOperation({
        description: 'Lookup of the IdP provider type the org has for signing in',
    })
    @ApiCreatedResponse({
        type: IdentityProviderTypeResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @Dto(IdentityProviderTypeResponseDto)
    @Post(AuthRoute.POST_AUTH_TYPE)
    generateIdentityProviderType(
        @Body() requestDto: AuthIdentityProviderTypeRequestDto,
    ): Promise<IdentityProviderType> {
        return this.authService.generateIdentityProviderType(requestDto);
    }

    @ApiOperation({
        description: 'Get auth resources required to build OAUTH redirection URLs',
    })
    @ApiOkResponse({
        type: IdentityProviderAuthResourcesResponseDto,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @Dto(IdentityProviderAuthResourcesResponseDto)
    @Get(AuthRoute.GET_AUTH_RESOURCES)
    getIdentityProviderAuthResources(
        @Query() requestDto: AuthIdentityProviderTypeRequestDto,
    ): Promise<IdentityProviderAuthResources> {
        return this.authService.getIdentityProviderAuthResources(requestDto);
    }

    @ApiOperation({
        description: 'Get login resources for a given email',
    })
    @ApiOkResponse({
        type: AuthIdentityProviderResourcesResponseDto,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @Dto(AuthIdentityProviderResourcesResponseDto)
    @Get(AuthRoute.GET_LOGIN_RESOURCES)
    getLoginResources(
        @Query() requestDto: AuthIdentityProviderTypeRequestDto,
    ): Promise<AuthIdentityProviderResources> {
        return this.authService.getLoginResources(requestDto);
    }

    @ApiOperation({
        description: 'Get applicable account types for the given email',
    })
    @ApiOkResponse({
        type: AuthAccountTypeResponseDto,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponseDto,
    })
    @Dto(AuthAccountTypeResponseDto)
    @Get(AuthRoute.GET_ACCOUNT_TYPE)
    getAccountTypesForEmail(
        @Query() queryDto: AuthQueryEmailRequestDto,
    ): Promise<Array<AccountAuthType>> {
        return this.authService.getAccountTypesForEmail(queryDto.email);
    }

    @ApiOperation({
        description: 'Authenticate the user by the Google code',
    })
    @ApiCreatedResponse({
        description: ApiResponse.CREATED,
        type: AuthResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponseDto,
    })
    @ApiGoneResponse({
        description: ApiResponse.GONE,
        type: ExceptionResponseDto,
    })
    @ApiPreconditionFailedResponse({
        description: ApiResponse.PRECONDITION_FAILED_CONNECTION,
        type: ExceptionResponseDto,
    })
    @Dto(AuthResponseDto)
    @Post(AuthRoute.POST_GOOGLE)
    async googleAuth(
        @Body() requestDto: AuthCodeRequestDto,
        @Res({ passthrough: true }) response: Response,
    ): Promise<AuthResponseType> {
        const entry = await this.authService.authenticate(IdentitySource.GOOGLE, {
            code: requestDto.code,
            ssoConnectionId: requestDto.ssoConnectionId,
        });

        const { account } = await this.authService.getCustomerAccountAndUserByEntry(entry);
        if (isNil(account)) {
            throw new UnauthorizedException(ErrorCode.ACCOUNT_NOT_FOUND);
        }
        checkAccountAuthenticationState(account);
        if (await this.authService.isSSOConnected(account, entry.email)) {
            return;
        }

        await this.refreshTokenService.createRefreshTokenAndSetResponseCookie(
            response,
            entry,
            RefreshTokenType.TENANT,
        );

        return {
            accessToken: this.jwtService.sign({
                id: entry.id,
                accountId: get(account, 'id'),
            }),
        };
    }

    @ApiOperation({
        description: 'Authenticate the user by the Microsoft365 code',
    })
    @ApiCreatedResponse({
        description: ApiResponse.CREATED,
        type: AuthResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponseDto,
    })
    @ApiGoneResponse({
        description: ApiResponse.GONE,
        type: ExceptionResponseDto,
    })
    @ApiPreconditionFailedResponse({
        description: ApiResponse.PRECONDITION_FAILED_CONNECTION,
        type: ExceptionResponseDto,
    })
    @Dto(AuthResponseDto)
    @Post(AuthRoute.POST_MICROSOFT_365)
    async microsoft365Auth(
        @Body() requestDto: AuthCodeRequestDto,
        @Res({ passthrough: true }) response: Response,
    ): Promise<AuthResponseType> {
        return this.doMicrosoft365Auth(requestDto, response, IdentitySource.MICROSOFT_365);
    }

    @ApiOperation({
        description: 'Authenticate the user by the Microsoft 365 GCC High code',
    })
    @ApiCreatedResponse({
        description: ApiResponse.CREATED,
        type: AuthResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponseDto,
    })
    @ApiGoneResponse({
        description: ApiResponse.GONE,
        type: ExceptionResponseDto,
    })
    @ApiPreconditionFailedResponse({
        description: ApiResponse.PRECONDITION_FAILED_CONNECTION,
        type: ExceptionResponseDto,
    })
    @Dto(AuthResponseDto)
    @Post(AuthRoute.POST_MICROSOFT_365_GCC_HIGH)
    async microsoft365GccHighAuth(
        @Body() requestDto: AuthCodeRequestDto,
        @Res({ passthrough: true }) response: Response,
    ): Promise<AuthResponseType> {
        return this.doMicrosoft365Auth(requestDto, response, IdentitySource.MICROSOFT_365_GCC_HIGH);
    }

    @ApiOperation({
        description: 'Authenticate the auditor by the Google code',
    })
    @ApiCreatedResponse({
        description: ApiResponse.CREATED,
        type: AuthResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponseDto,
    })
    @ApiGoneResponse({
        description: ApiResponse.GONE,
        type: ExceptionResponseDto,
    })
    @ApiPreconditionFailedResponse({
        description: ApiResponse.PRECONDITION_FAILED_CONNECTION,
        type: ExceptionResponseDto,
    })
    @Dto(AuthResponseDto)
    @Post(AuthRoute.POST_GOOGLE_FOR_AUDITORS)
    async googleAuditorAuth(
        @Body() requestDto: AuthCodeRequestDto,
        @Res({ passthrough: true }) response: Response,
    ): Promise<AuthResponseType> {
        const entry = await this.authService.authenticate(
            IdentitySource.GOOGLE,
            { code: requestDto.code },
            true,
        );

        const auditor = await this.auditorsCoreService.getOathAuditorByEmail(entry);

        if (isNil(auditor)) {
            throw new UnauthorizedException(ErrorCode.ACCOUNT_NOT_FOUND);
        }

        await this.refreshTokenService.createRefreshTokenAndSetResponseCookie(
            response,
            entry,
            RefreshTokenType.TENANT,
            AuthModes.AUDITOR,
        );

        return this.authSigningService.buildAuditorAuthResponse(auditor.id, AuthModes.AUDITOR);
    }

    @ApiOperation({
        description: 'Authenticate the auditor by the Microsoft365 code',
    })
    @ApiCreatedResponse({
        description: ApiResponse.CREATED,
        type: AuthResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponseDto,
    })
    @ApiGoneResponse({
        description: ApiResponse.GONE,
        type: ExceptionResponseDto,
    })
    @ApiPreconditionFailedResponse({
        description: ApiResponse.PRECONDITION_FAILED_CONNECTION,
        type: ExceptionResponseDto,
    })
    @Dto(AuthResponseDto)
    @Post(AuthRoute.POST_MICROSOFT_365_FOR_AUDITOR)
    async microsoft365AuditorAuth(
        @Body() requestDto: AuthCodeRequestDto,
        @Res({ passthrough: true }) response: Response,
    ): Promise<AuthResponseType> {
        const entry = await this.authService.authenticate(
            IdentitySource.MICROSOFT_365,
            { code: requestDto.code },
            true,
        );
        const auditor = await this.auditorsCoreService.getOathAuditorByEmail(entry);

        if (isNil(auditor)) {
            throw new UnauthorizedException(ErrorCode.ACCOUNT_NOT_FOUND);
        }

        await this.refreshTokenService.createRefreshTokenAndSetResponseCookie(
            response,
            entry,
            RefreshTokenType.TENANT,
            AuthModes.AUDITOR,
        );

        return this.authSigningService.buildAuditorAuthResponse(auditor.id, AuthModes.AUDITOR);
    }

    @ApiOperation({
        description: 'Generate okta authentication url by the user email',
    })
    @ApiOkResponse({
        type: AuthResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponseDto,
    })
    @ApiGoneResponse({
        description: ApiResponse.GONE,
        type: ExceptionResponseDto,
    })
    @ApiPreconditionFailedResponse({
        description: ApiResponse.PRECONDITION_FAILED_CONNECTION,
        type: ExceptionResponseDto,
    })
    @Get(AuthRoute.GET_AUTH_OKTA_URL)
    @Dto(AuthOktaUrlResponseDto)
    oktaGenerateAuthUrl(
        @Query() requestDto: AuthOktaUrlRequestDto,
    ): Promise<AuthOktaUrlResponseDto> {
        return this.authService.generateOktaAuthUrl(
            requestDto.email,
            requestDto.domain,
            requestDto.urlType,
        );
    }

    @ApiOperation({
        description: 'Authenticate the user by the Okta code',
    })
    @ApiCreatedResponse({
        description: ApiResponse.CREATED,
        type: AuthResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponseDto,
    })
    @ApiGoneResponse({
        description: ApiResponse.GONE,
        type: ExceptionResponseDto,
    })
    @ApiPreconditionFailedResponse({
        description: ApiResponse.PRECONDITION_FAILED_CONNECTION,
        type: ExceptionResponseDto,
    })
    @Dto(AuthResponseDto)
    @Post(AuthRoute.POST_OKTA)
    async oktaAuth(
        @Body() requestDto: AuthOktaCodeRequestDto,
        @Res({ passthrough: true }) response: Response,
    ): Promise<AuthResponseType> {
        const entry = await this.authService.authenticate(IdentitySource.OKTA_IDENTITY, {
            code: requestDto.code,
            ssoConnectionId: requestDto.ssoConnectionId,
            domain: requestDto.domain,
            userDomain: requestDto.userDomain,
        });

        const { account } = await this.authService.getCustomerAccountAndUserByEntry(entry);
        checkAccountAuthenticationState(account);
        if (await this.authService.isSSOConnected(account, entry.email)) {
            return;
        }

        await this.refreshTokenService.createRefreshTokenAndSetResponseCookie(
            response,
            entry,
            RefreshTokenType.TENANT,
        );

        return {
            accessToken: this.jwtService.sign({
                id: entry.id,
                accountId: get(account, 'id'),
            }),
        };
    }

    @ApiOperation({ description: 'Authenticate with a magic link' })
    @ApiCreatedResponse({
        description: ApiResponse.CREATED,
        type: AuthResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponseDto,
    })
    @ApiGoneResponse({
        description: ApiResponse.GONE,
        type: ExceptionResponseDto,
    })
    @Dto(AuthResponseDto)
    @Post(AuthRoute.POST_MAGIC_LINK)
    async magicLink(
        @Param('token', ParseUUIDPipe) magicToken: string,
        @Res({ passthrough: true }) response: Response,
    ): Promise<AuthResponseType> {
        // find the token used to create the magic link
        const { entry, magicLinkTokenType, authMode } =
            await this.authService.magicLink(magicToken);

        // there is a special refresh token expiration used for the AGENT
        // all other refresh tokens use the same expiration and use the type TENANT
        const isAgent = magicLinkTokenType === TokenType.AGENT_MAGIC_LINK;
        const isAuditorReadOnly = magicLinkTokenType === TokenType.AUDITOR_READ_ONLY;
        const isServiceUserActAs = magicLinkTokenType === TokenType.ACT_AS_SERVICE_USER;

        let refreshToken: RefreshToken;

        if (!(isAuditorReadOnly || isServiceUserActAs)) {
            // We are not reissuing the refresh token for auditors or service users logging into
            // the tenants as we will be able to renew access tokens for these auth modes with the
            // original refresh token. That's why here the RefreshTokenType can be either TENANT or AGENT.
            ({ refreshToken } =
                await this.refreshTokenService.createRefreshTokenAndSetResponseCookie(
                    response,
                    entry,
                    isAgent ? RefreshTokenType.AGENT : RefreshTokenType.TENANT,
                    authMode,
                ));
        }

        //ok so all of the above created the cookie

        // there are only 4 types of magic links supported here
        // TokenType.MAGIC_LINK, //AuthModes.NORMAL
        // TokenType.AGENT_MAGIC_LINK, //AuthModes.NORMAL
        // TokenType.AUDITOR_MAGIC_LINK, //AuthModes.AUDITOR
        // TokenType.SERVICE_USER_MAGIC_LINK, //AuthModes.SERVICE_USER

        // for all these types, the account should either
        // 1 - be accessible via domain lookup
        // 2 - auditor or service user which do not specify the account in their auth response
        const account = await this.accountsCoreService.getAccountByDomain(entry.domain);

        const auditor = await this.auditorsCoreService.getAuditorByEntry(entry);
        const serviceUser = await this.serviceUsersCoreService.getServiceUserByEntry(entry);

        const accessToken = makeAccessTokenForMagicLink(
            magicLinkTokenType,
            entry,
            account?.id,
            auditor,
            serviceUser,
            this.authSigningService,
            refreshToken,
        );

        return {
            accessToken,
            mode: authMode,
        };
    }

    @ApiOperation({
        description: 'Authenticate with magic link, Act As or Read Only',
    })
    @ApiCreatedResponse({
        description: ApiResponse.CREATED,
        type: AuthResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponseDto,
    })
    @ApiGoneResponse({
        description: ApiResponse.GONE,
        type: ExceptionResponseDto,
    })
    @Dto(AuthResponseDto)
    @Post(AuthRoute.POST_ACT_AS_MAGIC_LINK)
    async magicLinkActAs(
        @Param('token', ParseUUIDPipe) magicToken: string,
        @Res({ passthrough: true }) response: Response,
    ): Promise<AuthResponseType> {
        // TODO [ENG-69133]: Remove this once we have verified the Guest Admin login flow is working correctly in production
        try {
            // the tenant router here is being used to determine which tenant the user wants to act as
            const tenantRouter = await this.tenantRouterService.getRoute(magicToken, null);
            // TODO [ENG-69133]: Remove conditional logging for sbs.snapon.com domain once Guest Admin login verification
            // is confirmed working correctly in production. These logs are temporary debugging aids for tracking
            // the authentication flow specifically for this domain. Should be removed after verification is complete.
            const tenantRouterMeta =
                tenantRouter !== null
                    ? { accountId: tenantRouter.accountId, key: tenantRouter.key }
                    : null;

            let shouldLog = tenantRouter?.account?.domain === SNAPON_TEST_DOMAIN;

            //TODO [ENG-69133]: Remove this once we have verified the Guest Admin login flow is working correctly in production
            if (shouldLog) {
                PolloLogger.logger().log(
                    PolloMessage.msg(`magicLinkActAs: tenantRouter result`).setMetadata({
                        tenantRouter: tenantRouterMeta,
                    }),
                );
            }

            // if we determine there is a tenant route for this magic link then we assume the user is attempting to act as this tenant
            const readOnlyAccountId = !isNil(tenantRouter) ? tenantRouter.accountId : null;

            //TODO [ENG-69133]: Remove this once we have verified the Guest Admin login flow is working correctly in production
            if (shouldLog) {
                PolloLogger.logger().log(
                    PolloMessage.msg(`magicLinkActAs: readOnlyAccountId: ${readOnlyAccountId}`),
                );
            }
            // the account to access is also determined in this function
            // if the account id from the router is given, then we use that account
            // otherwise we attempt to determine the account from the entry
            const { entry, magicLinkTokenType, account, authMode, siteAdmin } =
                await this.authService.magicLinkActAs(magicToken, readOnlyAccountId);

            const domainToCheck = tenantRouter?.account?.domain || account?.domain;
            shouldLog = domainToCheck === SNAPON_TEST_DOMAIN;

            //TODO [ENG-69133]: Remove this once we have verified the Guest Admin login flow is working correctly in production
            if (shouldLog) {
                PolloLogger.logger().log(
                    PolloMessage.msg(`magicLinkActAs: Successfully got auth data`).setMetadata({
                        authMode,
                        magicLinkTokenType,
                        entryId: entry?.id,
                        accountId: account?.id,
                        siteAdminId: siteAdmin?.id,
                    }),
                );
            }

            const isAgent = magicLinkTokenType === TokenType.AGENT_MAGIC_LINK;
            const isAuditorReadOnly = magicLinkTokenType === TokenType.AUDITOR_READ_ONLY;
            const isServiceUserActAs = magicLinkTokenType === TokenType.ACT_AS_SERVICE_USER;

            let refreshToken: RefreshToken;

            if (!(isAuditorReadOnly || isServiceUserActAs)) {
                ({ refreshToken } =
                    await this.refreshTokenService.createRefreshTokenAndSetResponseCookie(
                        response,
                        entry,
                        isAgent ? RefreshTokenType.AGENT : RefreshTokenType.TENANT,
                        authMode,
                    ));

                //TODO [ENG-69133]: Remove this once we have verified the Guest Admin login flow is working correctly in production
                if (shouldLog) {
                    PolloLogger.logger().log(
                        PolloMessage.msg(
                            `magicLinkActAs: Successfully created refresh token`,
                        ).setMetadata({
                            authMode,
                            magicLinkTokenType,
                            entryId: entry?.id,
                            accountId: account?.id,
                            siteAdminId: siteAdmin?.id,
                            refreshTokenId: refreshToken?.id,
                        }),
                    );
                }
            }

            let accountId: string;

            if (!isNil(account)) {
                accountId = account.id;
            } else {
                accountId = isNil(tenantRouter) ? null : tenantRouter.accountId;
            }

            const auditor = await this.auditorsCoreService.getAuditorByEntry(entry);
            const accessToken = makeAccessTokenForMagicLinkActAs(
                magicLinkTokenType,
                entry,
                accountId,
                auditor,
                this.authSigningService,
                siteAdmin ?? undefined,
            );

            // in these modes, the user has another primary account (service user/auditor/support user), but is acting as a user on a tenant
            // we add the target account to the tenant router for the new refresh token
            // when we trigger the refresh token flow, this account will be used for this entry
            if (authMode === AuthModes.ACT_AS || authMode === AuthModes.ACT_AS_READ_ONLY) {
                //TODO [ENG-69133]: Remove this once we have verified the Guest Admin login flow is working correctly in production
                if (shouldLog) {
                    PolloLogger.logger().log(
                        PolloMessage.msg(
                            `magicLinkActAs: Adding refresh token to router`,
                        ).setMetadata({
                            accountId: tenantRouter?.accountId,
                            refreshTokenId: refreshToken?.id,
                            authMode,
                        }),
                    );
                }

                await this.authService.addRefreshTokenToRouter(
                    tenantRouter.accountId,
                    refreshToken,
                );
            }
            await this.tenantRouterService.removeRouteByKey(magicToken);

            return {
                accessToken,
                mode: authMode,
            };
            //TODO [ENG-69133]: Remove this try catch logic once we have verified the Guest Admin login flow is working correctly in production
        } catch (error) {
            PolloLogger.logger().error(
                PolloMessage.msg(`magicLinkActAs: Error in auth process ${error.message}`)
                    .setMetadata({
                        error,
                    })
                    .setError(error),
            );
            throw error;
        }
    }

    // ------- TEMPORARIES --------

    // TODO: remove this once we get the site admin tool going
    @ApiOperation({
        summary: 'THIS IS JUST TEMPORARY',
        description: 'Generate JWT token by email',
    })
    @ApiOkResponse({
        type: AuthResponseDto,
    })
    @Dto(AuthResponseDto)
    @Get(AuthRoute.TEMP_GET_JWT)
    async tempGenerateJwtToken(
        @Query('email') email: string,
        @Res({ passthrough: true }) response: Response,
    ): Promise<AuthResponseType> {
        if (isAuthTempEnvironment()) {
            /**
             * getEntryByEmail is OK here
             * this function will get the entry with all of its linking accounts
             * since this is a temporary endpoint for testing purposes
             * we're OK to wait here for performance if we're using this
             */
            const entry = await this.entryCoreService.getEntryByEmail(email);

            await this.refreshTokenService.createRefreshTokenAndSetResponseCookie(
                response,
                entry,
                RefreshTokenType.TENANT,
            );

            const auditor = await this.auditorsCoreService.getAuditorByEntry(entry);

            const serviceUser = await this.serviceUsersCoreService.getServiceUserByEntry(entry);

            // do not user this endpoint for auditors or service users
            // use GET /auth/temp/auditor for auditor access
            // Service User's are not currently supported
            // if the tenant user is also an auditor or a service user, this endpoint does not support that behavior
            if (!isNil(auditor) || !isNil(serviceUser)) {
                // I have retained the existing function implementation because it is relied upon for beacon
                // we will modify this in ENG-47945
                // this should throw a not found exception for these types of users
                const mode = !isNil(auditor) ? AuthModes.AUDITOR : AuthModes.SERVICE_USER;

                return {
                    accessToken: this.jwtService.sign({
                        id: entry.id,
                        accountId: get(entry.primaryAccount(), 'id'),
                    }),
                    mode,
                };
            }

            return this.authSigningService.buildTenantUserAuthResponse(
                entry.id,
                get(entry.primaryAccount(), 'id'), // this usage of primary account makes sense as the TENANT user should only be associated to one account if they are truely a tenant user
                AuthModes.NORMAL, // mode is not currently set here because it is assumed to be AuthModes.NORMAL - other code currently treats NULL as AuthModes.NORMAL
            );
        } else {
            throw new NotFoundException();
        }
    }

    @ApiOperation({
        summary: 'THIS IS JUST TEMPORARY',
        description: 'Generate JWT token for an Service User by email',
    })
    @ApiOkResponse({
        type: AuthResponseDto,
    })
    @Dto(AuthResponseDto)
    @Get(AuthRoute.TEMP_GET_JWT_SERVICE_USER)
    async tempGenerateJwtTokenForServiceUser(
        @Query('email') email: string,
        @Res({ passthrough: true }) response: Response,
    ): Promise<AuthResponseType> {
        if (isAuthTempEnvironment()) {
            const serviceUser =
                await this.serviceUsersCoreService.getServiceUserByEmailNoFail(email);

            if (isNil(serviceUser)) {
                throw new UnauthorizedException(ErrorCode.SERVICE_USER_NOT_FOUND);
            }

            await this.refreshTokenService.createRefreshTokenAndSetResponseCookie(
                response,
                serviceUser.entry,
                RefreshTokenType.TENANT,
                AuthModes.SERVICE_USER,
            );

            return this.authSigningService.buildServiceUserAuthResponse(
                serviceUser,
                AuthModes.SERVICE_USER,
            );
        } else {
            throw new NotFoundException();
        }
    }

    // TODO: remove this once we get the site admin tool going
    @ApiOperation({
        summary: 'THIS IS JUST TEMPORARY',
        description: 'Generate JWT token by email',
    })
    @ApiOkResponse({
        type: AuthResponseDto,
    })
    @Dto(AuthResponseDto)
    @Get(AuthRoute.TEMP_GET_AGENT_JWT)
    async tempGenerateJwtAgentToken(
        @Query('email') email: string,
        @Res({ passthrough: true }) response: Response,
    ): Promise<AuthResponseType> {
        if (!isLocal() && !isTest()) {
            throw new NotFoundException();
        }

        /**
         * getEntryByEmail is OK here
         * this function will get the entry with all of its linking accounts
         * since this is a temporary endpoint for testing purposes
         * we're OK to wait here for performance if we're using this
         */
        const entry = await this.entryCoreService.getEntryByEmail(email);

        const { cookie } = await this.refreshTokenService.createRefreshTokenAndSetResponseCookie(
            response,
            entry,
            RefreshTokenType.AGENT,
        );

        const refreshToken = await get(this.jwtService.decode(cookie), 'id');

        if (entry.accounts.length > 1) {
            PolloLogger.logger().warn(
                PolloMessage.msg(
                    'There are more than one accounts and this temp ' +
                        'function does not distinguish between the different accounts, ' +
                        'you may see this return to the first account that is registered ' +
                        'instead of a second account.',
                ),
            );
        }

        return {
            accessToken: this.authSigningService.signAgentAccessToken(
                entry.id,
                refreshToken,
                entry.accounts[0].id,
            ),
        };
    }

    @ApiOperation({
        summary: 'This endpoint will return a new access token',
        description:
            'Generates a new access token based on a given refresh token received via httponly cookie',
    })
    @ApiOkResponse({
        type: AuthResponseDto,
    })
    @Dto(AuthResponseDto)
    @Get(AuthRoute.REFRESH_TOKEN)
    async refreshAccessTokenWithRefreshTokenCookie(
        @Req() request: Request,
        @Query() requestDto: AuthRefreshRequestDto,
    ): Promise<AuthResponseType> {
        const encodedCookie = request.cookies[config.get('jwt.cookie.webRefreshTokenKey')];
        if (isNil(encodedCookie)) {
            throw new UnauthorizedException(ErrorCode.BAD_ACCESS_TOKEN);
        }

        const decodedCookie = this.jwtService.decode(encodedCookie);
        let cookieAuthMode = get(decodedCookie, 'mode');

        const { entry, refreshToken } =
            await this.refreshTokenService.getEntrybyRefreshToken(encodedCookie);

        // check if the user has an higher level account which may be used based on the current AuthMode
        const auditor = await this.auditorsCoreService.getAuditorByEntry(entry);
        const serviceUser = await this.serviceUsersCoreService.getServiceUserByEntry(entry);

        let accountId: string | null = null;

        if (isNil(requestDto.accountId)) {
            const filteredAccounts =
                entry.accounts?.filter(acc => acc.status === AccountStatus.ACTIVE) || [];

            const account = filteredAccounts[0];
            if (isNil(auditor) && isNil(serviceUser)) {
                // We check whether it's a regular user trying to refresh the token without an account.
                // This happens when you open a new tab after the access token has expired.

                if (filteredAccounts.length > 1 || isNil(account)) {
                    // If the user is associated to multiple active accounts to or no account at all
                    // then it should not be issued a new token.
                    throw new UnauthorizedException(ErrorCode.ACCOUNT_NOT_FOUND);
                }

                accountId = account.id;
            }
            // else, when the user is an auditor or service user, we expect account id to be null
        } else {
            // validate entry/accountId association
            // accounts are eager loaded from the entry above in getEntrybyRefreshToken
            const hasAccountAccess = entry.accounts.some(
                account => account.id === requestDto.accountId,
            );

            if (!hasAccountAccess) {
                throw new UnauthorizedException(ErrorCode.ACCOUNT_NOT_FOUND);
            }

            accountId = requestDto.accountId;
        }

        const accessToken = await this.authService.getAccessTokenByRefreshCookieAuthMode(
            cookieAuthMode,
            entry,
            accountId,
            auditor,
            serviceUser,
            this.authSigningService,
            refreshToken?.siteAdmin,
            requestDto.isAuditorReadOnly,
        );

        if (cookieAuthMode === AuthModes.SERVICE_USER && !isNil(requestDto.accountId)) {
            cookieAuthMode = AuthModes.ACT_AS_SERVICE_USER;
        } else if (cookieAuthMode === AuthModes.AUDITOR && !isNil(requestDto.accountId)) {
            cookieAuthMode = requestDto.isAuditorReadOnly
                ? AuthModes.AUDITOR_READ_ONLY
                : AuthModes.NORMAL;
        }

        return {
            accessToken,
            mode: cookieAuthMode,
        };
    }

    @ApiOperation({
        summary: 'This endpoint will revoke the refresh token related to the current user',
        description: 'Revoke the latest refresh token for the current user',
    })
    @ApiOkResponse({
        type: AuthLogoutResponseDto,
    })
    @Dto(AuthLogoutResponseDto)
    @Get(AuthRoute.LOGOUT)
    async logout(
        @Req() request: Request,
        @Res({ passthrough: true }) response: Response,
    ): Promise<string> {
        const refreshTokenCookie = request.cookies[config.get('jwt.cookie.webRefreshTokenKey')];

        if (!isNil(refreshTokenCookie)) {
            const decodedPayload = this.jwtService.decode(refreshTokenCookie);
            const mode = get(decodedPayload, 'mode');

            if (mode === AuthModes.AUDITOR_READ_ONLY || mode === AuthModes.SERVICE_USER) {
                const payload = this.jwtService.decode(refreshTokenCookie);
                if (!isNil(payload)) {
                    const { id } = payload as RefreshTokenPayloadType;
                    const refreshToken = await this.refreshTokenService.getRefreshTokenById(id);
                    await this.tenantRouterService.removeRouteByKey(refreshToken.id);
                }
            }

            const refreshTokenId = get(decodedPayload, 'id', null);
            if (!isNil(refreshTokenId)) {
                try {
                    const refreshToken =
                        await this.refreshTokenService.getRefreshTokenById(refreshTokenId);
                    await this.authService.logout(refreshToken);
                } catch (error) {}
            }

            const result = await this.refreshTokenService.revokeRefreshToken(refreshTokenCookie);

            response.cookie(
                config.get('jwt.cookie.webRefreshTokenKey'),
                null,
                getAuthCookieOptions(),
            );

            return result;
        } else {
            throw new UnauthorizedException(ErrorCode.BAD_ACCESS_TOKEN);
        }
    }

    @ApiOperation({
        description: 'Handle the WorkOS callback after a user has authenticated with their IdP',
    })
    @Get(AuthRoute.GET_SSO_CALLBACK)
    @Redirect('', 301)
    // eslint-disable-next-line local-rules/swagger-must-specify-success-response
    async workOsCallback(
        @Query() requestDto: WorkOsSsoCallbackRequestDto,
    ): Promise<{ url: string }> {
        try {
            const region = getServerRegion();
            /**
             * At this point the users are already authenticated with their IdP
             * Get the user info from WorkOs
             * Generate a magic link token since we can't redirect with an actual access token on the url
             */
            const magicLinkToken = await this.authService.authenticateWithWorkOs(requestDto);

            /**
             * Redirect the user to the FE where a handler will automatically login
             * using the magic link
             */
            return {
                url: `${config.get('url.webApp')}/sso/redirect?magicLinkToken=${
                    magicLinkToken.id
                }&region=${region}`,
            };
        } catch (error) {
            /**
             * Redirect the user to the FE where a handler will automatically pickup the error
             */
            return {
                url: `${config.get('url.webApp')}/sso/redirect?error=${error.code}`,
            };
        }
    }

    @ApiOperation({
        description: 'Get SSO connection id for a user email',
    })
    @ApiOkResponse({
        type: AuthSsoConnectionIdResponseDto,
    })
    @Dto(AuthSsoConnectionIdResponseDto)
    @Get(AuthRoute.GET_SSO_CONNECTION_ID)
    getSsoConnectionId(@Query() queryDto: AuthQueryEmailRequestDto): Promise<string> {
        return this.authService.getSsoConnectionId(queryDto.email);
    }

    @ApiOperation({
        description: 'Authenticate the service user with Google oauth',
    })
    @ApiCreatedResponse({
        description: ApiResponse.CREATED,
        type: AuthResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponseDto,
    })
    @ApiGoneResponse({
        description: ApiResponse.GONE,
        type: ExceptionResponseDto,
    })
    @ApiPreconditionFailedResponse({
        description: ApiResponse.PRECONDITION_FAILED_CONNECTION,
        type: ExceptionResponseDto,
    })
    @Dto(AuthResponseDto)
    @Post(AuthRoute.POST_GOOGLE_FOR_SERVICE_USER)
    async googleServiceUserAuth(
        @Body() requestDto: AuthCodeRequestDto,
        @Res({ passthrough: true }) response: Response,
    ): Promise<AuthResponseType> {
        const entry = await this.authService.authenticate(
            IdentitySource.GOOGLE,
            { code: requestDto.code },
            false,
            true,
        );

        const serviceUser = await this.serviceUsersCoreService.getServiceUserByEmailNoFail(
            entry.email,
        );

        if (isNil(serviceUser)) {
            throw new UnauthorizedException(ErrorCode.ACCOUNT_NOT_FOUND);
        }

        await this.refreshTokenService.createRefreshTokenAndSetResponseCookie(
            response,
            entry,
            RefreshTokenType.TENANT,
            AuthModes.SERVICE_USER,
        );

        return {
            accessToken: this.jwtService.sign({
                id: serviceUser.entry.id,
            }),
            mode: AuthModes.SERVICE_USER,
        };
    }

    @ApiOperation({
        description: 'Authenticate the service user by the Microsoft 365 oauth',
    })
    @ApiCreatedResponse({
        description: ApiResponse.CREATED,
        type: AuthResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponseDto,
    })
    @ApiGoneResponse({
        description: ApiResponse.GONE,
        type: ExceptionResponseDto,
    })
    @ApiPreconditionFailedResponse({
        description: ApiResponse.PRECONDITION_FAILED_CONNECTION,
        type: ExceptionResponseDto,
    })
    @Dto(AuthResponseDto)
    @Post(AuthRoute.POST_MICROSOFT_365_FOR_SERVICE_USER)
    async microsoft365ServiceUserAuth(
        @Body() requestDto: AuthCodeRequestDto,
        @Res({ passthrough: true }) response: Response,
    ): Promise<AuthResponseType> {
        const entry = await this.authService.authenticate(
            IdentitySource.MICROSOFT_365,
            { code: requestDto.code },
            false,
            true,
        );

        const serviceUser = await this.serviceUsersCoreService.getServiceUserByEmailNoFail(
            entry.email,
        );

        if (isNil(serviceUser)) {
            throw new UnauthorizedException(ErrorCode.ACCOUNT_NOT_FOUND);
        }

        await this.refreshTokenService.createRefreshTokenAndSetResponseCookie(
            response,
            entry,
            RefreshTokenType.TENANT,
            AuthModes.SERVICE_USER,
        );

        return {
            accessToken: this.jwtService.sign({
                id: serviceUser.entry.id,
            }),
            mode: AuthModes.SERVICE_USER,
        };
    }

    @ApiOperation({
        summary: 'Get auth region',
        description: 'Retrieves the region of the user using the email and the environment',
    })
    @ApiOkResponse({
        description: ApiResponse.OK,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponseDto,
    })
    @Dto(AuthRegionResponseDto)
    @Post(AuthRoute.POST_AUTH_REGION)
    @HttpCode(HttpStatus.OK)
    getAuthRegion(@Body() requestDto: AuthRegionRequestDto): Promise<RegionType> {
        return this.authService.getRegion(requestDto);
    }

    @ApiOperation({
        description: `Get region base on the cname or trustId for Trust Center
        <br><br>`,
    })
    @ApiHeaders([
        {
            name: 'x-cname-host',
        },
    ])
    @ApiQuery({
        name: 'trustId',
        type: String,
        description: 'Trust Center UUID. Optional if x-cname-host provided.',
        required: false,
    })
    @ApiOkResponse({
        description: ApiResponse.OK,
        type: AuthRegionResponseDto,
    })
    @Dto(AuthRegionResponseDto)
    @Get([AuthRoute.GET_TRUST_CENTER_REGION, AuthRoute.GET_TRUST_CENTER_REGION_MIRROR])
    async getTrustCenterRegion(@Req() request: Request): Promise<RegionType> {
        return this.authService.getTrustCenterRegion(request);
    }

    private async doMicrosoft365Auth(
        requestDto: AuthCodeRequestDto,
        response: Response,
        identitySource: IdentitySource,
    ): Promise<AuthResponseType> {
        const entry = await this.authService.authenticate(
            identitySource,
            { code: requestDto.code, ssoConnectionId: requestDto.ssoConnectionId }, // authCode
            false, // isAuditor
            false, // isServiceUser
            requestDto.domain, // domain
        );

        const { account } = await this.authService.getCustomerAccountAndUserByEntry(entry);
        checkAccountAuthenticationState(account);
        if (await this.authService.isSSOConnected(account, entry.email)) {
            return;
        }

        await this.refreshTokenService.createRefreshTokenAndSetResponseCookie(
            response,
            entry,
            RefreshTokenType.TENANT,
        );

        return {
            accessToken: this.jwtService.sign({
                id: entry.id,
                accountId: get(account, 'id'),
            }),
        };
    }
}
