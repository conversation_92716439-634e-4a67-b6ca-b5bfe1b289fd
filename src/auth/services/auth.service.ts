// @spell-checker:words processlist PIMS APAC onmicrosoft
/* eslint-disable no-await-in-loop */
import { AuthenticationResult } from '@azure/msal-node';
import {
    AuthModes,
    ErrorCode,
    FrameworkTag,
    Language,
    RegionType,
    SafeBaseMigrationStatus,
} from '@drata/enums';
import {
    BadRequestException,
    ConflictException,
    GoneException,
    HttpException,
    HttpStatus,
    Injectable,
    InternalServerErrorException,
    NotFoundException as NestNotFoundException,
    PreconditionFailedException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { InjectRepository } from '@nestjs/typeorm';
import { ConnectionMetadata } from 'app/companies/connections/classes/connection-metadata.class';
import { ConnectionEntity } from 'app/companies/connections/entities/connection.entity';
import { ConnectionsRepository } from 'app/companies/connections/repositories/connections.repository';
import { ConnectionsCoreService } from 'app/companies/connections/services/connections-core.service';
import { ConnectionsSsoCoreService } from 'app/companies/connections/services/connections-sso-core.service';
import { ActAs } from 'app/companies/entities/act-as.entity';
import { AdminOnboarding } from 'app/companies/entities/admin-onboarding.entity';
import { Company } from 'app/companies/entities/company.entity';
import { Product } from 'app/companies/products/entities/product.entity';
import { FeatureService } from 'app/feature-toggling/services/feature.service';
import { FrameworkService } from 'app/frameworks/services/framework.service';
import { PermissionsSyncService } from 'app/permissions/services/permissions-sync.service';
import { QuestionnairesVendors } from 'app/questionnaires/entities/questionnaires-vendors.entity';
import { Questionnaires } from 'app/questionnaires/entities/questionnaires.entity';
import { Setting } from 'app/settings/entities/setting.entity';
import { TrustCenterMonitoringControl } from 'app/trust-center/entities/monitoring-controls.entity';
import {
    getCnameFromHeader,
    getTrustIdFromQuery,
} from 'app/trust-center/helpers/trust-center.helper';
import { RoleEntity } from 'app/users/entities/role.entity';
import { UserRole } from 'app/users/entities/user-role.entity';
import { User } from 'app/users/entities/user.entity';
import { IdentitySource } from 'app/users/personnel/entities/identity-source.enum';
import { WorkOsSupportedIndentitySource } from 'app/users/personnel/entities/work-os-supported-identity-source';
import { PersonnelCoreService } from 'app/users/personnel/services/personnel-core.service';
import { PolicyGracePeriodSLA } from 'app/users/policies/entities/policy-grace-period-sla.entity';
import { PolicyMetadata } from 'app/users/policies/entities/policy-metadata.entity';
import { PolicyP3MatrixSLA } from 'app/users/policies/entities/policy-p3-matrix-sla.entity';
import { PolicyWeekTimeFrameSLA } from 'app/users/policies/entities/policy-week-time-frame-sla.entity';
import { Policy } from 'app/users/policies/entities/policy.entity';
import { UserIdentityRepository } from 'app/users/repositories/user-identity-repository';
import { UserRoleRepository } from 'app/users/repositories/user-role.repository';
import { UserRepository } from 'app/users/repositories/user.repository';
import { UsersCoreService } from 'app/users/services/users-core.service';
import { WorkOsSsoCallbackRequestDto } from 'app/work-os/dtos/work-os-sso-callback-request.dto';
import { WorkOsWebhookDataRequestDto } from 'app/work-os/dtos/work-os-webhook-data-request.dto';
import { CommentEntity } from 'app/wysiwyg/entities/comment.entity';
import { Auditor } from 'auditors/entities/auditor.entity';
import { AuditorsCoreService } from 'auditors/services/auditors-core.service';
import { AuditorsOrchestrationService } from 'auditors/services/auditors-orchestration.service';
import { AuthRoute } from 'auth/auth.routes';
import { SNAPON_TEST_DOMAIN } from 'auth/constants/domains';
import { AuthEmailRequestDto } from 'auth/dtos/auth-email-request.dto';
import { AuthIdentityProviderTypeRequestDto } from 'auth/dtos/auth-identity-provider-type-request.dto';
import { AuthOktaUrlResponseDto } from 'auth/dtos/auth-okta-url-response.dto';
import { AccountSafeBaseSettings } from 'auth/entities/account-safebase-settings.entity';
import { Account } from 'auth/entities/account.entity';
import { Entry } from 'auth/entities/entry.entity';
import { RefreshToken } from 'auth/entities/refresh-token.entity';
import { TenantRouter } from 'auth/entities/tenant-router.entity';
import { Token } from 'auth/entities/token.entity';
import { AccountAuthType } from 'auth/enums/account-auth-type.enum';
import { hasSiteAdminDeletedAccountForEntry } from 'auth/helpers/auth.helper';
import { AuditorAuthenticatedEvent } from 'auth/observables/events/auditor-authenticated.event';
import { MagicLinkAuditorEvent } from 'auth/observables/events/magic-link-auditor.event';
import { MagicLinkServiceUserEvent } from 'auth/observables/events/magic-link-service-user.event';
import { MagicLinkEvent } from 'auth/observables/events/magic-link.event';
import { NewAccountEvent } from 'auth/observables/events/new-account.event';
import { UserAuthenticatedSignOutEvent } from 'auth/observables/events/user-authenticated-sign-out.event';
import { UserAuthenticatedEvent } from 'auth/observables/events/user-authenticated.event';
import { AccountsCoreService } from 'auth/services/accounts-core.service';
import { AccountsService } from 'auth/services/accounts.service';
import { AuthSigningService } from 'auth/services/auth-signing.service';
import { SIGNUP_DEFAULT_ENTITLEMENTS } from 'auth/services/auth.constants';
import { EntryCoreService } from 'auth/services/entry-core.service';
import { MULTIPLE_ROUTERS_ALLOWED_OKTA_DOMAINS } from 'auth/services/tenant-router.constants';
import { TenantRouterService } from 'auth/services/tenant-router.service';
import {
    AccountCreationInit,
    AccountCreationResult,
} from 'auth/types/account-creation-result.type';
import { AccountEntryResult } from 'auth/types/account-entry-result.type';
import { AuthCode } from 'auth/types/auth-code.type';
import { CreateNewAccountParams } from 'auth/types/create-new-account-params.type';
import { ErrorAccountCreation } from 'auth/types/error-account-creation.type';
import {
    AuthIdentityProviderResources,
    EnterpriseSSOProviderAuthResourcesType,
    IdentityProviderAuthResources,
    MicrosoftIdentityProviderAuthResourcesType,
    OktaIdentityProviderAuthResourcesType,
    ProviderResourcesType,
} from 'auth/types/identity-provider-auth-resources.types';
import { MagicLinkTokenOptions } from 'auth/types/magic-link-token-options.type';
import axios from 'axios';
import { CacheBusterWithPrefix, CacheEntityBuster } from 'cache/cache.decorator';
import { CACHE_IS_UP } from 'cache/cache.module';
import { SuperCacheAccountBuster, SuperCacheBuster } from 'cache/super-cache.decorator';
import { PolloAdapter } from 'commons/adapters/pollo.adapter';
import { type DrataDataSource } from 'commons/classes/drata-data-source.class';
import { DrataEntityManager } from 'commons/classes/drata-entity-manager.class';
import { TypeOrmConfig } from 'commons/configs/typeorm.config';
import { getProvisionFrameworkLockKey } from 'commons/constants/cache-lock-keys.constants';
import { IsDomainDuplicated } from 'commons/decorators/is-domain-duplicated.decorator';
import { ActAsType } from 'commons/enums/act-as-type.enum';
import { AdminOnboardingStepType } from 'commons/enums/admin-onboarding-step-type.enum';
import { AccountStatus } from 'commons/enums/auth/account-status.enum';
import { AccountType } from 'commons/enums/auth/account-type.enum';
import { OktaUrlType } from 'commons/enums/auth/auth-okta-url-type.enum';
import { ClientType } from 'commons/enums/auth/client-type.enum';
import { IdentityProviderType } from 'commons/enums/auth/identity-provider-type.enum';
import { ProviderType } from 'commons/enums/auth/provider-type.enum';
import { ConnectionState } from 'commons/enums/auth/state-type.enum';
import { TokenType } from 'commons/enums/auth/token-type.enum';
import { AutopilotPolicyName } from 'commons/enums/autopilot/autopilot-policy-name.enum';
import { Caches } from 'commons/enums/cache.enum';
import { CommentType } from 'commons/enums/comment-type.enum';
import { FormStatus } from 'commons/enums/questionnaires/form-status.enum';
import { ReportVisibilityType } from 'commons/enums/report-visibility-type.enum';
import { SettingTypeSeed } from 'commons/enums/settings/setting-type-seed.enum';
import { AuditLogEventType } from 'commons/enums/site-admin/audit-log-event-type.enum';
import { AuditLogTargetType } from 'commons/enums/site-admin/audit-log-target-type.enum';
import { SLAType } from 'commons/enums/sla-type.enum';
import { PolicyScope } from 'commons/enums/users/policies/policy-scope.enum';
import { Role } from 'commons/enums/users/role.enum';
import { VendorCategory } from 'commons/enums/vendor/vendor-category.enum';
import { VendorRisk } from 'commons/enums/vendor/vendor-risk.enum';
import { ConflictException as OurConflictException } from 'commons/exceptions/conflict.exception';
import { LockedException } from 'commons/exceptions/locked.exception';
import {
    NotFoundException,
    NotFoundException as OurNotFoundException,
} from 'commons/exceptions/not-found.exception';
import { PreconditionFailedException as OurPreconditionFailedException } from 'commons/exceptions/precondition-failed.exception';
import { UnauthorizedException } from 'commons/exceptions/unauthorized.exception';
import {
    createDataSource,
    destroyDataSource,
    getGlobalDataSource,
} from 'commons/factories/data-source.manager';
import { checkAccountAuthenticationState } from 'commons/helpers/account.helper';
import { asyncForEach } from 'commons/helpers/array.helper';
import {
    authConnectionAllowsMagicLink,
    connectionHasProviderType,
    getIdentityProviderType,
    isMultiIdpEntitlementAndFeatureFlagsEnabled,
} from 'commons/helpers/connection.helper';
import { createTenantConnectionFor } from 'commons/helpers/database-connection.helper';
import {
    createDatabase,
    dropDatabase,
    duplicateDatabase,
    duplicateEntry,
    lockTimeout,
} from 'commons/helpers/database.helper';
import { hasExpired } from 'commons/helpers/date.helper';
import { getDomainFromEmail, isExcludedDomain } from 'commons/helpers/domain.helper';
import { getValues } from 'commons/helpers/enum.helper';
import {
    getRegionTypeFromTenancyRegion,
    getServerRegion,
    isProd,
} from 'commons/helpers/environment.helper';
import { getLanguage } from 'commons/helpers/language.helper';
import {
    validateAuthenticationDomains,
    validateAuthenticationIdentities,
} from 'commons/helpers/okta.helper';
import { getHtmlFromPolicyTemplate } from 'commons/helpers/policy.helper';
import { publishTrustCenterMonitorControls } from 'commons/helpers/publish-trust-center-monitor-instance.helper';
import { omitProperties } from 'commons/helpers/security.helper';
import { randomString } from 'commons/helpers/string.helper';
import {
    hasRole,
    isSupportUser,
    verifyPersonnelIsActive,
    verifyUserIsActive,
} from 'commons/helpers/user.helper';
import { safeConfigGet } from 'commons/helpers/utility.helper';
import {
    createDefaultAutopilotSchedule,
    createDefaultCompaniesSettings,
    createDefaultRisks,
    createDefaultVendorsSettings,
    createDrataAsVendorDiscovered,
    createManualReminder,
    createManualReminderScheduleConfiguration,
    createPolicyResponsibilities,
    createScheduleFollowUpReminders,
    createScheduleQuestionnaires,
    createVendorDataAccessedOrProcessedItems,
} from 'commons/seeds/app-seeding.helper';
import { AppService } from 'commons/services/app.service';
import { defaultQuestionnaire } from 'commons/templates/typeform/default-questionnaire';
import { AccountIdType } from 'commons/types/account-id.type';
import { Nullable } from 'commons/types/nullable.type';
import config from 'config';
import { AuthenticatorAdapter } from 'dependencies/authenticator/adapters/authenticator.adapter';
import { OktaAuthenticatorProvider } from 'dependencies/authenticator/providers/okta-authenticator.provider';
import { AuthenticatorPayloadType } from 'dependencies/authenticator/types/authenticator-payload.type';
import { Crm } from 'dependencies/crm/crm';
import { CrmAccount } from 'dependencies/crm/types/crm-account';
import { FormProvider } from 'dependencies/forms/form-provider';
import { Uploader } from 'dependencies/uploader/uploader';
import { AccountAdaptiveAutomationMetadata } from 'entitlements/entities/account-adaptive-automation-metadata.entity';
import { AccountEntitlementService } from 'entitlements/entitlements.service';
import { EnableRiskManagementEntitlementEvent } from 'entitlements/risk-management/events/enable-risk-management-entitlement.event';
import { TrustCenter } from 'entitlements/trust-center/entities/trust-center.entity';
import { TrustCenterEntitlementService } from 'entitlements/trust-center/services/trust-center-entitlement.service';
import { Request } from 'express';
import { FeatureFlagCategory } from 'feature-flags/enums/feature-flag-category.enum';
import { FeatureFlag } from 'feature-flags/feature-flag.enum';
import { FeatureFlagService } from 'feature-flags/feature-flag.service';
import { IdentifyEvent } from 'feature-flags/observables/events/identify.event';
import { Dictionary, findIndex, get, has, isEmpty, isNil, pickBy, size, uniq } from 'lodash';
import moment from 'moment';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { ServiceUserValidationRequestDto } from 'service-user/dtos/service-user-validation-request.dto';
import { ServiceUserEntity } from 'service-user/entities/service-user.entity';
import { ServiceUsersCoreService } from 'service-user/service-users-core.service';
import { ServiceUsersOrchestrationService } from 'service-user/service-users-orchestration.service';
import { CreateAccountRequestDto } from 'site-admin/dtos/create-account-request.dto';
import { NewAccountRequestDto } from 'site-admin/dtos/new-account-request.dto';
import { TrustCenterMonitoringControlTemplate } from 'site-admin/entities/monitoring-controls-template.entity';
import { PolicyTemplate } from 'site-admin/entities/policy-template.entity';
import { SiteAdminComment } from 'site-admin/entities/site-admin-comment.entity';
import { SiteAdmin } from 'site-admin/entities/site-admin.entity';
import { AuditLogEvent } from 'site-admin/observables/events/audit-log.event';
import { FrameworkTemplateRepository } from 'site-admin/repositories/framework-template.repository';
import { PolicyMetadataTemplateRepository } from 'site-admin/repositories/policy-metadata-template.repository';
import { SiteAdminTenantDatabaseHostService } from 'site-admin/services/site-admin-tenant-database-host.service';
import { tenantWrapper, tenantWrapperWithOverride } from 'tenancy/contexts/tenant-wrapper';
import { In, Repository } from 'typeorm';
import { EntityNotFoundError } from 'typeorm/error/EntityNotFoundError';
import { format } from 'util';
@Injectable()
export class AuthService extends AppService {
    constructor(
        private readonly accountService: AccountsService, // needed by @IsDomainDuplicated
        private readonly accountsCoreService: AccountsCoreService,
        private readonly entryCoreService: EntryCoreService,
        private readonly uploader: Uploader,
        private readonly auth: AuthenticatorAdapter,
        private readonly auditorsOrchestrationService: AuditorsOrchestrationService,
        private readonly auditorsCoreService: AuditorsCoreService,
        private readonly featureService: FeatureService,
        @InjectRepository(FrameworkTemplateRepository)
        private readonly frameworkTemplateRepository: FrameworkTemplateRepository,
        @InjectRepository(PolicyTemplate)
        private readonly policyTemplateRepository: Repository<PolicyTemplate>,
        @InjectRepository(Token)
        private readonly tokenRepository: Repository<Token>,
        @InjectRepository(SiteAdminComment)
        private readonly siteAdminCommentRepository: Repository<SiteAdminComment>,
        @InjectRepository(PolicyMetadataTemplateRepository)
        private readonly policyMetadataTemplateRepository: PolicyMetadataTemplateRepository,
        @InjectRepository(TrustCenterMonitoringControlTemplate)
        private readonly trustCenterMonitoringControlTemplateRepository: Repository<TrustCenterMonitoringControlTemplate>,
        private readonly tenantRouterService: TenantRouterService,
        private readonly accountEntitlementService: AccountEntitlementService,
        private readonly tenantDatabaseHostService: SiteAdminTenantDatabaseHostService,
        private readonly formProvider: FormProvider,
        private readonly trustCenterFeatureService: TrustCenterEntitlementService,
        private readonly featureFlagService: FeatureFlagService,
        private readonly crm: Crm,
        private readonly jwtService: JwtService,
        private readonly connectionsCoreService: ConnectionsCoreService,
        private readonly frameworkService: FrameworkService,
        private readonly usersCoreService: UsersCoreService,
        private readonly permissionsSyncService: PermissionsSyncService,
        private readonly personnelCoreService: PersonnelCoreService,
        private readonly serviceUsersCoreService: ServiceUsersCoreService,
        private readonly serviceUsersOrchestrationService: ServiceUsersOrchestrationService,
        private readonly connectionsSsoCoreService: ConnectionsSsoCoreService,
    ) {
        super();
    }

    /**
     *
     * @param accountId
     * @param account
     * @param user
     * @param dto
     * @returns
     */
    // add CacheBusterWithPrefix
    @SuperCacheAccountBuster<void>({
        stores: [
            Caches.ACCOUNT,
            Caches.ACCOUNT_CONTRACT_DETAILS,
            Caches.COMPANY_DATA,
            Caches.COMPANY_PERSONNEL,
            Caches.CONTROL_TEMPLATE,
            Caches.ENTRY,
            Caches.FRAMEWORK,
            Caches.LIST_CONTROLS,
            Caches.PUBLIC_API_KEY,
            Caches.REQUIREMENTS,
            Caches.USER_BY_ENTRY_ID,
            Caches.WORKSPACE_ID,
        ],
        useArgs: 1,
    })
    async superCacheAccountBuster(account: Account): Promise<void> {}

    @CacheEntityBuster<void, ServiceUserEntity>({
        type: ServiceUserEntity,
        by: 'entry.id',
        store: Caches.ENTRY,
    })
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    async cacheServiceUserBuster(serviceUser: ServiceUserEntity): Promise<void> {}

    @CacheEntityBuster<void, User>({
        type: User,
        by: 'entryId',
        store: Caches.ENTRY,
    })
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    async cacheUserBuster(user: User): Promise<void> {}

    @CacheEntityBuster<void, Entry>({
        type: Entry,
        by: 'id',
        store: Caches.ENTRY,
    })
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    async cacheEntryBuster(entry: Entry): Promise<void> {}

    /**
     * Do the Auditor lookup first, then continue to the normal user checks
     *
     * Note: This function looks a little weird, that's because we
     * don't want to give ANYTHING away that this email exists or not
     *
     * @param dto
     */

    async generateMagicLink(dto: AuthEmailRequestDto): Promise<void> {
        const { email, isAuditorPortal, isServiceUser, isMultiIdpSsoEnabled } = dto;

        const auditor = isAuditorPortal
            ? await this.auditorsOrchestrationService.getAuditorWithEmail(email)
            : null;

        if (!isNil(auditor)) {
            const activeAccount =
                await this.accountsCoreService.getActiveAccountByEmailNoFail(email);

            // throw error if no active accounts are associated to auditor
            if (isNil(activeAccount)) {
                throw new OurNotFoundException(ErrorCode.ACCOUNT_NOT_FOUND);
            }

            const auditorAccount = auditor.entry.primaryAccount();

            const auditorUser = await this.auditorsOrchestrationService.getAuditorFromAccount(
                auditorAccount.id,
                auditor,
            );

            const emailLanguage = getLanguage(auditorUser.language, auditorAccount.language);

            this._eventBus.publish(new MagicLinkAuditorEvent(auditor, emailLanguage));
            return;
        }

        const serviceUser = isServiceUser
            ? await this.serviceUsersCoreService.getServiceUserByEmailNoFail(email)
            : null;

        if (!isNil(serviceUser)) {
            const activeAccount =
                await this.accountsCoreService.getActiveAccountByEmailNoFail(email);

            // throw error if no active accounts are associated to serviceUser
            if (isNil(activeAccount)) {
                throw new OurNotFoundException(ErrorCode.ACCOUNT_NOT_FOUND);
            }

            // get user language
            const serviceProviderAccount = serviceUser.entry.primaryAccount();

            const serviceProviderUser =
                await this.serviceUsersOrchestrationService.getServiceUserFromAccount(
                    serviceProviderAccount.id,
                    serviceUser,
                );

            const emailLanguage = getLanguage(
                serviceProviderUser.language,
                serviceProviderAccount.language,
            );

            this._eventBus.publish(new MagicLinkServiceUserEvent(serviceUser, emailLanguage));

            return;
        }

        const entry = await this.entryCoreService.getEntryByEmailNoFail(email);

        if (isNil(entry) || isEmpty(entry.accounts)) {
            this.log(
                `entry with email ${email} was not found or entry has no accounts.
                isEntryNull: ${isNil(entry)}
                accountsCount: ${entry ? entry.accounts?.length : 0}`,
            );

            return;
        }

        const { account, user } = await this.getCustomerAccountAndUserByEntry(entry);

        if (isNil(user) || isNil(account)) {
            this.log(
                `user with email ${email} or customer account by entry were not found.
                isUserNull: ${isNil(user)}
                isAccountNull: ${isNil(account)}`,
            );
            return;
        }

        if (account.isPocExpired()) {
            throw new LockedException(ErrorCode.POC_EXPIRED);
        }

        const authConnection = await tenantWrapper(account, () =>
            this.getActiveAuthConnection(account, isMultiIdpSsoEnabled),
        );

        if (!isNil(authConnection) && !authConnectionAllowsMagicLink(authConnection?.clientType)) {
            this.log(`Unable to log user ${email} in`);
            return;
        }

        if (hasRole(user, [Role.ADMIN]) && !isAuditorPortal) {
            this._eventBus.publish(new MagicLinkEvent(user, account));
        }
    }
    /**
     * Do the Auditor lookup first, then continue to the normal user checks
     *
     * Note: This function looks a little weird, that's because we
     * don't want to give ANYTHING away that this email exists or not
     *
     * @param dto
     */

    async generateMagicLinkMultiverse(
        dto: AuthEmailRequestDto,
        currentUser: User,
    ): Promise<{ token: Token; region: string }> {
        const { email, isAuditorPortal, isServiceUser } = dto;
        // Check user is not trying to generate magic link for another user
        if (currentUser.email !== email) {
            throw new OurConflictException(
                'User is not allowed to generate magic link for another user',
                ErrorCode.CONFLICT_MISMATCH_DATA,
            );
        }
        const region = String(getServerRegion());

        const auditor = isAuditorPortal
            ? await this.auditorsOrchestrationService.getAuditorWithEmail(email)
            : null;

        if (!isNil(auditor)) {
            const activeAccount =
                await this.accountsCoreService.getActiveAccountByEmailNoFail(email);

            // throw error if no active accounts are associated to auditor
            if (isNil(activeAccount)) {
                throw new OurNotFoundException(ErrorCode.ACCOUNT_NOT_FOUND);
            }

            const token = await this.generateMagicLinkToken({ email, isAuditor: true });

            return { token, region };
        }

        const serviceUser = isServiceUser
            ? await this.serviceUsersCoreService.getServiceUserByEmailNoFail(email)
            : null;

        if (!isNil(serviceUser)) {
            const activeAccount =
                await this.accountsCoreService.getActiveAccountByEmailNoFail(email);

            // throw error if no active accounts are associated to serviceUser
            if (isNil(activeAccount)) {
                throw new OurNotFoundException(ErrorCode.ACCOUNT_NOT_FOUND);
            }

            const token = await this.generateMagicLinkToken({ email, isServiceUser: true });

            return { token, region };
        }

        const entry = await this.entryCoreService.getEntryByEmailNoFail(email);

        if (isNil(entry) || isEmpty(entry.accounts)) {
            this.log(
                `entry with email ${email} was not found or entry has no accounts.
                isEntryNull: ${isNil(entry)}
                accountsCount: ${entry ? entry.accounts?.length : 0}`,
            );

            // Its all good in this case since its not a public endpoint
            throw new OurNotFoundException(ErrorCode.USER_NOT_FOUND);
        }

        const { account, user } = await this.getCustomerAccountAndUserByEntry(entry);

        if (isNil(user) || isNil(account)) {
            this.log(
                `user with email ${email} or customer account by entry were not found.
                isUserNull: ${isNil(user)}
                isAccountNull: ${isNil(account)}`,
            );
            // Its all good in this case since its not a public endpoint, tbh I dont see any case you will get here at all
            throw new OurNotFoundException(ErrorCode.USER_NOT_FOUND);
        }

        if (account.isPocExpired()) {
            throw new LockedException(ErrorCode.POC_EXPIRED);
        }

        // NOTE: We are not verifying the user has magic link access because this is to log everyone who is authorized to be login to go into multiverse
        const token = await this.generateMagicLinkToken({ email });

        return { token, region };
    }

    /**
     * @deprecated This should be removed once we have the Consolidated Login flow implemented for all
     * @param dto
     * @returns
     */
    async generateIdentityProviderType(
        dto: AuthIdentityProviderTypeRequestDto,
    ): Promise<IdentityProviderType> {
        const { email } = dto;
        const emailDomain = getDomainFromEmail(email);
        const account = await this.getAccountFromEmail(email, emailDomain);
        if (isNil(account)) {
            return IdentityProviderType.MAGIC_LINK;
        }
        return tenantWrapper(account, async () => {
            const user = await this.usersCoreService.findOneByEmailNoFail(email);

            this.logger.log(
                PolloAdapter.acct(`User with email "${email}" roles:`, account).setIdentifier({
                    roles: user?.roles,
                }),
            );

            if (!isNil(user) && hasRole(user, [Role.AUDITOR])) {
                throw new OurConflictException(
                    'User should log from the Auditor page',
                    ErrorCode.CONFLICT_AUDITOR_LOGIN,
                );
            }

            // multi domain check
            if (emailDomain !== account.domain) {
                const company = await this.companyRepository.findOneBy({});
                if (!company) {
                    throw new NotFoundException(ErrorCode.COMPANY_NOT_FOUND);
                }

                if (!company.multiDomain) {
                    throw new OurConflictException(
                        'Multi Domain is disabled for this account',
                        ErrorCode.CONFLICT_MULTI_DOMAIN_DISABLED,
                    );
                }
            }

            const authConnection = await this.getActiveAuthConnection(account);

            /**
             * Non-admins will be unable to authenticate into the application
             * if there's no identity provider connected.
             */
            const isAdmin = !isNil(user) ? hasRole(user, [Role.ADMIN]) : false;
            if (isNil(authConnection) && !isAdmin) {
                this.logger.warn(
                    PolloAdapter.acct(
                        `No identity provider found and user with email ${email} is not an admin. `,
                        account,
                    ),
                );
                throw new OurNotFoundException(ErrorCode.CONNECTION_NOT_FOUND);
            }

            const identityProviderType = getIdentityProviderType(authConnection);

            // this tells LaunchDarkly who the user is
            this._eventBus.publish(new IdentifyEvent(user, account));

            this.logger.log(
                PolloAdapter.acct(
                    `Returning identity provider with type ${IdentityProviderType[identityProviderType]} for user with email ${email}`,
                    account,
                ),
            );

            return identityProviderType;
        });
    }

    public async getIdentityProviderAuthResources(
        dto: AuthIdentityProviderTypeRequestDto,
    ): Promise<IdentityProviderAuthResources> {
        const { email } = dto;
        const emailDomain = getDomainFromEmail(email);
        const account = await this.getAccountFromEmail(email, emailDomain);
        if (isNil(account)) {
            return {
                identityProviderType: IdentityProviderType.MAGIC_LINK,
                providerResources: { tenantLoginEnabled: false },
            };
        }

        return tenantWrapper(account, async () => {
            const user = await this.usersCoreService.findOneByEmailNoFail(email);
            if (!user) {
                throw new NotFoundException(ErrorCode.ENTITY_NOT_FOUND);
            }
            this.logger.log(
                PolloAdapter.acct(`User with email "${email}" roles:`, account).setIdentifier({
                    roles: user?.roles,
                }),
            );

            const tenantLoginEnabled = await this.featureFlagService.evaluate(
                {
                    category: FeatureFlagCategory.NONE,
                    name: FeatureFlag.RELEASE_MICROSOFT_TENANT_LOGIN_PAGE,
                    defaultValue: false,
                },
                user,
                account,
            );
            let identityProviderAuthResources: IdentityProviderAuthResources = {
                identityProviderType: IdentityProviderType.MAGIC_LINK,
                providerResources: { tenantLoginEnabled },
            };
            if (!tenantLoginEnabled) {
                this.logger.log(
                    PolloAdapter.acct(
                        `Returning identity provider auth resources for user with email ${email}`,
                        account,
                    ).setIdentifier({
                        identityProviderAuthResources,
                    }),
                );
                return identityProviderAuthResources;
            }

            const company = await this.companyRepository.findOneBy({});
            if (!company) {
                throw new NotFoundException(ErrorCode.COMPANY_NOT_FOUND);
            }
            const authConnection = await this.getActiveAuthConnection(account);

            /**
             * Non-admins will be unable to authenticate into the application
             * if there's no identity provider connected.
             */
            const isAdmin = !isNil(user) ? hasRole(user, [Role.ADMIN]) : false;
            if (isNil(authConnection) && !isAdmin) {
                this.logger.warn(
                    PolloAdapter.acct(
                        `No identity provider found and user with email ${email} is not an admin. `,
                        account,
                    ),
                );
                throw new OurNotFoundException(ErrorCode.CONNECTION_NOT_FOUND);
            }

            const identityProviderType = getIdentityProviderType(authConnection);

            let providerResources: ProviderResourcesType = undefined;
            const microsoftProviders = [
                IdentityProviderType.MICROSOFT_365,
                IdentityProviderType.MICROSOFT_365_GCC_HIGH,
            ];
            if (microsoftProviders.includes(identityProviderType)) {
                providerResources = this.buildMicrosoftAuthProviderResources(company);
            }

            identityProviderAuthResources = {
                identityProviderType,
                providerResources: {
                    ...identityProviderAuthResources.providerResources,
                    ...providerResources,
                },
            };

            this.logger.log(
                PolloAdapter.acct(
                    `Returning identity provider auth resources for user with email ${email}`,
                    account,
                ).setIdentifier({
                    identityProviderAuthResources,
                }),
            );

            return identityProviderAuthResources;
        });
    }

    /**
     * Get the login resources for a given email.
     * This takes into account if you were a service user or an auditor
     * or a standard user (with or without an IdP).
     *
     * Until we have a global router, this should be called on every region
     *
     * @param dto
     * @returns
     */
    public async getLoginResources(
        dto: AuthIdentityProviderTypeRequestDto,
    ): Promise<AuthIdentityProviderResources> {
        const { email } = dto;

        this.checkForSupportUser(email);

        const { account, accountTypes } = await this.getAccountDetailsWithTypes(email);

        if (isNil(account) || account.status !== AccountStatus.ACTIVE) {
            this.logger.log(
                PolloMessage.msg(`Active account was not found as a Standard User`)
                    .setIdentifier({
                        email,
                        account,
                        accountTypes,
                    })
                    .setContext(this.constructor.name)
                    .setSubContext(this.getAccountDetailsWithTypes.name),
            );
            return this.getSSOMagicLinkConfig(accountTypes);
        }

        return tenantWrapper(account, async () => {
            const company = await this.companyRepository.findOneBy({});

            if (isNil(company)) {
                this.logger.error(
                    PolloAdapter.acct(`Company record is missing from Account`, account)
                        .setContext(this.constructor.name)
                        .setSubContext(this.getLoginResources.name),
                );
                return this.getSSOMagicLinkConfig(accountTypes);
            }
            // multi domain check
            const emailDomain = getDomainFromEmail(email);
            if (emailDomain !== account.domain && !company.multiDomain) {
                return this.getSSOMagicLinkConfig(accountTypes);
            }

            const user = await this.usersCoreService.findOneByEmailNoFail(email);

            if (isNil(user)) {
                this.logger.error(
                    PolloAdapter.acct(
                        `User record is missing from Account when we assumed it was there`,
                        account,
                    )
                        .setIdentifier({
                            email,
                        })
                        .setContext(this.constructor.name)
                        .setSubContext(this.getLoginResources.name),
                );
                return this.getSSOMagicLinkConfig(accountTypes);
            }

            /**
             * Check for bad data cases where a user is an auditor or service user.
             * This should have been filtered out earlier from getAccountDetailsWithTypes().
             */
            if (hasRole(user, [Role.AUDITOR, Role.SERVICE_USER])) {
                this.logger.error(
                    PolloAdapter.acct(
                        `Data Integrity Error: User is an auditor or service user for account when they shouldn't be`,
                        account,
                    )
                        .setIdentifier({
                            user,
                            accountTypes,
                        })
                        .setContext(this.constructor.name)
                        .setSubContext(this.getLoginResources.name),
                );
                return this.getSSOMagicLinkConfig(accountTypes);
            }

            /**
             * check if the multi-idp-sso Feature Flag is disabled
             * we want to eventually remove this block (and the flag)
             */
            if (
                !(await isMultiIdpEntitlementAndFeatureFlagsEnabled(
                    account,
                    this.featureFlagService,
                ))
            ) {
                const { identityProviderType, providerResources } =
                    await this.getLoginResourcesIdentityProviders(
                        account,
                        company,
                        user,
                        email,
                        accountTypes,
                    );
                return {
                    accountTypes,
                    identityProviders: [
                        {
                            identityProviderType,
                            providerResources,
                        },
                    ],
                };
            }

            /**
             * We filter out magic link here because we don't want to show it
             * as an option when there are other options.
             * If there are no other options, we default to magic link in the following if block
             */
            const identityProviders = (
                await this.getIdentityProviders(account, company, user)
            ).filter(
                identityProvider =>
                    identityProvider.identityProviderType !== IdentityProviderType.MAGIC_LINK,
            );

            if (isEmpty(identityProviders)) {
                this.logger.warn(
                    PolloAdapter.acct(`No IdP found and user with email ${email}`, account)
                        .setIdentifier({
                            email,
                            account,
                            accountTypes,
                        })
                        .setContext(this.constructor.name)
                        .setSubContext(this.getLoginResources.name),
                );

                return this.getSSOMagicLinkConfig(accountTypes);
            }

            // this tells LaunchDarkly who the user is
            this._eventBus.publish(new IdentifyEvent(user, account));

            // WAHOO - WE GOT HERE!
            accountTypes.push(AccountAuthType.STANDARD_ACCOUNT);

            return {
                accountTypes,
                identityProviders,
            };
        });
    }

    private checkForSupportUser(email: string): void {
        if (isSupportUser(email)) {
            throw new OurPreconditionFailedException(ErrorCode.SUPPORT_USER_NOT_SUPPORTED);
        }
    }

    /**
     * Retrieves the account and account types for the given email.
     *
     * @param email
     * @returns
     */
    private async getAccountDetailsWithTypes(email: string): Promise<{
        account: Account | null;
        accountTypes: Array<AccountAuthType>;
    }> {
        const accountTypes = new Array<AccountAuthType>();

        const entry = await this.entryCoreService.getEntryByEmailNoRelationsNoFail(email);

        if (isNil(entry)) {
            return { account: null, accountTypes };
        }

        const serviceUser = await this.serviceUsersCoreService.getServiceUserByEntryIdNoRelations(
            entry.id,
        );

        // if the email is found as a service user append it to the account types
        if (!isNil(serviceUser)) {
            accountTypes.push(AccountAuthType.SERVICE_ACCOUNT);
        }

        const auditor = await this.auditorsCoreService.getAuditorByEntryNoRelations(entry.id);

        // if the email is found as an auditor append it to the account types
        if (!isNil(auditor)) {
            accountTypes.push(AccountAuthType.AUDITOR_ACCOUNT);
        }

        const account = await this.accountsCoreService.getAccountByEntryForStandardUser(entry.id);

        return { account, accountTypes };
    }

    private async getIdentityProviders(
        account: Account,
        company: Company,
        user: User,
    ): Promise<IdentityProviderAuthResources[]> {
        const connections = await this.getEnabledSsoConnections(user.id);

        return Promise.all(
            connections.map(connection =>
                this.getIdentityProviderDetails({ connection, account, company, user }),
            ),
        );
    }

    private async getEnabledSsoConnections(userId: number): Promise<ConnectionEntity[]> {
        const identityConnections =
            await this.userIdentityRepository.getUserIdentitiesWithConnectionByUserId(userId);

        const enabledSsoConnections: ConnectionEntity[] = [];

        for (const identity of identityConnections) {
            const isSsoEnabled = this.connectionsSsoCoreService.isSsoConnectionEnabled(
                identity.connection,
            );
            if (isSsoEnabled) {
                enabledSsoConnections.push(identity.connection);
            }
        }

        return enabledSsoConnections;
    }

    private async getIdentityProviderDetails(params: {
        connection: ConnectionEntity;
        account: Account;
        company: Company;
        user: User;
    }): Promise<IdentityProviderAuthResources> {
        const { connection, account, company, user } = params;
        const identityProviderType = getIdentityProviderType(connection);
        const providerResources = await this.getProviderLoginResources(
            account,
            user,
            connection,
            identityProviderType,
            company,
        );

        return { identityProviderType, providerResources };
    }

    private async getProviderLoginResources(
        account: Account,
        user: User,
        connection: ConnectionEntity,
        identityProviderType: IdentityProviderType,
        company: Company,
    ): Promise<ProviderResourcesType> {
        let providerResources = {};

        // Add null check for connection
        if (!connection) {
            this.logger.warn(
                PolloAdapter.acct(
                    `No connection found for identity provider type ${IdentityProviderType[identityProviderType]}`,
                    account,
                ),
            );
            return providerResources;
        }

        switch (identityProviderType) {
            case IdentityProviderType.MICROSOFT_365:
            case IdentityProviderType.MICROSOFT_365_GCC_HIGH:
                const tenantLoginEnabled = await this.featureFlagService.evaluate(
                    {
                        category: FeatureFlagCategory.NONE,
                        name: FeatureFlag.RELEASE_MICROSOFT_TENANT_LOGIN_PAGE,
                        defaultValue: false,
                    },
                    user,
                    account,
                );

                providerResources = {
                    ...this.buildMicrosoftAuthProviderResources(company),
                    tenantLoginEnabled,
                };
                break;

            case IdentityProviderType.OKTA:
                providerResources = this.buildOktaAuthProviderResources(connection);
                break;

            case IdentityProviderType.ENTERPRISE_SSO:
                providerResources = this.buildEnterpriseSSOAuthProviderResources(connection);
                break;

            default:
                break;
        }

        return {
            ...providerResources,
            alias: connection.clientAlias,
            ssoConnectionId: connection.id,
        };
    }

    private buildEnterpriseSSOAuthProviderResources(
        connection: ConnectionEntity,
    ): EnterpriseSSOProviderAuthResourcesType {
        return {
            connectionId: get(
                connection.getMetadata(),
                'workOs.connection.id',
                this.getDummyConnectionId(),
            ),
        };
    }

    private buildOktaAuthProviderResources(
        connection: ConnectionEntity,
    ): OktaIdentityProviderAuthResourcesType {
        const authenticator = this.auth.getInstance(
            IdentitySource.OKTA_IDENTITY,
        ) as OktaAuthenticatorProvider;

        return authenticator.generateAuthUrlFromConnection(connection, null, OktaUrlType.LOGIN);
    }

    private buildMicrosoftAuthProviderResources(
        company: Company,
    ): MicrosoftIdentityProviderAuthResourcesType {
        return {
            domainName: company.domain,
        };
    }

    private async getActiveAuthConnection(
        account: Account,
        isMultiIdpSsoEnabled?: boolean,
    ): Promise<ConnectionEntity> {
        const providerTypes = [ProviderType.ENTERPRISE_SSO, ProviderType.IDENTITY];

        const activeAuthConnections =
            await this.connectionsRepository.getConnectionsByProviderTypesV2({
                providerTypes,
                connectedOnly: true,
                successfulOnly: true,
                state: ConnectionState.ACTIVE,
            });

        if (isMultiIdpSsoEnabled) {
            return activeAuthConnections.find(c =>
                this.connectionsSsoCoreService.isSsoConnectionEnabled(c),
            );
        }

        return (
            activeAuthConnections.find(c =>
                connectionHasProviderType(c, ProviderType.ENTERPRISE_SSO),
            ) ??
            activeAuthConnections.find(c => connectionHasProviderType(c, ProviderType.IDENTITY))
        );
    }

    private async getIdentitySourceBySsoConnectionId(
        ssoConnectionId: number,
    ): Promise<IdentitySource> {
        const connection = await this.getIdentityConnectionBySsoConnectionId(ssoConnectionId);
        return this.getIdentitySourceForConnection(connection);
    }

    private async getIdentityConnectionBySsoConnectionId(
        ssoConnectionId: number,
    ): Promise<ConnectionEntity> {
        const connection = await this.connectionsCoreService.getConnectionById(ssoConnectionId);
        if (isNil(connection)) {
            throw new NotFoundException(ErrorCode.CONNECTION_NOT_FOUND);
        }
        const isSsoEnabled = this.connectionsSsoCoreService.isSsoConnectionEnabled(connection);
        if (!isSsoEnabled) {
            throw new NotFoundException(ErrorCode.CONNECTION_NOT_FOUND);
        }

        return connection;
    }

    async getIdentityConnectionByAccount(account: Account): Promise<ConnectionEntity> {
        let connection = await this.connectionsCoreService.getEnterpriseSSOConnection();

        if (isNil(connection)) {
            connection = await this.connectionsCoreService.getSingleSignOnConnection();
        }

        // TODO: use getIdentityProviderConnections() when multi IdP is implemented and we fetch multiple ranked connections
        if (isNil(connection)) {
            connection = await this.connectionsCoreService.getIdentityProviderConnection();
        }

        if (isNil(connection)) {
            throw new NotFoundException(ErrorCode.CONNECTION_NOT_FOUND);
        }
        return connection;
    }

    getIdentitySourceForConnection(connection: ConnectionEntity): IdentitySource {
        // TODO: make the client type an identity source
        const identitySource = IdentitySource[IdentitySource[connection.clientType]];

        /**
         * To keep the ClientType on the connection referencing
         * the actual providers we keep a list of WorkOS supported
         * indentity sources that have to match IdentitySource.WORK_OS
         */
        if (WorkOsSupportedIndentitySource.includes(identitySource)) {
            return IdentitySource.WORK_OS;
        }

        // GOOGLE_OAUTH uses the same flow as GOOGLE
        if (connection.clientType === ClientType.GOOGLE_OAUTH) {
            return IdentitySource.GOOGLE;
        }

        return identitySource;
    }

    /**
     *
     * @param account
     */
    async getIdentitySourceByAccount(account: Account): Promise<IdentitySource> {
        const connection = await this.getIdentityConnectionByAccount(account);
        return this.getIdentitySourceForConnection(connection);
    }

    /**
     *
     * @param account
     *
     **/
    async isSSOConnected(account: Account, email: string): Promise<boolean> {
        return tenantWrapper(account, async () => {
            const ssoExists = await this.connectionsCoreService.getConnectionByProviderType(
                ProviderType.ENTERPRISE_SSO,
            );

            if (ssoExists) {
                this.log(`Unable to log user ${email} in`, account);
                return true;
            }
            return false;
        });
    }

    /**
     *
     * @param type
     * @param code
     * @returns
     */
    async authenticate(
        type: IdentitySource,
        authCode: AuthCode,
        isAuditor?: boolean,
        isServiceUser?: boolean,
        domain?: string,
    ): Promise<Entry> {
        let tenantRouterAccount: Account | null = null;
        let accToken: string | null = null;
        let metadata: ConnectionMetadata | null = null;

        this.log('start of authenticate process', null, {
            type,
            authCode,
            isAuditor,
            isServiceUser,
            domain,
        });

        if (type === IdentitySource.OKTA_IDENTITY) {
            const { userDomain } = authCode;

            if (!isEmpty(userDomain)) {
                const account = await this.accountsCoreService.getAccountByDomain(userDomain);

                this.log('getting tenant router', account);

                const tenantRouter = await this.tenantRouterService.getRouteByKeyAndAccount(
                    authCode.domain,
                    ClientType.OKTA_IDENTITY,
                    account,
                );

                this.log('got tenant router', account, { tenantRouter });

                if (isNil(tenantRouter)) {
                    throw new NotFoundException(ErrorCode.CONNECTION_NOT_FOUND);
                }
                tenantRouterAccount = tenantRouter.account;

                this.log('tenant router account set', account, {
                    tenantRouterAccount,
                });

                await tenantWrapper(tenantRouterAccount, async () => {
                    const identitySource = authCode.ssoConnectionId
                        ? await this.getIdentitySourceBySsoConnectionId(authCode.ssoConnectionId)
                        : await this.getIdentitySourceByAccount(tenantRouterAccount);
                    this.log('Identity source retrieved', account, {
                        identitySource,
                    });

                    if (IdentitySource.OKTA_IDENTITY !== identitySource) {
                        throw new NotFoundException(ErrorCode.CONNECTION_NOT_FOUND);
                    }

                    this.log('getting identity provider connection', account);

                    // TODO: use getIdentityProviderConnections() when multi IdP is implemented and we fetch multiple ranked connections
                    const connection = authCode.ssoConnectionId
                        ? await this.getIdentityConnectionBySsoConnectionId(
                              authCode.ssoConnectionId,
                          )
                        : await this.connectionsCoreService.getIdentityProviderConnection();

                    this.log('got identity provider connection', account);

                    metadata = connection.getMetadata();

                    authCode = {
                        ...authCode,
                        clientKey: metadata.clientKey,
                        clientSecret: metadata.clientSecret,
                    };
                });
            } else {
                this.log('no user domain, getting tenant routers');
                const tenantRouters = await this.tenantRouterService.getRoutesByKeyAndClient(
                    authCode.domain,
                    ClientType.OKTA_IDENTITY,
                );

                this.log('found tenant routers', null, { tenantRouters });
                if (
                    isEmpty(tenantRouters) ||
                    (tenantRouters.length > 1 &&
                        !MULTIPLE_ROUTERS_ALLOWED_OKTA_DOMAINS.includes(authCode.domain))
                ) {
                    throw new NotFoundException(ErrorCode.CONNECTION_NOT_FOUND);
                }

                this.log('getting accToken');

                const result = await this.getAccessTokenFromMultipleTenantRouters(
                    authCode,
                    tenantRouters,
                    type,
                    isAuditor,
                    isServiceUser,
                );
                accToken = result.accToken;
                metadata = result.metadata;
                tenantRouterAccount = result.account;
            }
        }

        this.log('getting access token');

        let accessToken = '';

        try {
            accessToken =
                accToken ??
                (await this.auth.getAccessToken(type, authCode, isAuditor, isServiceUser));
        } catch (error) {
            /**
             * https://drata.atlassian.net/browse/ENG-39481
             * catch 500 status and handle it
             */
            this.logger.log(
                PolloMessage.msg(`authenticate failed trying to get access token.`)
                    .setError(error)
                    .setContext(this.constructor.name)
                    .setSubContext(this.authenticate.name)
                    .setDomain(domain ?? '')
                    .setIdentifier({
                        type,
                        isAuditor,
                        isServiceUser,
                    }),
            );

            if (
                error.message.includes('invalid_grant') &&
                (type === IdentitySource.MICROSOFT_365 ||
                    type === IdentitySource.MICROSOFT_365_GCC_HIGH)
            ) {
                throw new UnauthorizedException(ErrorCode.MICROSOFT_365_INVALID_GRANT);
            }

            throw new UnauthorizedException(ErrorCode.TOKEN_EXPIRED);
        }

        if (isNil(accessToken)) {
            throw new UnauthorizedException(ErrorCode.BAD_CODE);
        }

        this.log('calling getUserInfo');

        const payload = await this.auth.getUserInfo(type, accessToken, authCode, domain);

        if (isNil(payload)) {
            throw new UnauthorizedException(ErrorCode.BAD_ACCESS_TOKEN);
        }

        this.log('getUserInfo response', null, {
            payload: omitProperties(payload, ['credentials', 'serializedToken']),
            isAuditor,
            isServiceUser,
        });

        if (type === IdentitySource.OKTA_IDENTITY) {
            validateAuthenticationDomains(metadata, payload);

            // Workaround for not adhering to Drata App profiles within auth/sync
            if (payload.drataEmail) {
                payload.email = payload.drataEmail;
            } else if (
                payload.login &&
                ['booking.com'].includes(getDomainFromEmail(payload.email))
            ) {
                // TODO: TEMPORARY HACK for profile email override, allow drataEmail to override
                payload.email = payload.login;
            }
        }

        if (isAuditor) {
            return this.authenticateAuditor(payload.email);
        }

        if (isServiceUser) {
            return this.getAuthenticateServiceUser(payload.email);
        }

        this.log('get authenticate user', null, payload.email);

        /**
         * https://drata.atlassian.net/browse/ENG-39898
         */
        if (type === IdentitySource.WORK_OS) {
            this.logger.log(
                PolloMessage.msg(`About to authenticate WorkOS user`, this.constructor.name)
                    .setSubContext(this.authenticate.name)
                    .setDomain(domain)
                    .setIdentifier({
                        payload,
                        tenantRouterAccount,
                        type,
                    }),
            );
        }
        return this.authenticateUser(
            payload,
            type,
            tenantRouterAccount,
            domain,
            authCode.ssoConnectionId,
        );
    }

    async authenticateWithWorkOs(dto: WorkOsSsoCallbackRequestDto): Promise<Token> {
        try {
            const { code, error: errorCode, error_description: errorDescription } = dto;

            if (!isNil(errorCode)) {
                throw new Error(errorDescription);
            }
            const { email } = await this.authenticate(IdentitySource.WORK_OS, {
                code,
            });

            return await this.generateMagicLinkToken({ email });
        } catch (error) {
            this.logger.error(
                PolloMessage.msg(error.message).setError(error).setContext(this.constructor.name),
            );

            throw new UnauthorizedException(ErrorCode.AUTHENTICATION_ERROR_WORK_OS);
        }
    }

    /**
     * Find the Token used to create the magic link url.
     *
     *
     * Determine the AuthMode for the user based on the magic link Token.type (TokenType)
     *
     *
     * If a user is (an auditor or a service user) AND a tenant user they must generate the appropriate type of magic link to login to the correct account
     *
     *
     * The type of account returned will be based on the magic link TokenType
     *
     * @param magicToken
     */
    async magicLink(magicToken: string): Promise<{
        entry: Entry;
        magicLinkTokenType: TokenType;
        authMode: AuthModes;
    }> {
        let token: Token;

        try {
            // find the NOT-USED token by the passed in magic link/token
            // used tokens are soft deleted
            // Only the "Magic links"
            token = await this.tokenRepository.findOneOrFail({
                where: {
                    id: magicToken,
                    type: In([
                        TokenType.MAGIC_LINK, //AuthModes.NORMAL
                        TokenType.AGENT_MAGIC_LINK, //AuthModes.NORMAL
                        TokenType.AUDITOR_MAGIC_LINK, //AuthModes.AUDITOR
                        TokenType.SERVICE_USER_MAGIC_LINK, //AuthModes.SERVICE_USER
                    ]),
                },
            });
        } catch (error) {
            if (error instanceof EntityNotFoundError) {
                const err = new NotFoundException(ErrorCode.MAGIC_TOKEN_NOT_FOUND);

                err.message = error.message;
                err.stack = error.stack;

                throw err;
            }

            throw error;
        }

        if (hasExpired(token.expiresAt)) {
            throw new NotFoundException(ErrorCode.TOKEN_EXPIRED);
        }

        const auditor = await this.auditorsCoreService.getAuditorByEntry(token.entry);

        if (!isNil(auditor) && token.type === TokenType.AUDITOR_MAGIC_LINK) {
            await this.tokenRepository.softRemove(token);
            return {
                entry: token.entry,
                magicLinkTokenType: token.type,
                authMode: AuthModes.AUDITOR,
            };
        }

        const serviceUser = await this.serviceUsersCoreService.getServiceUserByEntry(token.entry);

        if (!isNil(serviceUser) && token.type === TokenType.SERVICE_USER_MAGIC_LINK) {
            await this.tokenRepository.softRemove(token);
            return {
                entry: token.entry,
                magicLinkTokenType: token.type,
                authMode: AuthModes.SERVICE_USER,
            };
        }

        const { account, user } = await this.getCustomerAccountAndUserByEntry(token.entry);

        if (isNil(account)) {
            throw new UnauthorizedException(ErrorCode.ACCOUNT_NOT_FOUND);
        }

        checkAccountAuthenticationState(account);

        // this verifies the user has roles set
        // TODO: This will need to be modified for custom roles
        const verifyResults = verifyUserIsActive(user);

        if (!verifyResults.active) {
            throw new UnauthorizedException(verifyResults.reason);
        }

        await this.tokenRepository.softRemove(token);

        return {
            entry: token.entry,
            magicLinkTokenType: token.type,
            authMode: AuthModes.NORMAL,
        };
    }

    /**
     * These magic Links are used to swap the current user with another user they have access to
     * ex: Site admin user's accessing a tenant, Auditors access a tenant in read only mode,
     *     Service users acting as an admin on a tenant or returning to a service user from a tenant
     *
     * @param magicToken
     */
    async magicLinkActAs(
        magicToken: string,
        readOnlyAccountId?: string | null,
    ): Promise<{
        entry: Entry;
        magicLinkTokenType: TokenType;
        account: Account;
        authMode: AuthModes;
        siteAdmin: SiteAdmin | undefined;
    }> {
        let token: Token;

        try {
            // find the NOT-USED token by the passed in magic link/token
            // used tokens are soft deleted
            // the token is used to identify the entry id of the user to act as
            token = await this.tokenRepository.findOneOrFail({
                where: {
                    id: magicToken,
                    type: In([
                        TokenType.ACT_AS,
                        TokenType.ACT_AS_READ_ONLY,
                        TokenType.AUDITOR_READ_ONLY,
                        TokenType.ACT_AS_SERVICE_USER,
                    ]),
                },
            });
        } catch (error) {
            if (error instanceof EntityNotFoundError) {
                const err = new NotFoundException(ErrorCode.MAGIC_TOKEN_NOT_FOUND);

                err.message = error.message;
                err.stack = error.stack;

                throw err;
            }

            throw error;
        }

        if (hasExpired(token.expiresAt)) {
            throw new NotFoundException(ErrorCode.TOKEN_EXPIRED);
        }

        /**
         * If the account id is provided, that account is used to indicate this is the tenant the user wants to access
         *
         * If no account id is provided, getCustomerAccountAndUserByEntry does it's best to guess
         * it searches the accounts associated with this entry for a user with a matching email and some roles
         *
         * If the magic link is for a read only mode login, the account id
         * must be used to differentiate to which tenant's account log into.
         */
        let account: Account;
        let user: User | null;

        //TODO [ENG-69133]: Remove this once we have verified the Guest Admin login flow is working correctly in production

        const domain = token.entry.accounts?.[0]?.domain || token.entry.domain || '';
        const isSnapOn = domain.includes(SNAPON_TEST_DOMAIN);

        if (!isNil(readOnlyAccountId)) {
            account = await this.accountsCoreService.getActiveAccountByEmailAndAccount(
                token.entry.email,
                readOnlyAccountId,
            );
            // TODO [ENG-69133]: remove this block when verify work on ticket and work on production
            user = await tenantWrapper(account, async () =>
                this.usersCoreService.findOneByEmailNoFail(token.entry.email),
            );

            if (isSnapOn) {
                this.logger.warn(
                    PolloMessage.msg(
                        `magicLinkActAs: SnapOn user with readOnlyAccountId`,
                    ).setIdentifier({
                        userId: user?.id,
                        userEmail: user?.email,
                        accountId: account?.id,
                        email: token.entry.email,
                    }),
                );
            }

            if (isNil(account) || isNil(user)) {
                throw new GoneException();
            }
        } else {
            const result = await this.getCustomerAccountAndUserByEntry(token.entry);
            account = result.account;
            user = result.user;

            if (isSnapOn) {
                this.logger.warn(
                    PolloMessage.msg(
                        `magicLinkActAs: SnapOn flow without readOnlyAccountId`,
                    ).setIdentifier({
                        email: token.entry.email,
                        accountId: account?.id,
                        userId: user?.id,
                        fallback: isNil(account) || isNil(user),
                    }),
                );
            }

            if (isNil(account) || isNil(user)) {
                throw new GoneException();
            }

            const verifyResults = verifyUserIsActive(user);

            if (!verifyResults.active) {
                throw new UnauthorizedException(verifyResults.reason);
            }
        }

        //TODO [ENG-69133]: Remove this once we have verified the Guest Admin login flow is working correctly this is only for testing
        const isGuestAdmin =
            user && hasRole(user, [Role.ADMIN]) && hasRole(user, [Role.SERVICE_USER]);
        if (isGuestAdmin) {
            try {
                const personnel = await this.personnelCoreService.getPersonnelByUserEmail(
                    user.email,
                );
                if (isNil(personnel)) {
                    this.logger.warn(
                        PolloMessage.msg(`Guest Admin without personnel`).setIdentifier({
                            email: user.email,
                            userId: user.id,
                            domain,
                        }),
                    );
                }
            } catch (error) {
                this.logger.warn(
                    PolloMessage.msg(`Error checking personnel for Guest Admin`)
                        .setIdentifier({ email: user.email })
                        .setError(error),
                );
            }
        }

        // soft delete the token to indicate it has been used
        await this.tokenRepository.softRemove(token);

        const siteAdmin = token?.siteAdmin ?? undefined;
        const authMode = this.getAuthModeFromTokenType(token.type);

        if (isSnapOn) {
            this.logger.warn(
                PolloMessage.msg(`magicLinkActAs: SnapOn login completed`).setIdentifier({
                    entryId: token.entry.id,
                    accountId: account.id,
                    userId: user?.id,
                    userEmail: user?.email,
                    authMode,
                    tokenType: token.type,
                }),
            );
        }

        return {
            entry: token.entry,
            magicLinkTokenType: token.type,
            account,
            authMode: authMode,
            siteAdmin,
        };
    }

    getAuthModeFromTokenType(tokenType: TokenType): AuthModes {
        switch (tokenType) {
            case TokenType.AUDITOR_READ_ONLY:
                return AuthModes.AUDITOR_READ_ONLY;

            case TokenType.ACT_AS:
                return AuthModes.ACT_AS;

            case TokenType.ACT_AS_SERVICE_USER:
                return AuthModes.ACT_AS_SERVICE_USER;

            case TokenType.ACT_AS_READ_ONLY:
                return AuthModes.ACT_AS_READ_ONLY;

            default:
                throw new UnauthorizedException(ErrorCode.TOKEN_NOT_FOUND);
        }
    }

    /**
     * This method will iterate through all the entry accounts to find the specific
     * tenant and user. This can be used to differentiate between the accounts where the
     * entry is an actual customer from the accounts in which it is an auditor.
     * Returns undefined in case the entry doesn't belong to one of our customer accounts
     * @param {Entry} entry the global entry
     * @returns {Account, User} returns the associated tenant Account and User
     */
    async getCustomerAccountAndUserByEntry(
        entry: Entry,
    ): Promise<{ account: Account | null; user: User | null }> {
        const filteredAccounts = entry.accounts.filter(
            account => account.status !== AccountStatus.SITE_ADMIN_DELETED,
        );

        for (const account of filteredAccounts) {
            const user = await this.getAccountUserByEntry(account, entry);
            if (isNil(user)) {
                this.logger.error(
                    PolloAdapter.acct('User not found', account)
                        .setContext(this.constructor.name)
                        .setSubContext('getCustomerAccountAndUserByEntry')
                        .setIdentifier({
                            email: entry.email,
                            userId: entry.id,
                        }),
                );
                return { account: null, user: null };
            }

            // TODO: Custom Roles will not support this
            if (
                hasRole(user, [Role.ADMIN, Role.TECHGOV, Role.EMPLOYEE]) &&
                !hasRole(user, [Role.SERVICE_USER])
            ) {
                return { account, user };
            }
        }

        this.logger.warn(
            PolloMessage.msg(`No account or user found for entry with email ${entry.email}`),
        );

        return { account: null, user: null };
    }

    /**
     * This method will iterate through all the entry accounts to find the specific
     * customer account. This can be used to differentiate between the accounts where the
     * entry is an actual customer from the accounts in which it is an auditor.
     * Returns undefined in case the entry doesn't belong to one of our customer accounts
     *
     * @param {Entry} entry the global entry
     * @returns {Account} returns the associated tenant Account
     */
    async getCustomerAccountByEntry(entry: Entry): Promise<Account> {
        const filteredAccounts = entry.accounts.filter(
            account => account.status !== AccountStatus.SITE_ADMIN_DELETED,
        );

        for (const account of filteredAccounts) {
            const user = await this.getAccountUserByEntry(account, entry);
            if (isNil(user)) {
                continue;
            }
            if (
                !hasRole(user, [Role.SERVICE_USER]) &&
                hasRole(user, [Role.ADMIN, Role.TECHGOV, Role.EMPLOYEE])
            ) {
                // this means the user exists and it is not an auditor neither service user.
                return account;
            }
        }
        return null;
    }

    /**
     * This method will iterate through all the entry accounts to find the specific
     * auditor account. This can be used to differentiate between the accounts where the
     * entry is an actual auditor from the accounts in which it is a customer.
     * Returns undefined in case the entry doesn't belong to one of our customer accounts
     * @param {Entry} entry the global entry
     * @returns {Account} returns the associated tenant Account
     */
    async getAuditorAccountByEntry(entry: Entry): Promise<Account> {
        const filteredAccounts = entry.accounts.filter(
            account => account.status !== AccountStatus.SITE_ADMIN_DELETED,
        );

        for (const account of filteredAccounts) {
            const user = await this.getAccountUserByEntry(account, entry);

            if (isNil(user)) {
                continue;
            }
            if (hasRole(user, [Role.AUDITOR])) {
                this._eventBus.publish(new AuditorAuthenticatedEvent(account, entry.id));
                // this means the user exists and it is or was an auditor.
                return account;
            }
        }
        return null;
    }

    /**
     *
     * @param accountId
     * @param refreshToken
     */
    @SuperCacheBuster<void>({
        stores: [
            Caches.ACCOUNT,
            Caches.ACCOUNT_CONTRACT_DETAILS,
            Caches.COMPANY_DATA,
            Caches.COMPANY_PERSONNEL,
            Caches.CONTROL_TEMPLATE,
            Caches.ENTRY,
            Caches.FRAMEWORK,
            Caches.LIST_CONTROLS,
            Caches.PUBLIC_API_KEY,
            Caches.REQUIREMENTS,
            Caches.USER_BY_ENTRY_ID,
            Caches.WORKSPACE_ID,
        ],
        useArgs: 1,
        bustAccount: true,
    })
    async addRefreshTokenToRouter(
        accountId: AccountIdType,
        refreshToken: RefreshToken,
    ): Promise<void> {
        const account = await this.accountsCoreService.getAccountById(accountId);
        if (isNil(account)) {
            throw new NotFoundException(ErrorCode.ACCOUNT_NOT_FOUND);
        }

        if (account.status !== AccountStatus.ACTIVE) {
            throw new GoneException();
        }

        await this.tenantRouterService.addRoute(account, refreshToken.id);
    }

    /**
     *
     * @param MagicLinkTokenOptions
     */
    async generateMagicLinkToken(options: MagicLinkTokenOptions): Promise<Token> {
        // set default values for all the optional options
        let tokenType = TokenType.MAGIC_LINK;

        if (options.isAuditor) {
            tokenType = TokenType.AUDITOR_MAGIC_LINK;
        } else if (options.isServiceUser) {
            tokenType = TokenType.SERVICE_USER_MAGIC_LINK;
        }

        const defaultOptions = {
            isAuditor: false,
            isServiceUser: false,
            tokenType,
        };

        // Merge the default optional values with the retrieved ones
        const optionsToApply = {
            ...defaultOptions,
            ...options,
        };

        // set the expires time based on config (in hours)
        const expiresAt = moment(new Date())
            .add(config.get('tokens.magicLinkExpiration'), 'hour')
            .toDate();

        // get the entry from email
        const entry = await this.entryCoreService.getEntryWithoutRelationsByEmailOrFail(
            options.email,
        );

        // create the new token
        const token = new Token();
        token.type = optionsToApply.tokenType;
        token.expiresAt = expiresAt;
        token.entry = entry;
        if (options?.siteAdmin) {
            token.siteAdmin = options.siteAdmin;
        }

        // save it up
        await this.tokenRepository.save(token);

        try {
            await this.entryCoreService.bustEntryCache(entry.id);
        } catch (error) {
            this.logger.error(
                PolloMessage.msg(`Could not bust on key: ${entry.id}`).setError(error),
            );
        }

        // return token here
        return token;
    }

    async generateMagicLinkTokenV2(options: MagicLinkTokenOptions): Promise<Token> {
        // set default values for all the optional options
        let tokenType = TokenType.MAGIC_LINK;

        if (options.isAuditor) {
            tokenType = TokenType.AUDITOR_MAGIC_LINK;
        } else if (options.isServiceUser) {
            tokenType = TokenType.SERVICE_USER_MAGIC_LINK;
        }

        const defaultOptions = {
            isAuditor: false,
            isServiceUser: false,
            tokenType,
        };

        // Merge the default optional values with the retrieved ones
        const optionsToApply = {
            ...defaultOptions,
            ...options,
        };

        // set the expires time based on config (in hours)
        const expiresAt = moment(new Date())
            .add(config.get('tokens.magicLinkExpiration'), 'hour')
            .toDate();

        // get the entry from email
        const entry = options.entry
            ? options.entry
            : await this.entryCoreService.getEntryWithoutRelationsByEmailOrFail(options.email);

        // create the new token
        const token = new Token();
        token.type = optionsToApply.tokenType;
        token.expiresAt = expiresAt;
        token.entry = entry;
        if (options?.siteAdmin) {
            token.siteAdmin = options.siteAdmin;
        }

        // save it up
        await this.tokenRepository.save(token);

        try {
            await this.entryCoreService.bustEntryCache(entry.id);
        } catch (error) {
            this.logger.error(
                PolloMessage.msg(`Could not bust on key: ${entry.id}`).setError(error),
            );
        }

        // return token here
        return token;
    }

    async getAccessTokenByRefreshCookieAuthMode(
        authMode: AuthModes,
        entry: Entry,
        accountId: string | null,
        auditor: Auditor | null,
        serviceUser: ServiceUserEntity | null,
        authSigningService: AuthSigningService,
        siteAdmin?: SiteAdmin,
        isAuditorReadOnly?: boolean,
    ): Promise<string> {
        switch (authMode) {
            case AuthModes.AUDITOR:
                if (isNil(auditor)) {
                    throw new NotFoundException(ErrorCode.AUDITOR_NOT_FOUND);
                }

                if (!isNil(accountId)) {
                    if (isAuditorReadOnly) {
                        return authSigningService.signReadOnlyTenantUserAccessToken(
                            accountId,
                            entry.id,
                            AuthModes.AUDITOR_READ_ONLY,
                            siteAdmin,
                        );
                    }

                    return authSigningService.signAuditorTenantUserAccessToken(
                        auditor.id,
                        accountId,
                        entry.id,
                        AuthModes.NORMAL,
                    );
                }

                return authSigningService.signAuditorAccessToken(auditor.id);
            case AuthModes.SERVICE_USER:
                if (isNil(serviceUser)) {
                    throw new NotFoundException(ErrorCode.SERVICE_USER_NOT_FOUND);
                }

                if (!isNil(accountId)) {
                    return authSigningService.signTenantUserAccessToken(
                        accountId,
                        entry.id,
                        siteAdmin ?? undefined,
                        AuthModes.ACT_AS_SERVICE_USER,
                    );
                }

                return authSigningService.signServiceUserAccessToken(serviceUser);

            case AuthModes.ACT_AS_READ_ONLY:
                if (isNil(accountId)) {
                    throw new NotFoundException(ErrorCode.ACCOUNT_NOT_FOUND);
                }

                return authSigningService.signReadOnlyTenantUserAccessToken(
                    accountId,
                    entry.id,
                    authMode,
                    siteAdmin,
                );
            case AuthModes.ACT_AS:
            case AuthModes.NORMAL:
            default:
                if (isNil(accountId)) {
                    throw new NotFoundException(ErrorCode.ACCOUNT_NOT_FOUND);
                }

                return authSigningService.signTenantUserAccessToken(
                    accountId,
                    entry.id,
                    siteAdmin ?? undefined,
                    authMode,
                );
        }
    }

    async generateOktaAuthUrl(
        email: string,
        domain: string,
        urlType: OktaUrlType,
    ): Promise<AuthOktaUrlResponseDto> {
        const authenticator = this.auth.getInstance(
            IdentitySource.OKTA_IDENTITY,
        ) as OktaAuthenticatorProvider;

        let account: Account;

        if (!isNil(email)) {
            const emailDomain = getDomainFromEmail(email);
            if (isExcludedDomain(emailDomain)) {
                /**
                 * Generic emails enter the system because of multi-domain enabled customers
                 * that have users mapped to generic emails (which we don't filter at the moment at IDP-sync time)
                 *
                 * We need to specifically look for entries with the exact email here.
                 */
                account = await this.accountsCoreService.getAccountByEmailNoFail(email);
            } else {
                account = await this.accountsCoreService.getAccountByDomain(emailDomain);
            }
        } else if (!isNil(domain)) {
            // the user is coming from the IDP
            domain = domain.toLowerCase();
            const key = domain;

            const tenantRouter = await this.tenantRouterService.getRoute(
                key,
                ClientType.OKTA_IDENTITY,
            );

            if (isNil(tenantRouter)) {
                throw new NotFoundException(ErrorCode.CONNECTION_NOT_FOUND);
            }

            account = tenantRouter.account;
        }

        if (isNil(account)) {
            throw new UnauthorizedException(ErrorCode.ACCOUNT_NOT_FOUND);
        } else {
            account.checkAccountStatus();
        }

        return tenantWrapper(account, async () => {
            const identitySource = await this.getIdentitySourceByAccount(account);

            if (IdentitySource.OKTA_IDENTITY !== identitySource) {
                throw new NotFoundException(ErrorCode.CONNECTION_NOT_FOUND);
            }

            // TODO: use getIdentityProviderConnections() when multi IdP is implemented and we fetch multiple ranked connections
            const connection = await this.connectionsCoreService.getIdentityProviderConnection();

            const authUrlData = authenticator.generateAuthUrlFromConnection(
                connection,
                domain,
                urlType,
            );

            return authUrlData as AuthOktaUrlResponseDto;
        });
    }

    /**
     *
     * @param currentAdmin
     * @param dto
     * @returns
     */
    @IsDomainDuplicated({ argIndexToCheck: 1 })
    async createAccount(currentAdmin: SiteAdmin, dto: CreateAccountRequestDto): Promise<Account> {
        // call common function to do all of the work
        const { account, serviceUser } = await this.processAccountCreation(currentAdmin, dto);

        // check if a new service user was created in the common function
        if (!isNil(serviceUser)) {
            // site-admin audit log
            this._eventBus.publish(
                new AuditLogEvent(
                    currentAdmin,
                    AuditLogEventType.SERVICE_USER_CREATED,
                    AuditLogTargetType.SERVICE_USER,
                    account.id,
                    serviceUser.entry,
                ),
            );
        }

        // site-admin audit log
        this._eventBus.publish(
            new AuditLogEvent(
                currentAdmin,
                AuditLogEventType.ACCOUNT_CREATED,
                AuditLogTargetType.ACCOUNT,
                account.id,
                account,
            ),
        );
        // try to bust the cache for both key patterns, it's wasteful but better than having stale data
        // we need more normalized use of cache key prefix.... so we don't need to do this kinda stuff
        try {
            await this.superCacheAccountBuster(account);
            await this.cacheServiceUserBuster(serviceUser);
            return account;
        } catch (error) {
            this.logger.error(
                PolloMessage.msg(`Can't bust, likely key mismatch or double-busted.`).setError(
                    error,
                ),
            );
            return account;
        }
    }

    /**
     *
     * @param currentAdmin
     * @param dto
     * @param isInvited
     * @param accountId
     * @returns
     */
    async createAccountWithFrameworks(
        currentAdmin: SiteAdmin,
        dto: NewAccountRequestDto,
        isInvited = false,
        accountId: string | null = null,
    ): Promise<Account> {
        await this.validateFrameworkTags(dto);

        // call common function to do all of the work
        const { account, user } = await this.processAccountCreation(
            currentAdmin,
            dto,
            isInvited,
            accountId,
        );

        await tenantWrapper(account, async () => {
            // create dem frameworks!
            const lockKey = getProvisionFrameworkLockKey(account.id);
            const lockAcquired = await this._cacheService.acquireLock(
                lockKey,
                config.get('cache.ttl.10min'),
            );

            if (!lockAcquired) {
                throw new OurConflictException(
                    'Framework provisioning already in progress',
                    ErrorCode.FRAMEWORKS_NOT_PROVISIONED,
                );
            }

            await this.frameworkService.provisionNewFrameworks(
                dto.enableFrameworkTags || [],
                dto.enabledControlsForFrameworkTags || [],
                account,
                user,
            );

            await this._cacheService.set(
                `${account.id}-provision-framework-tag-count`,
                size(dto.enableFrameworkTags?.filter(tag => tag !== FrameworkTag.NONE)),
                { ttl: config.get('cache.ttl.week') },
            );

            this.log('Enabled frameworks and cached the count', account);

            try {
                await this.superCacheAccountBuster(account);
                await this.cacheUserBuster(user);
            } catch (error) {
                this.logger.error(
                    PolloMessage.msg(`Can't bust, likely key mismatch or double-busted.`).setError(
                        error,
                    ),
                );
                return account;
            }
            return account;
        });
        return account;
    }

    private async processAccountCreation(
        currentAdmin: SiteAdmin,
        dto: NewAccountRequestDto | CreateAccountRequestDto,
        isInvited = false,
        accountId = null,
    ): Promise<AccountCreationResult> {
        const domain = getDomainFromEmail(dto.email);
        await this.validateAccountCreationDto(domain, dto);

        let response: AccountCreationInit = {
            account: null,
            company: null,
            serviceUser: null,
            user: null,
        };

        try {
            response = await this.createAccountCompanyAndUsers(dto, currentAdmin, accountId);

            if (!this.isCompleteAccountCreationResult(response)) {
                throw new InternalServerErrorException(
                    'Account creation failed — missing critical properties in response',
                    {
                        cause: new Error('account/user/company unable to get created'),
                    },
                );
            }

            await this.accountEntitlementService.enableAccountEntitlements(
                response.account,
                SIGNUP_DEFAULT_ENTITLEMENTS,
                currentAdmin,
            );

            this.log('Enabled default entitlements', response.account, {
                entitlements: SIGNUP_DEFAULT_ENTITLEMENTS,
            });

            this.publishCreateAccountEvents(response, currentAdmin, dto, isInvited);

            this.log('SUCCESS: Created Account', response.account ?? undefined);

            await this.bustAccountAndUser(response.account, response.user);

            return response;
        } catch (error) {
            await this.printErrorLogForAccountCreation({
                error,
                account: response.account,
                domain,
            });

            throw error;
        }
    }

    private async validateAccountCreationDto(
        domain: string,
        dto: NewAccountRequestDto | CreateAccountRequestDto,
    ) {
        this.logger.log(
            PolloMessage.msg('Validating dto for account creation')
                .setDomain(domain)
                .setResult(dto),
        );

        await this.validateRegion(dto.crmCompanyId, dto.currentRegion);

        if (isExcludedDomain(domain)) {
            throw new HttpException(
                'Only corporate email addresses are allowed',
                HttpStatus.PRECONDITION_FAILED,
            );
        }

        if (dto.type === AccountType.OTHER && isEmpty(dto.otherReason)) {
            throw new HttpException(
                'Other type accounts need to fill other reason',
                HttpStatus.PRECONDITION_FAILED,
            );
        }
    }

    private isCompleteAccountCreationResult(
        response: AccountCreationInit,
    ): response is AccountCreationResult {
        return !!(response.account && response.user && response.company);
    }

    private async bustAccountAndUser(account: Nullable<Account>, user: Nullable<User>) {
        // powerBust the cache for both key patterns, it's wasteful but better than having stale data
        // we need more normalized use of cache key prefix.... so we don't need to do this kinda stuff

        // sanity check
        try {
            if (account) {
                await this.superCacheAccountBuster(account);
            }
            if (user) {
                await this.cacheUserBuster(user);
            }
        } catch (error) {
            this.logger.error(
                PolloMessage.msg(`Can't bust, likely key mismatch or double-busted.`).setError(
                    error,
                ),
            );
        }
    }

    private async createAccountCompanyAndUsers(
        dto: NewAccountRequestDto | CreateAccountRequestDto,
        currentAdmin: SiteAdmin,
        accountId: Nullable<AccountIdType> = null,
    ): Promise<AccountCreationInit> {
        const domain = getDomainFromEmail(dto.email);
        const globalDataSource = await getGlobalDataSource();

        return globalDataSource.transaction(
            async (transactionalEntityManager: DrataEntityManager) => {
                const dbHost = await this.tenantDatabaseHostService.getTenantDatabaseHostByIdOrFail(
                    dto.databaseHostId,
                );

                const { dnsName: host, port } = dbHost;

                this.logger.log(
                    PolloMessage.msg(`Creating account on ${host}:${port}`).setDomain(domain),
                );

                const { account, entry } = await this.createNewAccount({
                    email: dto.email,
                    companyName: dto.companyName,
                    crmCompanyId: dto.crmCompanyId,
                    language: dto.language ?? null,
                    type: dto.type,
                    otherReason: dto.otherReason,
                    safeBaseMigrationStatus: dto.safeBaseMigrationStatus,
                    dbHost,
                    domain,
                    transactionalEntityManager,
                    accountId,
                    currentSiteAdmin: currentAdmin,
                });

                if (isNil(account)) {
                    throw new OurConflictException(
                        'Account creation failed: createNewAccount returned null or undefined',
                        ErrorCode.ACCOUNT_CREATION_FAILED,
                    );
                }

                this.logger.log(
                    PolloAdapter.acct(
                        `Creating tenant database for account on ${host}:${port}`,
                        account,
                    ).setResult(account.databaseName),
                );

                await this.createTenantDatabase(host, port, account.databaseName, domain);

                this.log('Account created', account);

                const { user, company } = await this.createSchemaForNewTenantAndPopulateData(
                    dto,
                    entry,
                    domain,
                );

                let serviceUser: Nullable<ServiceUserEntity> = null;
                if (!isNil(dto.serviceProviderEmail)) {
                    const serviceProviderDto = new ServiceUserValidationRequestDto();

                    serviceProviderDto.email = dto.serviceProviderEmail;

                    serviceUser = await tenantWrapper(account, () =>
                        this.serviceUsersOrchestrationService.createServiceUser(
                            account,
                            serviceProviderDto,
                            null, // creatorUser
                            true, // isNewAccount
                            transactionalEntityManager,
                        ),
                    );

                    this.log(`Created service provider: ${dto.serviceProviderEmail}`, account);
                }

                this.log('Created schema for tenant and populated data', account);

                return { account, company, user, serviceUser };
            },
        );
    }

    publishCreateAccountEvents(
        params: AccountCreationResult,
        currentAdmin: SiteAdmin,
        dto: NewAccountRequestDto | CreateAccountRequestDto,
        isInvited = false,
    ) {
        if (!params.account) {
            return;
        }
        this._eventBus.publish(new EnableRiskManagementEntitlementEvent(params.account));

        this.log('Enabled RA v2', params.account);

        if (params.user) {
            if (params.company) {
                this._eventBus.publish(
                    new NewAccountEvent(
                        params.account,
                        params.user,
                        currentAdmin,
                        params.company,
                        isInvited,
                        dto,
                    ),
                );
            }

            this._eventBus.publish(new IdentifyEvent(params.user, params.account));
        }
    }

    private async printErrorLogForAccountCreation(params: ErrorAccountCreation) {
        if ('account' in params && !isNil(params.account)) {
            // The account is created, got a subsecuent error
            await this.handleNewAccountError(params.account, params.error, params.domain);
        } else {
            // The account is not created
            this.printErrorWithNoAccount(
                params.domain,
                this.processAccountCreation.name,
                params.error,
            );
        }
    }

    /**
     *
     * @param refreshToken
     */
    async logout(refreshToken: RefreshToken): Promise<void> {
        const entry = get(refreshToken, 'entry', null) as Entry;

        if (isNil(entry)) {
            return;
        }

        const email = get(entry, 'email', null);

        if (isNil(email)) {
            return;
        }

        const account = await this.accountsCoreService.getAccountByEmailNoFail(email);

        if (isNil(account)) {
            return;
        }
        await tenantWrapper(account, async () => {
            const user = await this.usersCoreService.getUserByEmailNoFail(email);

            const isReadOnlyOrAuditor =
                hasRole(user, [Role.ACT_AS_READ_ONLY]) || hasRole(user, [Role.AUDITOR]);

            if (!isReadOnlyOrAuditor) {
                this._eventBus.publish(new UserAuthenticatedSignOutEvent(account, user));
            }
            const isWorkspaceAdmin = hasRole(user, [Role.WORKSPACE_ADMINISTRATOR]);
            if (isWorkspaceAdmin && config.get('cache.enabled') && CACHE_IS_UP) {
                const cacheWorkspaceIdKey = `${Caches.WORKSPACE_ID}_${entry.id}`;
                const currentId = await this._cacheService.get(cacheWorkspaceIdKey);
                if (!isNil(currentId)) {
                    await this._cacheService.del(cacheWorkspaceIdKey);
                }
            }
        });
    }

    /**
     *
     * @param databaseHost
     * @param databasePort
     * @param databaseName
     * @param domain
     */
    private async createTenantDatabase(
        databaseHost: string,
        databasePort: number,
        databaseName: string,
        domain: string,
    ): Promise<void> {
        let connection: DrataDataSource;

        try {
            /**
             * Create tenant connection config to a server. In terraform, we create an empty `drata`
             * database on all servers, so we'll establish a connection using that table (just for
             * the purpose of creating the new tenant DB) - even though we don't use that `drata` DB
             */
            connection = await createTenantConnectionFor(
                databaseName,
                TypeOrmConfig.createTypeOrmConnectionOptionsForTenantWithoutAccount(
                    databaseHost,
                    databasePort,
                ),
            );

            const createQuery = createDatabase(databaseName);
            await connection.query(createQuery);
        } catch (error) {
            if (error.code === duplicateDatabase()) {
                throw new OurConflictException(
                    `An account already exists with domain: ${domain}`,
                    ErrorCode.COMPANY_EXISTS,
                );
            } else {
                const errorMessage = has(error, 'message')
                    ? error.message
                    : 'Error creating tenant database';

                throw new Error(errorMessage);
            }
        } finally {
            if (!isNil(connection) && connection.isInitialized) {
                await destroyDataSource(connection.id);
            }
        }
    }

    async dropTenantDatabase(account: Account): Promise<void> {
        try {
            const connection = await getGlobalDataSource();

            await connection.query(dropDatabase(account.databaseName));

            this.log('Database correctly dropped', account);
        } catch (error) {
            const errorMessage = has(error, 'message')
                ? error.message
                : `Error dropping tenant database ${account.databaseName}}`;

            this.error(new Error(errorMessage), account);
            throw new Error(errorMessage);
        }
    }

    /**
     *
     * @param params
     * @returns
     */
    private async createNewAccount(params: CreateNewAccountParams): Promise<AccountEntryResult> {
        const {
            email,
            companyName,
            crmCompanyId,
            language,
            dbHost,
            domain,
            transactionalEntityManager,
            type,
            accountId,
            otherReason,
            currentSiteAdmin,
            safeBaseMigrationStatus,
        } = params;

        const accountRepository = transactionalEntityManager.getRepository(Account);
        const adaptiveAutomationRepository = transactionalEntityManager.getRepository(
            AccountAdaptiveAutomationMetadata,
        );

        const entryRepository = transactionalEntityManager.getRepository(Entry);
        const accountSafeBaseSettingsRepository =
            transactionalEntityManager.getRepository(AccountSafeBaseSettings);

        try {
            const account = new Account();

            if (!isNil(accountId)) {
                account.id = accountId;
            }

            account.domain = domain;
            account.status = AccountStatus.PENDING;
            account.companyName = companyName;
            account.databaseName = 'TEMP_DB_NAME';
            account.tenantDatabaseHost = dbHost;
            account.databaseHost = dbHost.dnsName;
            account.databasePort = dbHost.port;
            account.crmCompanyId = crmCompanyId;
            account.actAsType = ActAsType.DISABLED;
            account.type = type;
            account.otherReason = otherReason;
            account.createdBy = currentSiteAdmin;
            account.safeBaseMigrationStatus =
                safeBaseMigrationStatus ?? SafeBaseMigrationStatus.NOT_STARTED;

            if (!isProd()) {
                account.actAsType = ActAsType.READ_AND_WRITE;
                // we'll all be dead
                account.drataSupportAccessExpiresAt = new Date('2100');
            }

            account.language = language || Language.ENGLISH_US;

            let entry = await entryRepository.findOneBy({ email });

            if (!isNil(entry)) {
                if (hasSiteAdminDeletedAccountForEntry(entry)) {
                    throw new ConflictException(
                        `Account for ${email} has been previously deleted, ` +
                            `a special process must be followed to create a new account`,
                    );
                }
                const { user } = await this.getCustomerAccountAndUserByEntry(entry);

                if (!isEmpty(user)) {
                    throw new ConflictException(`Entry account for ${email} exists as a customer`);
                }

                entry.accounts = [account, ...entry.accounts];
            } else {
                entry = new Entry();
                entry.email = email;
                entry.accounts = [account];
            }

            const savedAccount = await accountRepository.save(account);

            const safeBaseSettings = new AccountSafeBaseSettings();
            safeBaseSettings.accountId = savedAccount.id;
            safeBaseSettings.migrationStatus = savedAccount.safeBaseMigrationStatus;
            // add safeBaseSettings to account for the return value
            account.safeBaseSettings = safeBaseSettings;

            try {
                await adaptiveAutomationRepository.save({
                    allotment: Number(config.get('customConnectionsAndTests.allotment')),
                    account: savedAccount,
                });
            } catch (e) {
                this.warn(
                    `Could not found adaptive_automation_metadata table: ${e.message}`,
                    account,
                );
            }

            await entryRepository.save(entry);
            await accountSafeBaseSettingsRepository.save(safeBaseSettings);

            // powerBust the cache for both key patterns, it's wasteful but better than having stale data
            // we need more normalized use of cache key prefix.... so we don't need to do this kinda stuff
            try {
                await this.superCacheAccountBuster(account);
                await this.cacheEntryBuster(entry);
            } catch (error) {
                this.logger.error(
                    PolloMessage.msg(`Can't bust, likely key mismatch or double-busted.`).setError(
                        error,
                    ),
                );
            }

            return {
                account,
                entry,
            };
        } catch (error) {
            if (error.code === lockTimeout()) {
                const queries = await transactionalEntityManager.query('SHOW FULL PROCESSLIST');
                this.logger.error(
                    PolloMessage.msg(
                        `MYSQL DEADLOCK here is the processlist ${JSON.stringify(queries)}`,
                    )
                        .setDomain(domain)
                        .setContext(this.constructor.name)
                        .setSubContext(this.createNewAccount.name)
                        .setError(error),
                );
            }
            if (error.code === duplicateEntry()) {
                throw new ConflictException(`Entry account for ${email} already exists`);
            } else {
                this.logger.error(
                    PolloMessage.msg('Error attempting create new account')
                        .setDomain(domain)
                        .setContext(this.constructor.name)
                        .setSubContext(this.createNewAccount.name)
                        .setIdentifier(error), // setting error in identifier to catch validation errors
                );

                throw error;
            }
        }
    }

    /**
     *
     * @param dto
     * @param entry
     * @param domain
     */
    private async createSchemaForNewTenantAndPopulateData(
        dto: NewAccountRequestDto | CreateAccountRequestDto,
        entry: Entry,
        domain: string,
    ): Promise<{ user: User; company: Company }> {
        /**
         * This could be kinda hacky too the first account of the entry will
         * always be customer side, 2nd to n will be auditor related accounts
         */
        const account = entry.accounts[0];

        const tenantConnection = await createDataSource(
            account.databaseName,
            TypeOrmConfig.createTypeOrmConnectionOptionsForTenantMigration(account),
        );

        this.log('Running migrations...', account);
        const migrations = await tenantConnection.runMigrations({
            transaction: 'all',
        });
        this.log('Done running migrations.', account, migrations);

        return tenantWrapper(account, async () => {
            const { firstName, lastName, jobTitle, language = null, companyName } = dto;

            this.log('Creating admin account...', account);
            const user = await this.createAccountAdmin(
                account,
                tenantConnection,
                entry,
                firstName,
                lastName,
                jobTitle,
                language,
            );
            this.log('Done creating admin account.', account);

            this.log('Creating company account...', account);
            const { company } = await this.createAccountCompany(
                tenantConnection,
                account,
                user,
                domain,
                companyName,
            );
            this.log('Done creating company account...', account);

            this.log('Creating admin onboarding steps...', account);
            const enabledFrameworkTags = get(dto, 'enableFrameworkTags', []);

            await this.createAdminOnboardingSteps(tenantConnection, company, enabledFrameworkTags);
            this.log('Done creating admin onboarding steps.', account);

            // seed the features from the templates
            this.log('Creating account features...', account);
            await this.featureService.createAccountFeatures();
            this.log('Done creating account features.', account);

            this.log('Publishing settings...', account);
            await this.publishSettings(tenantConnection);
            this.log('Done publishing settings.', account);

            this.log('Publishing policy templates...', account);
            const policies = await this.publishPolicyTemplates(
                tenantConnection,
                company,
                enabledFrameworkTags,
            );
            this.log(`Done publishing ${size(policies)} policy templates.`, account);

            this.log('Publishing policy metadata templates...', account);
            await this.publishPolicyMetadataTemplates(tenantConnection);
            this.log('Done publishing policy metadata templates.', account);

            this.log('Publishing wysiwyg comments...', account);
            await this.publishWysiwygComments(tenantConnection);
            this.log('Done publishing wysiwyg comments.', account);

            this.log('Creating trust center monitoring controls...', account);
            await this.createTrustCenterMonitoringControlsAndPopulate(tenantConnection);
            this.log('Done creating trust center monitoring controls.', account);

            this.log('Creating default autopilot schedule...', account);
            await createDefaultAutopilotSchedule(tenantConnection);
            this.log('Done default autopilot schedule.', account);

            this.log('Creating policy responsibilities...', account);
            await createPolicyResponsibilities(policies, tenantConnection);
            this.log('Done creating policy responsibilities.', account);

            this.log('Creating default vendors settings...', account);
            await createDefaultVendorsSettings(tenantConnection);
            this.log('Done creating default vendors settings.', account);

            this.log('Creating vendor data accessed...', account);
            await createVendorDataAccessedOrProcessedItems(tenantConnection);
            this.log('Done creating vendor data accessed...', account);

            this.log('Creating drata as vendor discovered...', account);
            await createDrataAsVendorDiscovered(tenantConnection);
            this.log('Done creating drata as vendor discovered...', account);

            await createDefaultRisks(tenantConnection);
            await createDefaultCompaniesSettings(tenantConnection, company);

            this.log('Creating follow up reminders schedule feature...', account);
            await createScheduleFollowUpReminders(tenantConnection);
            this.log('Done creating follow up reminders schedule feature...', account);

            this.log('Creating manual reminder schedule feature...', account);
            await createManualReminder(tenantConnection);
            this.log('Done creating manual reminders schedule feature...', account);

            this.log('Creating questionnaires schedule feature...', account);
            await createScheduleQuestionnaires(tenantConnection);
            this.log('Done creating questionnaires schedule feature...', account);

            this.log('Creating manual reminder schedule configuration...', account);
            await createManualReminderScheduleConfiguration(tenantConnection);
            this.log('Done creating manual reminder schedule configuration...', account);

            await this._cacheService.del(`globalstore:entry:${user.entryId}`);
            await this._cacheService.del(`globalstore:entry:${user.entryId},${account.id}`);
            await this._cacheService.del(`globalstore:company:${company.accountId}`);
            return {
                user,
                company,
            };
        });
    }

    private async createTrustCenterMonitoringControlsAndPopulate(
        connection: DrataDataSource,
    ): Promise<TrustCenterMonitoringControl[]> {
        const monitoringControlRepository =
            await this.trustCenterMonitoringControlTemplateRepository.find();

        return publishTrustCenterMonitorControls(connection, monitoringControlRepository);
    }

    /**
     *
     * @param account
     * @param connection
     */
    private async createDefaultRolesAndPermissions(
        account: Account,
        connection: DrataDataSource,
    ): Promise<void> {
        const roleRepository = connection.getRepository(RoleEntity);
        const rolesToAdd = [
            Role.EMPLOYEE,
            Role.TECHGOV,
            Role.AUDITOR,
            Role.ADMIN,
            Role.ACT_AS_READ_ONLY,
            Role.APP,
            Role.RISK_MANAGER,
            Role.WORKSPACE_ADMINISTRATOR,
            Role.SERVICE_USER,
            Role.CONTROL_MANAGER,
            Role.PEOPLE_OPS,
            Role.POLICY_MANAGER,
            Role.REVIEWER,
            Role.DEVOPS_ENGINEER,
            Role.KNOWLEDGE_BASE,
            Role.TRUST_CENTER_MANAGER,
            Role.TRUST_CENTER_REVIEWER,
            Role.RISK_REGISTER_OWNER,
        ];

        const generatedRoles: RoleEntity[] = [];

        await asyncForEach(rolesToAdd, async role => {
            const generatedRole = new RoleEntity();
            generatedRole.id = role;
            generatedRole.role = role;
            if (role === Role.ACT_AS_READ_ONLY || role === Role.APP) {
                generatedRole.deletedAt = new Date();
            }
            generatedRoles.push(generatedRole);
        });
        await roleRepository.save(generatedRoles);
        await this.permissionsSyncService.populateDefaultPermissions();
    }

    /**
     *
     * @param account
     * @param connection
     * @param entry
     * @param firstName
     * @param lastName
     * @param jobTitle
     * @param language
     * @returns
     */
    @CacheBusterWithPrefix<User>({
        stores: [Caches.ACCOUNT, Caches.USER_BY_ENTRY_ID, Caches.ENTRY],
    })
    @SuperCacheAccountBuster<User>({
        stores: [
            Caches.ACCOUNT,
            Caches.ACCOUNT_CONTRACT_DETAILS,
            Caches.COMPANY_DATA,
            Caches.COMPANY_PERSONNEL,
            Caches.CONTROL_TEMPLATE,
            Caches.ENTRY,
            Caches.FRAMEWORK,
            Caches.LIST_CONTROLS,
            Caches.PUBLIC_API_KEY,
            Caches.REQUIREMENTS,
            Caches.USER_BY_ENTRY_ID,
            Caches.WORKSPACE_ID,
        ],
        useArgs: 1,
    })
    private async createAccountAdmin(
        account: Account,
        connection: DrataDataSource,
        entry: Entry,
        firstName: string,
        lastName: string,
        jobTitle: string,
        language: Language | null,
    ): Promise<User> {
        const user = new User();
        user.entryId = entry.id;
        user.email = entry.email;
        user.firstName = firstName;
        user.lastName = lastName;
        user.jobTitle = jobTitle;
        user.language = language;

        const employeeRole = new UserRole();
        employeeRole.role = Role.EMPLOYEE;
        employeeRole.user = user;

        const adminRole = new UserRole();
        adminRole.role = Role.ADMIN;
        adminRole.user = user;

        await this.userRepository.save(user);
        await this.userRoleRepository.save(employeeRole);
        await this.userRoleRepository.save(adminRole);

        await this.createDefaultRolesAndPermissions(account, connection);

        const rolesToAdd = [Role[Role.ADMIN], Role[Role.EMPLOYEE]];

        await asyncForEach(rolesToAdd, async role => {
            await this.usersCoreService.addUsersRoles(role, user, null);
        });

        // powerBust the cache for both key patterns, it's wasteful but better than having stale data
        // we need more normalized use of cache key prefix.... so we don't need to do this kinda stuff
        try {
            await this.cacheUserBuster(user);
        } catch (error) {
            this.logger.error(
                PolloMessage.msg("Can't bust, likely key mismatch or double-busted.").setError(
                    error,
                ),
            );
        }

        return this.userRepository.findOneByOrFail({ id: user.id });
    }

    /**
     *
     * @param connection
     * @param account
     * @param user
     * @param domain
     * @param companyName
     */
    @CacheBusterWithPrefix<{ company: Company; product: Product }>({
        stores: [Caches.ACCOUNT, Caches.COMPANY_DATA, Caches.USER_BY_ENTRY_ID],
    })
    private async createAccountCompany(
        connection: DrataDataSource,
        account: Account,
        user: User,
        domain: string,
        companyName: string,
    ): Promise<{ company: Company; product: Product }> {
        let company = new Company();
        company.accountId = account.id;
        company.domain = domain;
        company.name = companyName;
        company.securityReportVisibility = ReportVisibilityType.ALL;
        company.securityReportSharable = false;
        company.securityTraining = null;
        company.backgroundCheck = null;
        company.adminOnboardedAt = null;
        company.renewalPeriodStartDate = null;
        company.actAs = null;

        company.logo = (
            await this.uploader.uploadCompanyLogo(domain, account.id).catch(() => null)
        )?.key;

        company.typeformWorkspace = await this.createTypeFormWorkspace(connection, account).catch(
            error => {
                this.logger.error(
                    PolloAdapter.acct(error.message, account)
                        .setError(error)
                        .setContext(this.constructor.name)
                        .setSubContext(this.createTypeFormWorkspace.name),
                );
                return null;
            },
        );

        let product = new Product();
        product.name = companyName;
        product.company = company;
        product.logo = null;
        product.primary = true;

        if (!isProd()) {
            const actAsRepository = connection.getRepository(ActAs);

            const actAsEntry = new ActAs();
            actAsEntry.type = ActAsType.READ_AND_WRITE;
            actAsEntry.user = user;
            // we'll all be dead
            actAsEntry.expiresAt = new Date('2100');
            company.actAs = actAsEntry;

            await actAsRepository.save(actAsEntry);
        }

        company = await this.companyRepository.save(company);
        product = await this.productRepository.save(product);

        return { company, product };
    }

    /**
     *
     * @param connection
     * @param account
     * @param domain
     */
    private async createTypeFormWorkspace(
        connection: DrataDataSource,
        account: Account,
    ): Promise<string> {
        const questionnairesRepository = connection.getRepository(Questionnaires);
        const questionnairesVendorsRepository = connection.getRepository(QuestionnairesVendors);
        const riskLevels = Object.values(VendorRisk).filter(
            riskLevel => !isNaN(Number(riskLevel)),
        ) as VendorRisk[];
        const categories = Object.values(VendorCategory).filter(
            category => !isNaN(Number(category)),
        ) as VendorCategory[];

        const workspace = isProd()
            ? await this.formProvider.createWorkspace(account.domain)
            : {
                  id: config.get('typeform.formIds.defaultQuestionnaire.workspaceId'),
              };

        const workspaceApiUrl = format(
            '%s%s/%s',
            config.get('typeform.api.url'),
            config.get('typeform.api.workspaces'),
            workspace.id,
        );
        defaultQuestionnaire.workspace.href = workspaceApiUrl;
        const createdForm = await this.formProvider.createForm(defaultQuestionnaire);

        let questionnaire = new Questionnaires();
        questionnaire.url = createdForm._link;
        questionnaire.typeFormId = createdForm.id;
        questionnaire = await questionnairesRepository.save(questionnaire);

        await this.formProvider.registerWebhook(createdForm.id);

        const questionnaireVendor = new QuestionnairesVendors();
        questionnaireVendor.title = createdForm.title;
        questionnaireVendor.status = FormStatus.DEFAULT;
        questionnaireVendor.riskLevels = riskLevels;
        questionnaireVendor.categories = categories;
        questionnaireVendor.questionnaire = questionnaire;
        await questionnairesVendorsRepository.save(questionnaireVendor);

        return workspace.id;
    }

    /**
     *
     * @param connection
     * @param company
     * @returns
     */
    private async publishPolicyTemplates(
        connection: DrataDataSource,
        company: Company,
        enableFrameworkTags: FrameworkTag[],
    ): Promise<Policy[]> {
        const policyRepository = connection.getRepository(Policy);
        const policyWeekTimeFrameSLARepository = connection.getRepository(PolicyWeekTimeFrameSLA);
        const policyP3MatrixSLARepository = connection.getRepository(PolicyP3MatrixSLA);

        const policyGracePeriodSLARepository = connection.getRepository(PolicyGracePeriodSLA);

        const policies = [];

        const policyTemplates = await this.policyTemplateRepository.find({
            order: {
                id: 'ASC',
            },
            relations: ['additionalTemplateContents'],
        });

        for (const policyTemplate of policyTemplates) {
            const policy = new Policy();
            policy.templateId = policyTemplate.id;
            policy.name = policyTemplate.name;
            policy.currentDescription = policyTemplate.description;
            policy.html = getHtmlFromPolicyTemplate(enableFrameworkTags, policyTemplate, company);
            policy.scope = PolicyScope.ALL;

            if (
                policyTemplate.name === AutopilotPolicyName.ISMS_POLICY ||
                policyTemplate.name === AutopilotPolicyName.ISMS_POLICY_2022 ||
                policyTemplate.name === AutopilotPolicyName.ISMS_AND_PIMS_POLICY
            ) {
                policy.scope = PolicyScope.NONE;
            }

            policy.groupsNotify = false;
            policy.policyStatus = policyTemplate.policyStatus;

            policies.push(policy);
        }

        /**
         * Setting reload:false here results in missing foreign key constraints.
         */
        await policyRepository.save(policies);

        for (const policyTemplate of policyTemplates) {
            if (policyTemplate.policySLATemplates.length === 0) {
                continue;
            }

            const policy = policies.find(p => p.templateId === policyTemplate.id);

            const policyWeekTimeFrameSLAs = [];
            const policyP3MatrixSLAs = [];
            const policyGracePeriodSLAs = [];

            for (const policySlaTemplate of policyTemplate.policySLATemplates) {
                switch (policySlaTemplate.type) {
                    case SLAType.WEEK_TIMEFRAME:
                        const policyWeekTimeFrameSLA = new PolicyWeekTimeFrameSLA();
                        policyWeekTimeFrameSLA.label = policySlaTemplate.label;
                        policyWeekTimeFrameSLA.policy = policy;
                        policyWeekTimeFrameSLAs.push(policyWeekTimeFrameSLA);
                        break;

                    case SLAType.P3_SEVERITY_MATRIX:
                        const policyP3MatrixSLA = new PolicyP3MatrixSLA();
                        policyP3MatrixSLA.label = policySlaTemplate.label;
                        policyP3MatrixSLA.policy = policy;
                        policyP3MatrixSLAs.push(policyP3MatrixSLA);
                        break;

                    case SLAType.GRACE_PERIOD:
                        const policyGracePeriodSLA = new PolicyGracePeriodSLA();
                        policyGracePeriodSLA.label = policySlaTemplate.label;
                        policyGracePeriodSLA.policy = policy;
                        policyGracePeriodSLAs.push(policyGracePeriodSLA);
                        break;

                    default:
                        break;
                }
            }

            if (policyWeekTimeFrameSLAs.length > 0) {
                await policyWeekTimeFrameSLARepository.save(policyWeekTimeFrameSLAs);
            }

            if (policyP3MatrixSLAs.length > 0) {
                await policyP3MatrixSLARepository.save(policyP3MatrixSLAs);
            }

            if (policyGracePeriodSLAs.length > 0) {
                await policyGracePeriodSLARepository.save(policyGracePeriodSLAs);
            }
        }

        return policies;
    }

    private async publishPolicyMetadataTemplates(connection: DrataDataSource): Promise<void> {
        const policyMetadataRepository = connection.getRepository(PolicyMetadata);
        const policyRepository = connection.getRepository(Policy);

        const policiesMetadata = [];
        const policyMetadataTemplates =
            await this.policyMetadataTemplateRepository.getPolicyMetadataTemplates();
        const policies = await policyRepository.find();

        for (const metadataTemplates of policyMetadataTemplates) {
            const index = findIndex(policies, ({ name }) => name === metadataTemplates.policy.name);

            if (index > -1) {
                policiesMetadata.push({
                    ...metadataTemplates,
                    policy: {
                        ...policies[index],
                    },
                });
            }
        }

        await policyMetadataRepository.save(policiesMetadata);
    }

    /**
     * @param {DrataDataSource} connection
     */
    private async publishWysiwygComments(connection: DrataDataSource): Promise<void> {
        const commentRepository = connection.getRepository(CommentEntity);
        const siteAdminComments = await this.siteAdminCommentRepository.findBy({
            type: CommentType.WYSIWYG,
        });
        const commentsToSave = [];
        for (const siteAdminComment of siteAdminComments) {
            const comment = new CommentEntity();
            comment.channelId = siteAdminComment.channelId;
            comment.threadId = siteAdminComment.threadId;
            comment.commentId = siteAdminComment.commentId;
            comment.content = siteAdminComment.content;
            comment.attributes = siteAdminComment.attributes;
            comment.type = siteAdminComment.type;
            comment.user = null;
            commentsToSave.push(comment);
        }
        await commentRepository.save(commentsToSave);
    }

    private async publishSettings(connection: DrataDataSource): Promise<void> {
        const settingRepository = connection.getRepository(Setting);
        const settings = [];

        getValues(SettingTypeSeed).forEach(setting => {
            const newSetting = Object.assign(new Setting(), {
                type: setting,
                enabledAt: null,
                config: '{}',
                defaultValue: false,
            });
            settings.push(newSetting);
        });

        await settingRepository.save(settings, { reload: false });
    }

    private async authenticateAuditor(email: string): Promise<Entry> {
        const account = await this.accountsCoreService.getActiveAccountByEmail(email, true);

        if (isNil(account)) {
            throw new UnauthorizedException(ErrorCode.ACCOUNT_NOT_FOUND);
        }

        const entry = await this.entryCoreService.getEntryByEmailWithAccount(email, account.id);

        this._eventBus.publish(new AuditorAuthenticatedEvent(account, entry.id));

        return entry;
    }

    /**
     * Make this private for future auth calls
     *
     * @param email
     * @param type
     */
    private async authenticateUser(
        payload: AuthenticatorPayloadType,
        type: IdentitySource,
        tenantRouterAccount?: Account,
        domain?: string,
        ssoConnectionId?: number,
    ): Promise<Entry> {
        const { login, sourceId, email } = payload;
        let entry: Entry | null = null;

        if (
            !isEmpty(domain) &&
            (type === IdentitySource.MICROSOFT_365 ||
                type === IdentitySource.MICROSOFT_365_GCC_HIGH)
        ) {
            entry = await this.entryCoreService.getEntryByEmailNoFail(email);

            if (isNil(entry)) {
                let guestEmail = '';

                if (email.includes('#EXT#')) {
                    /**
                     * https://drata.atlassian.net/browse/ENG-60109
                     * email is already formatted with #EXT#
                     */
                    const parts = email.split('#EXT#');
                    const indexOfFirst = parts[0].indexOf(domain);
                    const arr = parts[0].split('');
                    arr[indexOfFirst - 1] = '@';
                    guestEmail = arr.join('');
                } else {
                    /**
                     * https://drata.atlassian.net/browse/ENG-31304
                     * There is a weird case when external users are added to a tenant
                     * and they are not found by the email. In this case, we need to
                     * create a guest email and try to find the entry by that.
                     *
                     * MSFT will create a guest email with the following format:
                     * {local}_{domain}#EXT#@{AzureDomain}.onmicrosoft.com
                     */

                    guestEmail = `${email.replace('@', '_')}#EXT#@${domain}`;
                }
                this.logger.log(
                    PolloMessage.msg('Authenticating user with guest email')
                        .setDomain(domain)
                        .setIdentifier({ guestEmail, email, sourceId }),
                );

                /**
                 * getEntryByEmail is OK here
                 * this function will get the entry with all of its linking accounts
                 * since this entry record was just created - it will only have one linking account
                 * we want to rehydrate the account information for the next steps
                 */
                entry = await this.entryCoreService.getEntryByEmail(guestEmail);

                this.logger.log(
                    PolloMessage.msg('Guest email entry found')
                        .setDomain(domain)
                        .setIdentifier({ email, sourceId, entry }),
                );
            }
        } else {
            try {
                entry = await this.entryCoreService.getEntryByEmailNoFail(email);
                if (isNil(entry) && !isNil(login)) {
                    entry = await this.entryCoreService.getEntryByEmail(login);
                }

                if (isNil(entry)) {
                    throw new OurNotFoundException(ErrorCode.OKTA_IDENTITY_MISMATCH);
                }
            } catch (error) {
                const { response } = error;

                /**
                 * https://drata.atlassian.net/browse/ENG-39898
                 */
                this.logger.error(
                    PolloMessage.msg(`Error authenticating user with email`, this.constructor.name)
                        .setSubContext(this.authenticateUser.name)
                        .setDomain(domain)
                        .setIdentifier({
                            type,
                            ...response,
                        })
                        .setError(error),
                );

                /**
                 * log error information and re-throw error
                 */
                throw new NestNotFoundException();
            }
        }

        const { account } = await this.getCustomerAccountAndUserByEntry(entry);

        if (isNil(account)) {
            throw new PreconditionFailedException('No account found');
        }

        return tenantWrapper(account, async () => {
            const connection = ssoConnectionId
                ? await this.getIdentityConnectionBySsoConnectionId(ssoConnectionId)
                : await this.getIdentityConnectionByAccount(account);
            const identitySource = this.getIdentitySourceForConnection(connection);

            if (identitySource !== type) {
                throw new PreconditionFailedException('Identity System mismatch');
            }

            const isMicrosoftIdentitySource = identitySource === IdentitySource.MICROSOFT_365;
            if (isMicrosoftIdentitySource) {
                /**
                 * ENG-60425: Make sure the microsoft tenants match
                 *   - microsoft tenant associated to authenticated user
                 *   - microsoft tenant associated Drata tenant/account
                 */

                const logData = {
                    accountId: account.id,
                    entryId: entry.id,
                    email,
                    identitySource,
                    payload,
                };

                if (!payload.serializedToken) {
                    this.logger.error(
                        PolloMessage.msg(`Missing authentication token`, this.constructor.name)
                            .setSubContext(this.authenticateUser.name)
                            .setDomain(domain)
                            .setIdentifier(logData),
                    );

                    throw new PreconditionFailedException('Missing authentication token');
                }

                const parsedToken: AuthenticationResult = JSON.parse(payload.serializedToken);

                if (!parsedToken.tenantId) {
                    this.logger.error(
                        PolloMessage.msg(
                            'Authentication token missing tenantId',
                            this.constructor.name,
                        )
                            .setSubContext(this.authenticateUser.name)
                            .setDomain(domain)
                            .setIdentifier(logData),
                    );

                    throw new PreconditionFailedException('Authentication token missing tenantId');
                }

                // For ms365 connections, refreshToken contains the tenant ID
                const tenantIdsMatch =
                    parsedToken.tenantId === connection.getMetadata()?.refreshToken;
                if (!tenantIdsMatch) {
                    this.logger.error(
                        PolloMessage.msg('Microsoft tenant IDs mismatch', this.constructor.name)
                            .setSubContext(this.authenticateUser.name)
                            .setDomain(domain)
                            .setIdentifier({
                                ...logData,
                                connectionId: connection.id,
                                connectionTenantId: connection.getMetadata()?.refreshToken,
                            }),
                    );

                    throw new UnauthorizedException(ErrorCode.MICROSOFT_365_AUTH_TENANT_MISMATCH);
                }
            }

            await this._cacheService.del(`globalstore:entry:${account.id}`);

            /**
             * Error out when:
             * - The account is not found.
             * - If there is an initial account found by the tenant router, the
             *   tenant router's account and the account resolved by the email
             *   don't match.
             */
            if (
                isNil(account) ||
                (!isNil(tenantRouterAccount) && tenantRouterAccount.id !== account.id)
            ) {
                throw new UnauthorizedException(ErrorCode.ACCOUNT_NOT_FOUND);
            }

            const personnel = await this.personnelCoreService.getPersonnelByEmail(entry.email);
            if (IdentitySource.OKTA_IDENTITY === type) {
                validateAuthenticationIdentities(personnel, payload);
            }
            const verifyResults = verifyPersonnelIsActive(personnel);

            if (!verifyResults.active) {
                throw new UnauthorizedException(verifyResults.reason);
            }

            this._eventBus.publish(new UserAuthenticatedEvent(account, personnel));

            return entry;
        });
    }

    /**
     *
     * @param connection
     * @param company
     */
    private async createAdminOnboardingSteps(
        connection: DrataDataSource,
        company: Company,
        enabledFrameworkTags: number[],
    ): Promise<void> {
        const adminOnboardingRepository = connection.getRepository(AdminOnboarding);

        const stepsToCreate = [];

        // create a step for each type
        for (const value in AdminOnboardingStepType) {
            // iterating over enums in TS is strange. This ignores the non-key values
            if (isNaN(Number(value))) {
                continue;
            }

            if (
                !enabledFrameworkTags.includes(FrameworkTag.HIPAA) &&
                Number(value) === AdminOnboardingStepType.HIPAA_TRAINING
            ) {
                continue;
            }

            if (
                !enabledFrameworkTags.includes(FrameworkTag.NISTAI || FrameworkTag.ISO420012023) &&
                Number(value) === AdminOnboardingStepType.NIST_AI_TRAINING
            ) {
                continue;
            }
            const step = new AdminOnboarding();
            step.stepType = AdminOnboardingStepType[AdminOnboardingStepType[value]];
            step.company = company;

            stepsToCreate.push(step);
        }

        await adminOnboardingRepository.save(stepsToCreate);
    }

    public async validateFrameworkTags(dto: NewAccountRequestDto): Promise<void> {
        const enabledControlsForFrameworkTags = uniq(dto.enabledControlsForFrameworkTags);

        if (isEmpty(dto.enableFrameworkTags)) {
            return;
        }

        const enableFrameworkTags = uniq(dto.enableFrameworkTags);

        const invalidEnableFrameworkTags = await this.getInvalidFrameworkTags(enableFrameworkTags);

        if (!isEmpty(invalidEnableFrameworkTags)) {
            return Promise.reject(
                new BadRequestException(
                    `Enable framework tags are invalid: ${invalidEnableFrameworkTags.join(',')}`,
                ),
            );
        }

        const invalidEnabledControlsForFrameworkTags = await this.getInvalidFrameworkTags(
            enabledControlsForFrameworkTags,
        );

        if (!isEmpty(invalidEnabledControlsForFrameworkTags)) {
            return Promise.reject(
                new BadRequestException(
                    `Enabled controls framework tags are invalid: ${invalidEnabledControlsForFrameworkTags.join(
                        ',',
                    )}`,
                ),
            );
        }

        const enabledControlsButDisabledFramework = enabledControlsForFrameworkTags.filter(
            ft => !enableFrameworkTags.includes(ft),
        );

        if (!isEmpty(enabledControlsButDisabledFramework)) {
            return Promise.reject(
                new BadRequestException(
                    `Enabled controls framework tags are present, but the framework tag is not enabled: ${enabledControlsButDisabledFramework.join(
                        ',',
                    )}`,
                ),
            );
        }
    }

    /**
     *
     * @param crmCompanyId
     * @param region
     * @returns
     */
    private async validateRegion(crmCompanyId: string, region: RegionType): Promise<void> {
        if (!isEmpty(crmCompanyId)) {
            let crmAccount: CrmAccount;
            try {
                crmAccount = await this.crm.getAccountByCrmAccountId(crmCompanyId);
            } catch (e) {
                this.logger.error(
                    PolloMessage.msg('Unexpected failure validating region').setError(e),
                );
                return Promise.resolve();
            }

            // lets sanity check for tenancyRegion for more descriptive error message on failure
            const tenancyRegion = crmAccount.tenancyRegion
                ? getRegionTypeFromTenancyRegion(crmAccount.tenancyRegion)
                : null;

            const hasInvalidRegionTenancy = tenancyRegion !== region;

            if (hasInvalidRegionTenancy) {
                const errorMessage =
                    `The ${crmAccount.name}(${crmAccount.id}) has tenancy ` +
                    (isNil(tenancyRegion)
                        ? `set to no region; please update the field in Salesforce.`
                        : `in ${RegionType[tenancyRegion]}, it should be created in that region.`);

                this.logger.error(
                    new PolloMessage(errorMessage, this.constructor.name).setIdentifier({
                        tenancyRegion: crmAccount.tenancyRegion,
                        serverRegion: region,
                    }),
                );

                return Promise.reject(new BadRequestException(errorMessage));
            }
        }
    }

    /**
     *
     * @param email
     * @returns
     */
    async getSsoConnectionId(email: string): Promise<string> {
        try {
            /**
             * getEntryByEmail is OK here
             * this function will get the entry with all of its linking accounts
             * since getCustomerAccountAndUserByEntry() needs all the accounts
             */
            const entry = await this.entryCoreService.getEntryByEmail(email);
            const { account } = await this.getCustomerAccountAndUserByEntry(entry);

            if (isNil(account)) {
                return this.getDummyConnectionId();
            }

            const workOsConnection = await tenantWrapper(account, async () => {
                return this.connectionsCoreService.getConnectionByClientType(ClientType.WORK_OS);
            });

            /**
             * NOTE: Casting metadata as the intersection of ConnectionMetadata and
             * the inline type below to be able to access workOs.connection.id without
             * upsetting the TS compiler. We use WorkOsWebhookDataRequestDto as the type
             * for workOs.connection because that's the way it's saved when we receive
             * the webhook from workOs post-connection setup.
             *
             * See
             * https://github.com/drata/api/blob/a2fda4fdceb0c989ed46d62820c194ee12016210/src/app/work-os/work-os-webhook-handler/work-os-webhook-handler.service.ts#L24
             * for reference
             */
            const metadata = workOsConnection.getMetadata() as ConnectionMetadata & {
                workOs: { connection: WorkOsWebhookDataRequestDto };
            };

            return metadata.workOs.connection.id;
        } catch (error) {
            /**
             * NOTE: Since this is a publicly accessible endpoint, we're returning a real-looking
             * connection id for any invalid request. Returning a superficially successful
             * response reveals less information than returning an error response would.
             *
             * This was done as part of this ticket:
             * https://drata.atlassian.net/browse/ENG-9427
             */
            return this.getDummyConnectionId();
        }
    }

    private getDummyConnectionId(): string {
        const randomIdLength = 26;
        const randomId = randomString(randomIdLength).toUpperCase();
        const workOsConnectionIdPrefix = 'conn_';
        return `${workOsConnectionIdPrefix}${randomId}`;
    }

    /**
     * Looks for applicable account types based upon an email address.
     *
     * @param email The email address to check
     * @returns An array of AccountType for the given email
     */
    async getAccountTypesForEmail(email: string): Promise<Array<AccountAuthType>> {
        if (isSupportUser(email)) {
            throw new OurPreconditionFailedException(ErrorCode.SUPPORT_USER_NOT_SUPPORTED);
        }

        const accountTypes = new Array<AccountAuthType>();

        // If the email is found as a service user append it to the account types
        const serviceUser = await this.serviceUsersCoreService.getServiceUserByEmailNoFail(email);

        if (!isNil(serviceUser)) {
            accountTypes.push(AccountAuthType.SERVICE_ACCOUNT);
        }

        // Extract the domain from the email and lookup for it into the accounts
        // If we find any account matching the domain, we can assume it also can be an standard account
        const domain = getDomainFromEmail(email);
        const account = await this.accountsCoreService.getAccountByDomainIgnoreSupportUser(domain);
        if (!isNil(account)) {
            accountTypes.push(AccountAuthType.STANDARD_ACCOUNT);
        }

        const auditorUser = await this.auditorsOrchestrationService.getAuditorByEmail(email);

        if (!isNil(auditorUser)) {
            accountTypes.push(AccountAuthType.AUDITOR_ACCOUNT);
        }

        return accountTypes;
    }

    /**
     *
     * @param email
     * @returns
     */
    async getAuthenticateServiceUser(email: string): Promise<Entry> {
        const serviceUser = await this.serviceUsersCoreService.getServiceUserByEmailNoFail(email);

        if (isNil(serviceUser)) {
            throw new UnauthorizedException(ErrorCode.SERVICE_USER_NOT_FOUND);
        }

        return serviceUser.entry;
    }

    /**
     * Using the entry and the environment return the region.
     * @param dto
     * @returns region: NA || EU || APAC
     */
    async getRegion(dto: AuthEmailRequestDto): Promise<RegionType> {
        const { email } = dto;

        if (isSupportUser(email)) {
            throw new OurPreconditionFailedException(ErrorCode.SUPPORT_USER_NOT_SUPPORTED);
        }

        const serverRegion = getServerRegion();
        const account = await this.accountsCoreService.getAccountByEmailNoFail(email);

        if (isEmpty(account)) {
            throw new NotFoundException(ErrorCode.ACCOUNT_NOT_FOUND);
        }

        return serverRegion;
    }

    /**
     * Using the cname/trustId and the environment return the region.
     * @param {Request} request
     * @returns region: NA || EU
     */
    async getTrustCenterRegion(request: Request): Promise<RegionType> {
        const cname = getCnameFromHeader(request);
        const trustId = getTrustIdFromQuery(request);

        let trustCenter: TrustCenter;

        if (!isNil(trustId)) {
            trustCenter = await this.trustCenterFeatureService.getTrustCenterByTrustId(trustId);
        } else if (!isNil(cname)) {
            trustCenter = await this.trustCenterFeatureService.getTrustCenterByCNAME(cname);
        } else {
            throw new BadRequestException('CNAME or TrustId not provided');
        }

        const serverRegion = getServerRegion();

        if (!isEmpty(trustCenter)) {
            return serverRegion;
        }

        const euRegionUrl = safeConfigGet('region.EU.api');
        const apacRegionUrl = safeConfigGet('region.APAC.api');

        const crossRegionDomains: string[] = [];

        if (serverRegion === RegionType.NA) {
            if (euRegionUrl) {
                crossRegionDomains.push(euRegionUrl);
            }
            if (apacRegionUrl) {
                crossRegionDomains.push(apacRegionUrl);
            }
        }

        const getRegionForTrustCenter = async (regionUrl: string, req: Request) => {
            try {
                let baseUrl: string;
                let headers: Dictionary<any>;

                if (!isNil(trustId)) {
                    baseUrl = `${regionUrl}${AuthRoute.GET_TRUST_CENTER_REGION_MIRROR}?trustId=${trustId}`;

                    headers = pickBy({
                        origin: get(req, 'headers.origin'),
                    });
                } else if (!isNil(cname)) {
                    baseUrl = `${regionUrl}${AuthRoute.GET_TRUST_CENTER_REGION}`;

                    headers = pickBy({
                        'x-cname-host': get(req, 'headers.x-cname-host'),
                        origin: get(req, 'headers.origin'),
                    });
                }

                const { region } = (
                    await axios.get(baseUrl, {
                        headers,
                    })
                ).data;
                return region;
            } catch (e) {
                this.log('Region server unavailable...');
                return null;
            }
        };

        for (const crossRegionDomain of crossRegionDomains) {
            const trustCenterRegion = await getRegionForTrustCenter(crossRegionDomain, request);
            if (!isNil(trustCenterRegion)) {
                return trustCenterRegion;
            }
        }

        throw new BadRequestException('Invalid CNAME or TrustId');
    }

    private async getInvalidFrameworkTags(tags: number[]): Promise<number[]> {
        const frameworkTemplates = await this.frameworkTemplateRepository.find();
        return tags.filter(tag => !frameworkTemplates.some(ft => ft.tag === tag));
    }

    /**
     * @param authCode
     * @param tenantRouters
     * @param type
     * @param isAuditor
     * @param isServiceUser
     */
    private async getAccessTokenFromMultipleTenantRouters(
        authCode: AuthCode,
        tenantRouters: TenantRouter[],
        type: IdentitySource,
        isAuditor: boolean,
        isServiceUser?: boolean,
    ): Promise<{
        accToken: string | null;
        metadata: ConnectionMetadata | null;
        account: Account;
    }> {
        /**
         * First, we need to make sure that the grant is valid,
         * and use the okta code to get the correct access token from such payload.
         */
        const accToken = await this.getOktaAccessToken(
            tenantRouters,
            authCode,
            type,
            isAuditor,
            isServiceUser,
        );

        if (isEmpty(accToken)) {
            throw new UnauthorizedException(ErrorCode.OKTA_BAD_CODE);
        }

        const { account, metadata } = await this.getAccountAndMetadataFromOktaAccessToken(
            tenantRouters,
            accToken,
        );

        if (isNil(metadata)) {
            throw new UnauthorizedException(ErrorCode.OKTA_NO_ROUTER_MD_MATCH);
        }
        if (isNil(account)) {
            throw new UnauthorizedException(ErrorCode.OKTA_NO_ROUTER_ACCOUNT_MATCH);
        }

        return { accToken, metadata, account };
    }

    private async getOktaAccessToken(
        tenantRouters: TenantRouter[],
        authCode: AuthCode,
        type: IdentitySource,
        isAuditor: boolean,
        isServiceUser?: boolean,
    ): Promise<string | null> {
        for (const tenantRouter of tenantRouters) {
            const account = tenantRouter.account;
            const token = await tenantWrapper(account, async () => {
                const identitySource = await this.getIdentitySourceByAccount(account);
                if (IdentitySource.OKTA_IDENTITY !== identitySource) {
                    return null;
                }

                const metadata = await this.getConnectionMetadataFromAccount(account);

                authCode = {
                    ...authCode,
                    clientKey: metadata.clientKey,
                    clientSecret: metadata.clientSecret,
                };

                try {
                    const accToken = await this.auth.getAccessToken(
                        type,
                        authCode,
                        isAuditor,
                        isServiceUser,
                    );
                    return accToken;
                } catch (err) {
                    this.logger.warn(
                        PolloMessage.msg('Unable to get Okta Access Token').setError(err),
                    );
                }
                return null;
            });
            if (token) {
                return token;
            }
        }

        return null;
    }

    private async getAccountAndMetadataFromOktaAccessToken(
        tenantRouters: TenantRouter[],
        accessToken: string,
    ): Promise<{
        account: Account;
        metadata: ConnectionMetadata;
    }> {
        let metadata: ConnectionMetadata = null;
        let account: Account = null;

        const keySet = new Set<string>();

        tenantRouters.forEach(router => keySet.add(router.key));

        // if there are multiple keys, we cannot confidently tell which account to use.
        if (keySet.size !== 1) {
            throw new UnauthorizedException(ErrorCode.OKTA_MULTIPLE_ROUTER_KEYS_ON_LOGIN);
        }
        const key = keySet.values().next().value;

        const { sub } = this.jwtService.decode(accessToken);

        /**
         * this method returns all the linked accounts using the same tenant Router key and email.
         * if we have more than one, this IS a security concern since a user has two different
         * accounts linked to a single okta email on the same OIN app.
         * sanity check and throw if that's the case.
         * DO NOT CHANGE THIS LOGIC UNLESS THE IMPACTS OF https://drata.atlassian.net/browse/ENG-40143
         * ARE FULLY UNDERSTOOD
         */
        const subEntry = await this.entryCoreService.getEntryWithTenantRouterAccountsByEmail(
            sub,
            key,
        );

        if (subEntry?.accounts?.length !== 1) {
            throw new UnauthorizedException(
                ErrorCode.OKTA_MULTIPLE_ACCOUNTS_WITH_SAME_EMAIL_AND_TENANT_ROUTER_KEY,
            );
        }

        account = subEntry.accounts[0];

        metadata = await tenantWrapper(account, () =>
            this.getConnectionMetadataFromAccount(account),
        );

        return { account, metadata };
    }

    private async getConnectionMetadataFromAccount(account: Account): Promise<ConnectionMetadata> {
        // TODO: use getIdentityProviderConnections() when multi IdP is implemented and we fetch multiple ranked connections
        const connection = await this.connectionsCoreService.getIdentityProviderConnection();

        return connection.getMetadata();
    }

    /**
     *
     * @param error
     * @param subContext
     * @param account
     * @param domain
     * @param globalDataSource
     */
    @CacheBusterWithPrefix<void>({
        stores: [Caches.ACCOUNT],
    })
    @SuperCacheAccountBuster<void>({
        stores: [
            Caches.ACCOUNT,
            Caches.ACCOUNT_CONTRACT_DETAILS,
            Caches.COMPANY_DATA,
            Caches.COMPANY_PERSONNEL,
            Caches.CONTROL_TEMPLATE,
            Caches.ENTRY,
            Caches.FRAMEWORK,
            Caches.LIST_CONTROLS,
            Caches.PUBLIC_API_KEY,
            Caches.REQUIREMENTS,
            Caches.USER_BY_ENTRY_ID,
            Caches.WORKSPACE_ID,
        ],
        useArgs: 1,
    })
    private async handleNewAccountError(
        account: Account,
        error: any,
        domain: string,
    ): Promise<void> {
        this.printErrorWithNoAccount(domain, this.handleNewAccountError.name, error);

        const connection = await getGlobalDataSource();

        if (
            (error instanceof OurConflictException &&
                error.getCode() === ErrorCode.COMPANY_EXISTS) ||
            error instanceof ConflictException
        ) {
            throw new ConflictException(`An account was already created for ${domain}`);
        } else {
            const databaseName = get(account, 'databaseName');

            if (!isNil(databaseName)) {
                this.logger.log(
                    PolloAdapter.acct(`Dropping database: ${databaseName}`, account).setContext(
                        this.constructor.name,
                    ),
                );

                await connection.query(dropDatabase(databaseName));
            }

            const errorMessage = has(error, 'message')
                ? error.message
                : 'Error attempting to create account';

            throw new OurConflictException(errorMessage, ErrorCode.ACCOUNT_CREATION_FAILED);
        }
    }

    private printErrorWithNoAccount(domain: string, subContext: string, error: Error) {
        this.logger.error(
            PolloMessage.msg('Unable to create account')
                .setDomain(domain)
                .setContext(this.constructor.name)
                .setSubContext(subContext)
                .setIdentifier(error),
        );
    }

    private async getAccountFromEmail(email: string, emailDomain: string): Promise<Account | null> {
        let account: Account | null = null;
        if (isExcludedDomain(emailDomain)) {
            /**
             * Generic emails enter the system because of multi-domain enabled customers
             * that have users mapped to generic emails (which we don't filter at the moment at IDP-sync time)
             *
             * We need to specifically look for entries with the exact email here.
             */
            account = await this.accountsCoreService.getAccountByEmailNoFail(email);
        } else {
            // Since this is used only for standard accounts we just check the
            // given email and extract the domain for it, then we look for an account matching
            // this domain, the following method takes into account the multi-domain support already.
            account = await this.accountsCoreService.getAccountByDomain(emailDomain);
        }

        if (isNil(account) || account.status !== AccountStatus.ACTIVE) {
            const auditor = await this.auditorsOrchestrationService.getAuditorByEmail(email);
            if (!isNil(auditor)) {
                throw new OurConflictException(
                    'User should log in from the Auditor page',
                    ErrorCode.CONFLICT_AUDITOR_LOGIN,
                );
            }

            return null;
        }

        return account;
    }

    private async getAccountUserByEntry(account: Account, entry: Entry): Promise<User | null> {
        return tenantWrapperWithOverride(account, async () => {
            return this.usersCoreService.getUserByEmailNoFail(entry.email);
        });
    }

    private getSSOMagicLinkConfig(
        accountTypes: Array<AccountAuthType>,
    ): AuthIdentityProviderResources {
        return {
            accountTypes,
            identityProviders: [
                {
                    identityProviderType: IdentityProviderType.MAGIC_LINK,
                    providerResources: {},
                },
            ],
        };
    }

    /**
     * @deprecated This should be removed once we remove multi-idp-sso
     *
     * @param account
     * @param company
     * @param user
     * @param email
     * @param accountTypes
     * @returns
     */
    private async getLoginResourcesIdentityProviders(
        account: Account,
        company: Company,
        user: User,
        email: string,
        accountTypes: Array<AccountAuthType>,
    ): Promise<{
        identityProviderType: IdentityProviderType;
        providerResources: ProviderResourcesType;
    }> {
        const authConnection = await this.getActiveAuthConnection(account);
        const isAdmin = hasRole(user, [Role.ADMIN]);

        /**
         * Non-admins will be unable to authenticate into the application
         * if there's no identity provider connected.
         */
        if (isNil(authConnection) && !isAdmin) {
            this.logger.warn(
                PolloAdapter.acct(
                    `No IdP found and user with email ${email} is not an ADMIN`,
                    account,
                )
                    .setIdentifier({
                        email,
                        account,
                        accountTypes,
                    })
                    .setContext(this.constructor.name)
                    .setSubContext(this.getLoginResourcesIdentityProviders.name),
            );
            return {
                identityProviderType: IdentityProviderType.MAGIC_LINK,
                providerResources: {},
            };
        }

        // this tells LaunchDarkly who the user is
        this._eventBus.publish(new IdentifyEvent(user, account));

        const identityProviderType = getIdentityProviderType(authConnection);
        const providerResources = await this.getProviderLoginResources(
            account,
            user,
            authConnection,
            identityProviderType,
            company,
        );

        accountTypes.push(AccountAuthType.STANDARD_ACCOUNT);

        return { identityProviderType, providerResources };
    }

    private get userRepository(): UserRepository {
        return this.getCustomTenantRepository(UserRepository);
    }
    private get userRoleRepository(): UserRoleRepository {
        return this.getCustomTenantRepository(UserRoleRepository);
    }
    private get connectionsRepository(): ConnectionsRepository {
        return this.getCustomTenantRepository(ConnectionsRepository);
    }
    private get companyRepository(): Repository<Company> {
        return this.getTenantRepository(Company);
    }
    private get productRepository(): Repository<Product> {
        return this.getTenantRepository(Product);
    }
    private get userIdentityRepository(): UserIdentityRepository {
        return this.getCustomTenantRepository(UserIdentityRepository);
    }
}
