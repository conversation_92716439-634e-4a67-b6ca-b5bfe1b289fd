import { HttpStatus } from '@nestjs/common';
import axios from 'axios';
import config from 'config';
import https from 'https';
import { isEmpty } from 'lodash';
import {
    BaseInstruction,
    Flag,
    LaunchDarklyApiResponse,
    NewFlagRequest,
} from 'scripts/feature-flag/types';

const projectKey = 'drata-app';
const baseLaunchDarklyFlagsUrl = `https://app.launchdarkly.com/api/v2/flags/${projectKey}`;
const baseLaunchDarklyProjectsUrl = `https://app.launchdarkly.com/api/v2/projects/${projectKey}`;

const apiKey = config.get('launchdarkly.apiServiceToken');

const httpsAgent = new https.Agent({
    rejectUnauthorized: false,
});

const commonAxiosOptions = {
    headers: {
        Authorization: apiKey,
    },
    httpsAgent: httpsAgent,
};

const httpSuccessStatuses = new Set([HttpStatus.OK, HttpStatus.CREATED]);

const httpWrapper =
    <ArgsType extends Array<unknown>, DataType>(
        apiCall: (...args: ArgsType) => Promise<{ status: number; data: DataType }>,
    ) =>
        async (...args: ArgsType) => {
            const { status, data } = await apiCall(...args);

            if (httpSuccessStatuses.has(status)) {
                return data;
            }

            throw new Error(`Request failed with status code: ${status}`);
        };

export const listFlags = httpWrapper(
    async (query: string): Promise<LaunchDarklyApiResponse<Flag[]>> => {
        let url = baseLaunchDarklyFlagsUrl;
        if (query?.length > 0) {
            const filter = `query:${query}`;
            url = `${baseLaunchDarklyFlagsUrl}?filter=${filter}`;
        }

        const { status, data } = await axios.get<{ items: Flag[] }>(url, commonAxiosOptions);
        return { status, data: data.items };
    },
);

export const listAllFlags = httpWrapper(
    async (): Promise<LaunchDarklyApiResponse<Flag[]>> => {

        let offset = 0;
        const limit = 50;
        const allData: Flag[] = [];

        let remindingItems = 0;
        do {
            // eslint-disable-next-line no-await-in-loop
            const { data } = await axios.get<{ items: Flag[], totalCount: number }>(
                `${baseLaunchDarklyFlagsUrl}?limit=${limit}&offset=${offset}`, commonAxiosOptions);
            allData.push(...data.items);
            remindingItems = data.totalCount - allData.length;
            offset += limit;

        } while (remindingItems > 0 && allData.length < 5000);

        return { status: HttpStatus.OK, data: allData };
    },
);

export const getFlagByKey = httpWrapper(
    async (key: string, verbose: boolean = true): Promise<LaunchDarklyApiResponse<Flag>> => {
        if (verbose) {
            console.log('Getting flag details for key', key);
        }
        const url = `${baseLaunchDarklyFlagsUrl}/${key}`;
        const { data, status } = await axios.get<Flag>(url, commonAxiosOptions);
        return { data, status };
    },
);

export const updateFlag = httpWrapper(
    async (
        key: string,
        environment: string,
        instructions: BaseInstruction[],
    ): Promise<LaunchDarklyApiResponse<Flag>> => {
        const url = `${baseLaunchDarklyFlagsUrl}/${key}`;
        const { status, data } = await axios.patch<Flag>(
            url,
            { environmentKey: environment, instructions },
            {
                ...commonAxiosOptions,
                headers: {
                    ...commonAxiosOptions.headers,
                    'Content-Type': 'application/json; domain-model=launchdarkly.semanticpatch',
                },
            },
        );

        return { status, data };
    },
);

export const createFlag = httpWrapper(
    async (newFlagRequest: NewFlagRequest): Promise<LaunchDarklyApiResponse<Flag>> => {
        const newFlag = {};

        Object.entries(newFlagRequest).forEach(([key, value]) => {
            if (!isEmpty(value)) {
                newFlag[key] = value;
            }
        });

        const { status, data } = await axios.post<Flag>(
            baseLaunchDarklyFlagsUrl,
            newFlag,
            commonAxiosOptions,
        );
        return { status, data };
    },
);

export const getContextKinds = httpWrapper(
    async (): Promise<LaunchDarklyApiResponse<{ name: string;[key: string]: unknown }[]>> => {
        const { status, data } = await axios.get<{
            items: { name: string;[key: string]: unknown }[];
        }>(`${baseLaunchDarklyProjectsUrl}/context-kinds`, commonAxiosOptions);
        return { status, data: data.items };
    },
);

export const getContextAttributes = httpWrapper(
    async (
        contextName: string,
        environment: string,
    ): Promise<
        LaunchDarklyApiResponse<
            { names: { name: string; weight: string; redacted: boolean }[]; kind: string }[]
        >
    > => {
        let url = `${baseLaunchDarklyProjectsUrl}/environments/${environment}/context-attributes`;
        if (contextName) {
            url = `${url}?filter=kind equals "${contextName}"`;
        }
        const { status, data } = await axios.get<{
            items: { names: { name: string; weight: string; redacted: boolean }[]; kind: string }[];
        }>(url, commonAxiosOptions);
        return { status, data: data.items };
    },
);
