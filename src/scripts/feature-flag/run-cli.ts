import { select } from '@inquirer/prompts';
import { FlagActions } from 'scripts/feature-flag/enums/flag-actions.enum';
import { addNewRule, addValue, removeRule, removeValue } from 'scripts/feature-flag/helpers';
import {
    setFallthroughValue,
    setOffValue,
} from 'scripts/feature-flag/helpers/default-values.helper';
import {
    createFlagPrompt,
    environmentPromptForFlagDetails,
    environmentPromptForUpdate,
    getFlagDetailsBySearch,
    updateActionPrompt,
    yesOrNoPrompt,
} from 'scripts/feature-flag/launch-darkly-feature-prompts';
import { createFlag, getFlagByKey, listAllFlags, updateFlag } from 'scripts/feature-flag/launch-darkly.api';
import { Fallthrough, Flag } from 'scripts/feature-flag/types';

const createAFlag = async () => {
    const createFlagAnswers = await createFlagPrompt();
    console.log('Do you want to create the below flag?');
    console.log({
        Name: createFlagAnswers.name,
        Key: createFlagAnswers.key,
        Description: createFlagAnswers.description,
        Tags: createFlagAnswers.tags,
    });

    const createCommit: boolean = await yesOrNoPrompt();

    if (createCommit) {
        const newFlag = (await createFlag(createFlagAnswers)) ?? {};
        if (newFlag) {
            console.log('Flag created successfully!');
            console.log(newFlag);
        } else {
            console.log('There was an error creating your flag. Please try again');
        }
    } else {
        console.log('Create new flag has been aborted');
    }
};

const checkFlagInconsistent = (flag: Flag): string[] => {
    // ignore all dev env since we are testing with different value
    const importantEnvironments = ['local', 'qa', 'production'];

    const envs = flag.environments;
    const localFallthroughVariation = (envs['local'].fallthrough as Fallthrough).variation;
    const localOffVariation = envs['local'].offVariation;
    const errors = [] as string[];
    importantEnvironments.forEach(environment => {
        // checking each environment has turn on the rules
        if (envs[environment].on !== true) {
            errors.push(`Flag is not on in ${environment}`);
        }
        // checking each environment has the same fallthrough variation
        const fallthroughVariation: number = (envs[environment].fallthrough as Fallthrough).variation;
        if (fallthroughVariation !== localFallthroughVariation) {
            errors.push(`Fallthrough variation is different in ${environment}. ` +
                `Local is ${localFallthroughVariation}. ${environment} is ${fallthroughVariation}`);
        }

        const offVariation = envs[environment].offVariation;
        if (offVariation !== localOffVariation) {
            errors.push(`Off variation is different in ${environment}. `
                + `Local is ${localOffVariation}. ${environment} is ${offVariation}`);
        }
    });
    return errors; //     console.info(`✅ Flag ${flag.key} is consistent`);
};

const findAFlag = async () => {
    const foundFlag = await getFlagDetailsBySearch();
    if (!foundFlag) {
        return;
    }
    const specialAllEnv = 'All Environment'
    const env = await environmentPromptForFlagDetails([...Object.keys(foundFlag.environments), specialAllEnv]);
    const environmentFlagDetails = foundFlag.environments[env];

    console.log(`Name: ${foundFlag.name}`);
    console.log(`Key: ${foundFlag.key}`);
    console.log(`Description: ${foundFlag.description}`);
    console.log(`Type: ${foundFlag.kind}`);

    if (env === specialAllEnv) {
        console.log('Environment:');
        Object.keys(foundFlag.environments).forEach(environment => {
            console.log(`  ${environment}: ` +
                `on=${foundFlag.environments[environment].on}, ` +
                `fallthrough=${(foundFlag.environments[environment].fallthrough as Fallthrough).variation} ` +
                `off=${foundFlag.environments[environment].offVariation}`);
        });

        const errors = checkFlagInconsistent(foundFlag);
        if (errors.length > 0) {
            errors.forEach(error => {
                console.error(`❌ ${error}`);
            });
        } else {
            console.info(`✅ Flag is consistent`);
        }
        return;
    }

    const variationValues: boolean[] = [];

    foundFlag.variations.forEach(variation => {
        variationValues.push(variation.value);
    });

    console.log(`Values: ${variationValues}`);

    const variations = foundFlag.variations;
    const fallthroughVariation: number = (environmentFlagDetails.fallthrough as Fallthrough)
        .variation;
    const fallthroughValue: boolean = variations[fallthroughVariation].value;
    const offValue: boolean = variations[environmentFlagDetails.offVariation as number].value;

    console.log(`Flag is On?: ${environmentFlagDetails.on}`);
    console.log(`Fallthrough Value: ${fallthroughValue}`);
    console.log(`Off Value: ${offValue}`);
    console.log(`Number of Rules: ${environmentFlagDetails.rules.length}`);

    if (environmentFlagDetails.rules.length <= 0) {
        return;
    }

    console.log('Rules:');
    const rules = environmentFlagDetails.rules;
    let ruleNumber = 1;

    rules.forEach(rule => {
        const ruleDescription = rule.description?.length
            ? rule.description
            : `Nameless rule #${ruleNumber++}`;

        console.log(`Name: ${ruleDescription}`);
        console.log(`Returned Value: ${variations[rule.variation].value}`);

        rule.clauses.forEach(clause => {
            console.log(
                `When ${clause.contextKind} ${clause.attribute} ${clause.op}: ${clause.values}`,
            );
        });
    });
};

const getInstructionsAndCommitMessage = async (
    action: string,
    flagDetails: Flag,
    environment: string,
) => {
    switch (action) {
        case FlagActions.TURN_FLAG_ON:
            return {
                instructions: [{ kind: 'turnFlagOn' }],
                commitMessage: {
                    Flag: flagDetails.name,
                    Environment: environment,
                    Action: action,
                },
            };

        case FlagActions.TURN_FLAG_OFF:
            return {
                instructions: [{ kind: 'turnFlagOff' }],
                commitMessage: {
                    Flag: flagDetails.name,
                    Environment: environment,
                    Action: action,
                },
            };

        case FlagActions.DISABLE_CLIENT_SIDE:
            return {
                instructions: [
                    { kind: 'turnOffClientSideAvailability', value: 'usingEnvironmentId' },
                ],
                commitMessage: {
                    Flag: flagDetails.name,
                    Environment: environment,
                    Action: action,
                },
            };

        case FlagActions.ENABLE_CLIENT_SIDE:
            return {
                instructions: [
                    { kind: 'turnOnClientSideAvailability', value: 'usingEnvironmentId' },
                ],
                commitMessage: {
                    Flag: flagDetails.name,
                    Environment: environment,
                    Action: action,
                },
            };

        case FlagActions.ADD_VALUE:
            const addValueInstructions = await addValue(flagDetails, environment);
            return {
                instructions: addValueInstructions,
                commitMessage: {
                    Flag: flagDetails.name,
                    Environment: environment,
                    Action: action,
                    Value: addValueInstructions[0].values[0],
                },
            };

        case FlagActions.REMOVE_VALUE:
            const removeValueInstructions = await removeValue(flagDetails, environment);

            return {
                instructions: removeValueInstructions,
                commitMessage: {
                    Flag: flagDetails.name,
                    Environment: environment,
                    Action: action,
                    Value: removeValueInstructions[0].values[0],
                },
            };

        case FlagActions.ADD_NEW_RULE:
            const addNewRuleInstructions = await addNewRule(flagDetails, environment);
            return {
                instructions: addNewRuleInstructions,
                commitMessage: {
                    Flag: flagDetails.name,
                    Environment: environment,
                    Action: action,
                },
            };

        case FlagActions.CHANGE_FALLTHROUGH_VALUE:
            const changeFallthroughInstruction = await setFallthroughValue(flagDetails);
            return {
                instructions: changeFallthroughInstruction,
                commitMessage: {
                    Flag: flagDetails.name,
                    Environment: environment,
                    Action: action,
                },
            };

        case FlagActions.CHANGE_OFF_VALUE:
            const changeOffInstruction = await setOffValue(flagDetails);
            return {
                instructions: changeOffInstruction,
                commitMessage: {
                    Flag: flagDetails.name,
                    Environment: environment,
                    Action: action,
                },
            };

        case FlagActions.REMOVE_RULE:
            const removeRuleInstructions = await removeRule(flagDetails, environment);
            return {
                instructions: removeRuleInstructions,
                commitMessage: {
                    Flag: flagDetails.name,
                    Environment: environment,
                    Action: action,
                },
            };

        case FlagActions.ENABLE_TRACK_EVENTS:
            return {
                instructions: [{ kind: 'updateTrackEvents', trackEvents: true }],
                commitMessage: {
                    Flag: flagDetails.name,
                    Environment: environment,
                    Action: action,
                },
            };

        case FlagActions.DISABLE_TRACK_EVENTS:
            return {
                instructions: [{ kind: 'updateTrackEvents', trackEvents: false }],
                commitMessage: {
                    Flag: flagDetails.name,
                    Environment: environment,
                    Action: action,
                },
            };

        case FlagActions.DISABLE_RULE:
            throw new Error('Disable Rule functionality not yet implemented');

        default:
            throw new Error(`Invalid action: ${action}`);
    }
};

const updateAFlag = async () => {
    const updateFlagDetails = await getFlagDetailsBySearch();

    if (!updateFlagDetails) {
        return;
    }

    const environment = await environmentPromptForUpdate(updateFlagDetails.environments);
    const action = await updateActionPrompt();

    const { instructions, commitMessage } = await getInstructionsAndCommitMessage(
        action,
        updateFlagDetails,
        environment,
    );

    console.log('Do you want to make the below update?');
    console.log(commitMessage);
    const commitUpdate: boolean = await yesOrNoPrompt();

    if (commitUpdate) {
        await updateFlag(updateFlagDetails.key, environment, instructions);
        console.log('Flag updated successfully!');
    } else {
        console.log('Updated aborted!');
    }
};

const checkAllFlags = async () => {
    const flags = await listAllFlags();
    console.log(`Found ${flags.length} flags`);

    // Use Promise.all to fetch all flags in parallel instead of sequentially
    const flagsWithEnv = await Promise.all(
        flags.map(flag => getFlagByKey(flag.key, false))
    );

    // Process each flag with its environment data
    flagsWithEnv.forEach((flagWithEnv, index) => {
        const flag = flags[index];
        const errors = checkFlagInconsistent(flagWithEnv);
        if (errors.length > 0) {
            console.error(`❌ Flag ${flag.key} is inconsistent`);
            errors.forEach(error => {
                console.error(`  ${error}`);
            });
        } else {
            console.info(`✅ Flag ${flag.key} is consistent`);
        }
    });
};

const runCli = async () => {
    const CHECK_ALL_FLAGS = 'Check all flags (will take minutes)';
    const action = await select({
        message: 'What do you want to do?',
        choices: ['Create a flag', 'Find a flag', 'Update a flag', CHECK_ALL_FLAGS],
    });

    switch (action) {
        case 'Create a flag':
            await createAFlag();
            break;

        case 'Find a flag':
            await findAFlag();
            break;

        case 'Update a flag':
            await updateAFlag();
            break;

        case CHECK_ALL_FLAGS:
            await checkAllFlags();
            break;
    }
};

runCli().catch(error => {
    if (error.name !== 'ExitPromptError') {
        throw error;
    }
});
