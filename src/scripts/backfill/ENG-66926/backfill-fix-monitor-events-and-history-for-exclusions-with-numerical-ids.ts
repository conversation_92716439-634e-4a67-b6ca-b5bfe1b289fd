import { CheckResultStatus } from '@drata/enums';
import { MonitorData } from 'app/autopilot/classes/monitor-data.class';
import { resultExclusionFuzzyMatch } from 'app/autopilot/helper/fuzzy-matching.helper';
import { Event } from 'app/events/entities/event.entity';
import { ControlTestInstanceHistory } from 'app/monitors/entities/control-test-instance-history.entity';
import { MonitorInstanceExclusion } from 'app/monitors/entities/monitor-instance-exclusion.entity';
import { WorkflowEnabledTenantBackfill } from 'app/worker/workflows/backfills/base-classes/workflow-enabled-tenant-backfill';
import { BackfillClassActivityMapRecord } from 'app/worker/workflows/backfills/helpers/backfill-activity.types';
import { Account } from 'auth/entities/account.entity';
import { TemporalBackfillLogger } from 'commons/backfill/temporal-backfill.service';
import { DrataEntityManager } from 'commons/classes/drata-entity-manager.class';
import { numberProcess } from 'commons/helpers/cli.helper';
import { stringToNumber } from 'commons/helpers/string.helper';
import { CommanderProgramOptions } from 'commons/types/program-options.type';
import { isEmpty, isNil } from 'lodash';
import { Between, In } from 'typeorm';

// List of test IDs
export const DEFAULT_TEST_IDS: number[] = [
    2, 6, 7, 8, 9, 26, 37, 38, 39, 43, 45, 47, 48, 49, 50, 55, 57, 61, 63, 64, 65, 66, 86, 87, 88,
    94, 95, 96, 97, 98, 102, 104, 109, 111, 117, 119, 121, 129, 190, 191, 199,
];

/**
 * Backfill for ENG-66926
 *
 * This backfill fetches all controlTestInstanceHistory and events between a minimumDate and maximumDate,
 * with a CheckResultStatus of Failed, where the controlTestInstance TestIds are within the testIds array.
 *
 * Execute the command using the following:
 * ./bin/drata-cli backfill temporalBackfillFixMonitorEventsAndHistoryForExclusionsWithNumericalIds --run-as-workflow
 *
 * See more info with:
 * ./bin/drata-cli backfill temporalBackfillFixMonitorEventsAndHistoryForExclusionsWithNumericalIds --help
 *
 * Steps:
 * 1. Fetch data (histories, events, exclusions)
 *  1.1 - Fetch control test instance histories with the specified criteria
 *  1.2 - Fetch events related to these histories
 *  1.3 - Fetch all monitor instance exclusions for all test IDs
 * 2. Organize data by test ID
 *  2.1 - Create a map that correlates testId to control test histories and related events
 *  2.2 - Initialize the map with all test IDs
 *  2.3 - Group histories by control test instance test ID
 *  2.4 - Group events by the linked history's control test instance test id
 *  2.5 - Log summary information for each test ID
 * 3. Process data and fix events/histories
 *  3.1 Filter exclusions for by test ID
 *  3.2 - Process each history record and its events
 *   3.2.1: Get all events for each specific history
 *   3.2.2: Process each event of each history
 * 4. Save changes to the database
 *
 */
export class BackfillFixMonitorEventsAndHistoryForExclusionsWithNumericalIds extends WorkflowEnabledTenantBackfill {
    customOptions: CommanderProgramOptions[] = [
        {
            flags: '-ebs, --event-batch-size <num>',
            description: 'How many events should be processed in each batch',
            fn: stringToNumber,
            defaultValue: 150,
        },
        {
            flags: '-t, --test-ids <num>',
            description: 'One or more tests to run (space-delimited)',
            fn: numberProcess,
            defaultValue: [],
        },
    ];

    // Date range for the query
    private readonly minimumDate = new Date('2025-02-10T00:00:00.000Z'); // Issue start date -
    private readonly maximumDate = new Date('2025-05-01T23:59:59.999Z'); // Issue remediation date - deployment of https://drata.atlassian.net/browse/ENG-67536

    private readonly DEFAULT_EVENT_BATCH_SIZE = 150;
    private readonly HISTORY_BATCH_SIZE = 150;

    private testIds: number[];

    private eventBatchSize: number;

    async backfill({
        backfillName,
        tenantConnection,
        account,
        dryRun,
        customOptions, // Uncomment if needed
    }): Promise<void> {
        this.eventBatchSize = customOptions?.eventBatchSize ?? this.DEFAULT_EVENT_BATCH_SIZE;

        this.setTestIds(isEmpty(customOptions?.testIds) ? DEFAULT_TEST_IDS : customOptions.testIds);

        TemporalBackfillLogger.logInfo({
            msg: 'Starting backfill for fixing monitor events and history for exclusions with numerical IDs',
            backfillName,
            account,
        });

        TemporalBackfillLogger.logInfo({
            msg: `Running backfill for Test IDs: ${this.testIds.join(', ')}`,
            backfillName,
            account,
        });

        TemporalBackfillLogger.logInfo({
            msg: `Event batch size: ${this.eventBatchSize}`,
            backfillName,
            account,
        });

        TemporalBackfillLogger.logInfo({
            msg: `Fetching data between ${this.minimumDate.toISOString()} and ${this.maximumDate.toISOString()}`,
            backfillName,
            account,
        });

        if (dryRun) {
            TemporalBackfillLogger.logInfo({
                msg: 'Dry run mode - no changes will be made',
                backfillName,
                account,
            });
        }

        try {
            // Perform database operations within a transaction
            await tenantConnection.transaction(async (manager: DrataEntityManager) => {
                // Step 1: Fetch data (histories, events, exclusions)
                const { controlTestInstanceHistories, events, allExclusions } =
                    await this.fetchData(manager, backfillName, account);

                if (controlTestInstanceHistories.length === 0) {
                    TemporalBackfillLogger.logInfo({
                        msg: 'No control test instance histories found, skipping',
                        backfillName,
                        account,
                    });
                    return;
                }

                if (allExclusions.length === 0) {
                    TemporalBackfillLogger.logInfo({
                        msg: 'No exclusions found, skipping',
                        backfillName,
                        account,
                    });
                    return;
                }

                // Step 2: Organize data by test ID
                const testIdMap = this.organizeDataByTestId(
                    controlTestInstanceHistories,
                    events,
                    backfillName,
                    account,
                );

                // Step 3: Process data and fix events/histories
                const { updatedEvents, updatedHistories } = this.processAndFixData(
                    testIdMap,
                    allExclusions,
                    backfillName,
                    account,
                );

                // Step 4: process changes
                await this.processChanges(
                    manager,
                    updatedEvents,
                    updatedHistories,
                    dryRun,
                    backfillName,
                    account,
                );

                TemporalBackfillLogger.logInfo({
                    msg: 'Successfully executed backfill operations',
                    backfillName,
                    account,
                });
            });
        } catch (error) {
            TemporalBackfillLogger.logError({
                msg: 'Error executing backfill',
                backfillName,
                account,
                error,
            });
            throw error;
        }
    }

    /**
     * Step 1: Fetch all necessary data from the database
     * - Control test instance histories
     * - Events related to these histories
     * - Monitor instance exclusions for the test IDs
     */
    private async fetchData(
        manager: DrataEntityManager,
        backfillName: string,
        account: Account,
    ): Promise<{
        controlTestInstanceHistories: ControlTestInstanceHistory[];
        events: Event[];
        allExclusions: MonitorInstanceExclusion[];
    }> {
        // Step 1.1: Fetch control test instance histories
        const controlTestInstanceHistories = await this.fetchControlTestInstanceHistories(
            manager,
            backfillName,
            account,
        );

        if (controlTestInstanceHistories.length === 0) {
            return { controlTestInstanceHistories, events: [], allExclusions: [] };
        }

        // Step 1.2: Get the IDs of the control test instance histories
        const historyIds = controlTestInstanceHistories.map(
            (history: ControlTestInstanceHistory) => history.id,
        );

        // Step 1.3: Fetch events related to these histories
        const events = await this.fetchEvents(manager, historyIds, backfillName, account);

        // Step 1.4: Fetch all monitor instance exclusions for all test IDs
        const allExclusions = await this.fetchExclusions(manager, backfillName, account);

        return { controlTestInstanceHistories, events, allExclusions };
    }

    /**
     * Step 1.1: Fetch control test instance histories with the specified criteria
     */
    private async fetchControlTestInstanceHistories(
        manager: DrataEntityManager,
        backfillName: string,
        account: Account,
    ): Promise<ControlTestInstanceHistory[]> {
        const controlTestInstanceHistories = await manager.find(ControlTestInstanceHistory, {
            where: {
                createdAt: Between(this.minimumDate, this.maximumDate),
                checkResultStatus: CheckResultStatus.FAILED,
                controlTestInstance: {
                    testId: In(this.testIds),
                },
            },
            relations: ['controlTestInstance', 'controlTestInstance.products'],
        });

        TemporalBackfillLogger.logInfo({
            msg: `Found ${controlTestInstanceHistories.length} control test instance histories`,
            backfillName,
            account,
        });

        if (controlTestInstanceHistories.length === 0) {
            TemporalBackfillLogger.logInfo({
                msg: 'No control test instance histories found, skipping',
                backfillName,
                account,
            });
        }

        return controlTestInstanceHistories;
    }

    /**
     * Step 1.2: Fetch events related to the control test instance histories
     */
    private async fetchEvents(
        manager: DrataEntityManager,
        historyIds: number[],
        backfillName: string,
        account: Account,
    ): Promise<Event[]> {
        let events: Event[] = [];

        let batchCount = 1;
        for (let i = 0; i < historyIds.length; i += this.eventBatchSize) {
            const batch = historyIds.slice(i, i + this.eventBatchSize);
            // we want to purposefully fetch events slowly to avoid query timeouts.
            //eslint-disable-next-line no-await-in-loop
            const batchEvents = await manager
                .createQueryBuilder(Event, 'event')
                .addSelect('controlTestInstance.testId')
                .innerJoinAndSelect(
                    'event.controlTestInstanceHistory',
                    'controlTestInstanceHistory',
                )
                .innerJoin('controlTestInstanceHistory.controlTestInstance', 'controlTestInstance')
                .where('controlTestInstanceHistory.id IN (:...ids)', { ids: batch })
                .andWhere('event.checkResultStatus = :checkResultStatus', {
                    checkResultStatus: CheckResultStatus.FAILED,
                })
                .getMany();

            events = events.concat(batchEvents);
            TemporalBackfillLogger.logInfo({
                msg: `Fetched ${batchEvents.length} events for batch ${batchCount}. (${events.length} Total)`,
                backfillName,
                account,
            });
            batchCount++;
        }

        TemporalBackfillLogger.logInfo({
            msg: `Found ${events.length} events related to the control test instance histories`,
            backfillName,
            account,
        });

        return events;
    }

    /**
     * Step 1.3: Fetch all monitor instance exclusions for the test IDs
     */
    private async fetchExclusions(
        manager: DrataEntityManager,
        backfillName: string,
        account: Account,
    ): Promise<MonitorInstanceExclusion[]> {
        const allExclusions = await manager
            .createQueryBuilder(MonitorInstanceExclusion, 'exclusion')
            .addSelect('controlTestInstance.testId')
            .innerJoin('exclusion.controlTestInstance', 'controlTestInstance')
            .where('controlTestInstance.testId IN (:...testIds)', {
                testIds: this.testIds,
            })
            .withDeleted()
            .getMany();

        TemporalBackfillLogger.logInfo({
            msg: `Found ${allExclusions.length} exclusions for all test IDs`,
            backfillName,
            account,
        });

        return allExclusions;
    }

    /**
     * Step 2: Organize data by test ID
     * - Create a map that correlates testId to control test histories and related events
     * - Group histories and events by test ID
     * - Log summary information for each test ID
     */
    organizeDataByTestId(
        controlTestInstanceHistories: ControlTestInstanceHistory[],
        events: Event[],
        backfillName: string,
        account: Account,
    ): Map<number, { histories: ControlTestInstanceHistory[]; events: Event[] }> {
        // Step 2.1: Create a map that correlates testId to control test histories and related events
        const testIdMap = new Map<
            number, // testId
            {
                histories: ControlTestInstanceHistory[];
                events: Event[];
            }
        >();

        // Step 2.2: Initialize the map with all test IDs
        this.testIds.forEach((testId: number) => {
            testIdMap.set(testId, { histories: [], events: [] });
        });

        // Step 2.3: Group histories by test ID
        this.groupHistoriesByTestId(controlTestInstanceHistories, testIdMap);

        // Step 2.4: Group events by test ID
        this.groupEventsByTestId(events, testIdMap);

        // Step 2.5: Log summary information for each test ID
        this.logTestIdSummary(testIdMap, backfillName, account);

        return testIdMap;
    }

    /**
     * Step 2.3: Group histories by test ID
     */
    private groupHistoriesByTestId(
        controlTestInstanceHistories: ControlTestInstanceHistory[],
        testIdMap: Map<number, { histories: ControlTestInstanceHistory[]; events: Event[] }>,
    ): void {
        controlTestInstanceHistories.forEach((history: ControlTestInstanceHistory) => {
            const testId = history.controlTestInstance.testId;
            const entry = testIdMap.get(testId);
            if (entry) {
                entry.histories.push(history);
            }
        });
    }

    /**
     * Step 2.4: Group events by test ID
     */
    private groupEventsByTestId(
        events: Event[],
        testIdMap: Map<number, { histories: ControlTestInstanceHistory[]; events: Event[] }>,
    ): void {
        events.forEach((event: Event) => {
            const history = event.controlTestInstanceHistory;
            if (history && history.controlTestInstance) {
                const testId = history.controlTestInstance.testId;
                const entry = testIdMap.get(testId);
                if (entry) {
                    entry.events.push(event);
                }
            }
        });
    }

    /**
     * Step 2.5: Log summary information for each test ID
     */
    private logTestIdSummary(
        testIdMap: Map<number, { histories: ControlTestInstanceHistory[]; events: Event[] }>,
        backfillName: string,
        account: Account,
    ): void {
        for (const [testId, data] of testIdMap.entries()) {
            TemporalBackfillLogger.logInfo({
                msg: `Test ID ${testId}: ${data.histories.length} histories, ${data.events.length} events`,
                backfillName,
                account,
            });
        }
    }

    /**
     * Step 3: Process data and fix events/histories
     * - Loop through each test ID
     * - Process each history record and its events
     * - Update events and histories based on exclusions
     */
    private processAndFixData(
        testIdMap: Map<number, { histories: ControlTestInstanceHistory[]; events: Event[] }>,
        allExclusions: MonitorInstanceExclusion[],
        backfillName: string,
        account: Account,
    ): { updatedEvents: Event[]; updatedHistories: ControlTestInstanceHistory[] } {
        const updatedEvents: Event[] = [];
        const updatedHistories: ControlTestInstanceHistory[] = [];

        // Loop through each test ID
        for (const [testId, data] of testIdMap.entries()) {
            TemporalBackfillLogger.logInfo({
                msg: `Processing test ID ${testId}`,
                backfillName,
                account,
            });

            // Step 3.1: Filter exclusions for this test ID
            const exclusions = this.filterExclusionsForTestId(
                allExclusions,
                testId,
                backfillName,
                account,
            );

            // Step 3.2: Process each history record
            this.processHistoryRecords(
                data.histories,
                data.events,
                exclusions,
                updatedEvents,
                updatedHistories,
                backfillName,
                account,
                testId,
            );
        }

        return { updatedEvents, updatedHistories };
    }

    /**
     * Step 3.1: Filter exclusions for a specific test ID
     */
    filterExclusionsForTestId(
        allExclusions: MonitorInstanceExclusion[],
        testId: number,
        backfillName: string,
        account: Account,
    ): MonitorInstanceExclusion[] {
        const exclusions = allExclusions.filter(
            exclusion => exclusion.controlTestInstance.testId === testId,
        );

        TemporalBackfillLogger.logInfo({
            msg: `Found ${exclusions.length} exclusions for test ID ${testId}`,
            backfillName,
            account,
        });

        return exclusions;
    }

    /**
     * Step 3.2: Process history records and their events
     */
    private processHistoryRecords(
        histories: ControlTestInstanceHistory[],
        events: Event[],
        exclusions: MonitorInstanceExclusion[],
        updatedEvents: Event[],
        updatedHistories: ControlTestInstanceHistory[],
        backfillName: string,
        account: Account,
        testId: number,
    ): void {
        if (exclusions.length === 0) {
            TemporalBackfillLogger.logInfo({
                msg: `No exclusions linked to test ${testId} , skipping.`,
                backfillName,
                account,
            });
            return;
        }
        const historyEventMap = new Map<number, Event[]>();

        // populate the map to empty arrays
        histories.forEach((history: ControlTestInstanceHistory) => {
            historyEventMap.set(history.id, []);
        });
        // populate the map with the events
        events.forEach((event: Event) => {
            const historyId = event.controlTestInstanceHistory?.id;
            if (historyId) {
                const historyEvents = historyEventMap.get(historyId);
                if (historyEvents) {
                    historyEvents.push(event);
                }
            }
        });

        // Process each history record
        for (const history of histories) {
            // Step 3.2.1: Get all events for this history
            const historyEvents = historyEventMap.get(history.id);

            if (!historyEvents) {
                TemporalBackfillLogger.logInfo({
                    msg: `No events found for history ${history.id}`,
                    backfillName,
                    account,
                });
                continue;
            }

            let allEventsFixed = true;
            let eventsUpdated = false;

            // Step 3.2.2: Process each event
            for (const event of historyEvents) {
                const eventUpdated = this.processEvent(
                    event,
                    exclusions,
                    allEventsFixed,
                    updatedEvents,
                    backfillName,
                    account,
                );

                if (eventUpdated.updated) {
                    eventsUpdated = true;
                }

                if (!eventUpdated.allFixed) {
                    allEventsFixed = false;
                }
            }

            // Step 3.2.3: Update history status if needed
            this.updateHistoryIfNeeded(history, allEventsFixed, eventsUpdated, updatedHistories);
        }
    }

    /**
     * Step 3.2.2: Process an individual event
     * - Parse the event metadata
     * - Check each failed item against exclusions
     * - Update the event if needed
     */
    processEvent(
        event: Event,
        exclusions: MonitorInstanceExclusion[],
        allEventsFixed: boolean,
        updatedEvents: Event[],
        backfillName: string,
        account: Account,
    ): { updated: boolean; allFixed: boolean } {
        let localAllEventsFixed = allEventsFixed;
        let updated = false;

        // Parse the event metadata
        let metadata: any = {};
        try {
            metadata = JSON.parse(event.metadata);
        } catch (error) {
            TemporalBackfillLogger.logError({
                msg: `Error parsing metadata for event ${event.id}`,
                backfillName,
                account,
                error,
            });
            return { updated: false, allFixed: localAllEventsFixed };
        }

        // Check if the event has response data with fail/pass arrays
        if (metadata.response && metadata.response.data && metadata.response.data.results) {
            const results = metadata.response.data.results;

            // Initialize arrays if they don't exist
            if (!results.fail) results.fail = [];
            if (!results.pass) results.pass = [];
            if (!results.exclusions) results.exclusions = [];

            const activeExclusions = exclusions.filter(exclusion => {
                // 1. The exclusion must have been created before or at the time of the event
                const wasCreatedBeforeEvent = exclusion.createdAt <= event.createdAt;
                // 2. The exclusion must either not be deleted, or deleted after the event
                const wasActiveAtEventTime =
                    isNil(exclusion.deletedAt) || exclusion.deletedAt > event.createdAt;

                return wasCreatedBeforeEvent && wasActiveAtEventTime;
            });

            const { needsUpdate, newFail, newPass, allFixed } = this.processEventItems(
                results.fail,
                results.pass,
                event,
                activeExclusions,
            );

            localAllEventsFixed = allFixed;

            if (needsUpdate) {
                // Update the metadata
                metadata.response.data.results.fail = newFail;
                metadata.response.data.results.pass = newPass;

                // Update the event
                event.metadata = JSON.stringify(metadata);

                // If all items are now passing OR all failed items have exclusions, update the event status
                if (newFail.filter(item => !item.exclusionId).length === 0) {
                    event.checkResultStatus = CheckResultStatus.PASSED;
                }

                updatedEvents.push(event);
                updated = true;
            }
        }

        return { updated, allFixed: localAllEventsFixed };
    }

    /**
     * Step 3.2.2.1: Process event items (fail/pass)
     * - Check each failed item against exclusions
     * - Move items from fail to pass if they have valid exclusions
     */
    processEventItems(
        failItems: MonitorData[],
        passItems: any[],
        event: Event,
        exclusions: MonitorInstanceExclusion[],
    ): { needsUpdate: boolean; newFail: any[]; newPass: any[]; allFixed: boolean } {
        let needsUpdate = false;
        let allFixed = true;
        let exclusionId: number;

        // Check each failed item against exclusions
        const newFail: any[] = [];
        const newPass: any[] = [...passItems]; // Add existing pass items to newPass

        // Check each fail item against exclusions
        failItems.forEach((item: any) => {
            const itemId = item.id?.toString();
            // if there's no way to identify exclusion, skip
            if (!itemId) {
                newFail.push(item);
                return;
            }

            if (item.exclusionId) {
                // Keep in fail, this already has an exclusion
                newFail.push(item);
                return;
            }

            // Create a Data object from the item for fuzzy matching
            const itemData = {
                id: itemId,
                // Add other properties if available in the item
                accountId: item.accountId,
                region: item.region,
            } as MonitorData;

            // Check if this item has a valid exclusion using fuzzy matching
            const hasValidExclusion = exclusions.some(exclusion => {
                // save the exclusion id in case the item has a valid exclusion
                exclusionId = exclusion.id;

                /**
                 * NOTE: the resultExclusionFuzzyMatch takes into consideration that every single item
                 * has an accountId. This is not always the case for some older events. Adding a special case for an early return for these exclusions
                 */

                if (
                    exclusion.targetId.includes('::') &&
                    exclusion.targetId.split('::')[1] === itemId
                ) {
                    return true;
                }

                // Use resultExclusionFuzzyMatch for comprehensive fuzzy matching
                return resultExclusionFuzzyMatch(itemData, exclusion);
            });

            if (hasValidExclusion) {
                // fail, but include exclusion
                item.exclusionId = exclusionId;
                newFail.push(item);
                needsUpdate = true;
            } else {
                // Keep in fail
                newFail.push(item);
                allFixed = false;
            }
        });

        return { needsUpdate, newFail, newPass, allFixed };
    }

    /**
     * Step 3.2.3: Update history status if needed
     */
    private updateHistoryIfNeeded(
        history: ControlTestInstanceHistory,
        allEventsFixed: boolean,
        eventsUpdated: boolean,
        updatedHistories: ControlTestInstanceHistory[],
    ): void {
        // If all events for this history are fixed, update the history status
        if (
            allEventsFixed &&
            eventsUpdated &&
            history.checkResultStatus === CheckResultStatus.FAILED
        ) {
            history.checkResultStatus = CheckResultStatus.PASSED;
            updatedHistories.push(history);
        }
    }

    /**
     * Step 4: Save changes to the database
     */
    private async processChanges(
        manager: DrataEntityManager,
        updatedEvents: Event[],
        updatedHistories: ControlTestInstanceHistory[],
        dryRun: boolean,
        backfillName: string,
        account: Account,
    ): Promise<void> {
        if (!dryRun) {
            if (updatedEvents.length > 0) {
                TemporalBackfillLogger.logInfo({
                    msg: `Saving ${updatedEvents.length} updated events`,
                    backfillName,
                    account,
                });
                let batchCount = 1;
                for (let i = 0; i < updatedEvents.length; i += this.eventBatchSize) {
                    // eslint-disable-next-line no-await-in-loop
                    await manager.save(Event, updatedEvents.slice(i, i + this.eventBatchSize));
                    TemporalBackfillLogger.logInfo({
                        msg: `Saved ${
                            updatedEvents.slice(i, i + this.eventBatchSize).length
                        } updated events for batch ${batchCount}`,
                        backfillName,
                        account,
                    });
                    batchCount++;
                }
            } else {
                TemporalBackfillLogger.logInfo({
                    msg: 'No events to update',
                    backfillName,
                    account,
                });
            }

            if (updatedHistories.length > 0) {
                TemporalBackfillLogger.logInfo({
                    msg: `Saving ${updatedHistories.length} updated history records`,
                    backfillName,
                    account,
                });
                let batchCount = 1;
                for (let i = 0; i < updatedHistories.length; i += this.HISTORY_BATCH_SIZE) {
                    const batch = updatedHistories.slice(i, i + this.HISTORY_BATCH_SIZE);
                    // eslint-disable-next-line no-await-in-loop
                    await manager.save(ControlTestInstanceHistory, batch);
                    TemporalBackfillLogger.logInfo({
                        msg: `Saved ${batch.length} updated history records for batch ${batchCount}`,
                        backfillName,
                        account,
                    });
                    batchCount++;
                }
            } else {
                TemporalBackfillLogger.logInfo({
                    msg: 'No history records to update',
                    backfillName,
                    account,
                });
            }
        } else {
            TemporalBackfillLogger.logInfo({
                msg: `Dry run: Would have updated ${updatedEvents.length} events and ${updatedHistories.length} history records`,
                backfillName,
                account,
                metadata: {
                    updatedEvents: updatedEvents.map(event => event.id),
                    updatedHistories: updatedHistories.map(history => history.id),
                },
            });
        }
    }
    setTestIds(testIds: number[]) {
        this.testIds = testIds;
    }
}

/**
 * Export the backfill class activity map record
 */
export const temporalBackfillFixMonitorEventsAndHistoryForExclusionsWithNumericalIds: BackfillClassActivityMapRecord =
    {
        backfillClass: new BackfillFixMonitorEventsAndHistoryForExclusionsWithNumericalIds(),
    };
