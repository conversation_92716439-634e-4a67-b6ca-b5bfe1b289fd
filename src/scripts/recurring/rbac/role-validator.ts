import { Role } from 'commons/enums/users/role.enum';

type RoleValidationResult = {
    isValid: boolean;
    roleIds: string;
    error?: Error;
};

export function validateRoles(roles: string[]): RoleValidationResult {
    let roleIds = '';
    const rolesToUpdate = roles[0].split(',');
    if (rolesToUpdate?.length > 1) {
        return {
            isValid: false,
            error: new Error(
                `Invalid number of roles in --role: Must be one and only one of role.enum.ts: ${rolesAsString()}`,
            ),
            roleIds,
        };
    }

    if (rolesToUpdate) {
        for (const roleKey of rolesToUpdate) {
            if (!Role[roleKey]) {
                return {
                    isValid: false,
                    error: new Error(
                        `Invalid role in --role: ${roleKey}. Must be one of ${rolesAsString()}`,
                    ),
                    roleIds,
                };
            }
        }
        roleIds = rolesToUpdate.map(roleKey => Role[roleKey]).join(', ');
    }
    return {
        isValid: true,
        roleIds,
    };
}

export function rolesAsString() {
    return Object.entries(Role)
        .filter(x => !isNaN(typeof x[1] === 'string' ? parseInt(x[1]) : x[1]))
        .map(x => `${x[0]} = ${x[1]}`);
}
