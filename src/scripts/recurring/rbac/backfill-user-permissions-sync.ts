import { UNIVERSAL_MANAGE_SUBJECTS } from 'app/users/constants/user-permissions.constants';
import { RolesPermissions } from 'app/users/entities/roles-permissions.entity';
import { User } from 'app/users/entities/user.entity';
import { UsersPermissions } from 'app/users/entities/users-permissions.entity';
import { getContext } from 'app/worker/helpers/logging-activity-context.helper';
import { WorkflowEnabledTenantBackfill } from 'app/worker/workflows/backfills/base-classes/workflow-enabled-tenant-backfill';
import { BackfillClassActivityMapRecord } from 'app/worker/workflows/backfills/helpers/backfill-activity.types';
import { TemporalBackfillLogger } from 'commons/backfill/temporal-backfill.service';
import { Action } from 'commons/enums/users/action.enum';
import { Role } from 'commons/enums/users/role.enum';
import { Subject } from 'commons/enums/users/subject.enum';
import { numberProcess, stringProcess } from 'commons/helpers/cli.helper';
import { CommanderProgramOptions } from 'commons/types/program-options.type';
import { isEmpty, isNil } from 'lodash';
import { validateRoles } from 'scripts/recurring/rbac/role-validator';

/**
 * This backfill is used to populate the users_permissions_map denormalized table.
 * By default, it deletes the entire table and repopulates it based on tables: user, role, permission
 */
export class BackfillUserPermissionsSync extends WorkflowEnabledTenantBackfill {
    customOptions: CommanderProgramOptions[] = [
        {
            flags: '--role [role...]',
            description: 'Role to update users_permissions_map for, space delimited',
            fn: stringProcess,
            defaultValue: [],
        },
        {
            flags: '--subjects [subjects...]',
            description: 'Subject to update users_permissions_map for, space delimited',
            fn: numberProcess,
            defaultValue: [],
        },
        {
            flags: '--isDeleting <boolean>',
            description: 'Completely wipe users_permissions_map and re-sync',
            defaultValue: false,
        },
    ];

    async backfill({
        backfillName,
        tenantConnection,
        account,
        dryRun,
        customOptions,
    }): Promise<void> {
        try {
            const context = getContext();
            TemporalBackfillLogger.logInfo({
                msg: `Starting backfill for: ${account.id}`,
                account,
                backfillName,
            });
            const { role, subjects, isDeleting } = customOptions;
            TemporalBackfillLogger.logInfo({
                msg: 'Running with params:',
                metadata: { subjects, role, isDeleting },
                account,
                backfillName,
            });

            if (subjects?.length > 0 && role) {
                TemporalBackfillLogger.logError({
                    msg: 'User error: Parameters',
                    error: new Error('--subjects and --role are defined. Only one allowed.'),
                    account,
                    backfillName,
                });
                return;
            }
            let newSubjects = false;
            if (subjects?.length > 0) {
                TemporalBackfillLogger.logInfo({
                    msg: 'Running with specific subjects',
                    metadata: { subjects },
                    account,
                    backfillName,
                });
                newSubjects = true;
            }
            const { isValid: isValidRole, roleIds, error: roleError } = validateRoles(role);
            if (!isValidRole && roleError) {
                TemporalBackfillLogger.logError({ msg: 'Invalid role', error: roleError });
            }

            const softDeletedIds: number[] = [];
            const softDeletedEntries: any[] = [];

            const queryRunner = tenantConnection.createQueryRunner();
            await queryRunner.connect();
            await queryRunner.startTransaction();
            try {
                const userRepository = queryRunner.manager.getRepository(User);
                const userPermissionsRepository =
                    queryRunner.manager.getRepository(UsersPermissions);
                const rolesPermissionsRepository =
                    queryRunner.manager.getRepository(RolesPermissions);

                const rolesPermissions = await rolesPermissionsRepository.find({
                    relations: ['role', 'permission'],
                });

                //get all users with roles and permissions
                const usersQuery = userRepository
                    .createQueryBuilder('User')
                    .withDeleted()
                    .leftJoinAndSelect('User.userRoles', 'UserRoles')
                    .leftJoinAndSelect('User.permissions', 'UserPermission')
                    .leftJoinAndSelect('UserPermission.permission', 'Permission')
                    .leftJoinAndSelect('UserRoles.product', 'Product');

                if (roleIds) {
                    usersQuery.where(`UserRoles.fk_role_id IN (${roleIds})`);
                }

                const users = await usersQuery.getMany();

                //Get control manager  users
                const controlManagerUsers = users.filter(x =>
                    x.userRoles.some(r => [Role.CONTROL_MANAGER].includes(r.roleId)),
                );
                //Get control manager user with restricted view (without ViewAllControls permission)
                const restrictedViewControlUsers = controlManagerUsers.filter(
                    u => !u.permissions.some(p => p.permission.subject === Subject.ViewAllControls),
                );

                //Get risks manager users
                const riskManagerUsers = users.filter(x =>
                    x.userRoles.some(r => [Role.RISK_MANAGER].includes(r.roleId)),
                );

                //Get risk manager user with restricted view (without ViewAllRisks permission)
                const restrictedViewRisksUsers = riskManagerUsers.filter(
                    us => !us.permissions.some(p => p.permission.subject == Subject.ViewAllRisks),
                );

                TemporalBackfillLogger.logInfo({
                    msg: `Cleaning user permissions for ${account.domain}`,
                    account,
                    backfillName,
                });
                let permissionsToDelete: number[] = [];
                if (newSubjects) {
                    //get user permission ids to delete
                    const currentPermissionsQuery = userPermissionsRepository
                        .createQueryBuilder('usersPermissions')
                        .innerJoin('usersPermissions.permission', 'permission')
                        .select('usersPermissions.id')
                        .where('permission.subject in (:...subjects)', { subjects });

                    if (roleIds) {
                        currentPermissionsQuery.where(`fk_role_id IN (${roleIds})`);
                    }
                    const currentPermissions = await currentPermissionsQuery.getMany();
                    if (!isEmpty(currentPermissions)) {
                        //delete related users permissions
                        TemporalBackfillLogger.logInfo({
                            msg: `Found permissions with specified subjects ${subjects} for ${account.domain}, exiting`,
                            account,
                            backfillName,
                        });
                        return;
                    }
                } else {
                    const allPermissionsQuery = userPermissionsRepository
                        .createQueryBuilder('usersPermissions')
                        .withDeleted()
                        .leftJoinAndSelect('usersPermissions.user', 'User')
                        .leftJoinAndSelect('usersPermissions.permission', 'Permission')
                        .leftJoinAndSelect('usersPermissions.role', 'Role');
                    if (roleIds) {
                        allPermissionsQuery.where(`fk_role_id IN (${roleIds})`);
                    }
                    const allPermissions = await allPermissionsQuery.getMany();
                    TemporalBackfillLogger.logInfo({
                        msg: `Found ${allPermissions.length} permissions for ${account.domain}`,
                        metadata: { perm: JSON.stringify(allPermissions[0], null, 2) },
                        account,
                        backfillName,
                    });
                    if (isDeleting) {
                        permissionsToDelete = allPermissions.map(p => p.id);
                    } else {
                        for (const perm of allPermissions) {
                            if (!isNil(perm.deletedAt)) {
                                // find if other user/permission/role pair exists and is not deleted
                                const foundDuplicate = allPermissions.find(
                                    p =>
                                        p.user.id === perm.user.id &&
                                        p.permission.id === perm.permission.id &&
                                        p.role.id === perm.role.id &&
                                        isNil(p.deletedAt),
                                );
                                if (isNil(foundDuplicate)) {
                                    TemporalBackfillLogger.logInfo({
                                        msg: `Found permissions softDeleted permission without duplicate, will not hard delete`,
                                        metadata: { softDeleted: perm, foundDuplicate },
                                        account,
                                        backfillName,
                                    });
                                    softDeletedIds.push(perm.id);
                                    softDeletedEntries.push({
                                        userId: perm.user.id,
                                        permissionId: perm.permission.id,
                                        roleId: perm.role.id,
                                    });
                                } else {
                                    TemporalBackfillLogger.logInfo({
                                        msg: `Found permissions softDeleted permission with duplicate, will hard delete`,
                                        metadata: { softDeleted: perm, foundDuplicate },
                                        account,
                                        backfillName,
                                    });
                                    permissionsToDelete.push(perm.id);
                                }
                            } else {
                                permissionsToDelete.push(perm.id);
                            }
                        }
                    }
                }

                TemporalBackfillLogger.logInfo({
                    msg: `Adding permissions to ${users.length} users for ${account.domain}`,
                    account,
                    backfillName,
                });

                const userPermissionsArray: UsersPermissions[] = [];
                let id: number | null = 0;
                if (newSubjects) {
                    id = null;
                }
                const createdAt = new Date();

                //iterate users
                users.map(u => {
                    context.heartbeat({ userId: u.id, action: 'iterating users' });

                    //check all roles
                    u.userRoles.map(userRole => {
                        if (roleIds && !roleIds.includes(userRole.roleId.toString())) {
                            return; // Skip processing for roles that aren't in rolesToUpdate
                        }

                        const permissionsByRoles = rolesPermissions.filter(
                            x => x.roleId === userRole.roleId,
                        );
                        permissionsByRoles.map(p => {
                            //if has subject parameter and is not in the list return / continue
                            if (
                                !isEmpty(subjects) &&
                                !subjects.some((s: number) => s === Number(p.permission.subject))
                            ) {
                                return;
                            }

                            // check array of softDeleted
                            if (
                                softDeletedEntries.some(
                                    x =>
                                        x.userId === u.id &&
                                        x.permissionId === p.permissionId &&
                                        x.roleId === userRole.roleId,
                                )
                            ) {
                                return;
                            }

                            const isReadOnly = !isNil(userRole?.readOnlyAt);

                            //Check restricted view control manager
                            if (
                                p.roleId === Role.CONTROL_MANAGER && // current is control manager role
                                p.permission.subject === Subject.ViewAllControls && // current subject is ViewAllControls
                                restrictedViewControlUsers.some(us => us.id === u.id) // current user is restricted view
                            ) {
                                return;
                            }
                            //Check restricted view risks manager
                            if (
                                p.roleId === Role.RISK_MANAGER && // current is risk manager role
                                p.permission.subject === Subject.ViewAllRisks && // current subject is ViewAllRisks
                                restrictedViewRisksUsers.some(us => us.id === u.id) // current user is restricted view
                            ) {
                                return;
                            }
                            //check if exists in current array
                            const permissionExists = userPermissionsArray.find(x => {
                                if (x.role) {
                                    return (
                                        x.user.id === u.id &&
                                        x.role.id === userRole.roleId &&
                                        x.permission.id === p.permissionId &&
                                        x.productId === userRole.product?.id
                                    );
                                }
                            });

                            if (isNil(permissionExists)) {
                                const permission = p.permission;
                                //if is read only do not insert manage actions except for the ones that are supposed to be given to everyone
                                if (
                                    !(
                                        isReadOnly &&
                                        permission.action === Action.MANAGE &&
                                        !UNIVERSAL_MANAGE_SUBJECTS.includes(permission.subject)
                                    ) &&
                                    p.role
                                ) {
                                    if (!isNil(id)) {
                                        ++id;
                                        while (softDeletedIds.includes(id)) {
                                            ++id;
                                        }
                                    }
                                    userPermissionsArray.push({
                                        id: roleIds ? undefined : id,
                                        user: u,
                                        permission: permission,
                                        role: p.role,
                                        createdAt,
                                        productId: userRole.product?.id,
                                    } as UsersPermissions);
                                }
                            }
                        });
                    });
                });

                context.heartbeat({ action: 'deleting permissions' });
                if (isEmpty(permissionsToDelete)) {
                    TemporalBackfillLogger.logInfo({
                        msg: `No permissions to delete for ${account.domain}`,
                        account,
                        backfillName,
                    });
                } else {
                    TemporalBackfillLogger.logInfo({
                        msg: `Deleting ${permissionsToDelete.length} permissions for ${account.domain}`,
                        account,
                        backfillName,
                        metadata: {
                            totalPermissionsToDelete: permissionsToDelete.length,
                            sliceOfIds: permissionsToDelete.slice(0, 5),
                        },
                    });

                    await queryRunner.manager.delete(UsersPermissions, permissionsToDelete);
                }

                // Add detailed logging for each user and their permissions
                users.slice(0, 2).forEach((user, index) => {
                    const userPermissions = userPermissionsArray.filter(p => p.user.id === user.id);
                    if (!isEmpty(userPermissions)) {
                        TemporalBackfillLogger.logInfo({
                            msg: `User permissions summary: slice of ${userPermissions.length} permissions for ${account.domain}: index ${index}`,
                            account,
                            backfillName,
                            metadata: {
                                userId: user.id,
                                roles: user.userRoles.map(ur => ({
                                    role: Role[ur.roleId],
                                    productId: ur.product?.id,
                                    readOnlyAt: ur.readOnlyAt,
                                })),
                                permissionsSlice: userPermissions.slice(0, 2).map(p => ({
                                    action: Action[p.permission.action],
                                    subject: Subject[p.permission.subject],
                                    roleId: p.role?.id ? Role[p.role.id] : null,
                                    productId: p.productId,
                                })),
                            },
                        });
                    }
                });

                TemporalBackfillLogger.logInfo({
                    msg: `Inserting ${userPermissionsArray.length} permissions for ${account.domain}`,
                    account,
                    backfillName,
                    metadata: {
                        totalUsers: users.length,
                        totalPermissions: userPermissionsArray.length,
                        restrictedControlManagers: restrictedViewControlUsers.length,
                        restrictedRiskManagers: restrictedViewRisksUsers.length,
                    },
                });
                context.heartbeat({ action: 'inserting permissions' });

                await queryRunner.manager.save(UsersPermissions, userPermissionsArray, {
                    chunk: 2000,
                    reload: false,
                });

                if (dryRun) {
                    TemporalBackfillLogger.logInfo({
                        msg: 'dryRun true. Rolling back transaction.',
                        account,
                        backfillName,
                    });
                    await queryRunner.rollbackTransaction();
                } else {
                    await queryRunner.commitTransaction();
                }
            } catch (transactionError) {
                TemporalBackfillLogger.logError({
                    msg: `ERROR: Something went wrong. Rolling back transaction`,
                    error: transactionError,
                    account,
                    backfillName,
                });
                await queryRunner.rollbackTransaction();
            }
        } catch (backfillError) {
            TemporalBackfillLogger.logError({
                msg: `ERROR: Backfill failed`,
                error: backfillError,
                account,
                backfillName,
            });
            throw backfillError;
        }
    }
}

export const temporalBackfillUserPermissionsSync: BackfillClassActivityMapRecord = {
    backfillClass: new BackfillUserPermissionsSync(),
};
