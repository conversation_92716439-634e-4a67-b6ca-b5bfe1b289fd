import { exportAccountWorkflowV5 } from 'app/worker/workflows/administration/account-migration/export/export-account.v5.workflow';
import { Account } from 'auth/entities/account.entity';
import { type DrataDataSource } from 'commons/classes/drata-data-source.class';
import { getGlobalDataSource } from 'commons/factories/data-source.manager';
import { TemporalClient, getTemporalClient } from 'commons/helpers/temporal/client';
import config from 'config';
import { shouldRunAccountMigrationTests } from 'tests/app/worker/administration/account-migration/test.helper';

describe('exportAccountWorkflowV5', () => {
    let temporalClient: TemporalClient;
    let globalDataSource: DrataDataSource;
    let account: Account;

    beforeAll(async () => {
        temporalClient = await getTemporalClient();
        globalDataSource = await getGlobalDataSource();
        const accountRepo = globalDataSource.getRepository(Account);
        account = await accountRepo.findOneOrFail({
            where: { domain: 'drata.com' },
        });
    });

    it('should successfully execute the export account workflow', async () => {
        if (!shouldRunAccountMigrationTests('exportAccountWorkflowV5')) {
            return;
        }
        const tablesToHandleSeparately = ['event', 'user'];
        return expect(
            temporalClient.executeWorkflow(exportAccountWorkflowV5, {
                taskQueue: config.get('temporal.taskQueues.temporal-default'),
                args: [{ accountId: account.id, tablesToHandleSeparately }],
                memo: { accountId: account.id, domain: account.domain },
            }),
        ).resolves.not.toThrow();
    });

    afterAll(async () => {
        if (globalDataSource) await globalDataSource.destroy();
    });
});
