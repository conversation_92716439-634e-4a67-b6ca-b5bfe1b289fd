/* eslint-disable no-restricted-imports */
import {
    AST_NODE_TYPES,
    AST_TOKEN_TYPES,
    ESLintUtils,
    ParserServicesWithTypeInformation,
    TSESTree,
} from '@typescript-eslint/utils';
import { ReportDescriptor, RuleContext, RuleFixer } from '@typescript-eslint/utils/ts-eslint';
import * as ts from 'typescript';
import {
    addImportIfNeeded,
    buildDecoratorMap,
    findImportDeclarations,
    findPropertyNamedIn,
    hasPropertyWithLiteralValue,
} from './typescript-eslint.helper';

const API_PROPERTY_DECORATOR = 'ApiProperty';
const API_PROPERTY_OPTIONAL_DECORATOR = 'ApiPropertyOptional';

declare module 'typescript' {
    interface TypeChecker {
        // Exposing part of the internal TS API since it's the most accurate
        // way to check it

        /**
         * @returns `true` if the given type is an array type:
         * - `Array<foo>`
         * - `Readonly<PERSON>rray<foo>`
         * - `foo[]`
         * - `readonly foo[]`
         */
        isArrayType(type: Type): type is TypeReference;
    }
}

// undefined indicates we have no opinion
// null indicates their current value should be removed
// string is our suggested value
interface SwaggerType {
    type?: string;
    format?: string;
    isArray?: string | null;
    items?: string;
    enum?: string;
    oneOf?: string | null;
    nullable?: string | null;
    required?: string | null;
    selfRequired?: string | null;
}
type SwaggerField = keyof SwaggerType;

type MessageId =
    | 'missingDecorator'
    | 'wrongDecorator'
    | 'fixDecorator'
    | 'missingArgument'
    | 'missingProp'
    | 'addProp'
    | 'wrongValue'
    | 'fixValue'
    | 'extraProp'
    | 'removeProp';

const createRule = ESLintUtils.RuleCreator(
    () =>
        'https://www.notion.so/drata/swagger-api-property-must-match-type-7bd58df0332845bd93cfdfeab68afb7d',
);
export default createRule({
    name: 'swagger-api-property-must-match-type',
    defaultOptions: [],
    meta: {
        type: 'problem',
        docs: {
            description: `The @ApiProperty() decorator settings should match class property.`,
        },
        fixable: 'code',
        hasSuggestions: true,
        schema: [],
        messages: {
            missingDecorator: 'The "{{property}}" property should have an @{{value}} decorator.',
            wrongDecorator: 'The "{{property}}" property should have an @{{value}} decorator.',
            fixDecorator: 'Change decorator to @{{value}}.',
            missingArgument:
                'The "{{property}}" @ApiProperty decorator must have an object argument.',
            missingProp: 'The "{{property}}" @ApiProperty decorator should have "{{keyValue}}".',
            addProp: 'Add "{{keyValue}}".',
            wrongValue:
                'The "{{property}}" @ApiProperty decorator\'s "{{key}}" should be "{{value}}".',
            fixValue: 'Change value of the "{{key}}" property to "{{value}}".',
            extraProp: 'The "{{property}}" @ApiProperty decorator\'s "{{key}}" should removed.',
            removeProp: 'Remove "{{key}}".',
        },
    },
    create: context => ({
        PropertyDefinition: propertyDefinition => {
            // Make sure we're on a class DTO class.
            if (
                propertyDefinition.parent.type !== AST_NODE_TYPES.ClassBody ||
                propertyDefinition.parent.parent.type !== AST_NODE_TYPES.ClassDeclaration ||
                !propertyDefinition.parent.parent.id?.name?.endsWith('Dto')
            ) {
                return;
            }

            // Make sure we've got type info.
            if (propertyDefinition.typeAnnotation?.type !== AST_NODE_TYPES.TSTypeAnnotation) {
                return;
            }

            const classProperty =
                propertyDefinition.key.type == AST_NODE_TYPES.Identifier
                    ? propertyDefinition.key.name
                    : 'The property';

            const decoratorMap = buildDecoratorMap(propertyDefinition.decorators || []);

            // Make every property has the @ApiProperty or @ApiPropertyOptional decorator.
            const decorator =
                decoratorMap.get(API_PROPERTY_DECORATOR) ??
                decoratorMap.get(API_PROPERTY_OPTIONAL_DECORATOR);
            if (!decorator) {
                const suggetedDecorator = propertyDefinition.optional
                    ? API_PROPERTY_OPTIONAL_DECORATOR
                    : API_PROPERTY_DECORATOR;
                const swaggerImports = findImportDeclarations(
                    context.sourceCode.ast,
                    '@nestjs/swagger',
                );
                context.report({
                    node: propertyDefinition,
                    messageId: 'missingDecorator',
                    data: { property: classProperty, value: suggetedDecorator },
                    *fix(fixer: RuleFixer) {
                        const ruleFix = addImportIfNeeded(
                            fixer,
                            swaggerImports,
                            '@nestjs/swagger',
                            suggetedDecorator,
                        );
                        if (ruleFix) {
                            yield ruleFix;
                        }
                        yield fixer.insertTextBefore(
                            propertyDefinition,
                            `@${suggetedDecorator}({})\n`,
                        );
                    },
                });
                return;
            }

            // Make sure the decorator has an object literal argument.
            if (decorator.expression.type !== AST_NODE_TYPES.CallExpression) {
                return;
            }
            if (
                decorator.expression.arguments.length != 1 ||
                decorator.expression.arguments[0].type != AST_NODE_TYPES.ObjectExpression
            ) {
                const expression = decorator.expression;
                context.report({
                    node: expression,
                    messageId: 'missingArgument',
                    data: { property: classProperty },
                    fix: (fixer: RuleFixer) =>
                        fixer.replaceTextRange(
                            [expression.callee.range[1], expression.range[1]],
                            '({})',
                        ),
                });
                return;
            }

            // Avoid dealing with the serialized JSON fields.
            if (
                decoratorMap.has('JSONStringToObject') ||
                decoratorMap.has('JSONStringToObjectArray')
            ) {
                return;
            }

            const decoratorIdentifier = decorator.expression.callee as TSESTree.Identifier;
            const decoratorProperties = decorator.expression.arguments[0];

            // Check if oneOf is present in the decorator
            const hasOneOf = findPropertyNamedIn('oneOf', decoratorProperties) !== undefined;

            if (decoratorMap.has(API_PROPERTY_OPTIONAL_DECORATOR) && !propertyDefinition.optional) {
                const swaggerImports = findImportDeclarations(
                    context.sourceCode.ast,
                    '@nestjs/swagger',
                );
                context.report({
                    node: decoratorIdentifier,
                    messageId: 'wrongDecorator',
                    data: { property: classProperty, value: API_PROPERTY_DECORATOR },
                    suggest: [
                        {
                            messageId: 'fixDecorator',
                            data: { value: API_PROPERTY_DECORATOR },
                            *fix(fixer: RuleFixer) {
                                const ruleFix = addImportIfNeeded(
                                    fixer,
                                    swaggerImports,
                                    '@nestjs/swagger',
                                    API_PROPERTY_DECORATOR,
                                );
                                if (ruleFix) {
                                    yield ruleFix;
                                }
                                yield fixer.replaceText(
                                    decoratorIdentifier,
                                    API_PROPERTY_DECORATOR,
                                );
                            },
                        },
                    ],
                });
            }

            const services = ESLintUtils.getParserServices(context);
            const swaggerType = getTypeAndFormatFromTS(services, propertyDefinition);
            if (!swaggerType) {
                return;
            }

            // If we've marked it as binary, it should be a string type. We'll
            // assume there's another decorator to convert it to property's type.
            if (hasPropertyWithLiteralValue('format', 'binary', decoratorProperties)) {
                swaggerType.type = "'string'";
                swaggerType.isArray = undefined;
                swaggerType.items = undefined;
                swaggerType.required = undefined;
                swaggerType.selfRequired = undefined;
            }

            // If they're using @ApiPropertyOptional, required will always be
            // false.
            if (decoratorMap.has(API_PROPERTY_OPTIONAL_DECORATOR)) {
                swaggerType.required = null;
                swaggerType.selfRequired = null;
            }

            // If oneOf is present in the decorator, don't validate type or oneOf fields
            // since oneOf is a complex structure that can't be easily inferred
            if (hasOneOf) {
                swaggerType.type = undefined;
                swaggerType.oneOf = undefined;
            }

            // Divide the swagger keys with values.
            const [existingKeys, missingKeys] = (Object.keys(swaggerType) as SwaggerField[]).reduce(
                (result, key) => {
                    const exists = findPropertyNamedIn(key, decoratorProperties);
                    result[exists ? 0 : 1].push(key);
                    return result;
                },
                [[], []] as [SwaggerField[], SwaggerField[]],
            );

            // Incorrect existing values get their own suggestion.
            for (const key of existingKeys) {
                const descriptor = compareCurrentProperty(
                    context,
                    classProperty,
                    decoratorProperties,
                    key,
                    swaggerType[key],
                );
                if (descriptor) {
                    context.report(descriptor);
                }
            }

            // All the missing properties can go in one suggestion.
            const neededKeys = missingKeys.filter(
                key => swaggerType[key] !== null && swaggerType[key] !== undefined,
            );
            const otherDescriptor = addMissingProperties(
                classProperty,
                decoratorIdentifier,
                decoratorProperties,
                neededKeys,
                swaggerType,
            );
            if (otherDescriptor) {
                context.report(otherDescriptor);
            }
        },
    }),
});

function compareCurrentProperty(
    context: RuleContext<MessageId, []>,
    classProperty: string,
    properties: TSESTree.ObjectExpression,
    key: SwaggerField,
    expectedValue: string | null | undefined,
): ReportDescriptor<MessageId> | undefined {
    if (expectedValue === undefined) {
        return;
    }

    const property = findPropertyNamedIn(key, properties);
    if (!property) {
        return;
    }

    const current = property.value;

    if (expectedValue === null) {
        // Null indicates it should be removed.
        if (
            (current.type === AST_NODE_TYPES.Literal && current.raw !== expectedValue) ||
            (current.type === AST_NODE_TYPES.Identifier && current.name !== expectedValue)
        ) {
            const fix = (fixer: RuleFixer) => {
                const sourceCode = context.sourceCode;
                // Start from the previous token so we also remove in leading white space.
                const tokenBefore = sourceCode.getTokenBefore(property);
                const tokenAfter = sourceCode.getTokenAfter(property);
                const startRange = tokenBefore ? tokenBefore.range[1] : property.range[0];
                // If there's a comma after, remove it too.
                if (
                    tokenAfter &&
                    tokenAfter.type == AST_TOKEN_TYPES.Punctuator &&
                    tokenAfter.value === ','
                ) {
                    return fixer.removeRange([startRange, tokenAfter.range[1]]);
                }
                return fixer.removeRange([startRange, property.range[1]]);
            };
            return {
                node: property,
                messageId: 'extraProp',
                data: { property: classProperty, key },
                // fix,
                suggest: [
                    {
                        messageId: 'removeProp',
                        data: { key },
                        fix,
                    },
                ],
            };
        }
    } else if (
        current.type === AST_NODE_TYPES.ArrayExpression ||
        (current.type === AST_NODE_TYPES.ArrowFunctionExpression &&
            current.body.type == AST_NODE_TYPES.ArrayExpression) ||
        (current.type === AST_NODE_TYPES.Literal && current.raw !== expectedValue) ||
        (current.type === AST_NODE_TYPES.Identifier && current.name !== expectedValue)
    ) {
        let node = current;
        if (
            current.type === AST_NODE_TYPES.ArrowFunctionExpression &&
            current.body.type == AST_NODE_TYPES.ArrayExpression
        ) {
            node = current.body;
        }
        const fix = (fixer: RuleFixer) => fixer.replaceText(node, expectedValue);
        return {
            node,
            messageId: 'wrongValue',
            data: { property: classProperty, key, value: expectedValue },
            // fix,
            suggest: [
                {
                    messageId: 'fixValue',
                    data: { key, value: expectedValue },
                    fix,
                },
            ],
        };
    }
}

function addMissingProperties(
    classProperty: string,
    callee: TSESTree.Expression,
    properties: TSESTree.ObjectExpression,
    missingKeys: SwaggerField[],
    swaggerType: SwaggerType,
): ReportDescriptor<MessageId> | undefined {
    if (missingKeys.length === 0) {
        return;
    }
    const keyValue = missingKeys.map(key => `${key}: ${swaggerType[key]}`).join(', ');
    const fix = (fixer: RuleFixer) => {
        if (properties.properties.length) {
            return fixer.insertTextBefore(properties.properties[0], `${keyValue},`);
        }
        return fixer.replaceText(properties, `{ ${keyValue} }`);
    };
    return {
        node: callee,
        messageId: 'missingProp',
        data: { property: classProperty, keyValue },
        // fix,
        suggest: [{ messageId: 'addProp', data: { keyValue }, fix }],
    };
}

// Type guard for UnionOrIntersectionType
function isUnionOrIntersectionType(type: ts.Type): type is ts.UnionOrIntersectionType {
    return !!(type.flags & ts.TypeFlags.UnionOrIntersection);
}

function getTypeAndFormatFromTS(
    services: ParserServicesWithTypeInformation,
    node: TSESTree.PropertyDefinitionComputedName | TSESTree.PropertyDefinitionNonComputedName,
): SwaggerType | null {
    let isArray: string | undefined | null = undefined;
    let nullable: string | undefined | null = undefined;
    const required = node.optional ? 'false' : undefined;

    const checker = services.program.getTypeChecker();
    let type = services.getTypeAtLocation(node);

    // Look for union types: nullable fields `boolean | null`, complex unions `string | number | boolean`
    if (
        node.typeAnnotation?.type == AST_NODE_TYPES.TSTypeAnnotation &&
        node.typeAnnotation.typeAnnotation.type == AST_NODE_TYPES.TSUnionType
    ) {
        const types = node.typeAnnotation.typeAnnotation.types;
        const [nil, nonNil] = types.reduce(
            (result, t) => {
                result[
                    t.type == AST_NODE_TYPES.TSNullKeyword ||
                    t.type == AST_NODE_TYPES.TSUndefinedKeyword
                        ? 0
                        : 1
                ].push(t);
                return result;
            },
            [[], []] as [TSESTree.TypeNode[], TSESTree.TypeNode[]],
        );
        if (nil.length > 0) {
            nullable = 'true';
        }
        if (nonNil.length === 1) {
            type = services.getTypeAtLocation(nonNil[0]);
        } else if (nonNil.length > 1) {
            // Complex union type with multiple non-null types
            // Don't suggest specific types since this should use oneOf
            return { nullable, required };
        }
    }

    // Optional fields get turned into a union with undefined... but confusingly
    // the Boolean type is a union of the true and false literal so ignore those.
    if (node.optional && type.flags & ts.TypeFlags.Union && !(type.flags & ts.TypeFlags.Boolean)) {
        type = (type as ts.UnionType).types.find(t => !(t.flags & ts.TypeFlags.Undefined))!;
    }

    if (checker.isArrayType(type)) {
        const typeArguments = checker.getTypeArguments(type);
        if (typeArguments.length === 1) {
            isArray = 'true';
            type = typeArguments[0];
        }
    } else {
        isArray = null;
    }

    if (type.flags & ts.TypeFlags.EnumLiteral && isUnionOrIntersectionType(type)) {
        const isNumeric = type.types.every(t => t.flags & ts.TypeFlags.NumberLiteral);
        if (isNumeric) {
            // return { enum: `getKeys(${type.symbol.name})`, isArray, nullable };
        }
        return { enum: type.symbol.name, isArray, nullable, required };
    }
    if (type.flags & ts.TypeFlags.Boolean) {
        return { type: "'boolean'", isArray, nullable, required };
    }
    if (type.flags & ts.TypeFlags.Number) {
        return { type: "'number'", isArray, nullable, required };
    }
    if (type.flags & ts.TypeFlags.String) {
        return { type: "'string'", isArray, nullable, required };
    }
    if (type.flags & ts.TypeFlags.Object && type.symbol) {
        const objectFlags = (type as ts.ObjectType).objectFlags;
        if (type.symbol.name === 'Date') {
            return { type: "'string'", format: "'date-time'", isArray, nullable, required };
        }
        if (!(objectFlags & ts.ObjectFlags.Class)) {
            // For non-class objects, treat it as a simple object since they
            // won't have decorators.
            if (isArray) {
                return {
                    type: "'array'",
                    items: "{ type: 'object' }",
                    isArray: null,
                    nullable,
                    required,
                };
            }
            return {
                type: "'object'",
                isArray: null,
                nullable,
                required: null,
                selfRequired: required,
            };
        }
        if (type.symbol.name === '__type') {
            // TODO needs a unit test
            // It's a complex type we don't understand
            return null;
        }
        return { type: type.symbol.name, isArray, nullable, required };
    }
    return { nullable, required };
}
