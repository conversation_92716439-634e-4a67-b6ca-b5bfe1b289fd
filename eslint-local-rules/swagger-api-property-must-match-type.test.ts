// Run this test:
//     yarn ts-node eslint-local-rules/swagger-api-property-must-match-type.test.ts
/* eslint-disable no-restricted-imports */
/* eslint-disable @typescript-eslint/no-misused-promises -- Node.js test functions return promises but RuleTester expects void-returning functions. The promise handling is managed by the Node.js test runner. */
import { RuleTester } from '@typescript-eslint/rule-tester';
import * as test from 'node:test';
import rule from './swagger-api-property-must-match-type';

RuleTester.afterAll = test.after;
RuleTester.describe = test.describe;
RuleTester.it = test.it;
RuleTester.itOnly = test.it.only;

const ruleTester = new RuleTester({
    parser: '@typescript-eslint/parser',
    parserOptions: {
        ecmaVersion: 2021,
        tsconfigRootDir: `${__dirname}/fixtures`,
        project: './tsconfig.json',
    },
});
ruleTester.run('swagger-api-property-must-match-type', rule, {
    valid: [
        {
            name: 'Non DTO class, ignored',
            code: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
export class SimpleDt {
    @ApiProperty({})
    status?: string;
}`,
        },
        {
            name: '@ApiPropertyOptional(), correct',
            code: `
function ApiPropertyOptional(options: Record<string, any>): PropertyDecorator { return () => options }
export class SimpleDto {
    @ApiPropertyOptional({type: 'string'})
    status?: string;
}`,
        },
        {
            name: 'boolean, correct',
            code: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
export class SimpleDto {
    @ApiProperty({
        type: 'boolean',
    })
    status: boolean;
}`,
        },
        {
            name: 'boolean | null, correct',
            code: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
export class SimpleDto {
    @ApiProperty({ type: 'boolean', nullable: true })
    nullableBool: boolean | null;
}`,
        },
        {
            name: 'boolean[], correct',
            code: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
export class SimpleDto {
    @ApiProperty({ type: 'boolean', isArray: true })
    status: boolean[];
}`,
        },
        {
            name: 'string, correct',
            code: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
export class SimpleDto {
    @ApiProperty({ type: 'string' })
    status: string;
}`,
        },
        {
            name: 'string | null, correct',
            code: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
export class SimpleDto {
    @ApiProperty({ type: 'string', nullable: true})
    status: string | null;
}`,
        },
        {
            name: 'string[], correct',
            code: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
export class SimpleDto {
  @ApiProperty({ type: 'string', isArray: true })
  status: string[];
}`,
        },
        {
            name: 'Array<string>, correct',
            code: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
export class SimpleDto {
  @ApiProperty({ type: 'string', isArray: true })
  status: Array<string>;
}`,
        },
        {
            name: 'number, correct',
            code: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
export class SimpleDto {
    @ApiProperty({ type: 'number'})
    status: number;
}`,
        },
        {
            name: 'enum, string, correct',
            code: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
export enum StringEnum { GOOGLE = 'Google', OKTA = 'Okta' }
export class SimpleDto {
    @ApiProperty({
        enum: StringEnum,
        type: 'string',
    })
    status: StringEnum;
}`,
        },
        {
            name: 'enum, integer, correct',
            code: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
export enum IntegerEnum { GOOGLE = 1, OKTA = 2 }
export class SimpleDto {
    @ApiProperty({
        enum: IntegerEnum,
        type: 'number',
    })
    status: IntegerEnum;
}`,
        },
        {
            name: 'enum, single integer, omitted',
            // If there's only a single value the type checker ignores the symbol so we can't offer up a suggestion.
            code: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
export enum IntegerEnum { GOOGLE = 1 }
export class SimpleDto {
    @ApiProperty({
    })
    status: IntegerEnum;
}`,
        },
        {
            name: 'enum[], integer, correct',
            code: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
export enum IntegerEnum { GOOGLE = 1, OKTA = 2 }
export class SimpleDto {
    @ApiProperty({
        enum: IntegerEnum,
        type: 'number',
        isArray: true
    })
    status: IntegerEnum[];
}`,
        },
        {
            name: 'Date, correct',
            code: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
export class SimpleDto {
    @ApiProperty({
        type: 'string',
        format: 'date-time'
    })
    status: Date;
}`,
        },
        {
            name: 'Relation<T>, correct',
            code: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
class Something {}
type Relation<T> = T;
export class SimpleDto {
    @ApiProperty({
        type: Something,
    })
    status: Relation<Something>;
}`,
        },
        {
            name: 'type, correct',
            code: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
type SomeType = { id: number; name: string; };
export class SimpleDto {
    @ApiProperty({
        type: 'object',
    })
    status: SomeType;
}`,
        },
        {
            name: 'type[], correct',
            code: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
type SomeType = { id: number; name: string; };
export class SimpleDto {
    @ApiProperty({
        type: 'array',
        items: { type: 'object' }
    })
    status: SomeType[];
}`,
        },
        {
            name: 'class, lazy function, correct',
            code: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
export class SimpleDto {
    @ApiProperty({
        type: () => Lazy,
    })
    status: Lazy;
}
class Lazy {}
`,
        },
        {
            name: 'optional, correct',
            code: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
export class SimpleDto {
    @ApiProperty({ type: 'string', required: false })
    optionalProp?: string;
}`,
        },
        {
            name: "@ApiProperty({format: 'binary'}), ignored",
            code: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
export type UploadedFileType = {
    fieldname: string;
    originalname: string;
    encoding: string;
    mimetype: string;
    buffer: Buffer;
    size: number;
};
export class SimpleDto {
    @ApiProperty({
        type: 'string',
        format: 'binary',
        isArray: true,
    })
    file: UploadedFileType[];
}`,
        },
        {
            name: 'with @JSONStringToObjectArray(), ignored',
            code: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
type SomeType = { id: number; name: string; };
export class SimpleDto {
    @ApiProperty({
    })
    @JSONStringToObjectArray()
    status: SomeType[];
}`,
        },
        {
            name: 'with @JSONStringToObject(), ignored',
            code: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
type SomeType = { id: number; name: string; };
export class SimpleDto {
    @ApiProperty({
    })
    @JSONStringToObject()
    status: SomeType;
}`,
        },
        {
            name: 'oneOf with union type, correct',
            code: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
export class SimpleDto {
    @ApiProperty({
        oneOf: [
            { type: 'string', example: 'any' },
            { type: 'number', example: 1 },
            { type: 'boolean', example: true },
        ],
    })
    value: string | number | boolean;
}`,
        },
        {
            name: 'oneOf with union type and type field, correct',
            code: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
export class SimpleDto {
    @ApiProperty({
        oneOf: [
            { type: 'string', example: 'any' },
            { type: 'number', example: 1 },
            { type: 'boolean', example: true },
        ],
        type: 'string',
    })
    value: string | number | boolean;
}`,
        },
    ],
    //
    ////////////////////////////////////////////////////////////////////////////
    //
    invalid: [
        {
            name: '@ApiProperty(), omitted',
            code: `
export class SimpleDto {
    status: string;
}`,
            errors: [
                {
                    messageId: 'missingDecorator',
                    data: { property: 'status', value: 'ApiProperty' },
                },
            ],
            output: `import { ApiProperty } from '@nestjs/swagger';

export class SimpleDto {
    @ApiProperty({})
status: string;
}`,
        },
        {
            name: '@ApiPropertyOptional(), omitted',
            code: `
export class SimpleDto {
    status?: string;
}`,
            errors: [
                {
                    messageId: 'missingDecorator',
                    data: { property: 'status', value: 'ApiPropertyOptional' },
                },
            ],
            output: `import { ApiPropertyOptional } from '@nestjs/swagger';

export class SimpleDto {
    @ApiPropertyOptional({})
status?: string;
}`,
        },
        {
            name: '@ApiProperty(), no arguments',
            code: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
export enum IntegerEnum { GOOGLE = 1, OKTA = 2 }
export class SimpleDto {
    @ApiProperty()
    status: IntegerEnum[];
}`,
            errors: [{ messageId: 'missingArgument', data: { property: 'status' } }],
            output: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
export enum IntegerEnum { GOOGLE = 1, OKTA = 2 }
export class SimpleDto {
    @ApiProperty({})
    status: IntegerEnum[];
}`,
        },
        {
            name: '@ApiPropertyOptional(), no arguments',
            code: `
function ApiPropertyOptional(options: Record<string, any>): PropertyDecorator { return () => options }
export enum IntegerEnum { GOOGLE = 1, OKTA = 2 }
export class SimpleDto {
    @ApiPropertyOptional()
    status: IntegerEnum[];
}`,
            errors: [{ messageId: 'missingArgument', data: { property: 'status' } }],
            output: `
function ApiPropertyOptional(options: Record<string, any>): PropertyDecorator { return () => options }
export enum IntegerEnum { GOOGLE = 1, OKTA = 2 }
export class SimpleDto {
    @ApiPropertyOptional({})
    status: IntegerEnum[];
}`,
        },
        {
            name: '@ApiPropertyOptional(), wrong',
            code: `import { ApiPropertyOptional } from '@nestjs/swagger';
export class SimpleDto {
    @ApiPropertyOptional({ type: 'string' })
    status: string;
}`,
            errors: [
                {
                    messageId: 'wrongDecorator',
                    data: { property: 'status', value: 'ApiProperty' },
                    suggestions: [
                        {
                            messageId: 'fixDecorator',
                            data: { value: 'ApiProperty' },
                            output: `import { ApiProperty,ApiPropertyOptional } from '@nestjs/swagger';
export class SimpleDto {
    @ApiProperty({ type: 'string' })
    status: string;
}`,
                        },
                    ],
                },
            ],
        },
        {
            name: '@ApiPropertyOptional(), unnecessary required',
            code: `import { ApiPropertyOptional } from '@nestjs/swagger';
export class SimpleDto {
    @ApiPropertyOptional({ type: 'string', required: false })
    status?: string;
}`,
            errors: [
                {
                    messageId: 'extraProp',
                    data: { property: 'status', key: 'required' },
                    suggestions: [
                        {
                            messageId: 'removeProp',
                            data: { key: 'required' },
                            output: `import { ApiPropertyOptional } from '@nestjs/swagger';
export class SimpleDto {
    @ApiPropertyOptional({ type: 'string', })
    status?: string;
}`,
                        },
                    ],
                },
            ],
        },
        {
            name: '@ApiPropertyOptional(), unnecessary selfRequired',
            code: `import { ApiPropertyOptional } from '@nestjs/swagger';
type SomeType = { id: number; name: string; };
export class SimpleDto {
    @ApiPropertyOptional({ type: 'object', selfRequired: true, additionalProperties: true })
    optionalType?: SomeType;
}`,
            errors: [
                {
                    messageId: 'extraProp',
                    data: { property: 'optionalType', key: 'selfRequired' },
                    suggestions: [
                        {
                            messageId: 'removeProp',
                            data: { key: 'selfRequired' },
                            output: `import { ApiPropertyOptional } from '@nestjs/swagger';
type SomeType = { id: number; name: string; };
export class SimpleDto {
    @ApiPropertyOptional({ type: 'object', additionalProperties: true })
    optionalType?: SomeType;
}`,
                        },
                    ],
                },
            ],
        },
        {
            name: 'boolean, omitted',
            code: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
export class SimpleDto {
    @ApiProperty({})
    status: boolean;
}`,
            errors: [
                {
                    messageId: 'missingProp',
                    data: { property: 'status', keyValue: "type: 'boolean'" },
                    suggestions: [
                        {
                            messageId: 'addProp',
                            data: { keyValue: "type: 'boolean'" },
                            output: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
export class SimpleDto {
    @ApiProperty({ type: 'boolean' })
    status: boolean;
}`,
                        },
                    ],
                },
            ],
        },
        {
            name: 'boolean | null, omitted',
            code: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
export class SimpleDto {
    @ApiProperty({})
    nullableBool: boolean | null;
}`,
            errors: [
                {
                    messageId: 'missingProp',
                    data: { property: 'nullableBool', keyValue: "type: 'boolean', nullable: true" },
                    suggestions: [
                        {
                            messageId: 'addProp',
                            data: { keyValue: "type: 'boolean', nullable: true" },
                            output: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
export class SimpleDto {
    @ApiProperty({ type: 'boolean', nullable: true })
    nullableBool: boolean | null;
}`,
                        },
                    ],
                },
            ],
        },
        {
            name: 'boolean | undefined, omitted',
            code: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
export class SimpleDto {
    @ApiProperty({})
    nullableBool: boolean | undefined;
}`,
            errors: [
                {
                    messageId: 'missingProp',
                    data: { property: 'nullableBool', keyValue: "type: 'boolean', nullable: true" },
                    suggestions: [
                        {
                            messageId: 'addProp',
                            data: { keyValue: "type: 'boolean', nullable: true" },
                            output: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
export class SimpleDto {
    @ApiProperty({ type: 'boolean', nullable: true })
    nullableBool: boolean | undefined;
}`,
                        },
                    ],
                },
            ],
        },
        {
            name: 'optional boolean | null, omitted',
            code: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
export class SimpleDto {
    @ApiProperty({})
    optionalNullableBool?: boolean | null;
}`,
            errors: [
                {
                    messageId: 'missingProp',
                    data: {
                        property: 'optionalNullableBool',
                        keyValue: "type: 'boolean', nullable: true, required: false",
                    },
                    suggestions: [
                        {
                            messageId: 'addProp',
                            data: { keyValue: "type: 'boolean', nullable: true, required: false" },
                            output: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
export class SimpleDto {
    @ApiProperty({ type: 'boolean', nullable: true, required: false })
    optionalNullableBool?: boolean | null;
}`,
                        },
                    ],
                },
            ],
        },
        {
            name: 'boolean[], omitted',
            code: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
export class SimpleDto {
    @ApiProperty({})
    boolArray: boolean[];
}`,
            errors: [
                {
                    messageId: 'missingProp',
                    data: { property: 'boolArray', keyValue: "type: 'boolean', isArray: true" },
                    suggestions: [
                        {
                            messageId: 'addProp',
                            data: { keyValue: "type: 'boolean', isArray: true" },
                            output: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
export class SimpleDto {
    @ApiProperty({ type: 'boolean', isArray: true })
    boolArray: boolean[];
}`,
                        },
                    ],
                },
            ],
        },
        {
            name: 'string, omitted',
            code: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
export class SimpleDto {
    @ApiProperty({})
    status: string;
}`,
            errors: [
                {
                    messageId: 'missingProp',
                    data: { property: 'status', keyValue: "type: 'string'" },
                    suggestions: [
                        {
                            messageId: 'addProp',
                            data: { keyValue: "type: 'string'" },
                            output: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
export class SimpleDto {
    @ApiProperty({ type: 'string' })
    status: string;
}`,
                        },
                    ],
                },
            ],
        },
        {
            name: 'string | null, omitted nullable',
            code: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
export class SimpleDto {
    @ApiProperty({ type: 'string' })
    nullableString: string | null;
}`,
            errors: [
                {
                    messageId: 'missingProp',
                    data: { property: 'nullableString', keyValue: 'nullable: true' },
                    suggestions: [
                        {
                            messageId: 'addProp',
                            data: { keyValue: 'nullable: true' },
                            output: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
export class SimpleDto {
    @ApiProperty({ nullable: true,type: 'string' })
    nullableString: string | null;
}`,
                        },
                    ],
                },
            ],
        },
        {
            name: 'optional string, omitted',
            code: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
export class SimpleDto {
    @ApiProperty({ type: 'string' })
    optionalProp?: string;
}`,
            errors: [
                {
                    messageId: 'missingProp',
                    data: { property: 'optionalProp', keyValue: 'required: false' },
                    suggestions: [
                        {
                            messageId: 'addProp',
                            data: { keyValue: 'required: false' },
                            output: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
export class SimpleDto {
    @ApiProperty({ required: false,type: 'string' })
    optionalProp?: string;
}`,
                        },
                    ],
                },
            ],
        },
        {
            name: 'string[], omitted',
            code: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
export class SimpleDto {
    @ApiProperty({})
    status: string[];
}`,
            errors: [
                {
                    messageId: 'missingProp',
                    data: { property: 'status', keyValue: "type: 'string', isArray: true" },
                    suggestions: [
                        {
                            messageId: 'addProp',
                            data: { keyValue: "type: 'string', isArray: true" },
                            output: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
export class SimpleDto {
    @ApiProperty({ type: 'string', isArray: true })
    status: string[];
}`,
                        },
                    ],
                },
            ],
        },
        {
            name: 'string[], wrong',
            code: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
export class SimpleDto {
    @ApiProperty({ isArray: true, type: 'array' })
    status: string[];
}`,
            errors: [
                {
                    messageId: 'wrongValue',
                    data: { property: 'status', key: 'type', value: "'string'" },
                    suggestions: [
                        {
                            messageId: 'fixValue',
                            data: { key: 'type', value: "'string'" },
                            output: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
export class SimpleDto {
    @ApiProperty({ isArray: true, type: 'string' })
    status: string[];
}`,
                        },
                    ],
                },
            ],
        },
        {
            name: 'number, omitted',
            code: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
export class SimpleDto {
    @ApiProperty({})
    status: number;
}`,
            errors: [
                {
                    messageId: 'missingProp',
                    data: { property: 'status', keyValue: "type: 'number'" },
                    suggestions: [
                        {
                            messageId: 'addProp',
                            data: { keyValue: "type: 'number'" },
                            output: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
export class SimpleDto {
    @ApiProperty({ type: 'number' })
    status: number;
}`,
                        },
                    ],
                },
            ],
        },
        {
            name: 'number | null, omitted nullable, wrong type',
            code: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
export class SimpleDto {
    @ApiProperty({ type: 'string' })
    nullableNumber: number | null;
}`,
            errors: [
                {
                    messageId: 'missingProp',
                    data: { property: 'nullableNumber', keyValue: 'nullable: true' },
                    suggestions: [
                        {
                            messageId: 'addProp',
                            data: { keyValue: 'nullable: true' },
                            output: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
export class SimpleDto {
    @ApiProperty({ nullable: true,type: 'string' })
    nullableNumber: number | null;
}`,
                        },
                    ],
                },
                {
                    messageId: 'wrongValue',
                    data: { property: 'nullableNumber', key: 'type', value: "'number'" },
                    suggestions: [
                        {
                            messageId: 'fixValue',
                            data: { property: 'nullableNumber', key: 'type', value: "'number'" },
                            output: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
export class SimpleDto {
    @ApiProperty({ type: 'number' })
    nullableNumber: number | null;
}`,
                        },
                    ],
                },
            ],
        },
        {
            name: 'number[], wrong type & isArray',
            code: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
export class SimpleDto {
    @ApiProperty({type: Number, isArray:false})
    status: number[];
}`,
            errors: [
                {
                    messageId: 'wrongValue',
                    data: { property: 'status', key: 'type', value: "'number'" },
                    suggestions: [
                        {
                            messageId: 'fixValue',
                            data: { property: 'status', key: 'type', value: "'number'" },
                            output: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
export class SimpleDto {
    @ApiProperty({type: 'number', isArray:false})
    status: number[];
}`,
                        },
                    ],
                },
                {
                    messageId: 'wrongValue',
                    data: { property: 'status', key: 'isArray', value: 'true' },
                    suggestions: [
                        {
                            messageId: 'fixValue',
                            data: { property: 'status', key: 'isArray', value: 'true' },
                            output: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
export class SimpleDto {
    @ApiProperty({type: Number, isArray:true})
    status: number[];
}`,
                        },
                    ],
                },
            ],
        },
        {
            name: 'enum, integer, omitted',
            code: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
export enum IntegerEnum { GOOGLE = 1, OKTA = 2 }
export class SimpleDto {
    @ApiProperty({
    })
    status: IntegerEnum;
}`,
            errors: [
                {
                    messageId: 'missingProp',
                    data: { property: 'status', keyValue: 'enum: IntegerEnum' },
                    suggestions: [
                        {
                            messageId: 'addProp',
                            data: { keyValue: 'enum: IntegerEnum' },
                            output: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
export enum IntegerEnum { GOOGLE = 1, OKTA = 2 }
export class SimpleDto {
    @ApiProperty({ enum: IntegerEnum })
    status: IntegerEnum;
}`,
                        },
                    ],
                },
            ],
        },
        {
            name: 'enum, string, omitted',
            code: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
export enum StringEnum { GOOGLE = 'Google', OKTA = 'Okta' }
export class SimpleDto {
    @ApiProperty({})
    status: StringEnum;
}`,
            errors: [
                {
                    messageId: 'missingProp',
                    data: { property: 'status', keyValue: 'enum: StringEnum' },
                    suggestions: [
                        {
                            messageId: 'addProp',
                            data: { keyValue: 'enum: StringEnum' },
                            output: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
export enum StringEnum { GOOGLE = 'Google', OKTA = 'Okta' }
export class SimpleDto {
    @ApiProperty({ enum: StringEnum })
    status: StringEnum;
}`,
                        },
                    ],
                },
            ],
        },
        {
            name: 'enum | null, string, omitted enum & wrong nullable',
            code: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
export enum StringEnum { GOOGLE = 'Google', OKTA = 'Okta' }
export class SimpleDto {
    @ApiProperty({ nullable: false })
    nullableStringEnum: StringEnum | null;
}`,
            errors: [
                {
                    messageId: 'missingProp',
                    data: { property: 'nullableStringEnum', keyValue: 'enum: StringEnum' },
                    suggestions: [
                        {
                            messageId: 'addProp',
                            data: { keyValue: 'enum: StringEnum' },
                            output: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
export enum StringEnum { GOOGLE = 'Google', OKTA = 'Okta' }
export class SimpleDto {
    @ApiProperty({ enum: StringEnum,nullable: false })
    nullableStringEnum: StringEnum | null;
}`,
                        },
                    ],
                },
                {
                    messageId: 'wrongValue',
                    data: { property: 'nullableStringEnum', key: 'nullable', value: 'true' },
                    suggestions: [
                        {
                            messageId: 'fixValue',
                            data: { key: 'nullable', value: 'true' },
                            output: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
export enum StringEnum { GOOGLE = 'Google', OKTA = 'Okta' }
export class SimpleDto {
    @ApiProperty({ nullable: true })
    nullableStringEnum: StringEnum | null;
}`,
                        },
                    ],
                },
            ],
        },
        {
            name: 'enum[], integer, omitted',
            code: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
export enum IntegerEnum { GOOGLE = 1, OKTA = 2 }
export class SimpleDto {
    @ApiProperty({
    })
    status: IntegerEnum[];
}`,
            errors: [
                {
                    messageId: 'missingProp',
                    data: {
                        property: 'status',
                        keyValue: 'enum: IntegerEnum, isArray: true',
                    },
                    suggestions: [
                        {
                            messageId: 'addProp',
                            data: { keyValue: 'enum: IntegerEnum, isArray: true' },
                            output: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
export enum IntegerEnum { GOOGLE = 1, OKTA = 2 }
export class SimpleDto {
    @ApiProperty({ enum: IntegerEnum, isArray: true })
    status: IntegerEnum[];
}`,
                        },
                    ],
                },
            ],
        },
        {
            name: 'Date, omitted',
            code: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
export class SimpleDto {
    @ApiProperty({
    })
    at: Date;
}`,
            errors: [
                {
                    messageId: 'missingProp',
                    data: { property: 'at', keyValue: "type: 'string', format: 'date-time'" },
                    suggestions: [
                        {
                            messageId: 'addProp',
                            data: { keyValue: "type: 'string', format: 'date-time'" },
                            output: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
export class SimpleDto {
    @ApiProperty({ type: 'string', format: 'date-time' })
    at: Date;
}`,
                        },
                    ],
                },
            ],
        },
        {
            name: 'class, omitted',
            code: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
class Something {}
export class SimpleDto {
    @ApiProperty({
    })
    something: Something;
}`,
            errors: [
                {
                    messageId: 'missingProp',
                    data: { property: 'something', keyValue: 'type: Something' },
                    suggestions: [
                        {
                            messageId: 'addProp',
                            data: { keyValue: 'type: Something' },
                            output: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
class Something {}
export class SimpleDto {
    @ApiProperty({ type: Something })
    something: Something;
}`,
                        },
                    ],
                },
            ],
        },
        {
            name: 'optional class, omitted',
            code: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
class Something {}
export class SimpleDto {
    @ApiProperty({
    })
    something?: Something;
}`,
            errors: [
                {
                    messageId: 'missingProp',
                    data: { property: 'something', keyValue: 'type: Something, required: false' },
                    suggestions: [
                        {
                            messageId: 'addProp',
                            data: { keyValue: 'type: Something, required: false' },
                            output: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
class Something {}
export class SimpleDto {
    @ApiProperty({ type: Something, required: false })
    something?: Something;
}`,
                        },
                    ],
                },
            ],
        },
        {
            name: 'class | null, omitted',
            code: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
class Something {}
export class SimpleDto {
    @ApiProperty({
    })
    nullableSomething: Something | null;
}`,
            errors: [
                {
                    messageId: 'missingProp',
                    data: {
                        property: 'nullableSomething',
                        keyValue: 'type: Something, nullable: true',
                    },
                    suggestions: [
                        {
                            messageId: 'addProp',
                            data: { keyValue: 'type: Something, nullable: true' },
                            output: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
class Something {}
export class SimpleDto {
    @ApiProperty({ type: Something, nullable: true })
    nullableSomething: Something | null;
}`,
                        },
                    ],
                },
            ],
        },
        {
            name: 'Array<class>, omitted',
            code: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
class Something {}
export class SimpleDto {
    @ApiProperty({
    })
    something: Array<Something>;
}`,
            errors: [
                {
                    messageId: 'missingProp',
                    data: {
                        property: 'something',
                        keyValue: 'type: Something, isArray: true',
                    },
                    suggestions: [
                        {
                            messageId: 'addProp',
                            data: { keyValue: 'type: Something, isArray: true' },
                            output: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
class Something {}
export class SimpleDto {
    @ApiProperty({ type: Something, isArray: true })
    something: Array<Something>;
}`,
                        },
                    ],
                },
            ],
        },
        {
            name: 'class[], using alternate style, omitted isArray & wrong type',
            code: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
class AltStyle {}
export class SimpleDto {
    @ApiProperty({ type: [AltStyle] })
    something: AltStyle[];
}`,
            errors: [
                {
                    messageId: 'missingProp',
                    data: { property: 'something', keyValue: 'isArray: true' },
                    suggestions: [
                        {
                            messageId: 'addProp',
                            data: { keyValue: 'isArray: true' },
                            output: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
class AltStyle {}
export class SimpleDto {
    @ApiProperty({ isArray: true,type: [AltStyle] })
    something: AltStyle[];
}`,
                        },
                    ],
                },
                {
                    messageId: 'wrongValue',
                    data: { property: 'something', key: 'type', value: 'AltStyle' },
                    suggestions: [
                        {
                            messageId: 'fixValue',
                            data: { key: 'type', value: 'AltStyle' },
                            output: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
class AltStyle {}
export class SimpleDto {
    @ApiProperty({ type: AltStyle })
    something: AltStyle[];
}`,
                        },
                    ],
                },
            ],
        },
        {
            name: 'optional type, wrong',
            code: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
type SomeType = { id: number; name: string; };
export class SimpleDto {
    @ApiProperty({ required: true })
    optionalType?: SomeType;
}`,
            errors: [
                {
                    messageId: 'missingProp',
                    data: {
                        property: 'optionalType',
                        keyValue: "type: 'object', selfRequired: false",
                    },
                    suggestions: [
                        {
                            messageId: 'addProp',
                            data: { keyValue: "type: 'object', selfRequired: false" },
                            output: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
type SomeType = { id: number; name: string; };
export class SimpleDto {
    @ApiProperty({ type: 'object', selfRequired: false,required: true })
    optionalType?: SomeType;
}`,
                        },
                    ],
                },
                {
                    messageId: 'extraProp',
                    data: { property: 'optionalType', key: 'required' },
                    suggestions: [
                        {
                            messageId: 'removeProp',
                            data: { key: 'required' },
                            output: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
type SomeType = { id: number; name: string; };
export class SimpleDto {
    @ApiProperty({ })
    optionalType?: SomeType;
}`,
                        },
                    ],
                },
            ],
        },
        {
            name: 'type[], omitted items, wrong type & extra isArray',
            code: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
type SomeType = { id: number; name: string; };
export class SimpleDto {
    @ApiProperty({
        type: SomeType,
        isArray: true,
    })
    typeArray: SomeType[];
}`,
            errors: [
                {
                    messageId: 'missingProp',
                    data: { property: 'typeArray', keyValue: "items: { type: 'object' }" },
                    suggestions: [
                        {
                            messageId: 'addProp',
                            data: { keyValue: "items: { type: 'object' }" },
                            output: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
type SomeType = { id: number; name: string; };
export class SimpleDto {
    @ApiProperty({
        items: { type: 'object' },type: SomeType,
        isArray: true,
    })
    typeArray: SomeType[];
}`,
                        },
                    ],
                },
                {
                    messageId: 'wrongValue',
                    data: { property: 'typeArray', key: 'type', value: "'array'" },
                    suggestions: [
                        {
                            messageId: 'fixValue',
                            data: { key: 'type', value: "'array'" },
                            output: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
type SomeType = { id: number; name: string; };
export class SimpleDto {
    @ApiProperty({
        type: 'array',
        isArray: true,
    })
    typeArray: SomeType[];
}`,
                        },
                    ],
                },
                {
                    messageId: 'extraProp',
                    data: { property: 'typeArray', key: 'isArray' },
                    suggestions: [
                        {
                            messageId: 'removeProp',
                            data: { key: 'isArray' },
                            output: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
type SomeType = { id: number; name: string; };
export class SimpleDto {
    @ApiProperty({
        type: SomeType,
    })
    typeArray: SomeType[];
}`,
                        },
                    ],
                },
            ],
        },
        {
            name: 'optional type[], omitted, wrong',
            code: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
type SomeType = { id: number; name: string; };
export class SimpleDto {
    @ApiProperty({ required: true })
    optionalTypeArray?: SomeType[];
}`,
            errors: [
                {
                    messageId: 'missingProp',
                    data: {
                        property: 'optionalTypeArray',
                        keyValue: "type: 'array', items: { type: 'object' }",
                    },
                    suggestions: [
                        {
                            messageId: 'addProp',
                            data: { keyValue: "type: 'array', items: { type: 'object' }" },
                            output: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
type SomeType = { id: number; name: string; };
export class SimpleDto {
    @ApiProperty({ type: 'array', items: { type: 'object' },required: true })
    optionalTypeArray?: SomeType[];
}`,
                        },
                    ],
                },
                {
                    messageId: 'wrongValue',
                    data: { property: 'optionalTypeArray', key: 'required', value: 'false' },
                    suggestions: [
                        {
                            messageId: 'fixValue',
                            data: { key: 'required', value: 'false' },
                            output: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
type SomeType = { id: number; name: string; };
export class SimpleDto {
    @ApiProperty({ required: false })
    optionalTypeArray?: SomeType[];
}`,
                        },
                    ],
                },
            ],
        },
        {
            name: 'interface, omitted',
            code: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
export interface Interface {}
export class SimpleDto {
    @ApiProperty({})
    interfaceProp: Interface;
}`,
            errors: [
                {
                    messageId: 'missingProp',
                    data: { property: 'interfaceProp', keyValue: "type: 'object'" },
                    suggestions: [
                        {
                            messageId: 'addProp',
                            data: { keyValue: "type: 'object'" },
                            output: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
export interface Interface {}
export class SimpleDto {
    @ApiProperty({ type: 'object' })
    interfaceProp: Interface;
}`,
                        },
                    ],
                },
            ],
        },
        {
            name: 'optional interface, omitted',
            code: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
export interface Interface {}
export class SimpleDto {
    @ApiProperty({})
    interfaceProp?: Interface;
}`,
            errors: [
                {
                    messageId: 'missingProp',
                    data: {
                        property: 'interfaceProp',
                        keyValue: "type: 'object', selfRequired: false",
                    },
                    suggestions: [
                        {
                            messageId: 'addProp',
                            data: { keyValue: "type: 'object', selfRequired: false" },
                            output: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
export interface Interface {}
export class SimpleDto {
    @ApiProperty({ type: 'object', selfRequired: false })
    interfaceProp?: Interface;
}`,
                        },
                    ],
                },
            ],
        },
        {
            name: 'Relation<T>, omitted',
            code: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
class Something {}
type Relation<T> = T;
export class SimpleDto {
    @ApiProperty({})
    status: Relation<Something>;
}`,
            errors: [
                {
                    messageId: 'missingProp',
                    data: { property: 'status', keyValue: 'type: Something' },
                    suggestions: [
                        {
                            messageId: 'addProp',
                            data: { keyValue: 'type: Something' },
                            output: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
class Something {}
type Relation<T> = T;
export class SimpleDto {
    @ApiProperty({ type: Something })
    status: Relation<Something>;
}`,
                        },
                    ],
                },
            ],
        },
        {
            name: 'class[], lazy function returns array, wrong',
            code: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
class Lazy {}
export class SimpleDto {
    @ApiProperty({ isArray: true, type: () => [Lazy] })
    status: Lazy[];
}`,
            errors: [
                {
                    messageId: 'wrongValue',
                    data: { property: 'status', key: 'type', value: 'Lazy' },
                    suggestions: [
                        {
                            messageId: 'fixValue',
                            data: { key: 'type', value: 'Lazy' },
                            output: `
function ApiProperty(options: Record<string, any>): PropertyDecorator { return () => options }
class Lazy {}
export class SimpleDto {
    @ApiProperty({ isArray: true, type: () => Lazy })
    status: Lazy[];
}`,
                        },
                    ],
                },
            ],
        },
    ],
});
