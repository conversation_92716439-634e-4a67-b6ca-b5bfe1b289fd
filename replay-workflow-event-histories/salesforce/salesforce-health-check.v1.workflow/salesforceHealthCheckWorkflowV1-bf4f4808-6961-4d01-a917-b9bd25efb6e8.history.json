{"workflowId": "salesforceHealthCheckWorkflowV1-bf4f4808-6961-4d01-a917-b9bd25efb6e8", "history": {"events": [{"eventId": "1", "eventTime": {"seconds": "**********", "nanos": 770773000}, "eventType": "EVENT_TYPE_WORKFLOW_EXECUTION_STARTED", "taskId": "1048871", "workflowExecutionStartedEventAttributes": {"workflowType": {"name": "salesforceHealthCheckWorkflowV1"}, "taskQueue": {"name": "tw_queue", "kind": "TASK_QUEUE_KIND_NORMAL"}, "input": {"payloads": [{"metadata": {"encryption-key-id": "ZGV2LWtleS0x", "encoding": "YmluYXJ5L2VuY3J5cHRlZA=="}, "data": "afTpCmTwQ2zNXag81aaXxUQzGFzYLmQTYrqpDom+jXlDHArQn8Crde+2qohoQbHMXGCJzyeNpuNhzcWhZ8VAwBIrt/N3+8gtYLh+EKg7xDH6+2Fyy5L4CBYmG1K/EVBghXpc/jT+lDLtjkXSrSisMT+q7ZhymAnnpg=="}]}, "workflowTaskTimeout": {"seconds": "10"}, "originalExecutionRunId": "019962ce-df22-7bcb-b97c-a774dad88314", "identity": "56743@tomb-02", "firstExecutionRunId": "019962ce-df22-7bcb-b97c-a774dad88314", "attempt": 1, "firstWorkflowTaskBackoff": {}, "searchAttributes": {}, "header": {"fields": {"_tracer-data": {"metadata": {"encoding": "anNvbi9wbGFpbg=="}, "data": "eyJ0cmFjZXBhcmVudCI6IjAwLWZiNjhhOWU0NTZhYjRiZjJkODkyMWFlYTllYWZjMjIxLTVkZjVhNzUyNTRlYTgwMzgtMDEifQ=="}}}, "workflowId": "salesforceHealthCheckWorkflowV1-bf4f4808-6961-4d01-a917-b9bd25efb6e8"}}, {"eventId": "2", "eventTime": {"seconds": "**********", "nanos": 776120000}, "eventType": "EVENT_TYPE_WORKFLOW_TASK_SCHEDULED", "taskId": "1048872", "workflowTaskScheduledEventAttributes": {"taskQueue": {"name": "tw_queue", "kind": "TASK_QUEUE_KIND_NORMAL"}, "startToCloseTimeout": {"seconds": "10"}, "attempt": 1}}, {"eventId": "3", "eventTime": {"seconds": "**********", "nanos": 134047000}, "eventType": "EVENT_TYPE_WORKFLOW_TASK_STARTED", "taskId": "1048883", "workflowTaskStartedEventAttributes": {"scheduledEventId": "2", "identity": "workflowsWorker-error-unset-task-id-in-dd-tracer", "requestId": "495bd882-8262-45d1-8984-b435fe37eb6f", "historySizeBytes": "610", "workerVersion": {"buildId": "@temporalio/worker@1.11.7+58fd8150f4df7fc9abff833f63159f90805c38d0abc2b1c84ea58af677b8483d"}}}, {"eventId": "4", "eventTime": {"seconds": "**********", "nanos": 244543000}, "eventType": "EVENT_TYPE_WORKFLOW_TASK_COMPLETED", "taskId": "1048891", "workflowTaskCompletedEventAttributes": {"scheduledEventId": "2", "startedEventId": "3", "identity": "workflowsWorker-error-unset-task-id-in-dd-tracer", "workerVersion": {"buildId": "@temporalio/worker@1.11.7+58fd8150f4df7fc9abff833f63159f90805c38d0abc2b1c84ea58af677b8483d"}, "sdkMetadata": {"coreUsedFlags": [3, 2, 1]}, "meteringMetadata": {}}}, {"eventId": "5", "eventTime": {"seconds": "**********", "nanos": 245372000}, "eventType": "EVENT_TYPE_WORKFLOW_PROPERTIES_MODIFIED", "taskId": "1048892", "workflowPropertiesModifiedEventAttributes": {"workflowTaskCompletedEventId": "4", "upsertedMemo": {"fields": {"correlationId": {"metadata": {"encoding": "YmluYXJ5L2VuY3J5cHRlZA==", "encryption-key-id": "ZGV2LWtleS0x"}, "data": "rYo+XxmAuIxBi1n2lsDKjUY3uEUjj8hZmv+mYUPTAMT0RTOgvYq47xDbnP3Ju7ZlJkQOXVc1hclRunp2rs5LtOakReS7Cx1ZJQurRubR8hVTHal4ugyAvxbSsf42RHK55hx4cag5skGUoPiNcbJqNi21rtUYtbunQUW0GparG3EO9iLY/AmUdfTipTf7BE92tacsjtxE70kTeN6+MQ=="}, "tenantHost": {"metadata": {"encoding": "YmluYXJ5L2VuY3J5cHRlZA==", "encryption-key-id": "ZGV2LWtleS0x"}, "data": "tnsy0B9avh0MWAfArPCTfvxfyC79TfE5fm84e5cB+JiQrSRkBLsNo3di8rxMUuyYaHL7SeATOTn2HyMGsQAnfSITXtOei6PI3qmzKCfOig1BgVaq85y8afjrnkiRKBHEi7UMWFHMpegblFKsWxMHBniDAvydDw=="}, "accountId": {"metadata": {"encoding": "YmluYXJ5L2VuY3J5cHRlZA==", "encryption-key-id": "ZGV2LWtleS0x"}, "data": "hjcTiVdLESJi+uU2gVNJM+xUXlnJYZN4iDEHrtHbgff+l8HcnGG1RQKo4hafT6bJwNgO7KngcAxwkzvr864b5EkXNe/0ltJ8u1CAJTISzvRBaY967PzSzLsdNK++UorsswqiEfwFkewSjoWJUcBO2XesxPqz+w=="}, "domain": {"metadata": {"encryption-key-id": "ZGV2LWtleS0x", "encoding": "YmluYXJ5L2VuY3J5cHRlZA=="}, "data": "geNV+ofY0rqqQzrdVFRpXOB2T1WAbVaYefbLw/C1wg6TPtlw/r/7Ycwhy0w2zt8gCjgHtwqmpgKvLQl08Oot2UP9yHYVUfhzgeSAJVxRYPAmpBRCUjWaCsOdtMTCHBhBwPli7tvVpLFd8wj1cL/GQ5uljQqeBg=="}}}}}, {"eventId": "6", "eventTime": {"seconds": "**********", "nanos": *********}, "eventType": "EVENT_TYPE_ACTIVITY_TASK_SCHEDULED", "taskId": "1048893", "activityTaskScheduledEventAttributes": {"activityId": "1", "activityType": {"name": "SalesforceHealthCheckActivityService_performHealthCheck"}, "taskQueue": {"name": "tw_queue", "kind": "TASK_QUEUE_KIND_NORMAL"}, "header": {"fields": {"_tracer-data": {"metadata": {"encoding": "anNvbi9wbGFpbg=="}, "data": "eyJ0cmFjZXBhcmVudCI6IjAwLWZiNjhhOWU0NTZhYjRiZjJkODkyMWFlYTllYWZjMjIxLWIyNmEyNjRkOGNjOTFjNDctMDEifQ=="}, "workflowContext": {"metadata": {"encoding": "anNvbi9wbGFpbg=="}, "data": "eyJhdHRlbXB0IjoxLCJuYW1lc3BhY2UiOiJsb2NhbGRldiIsInJ1blN0YXJ0VGltZSI6IjIwMjUtMDktMTlUMTY6NDE6MjIuMTM0WiIsInRhc2tRdWV1ZSI6InR3X3F1ZXVlIiwid29ya2Zsb3dJZCI6InNhbGVzZm9yY2VIZWFsdGhDaGVja1dvcmtmbG93VjEtYmY0ZjQ4MDgtNjk2MS00ZDAxLWE5MTctYjliZDI1ZWZiNmU4Iiwid29ya2Zsb3dSdW5JZCI6IjAxOTk2MmNlLWRmMjItN2JjYi1iOTdjLWE3NzRkYWQ4ODMxNCIsIndvcmtmbG93VHlwZSI6InNhbGVzZm9yY2VIZWFsdGhDaGVja1dvcmtmbG93VjEiLCJjb3JyZWxhdGlvbklkIjoiMTM2MDc2ODctY2E4MS00ZTExLTg3ZWUtZGRkZDg4OWY2NDk5In0="}}}, "scheduleToCloseTimeout": {}, "scheduleToStartTimeout": {}, "startToCloseTimeout": {"seconds": "300"}, "heartbeatTimeout": {}, "workflowTaskCompletedEventId": "4", "retryPolicy": {"initialInterval": {"seconds": "1"}, "backoffCoefficient": 2, "maximumInterval": {"seconds": "100"}, "maximumAttempts": 3}, "useWorkflowBuildId": true}}, {"eventId": "7", "eventTime": {"seconds": "**********", "nanos": 246923000}, "eventType": "EVENT_TYPE_ACTIVITY_TASK_STARTED", "taskId": "1048923", "activityTaskStartedEventAttributes": {"scheduledEventId": "6", "identity": "activitiesWorker-error-unset-task-id-in-dd-tracer", "requestId": "9df87e8a-09bb-4cbb-b5f1-98dd165c570d", "attempt": 1, "workerVersion": {"buildId": "@temporalio/worker@1.11.7"}}}, {"eventId": "8", "eventTime": {"seconds": "**********", "nanos": 967614000}, "eventType": "EVENT_TYPE_ACTIVITY_TASK_COMPLETED", "taskId": "1048924", "activityTaskCompletedEventAttributes": {"result": {"payloads": [{"metadata": {"encoding": "YmluYXJ5L2VuY3J5cHRlZA==", "encryption-key-id": "ZGV2LWtleS0x"}, "data": "4++KGp7UOJbF1fpVT0BVr208R3zBHAitfvF8Mc0Ugl7Z7F/bMAiApkQHCw1EkAgBRa9tVzhLjK0MPe0colmPo+dIHmND1AotteU4K3S/0BuNirewqx0ctVqmglclZOz8PvzNrbPXfTHcjVD5Btf2DeMP/R7tUCNZBtdAmvUDcg8wdz1K1DTbmnMLN2LC6hB+hRbNppIFuTy4rovHDAvFxpPZkS/zHVPAdpWq52EDcy1H0veurxf76YEm82dv8+sKcBLGWYMpiiKaF1yTJOhqoPsnQVRDRUzm3pSTmheh8LUOx8D6ZPNmETj7Q2n8+ww1KjwawCxNnIW/EmumBr3FLIwlHL+g49zZgcKO4zDtpGmboSkzJ/e304PAJlNVCHO8YKZOiIUxEUVMdgn1FReAd0/bwP654PKI/6FkNDufWAuW"}]}, "scheduledEventId": "6", "startedEventId": "7", "identity": "activitiesWorker-error-unset-task-id-in-dd-tracer"}}, {"eventId": "9", "eventTime": {"seconds": "**********", "nanos": 967633000}, "eventType": "EVENT_TYPE_WORKFLOW_TASK_SCHEDULED", "taskId": "1048925", "workflowTaskScheduledEventAttributes": {"taskQueue": {"name": "workflowsWorker-error-unset-task-id-in-dd-tracer-b7688d8adeea4cb5a87fbed6ac192d93", "kind": "TASK_QUEUE_KIND_STICKY", "normalName": "tw_queue"}, "startToCloseTimeout": {"seconds": "10"}, "attempt": 1}}, {"eventId": "10", "eventTime": {"seconds": "**********", "nanos": 970069000}, "eventType": "EVENT_TYPE_WORKFLOW_TASK_STARTED", "taskId": "1048929", "workflowTaskStartedEventAttributes": {"scheduledEventId": "9", "identity": "workflowsWorker-error-unset-task-id-in-dd-tracer", "requestId": "b5650b7b-1819-4e19-84fb-30cb0489d386", "historySizeBytes": "3286", "workerVersion": {"buildId": "@temporalio/worker@1.11.7+58fd8150f4df7fc9abff833f63159f90805c38d0abc2b1c84ea58af677b8483d"}}}, {"eventId": "11", "eventTime": {"seconds": "**********", "nanos": 977787000}, "eventType": "EVENT_TYPE_WORKFLOW_TASK_COMPLETED", "taskId": "1048933", "workflowTaskCompletedEventAttributes": {"scheduledEventId": "9", "startedEventId": "10", "identity": "workflowsWorker-error-unset-task-id-in-dd-tracer", "workerVersion": {"buildId": "@temporalio/worker@1.11.7+58fd8150f4df7fc9abff833f63159f90805c38d0abc2b1c84ea58af677b8483d"}, "sdkMetadata": {}, "meteringMetadata": {}}}, {"eventId": "12", "eventTime": {"seconds": "**********", "nanos": 977823000}, "eventType": "EVENT_TYPE_WORKFLOW_EXECUTION_COMPLETED", "taskId": "1048934", "workflowExecutionCompletedEventAttributes": {"result": {"payloads": [{"metadata": {"encryption-key-id": "ZGV2LWtleS0x", "encoding": "YmluYXJ5L2VuY3J5cHRlZA=="}, "data": "xz93qryteySB3u1TfSOFYHzZqQX0/11ZW/lmRK06m1eB2LuChn8Il2ZuNH6u+YAiWuaIKf/XKMj1yehiBzKlrnxvraTWpClI76PcLOI6Apok2KOF8s08ajYF1ZflECZ+NQCNY6qn9VkDh1ZGnSpVysHG+rzTyhRPNlCR0MhPH3sS7TLCMWF2y/smowj5yV8izX4ReU7B7uakw3K9axuVMMIvFU8jTc/eJCfN/A5YfVFG/ArTQOXiuVH66H1AU0F14IAA2IMXgeiBL4k+2q3eHTgSrWGepyUV9txvkpmqZfVtATORZGSwAJmN3YeMFEAe/Hc/yTpq7LBrVKpCbOKgGIsVdBcJARJX8+D/KId/Ied7HAW9FonP27y2dV1MlHc+NaneEj4yyBjtw/Af1ao6zdic698UKX+r7oGp6xykyXqA"}]}, "workflowTaskCompletedEventId": "11"}}]}}