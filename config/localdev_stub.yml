airtable:
    apiKey:
        _secretName: secrets/AIRTABLE_API_PAT

accountMigration:
    exportDir: 'db-backups/account-migration/export'
    importDir: 'db-backups/account-migration/import'
    kmsKeyId: 'alias/dt-dev-account-migration-kms-key'
    region: 'us-west-2'
    s3:
        bucketName: 'dt-dev-00-tenant-migration-us-west-2'
    dynamoDb:
        ap2TableName: 'ap-schema-v1.0.1'

amply:
    key:
        _secretName: secrets/AMPLY_KEY

api:
    autopilotEnabled: true
    jsonLogging: false
    autoLogging: false
    autoLoggingReq: false
    autoLoggingContext: false
    requestLogging: false
    pinoLogLevel: 'debug'
    logLevel: 'log'
    pathPrefix: 'src'
    enableMtls: false
apideck:
    api:
        key:
            _secretName: secrets/APIDECK_API_KEY
    app:
        id:
            _secretName: secrets/APIDECK_APP_ID

asana:
    oauth2:
        secret:
            _secretName: secrets/ASANA_OAUTH_SECRET

atlassian:
    oauth2:
        key:
            _secretName: secrets/ATLASSIAN_KEY
        secret:
            _secretName: secrets/ATLASSIAN_SECRET

autopilot2:
    api:
        enabled: true

aws:
    eventbridge:
        enabled: true
        busName: 'drata-shared-eb-bus'
        url: 'http://localhost:4566' # localstack global-router url
    govcloud:
        key:
            _secretName: secrets/AWS_GOVCLOUD_KEY
        secret:
            _secretName: secrets/AWS_GOVCLOUD_SECRET
        intermediate:
            role:
                arn: arn:aws-us-gov:iam::345844027492:role/dt-gc-dev-autopilot-access-role

azureDevops:
    boards:
        oauth2:
            read:
                secret:
                    _secretName: localdev/AZURE_BOARDS_OAUTH_SECRET
            write:
                secret:
                    _secretName: localdev/AZURE_BOARDS_WRITE_OAUTH_SECRET
    repos:
        oauth2:
            secret:
                _secretName: localdev/AZURE_REPOS_OAUTH_SECRET
    devops:
        oauth2:
            secret:
                _secretName: secrets/AZURE_DEVOPS_OAUTH_SECRET

bambooHR:
    api:
        key:
            _secretName: secrets/BAMBOOHR_API_KEY
    oauth2:
        redirect: http://localhost:5000/callback/bambooHr
        key:
            _secretName: secrets/BAMBOOHR_CLIENT_ID
        secret:
            _secretName: secrets/BAMBOOHR_CLIENT_SECRET

bitbucket:
    oauth2:
        secret:
            _secretName: localdev/BITBUCKET_OAUTH_SECRET
    code:
        oauth2:
            secret:
                _secretName: localdev/BITBUCKET_CODE_OAUTH_SECRET
cache:
    log:
        enabled: true

checkr:
    oauth2:
        key: '575a7a76c4e81b13df4ff885'
        secret:
            _secretName: localdev/checkr/CHECKR_OAUTH_SECRET
    #   ngrok-checkr-2
    #       on web in config.js update the clientId with the key value below
    oauth2ngrok:
        key: '07a9841f5f804a5f0500876b'
        secret:
            _secretName: localdev/checkr/ngrok/CHECKR_OAUTH_SECRET

clickup:
    oauth2:
        secret:
            _secretName: secrets/CLICKUP_OAUTH_SECRET

confluence:
    oauth2:
        secret:
            _secretName: localdev/CONFLUENCE_OAUTH_SECRET

convertApi:
    secret:
        _secretName: secrets/CONVERT_API_SECRET

courier:
    api:
        key:
            _secretName: secrets/COURIER_API_KEY
cube:
    api:
        secret:
            _secretName: secrets/CUBE_API_SECRET

curricula:
    api:
        url: https://dev.curricula.com/api/v1
        endpoints:
            learners: 'learners'
            assignments: 'accounts/%s/assignments'
            accounts: 'accounts'
            learner-activity: 'assignments/%s/learner-activity'
            certificate: 'assignments/%s/completion-certificate'
    oauth2:
        key: 'oKJzwmRWB1q8P20r5MA94V6jDbyvxGrd'
        secret:
            _secretName: localdev/CURRICULA_OAUTH_SECRET
        redirect: 'http://localhost:5000/callback/curricula'
        tokenUrl: 'https://dev.curricula.com/oauth/token'
        accessToken:
            grantType: 'authorization_code'
        refreshToken:
            grantType: 'refresh_token'
        scopes: 'assignments:read learners:read assignments:learner-activity'

coverdash:
    api:
        clientSecret:
            _secretName: secrets/COVERDASH_CLIENT_SECRET

db:
    host: '127.0.0.1'
    username: 'drata'
    password: ''
    port: 3306
    databaseName: 'drata'
    # logging: false
    logging: ['error']
    loggingType: 'pollo'
    tenants: []

# db:
#     type: 'postgres'
#     host: 'localhost'
#     username: 'drata'
#     password: ''
#     port: 26257
#     databaseName: 'drata'
#     # logging: false
#     logging: ['error']
#     loggingType: 'pollo'
#     tenants: []

digitalocean:
    oauth2:
        secret:
            _secretName: localdev/DIGITAL_OCEAN_CLIENT_SECRET

docraptor:
    apiKey:
        _secretName: secrets/DOCRAPTOR_API_KEY

    ownerPassword:
        _secretName: secrets/DOCRAPTOR_OWNER_PASSWORD

docs:
    gitbook:
        secret:
            _secretName: secrets/GITBOOK_SECRET

docusign:
    api:
        secret:
            _secretName: secrets/DOCUSIGN_APP_SECRET
        clientId: ************************************ # DrataDev App integration key
        privateKey:
            _secretName: secrets/DOCUSIGN_PRIVATE_KEY

email:
    trustCenterName: '[LOCAL] Trust Center'

github:
    versionControl:
        privateKey: 'Copy and paste needed key here'
        privateKeyDrataAcmeOrgFree: # Drata [ACME] Free Org [DEV/TESTING]
            _secretName: localdev/github/drata_acme_org_free/GITHUB_PRIVATE_KEY
        privateKeyAcmePaid: # Drata [ACME] Paid Org [DEV/TESTING]
            _secretName: localdev/github/acme_paid/GITHUB_PRIVATE_KEY
        privateKeyAcmeEnterprise: # Drata [ACME] Paid Enterprise [DEV/TESTING]
            _secretName: localdev/github/acme_enterprise/GITHUB_PRIVATE_KEY
    issues:
        privateKey: 'Copy and paste needed key here'
        privateKeyDrataAcmeOrgFree: # Drata [ACME] Free Org [DEV/TESTING]
            _secretName: localdev/github/drata_acme_org_free/GITHUB_ISSUES_PRIVATE_KEY
        privateKeyAcmePaid: # Drata [ACME] Paid Org [DEV/TESTING]
            _secretName: localdev/github/acme_paid/GITHUB_ISSUES_PRIVATE_KEY
    code:
        privateKey: 'Copy and paste needed key here'
        privateKeyDrataAcmeOrgFree: # Drata [ACME] Free Org [DEV/TESTING]
            _secretName: localdev/github/drata_acme_org_free/GITHUB_CODE_PRIVATE_KEY

gitlab:
    oauth2:
        secret:
            _secretName: secrets/GITLAB_OAUTH_SECRET

gitlabIssues:
    oauth2:
        secret:
            _secretName: secrets/GITLAB_ISSUES_OAUTH_SECRET

goodhire:
    api:
        key:
            _secretName: secrets/GOODHIRE_KEY

        webhookSignatureKey:
            _secretName: secrets/GOODHIRE_WEBHOOK_SIGNATURE_KEY

google:
    privateKey:
        _secretName: secrets/GOOGLE_PRIVATE_KEY
    oauth:
        secret:
            _secretName: secrets/GOOGLE_OAUTH_SECRET
    multipleDomainTenants:
        - drata.co

gusto:
    api:
        token:
            _secretName: secrets/GUSTO_API_TOKEN

    oauth2:
        secret:
            _secretName: secrets/GUSTO_OAUTH_SECRET

heroku:
    oauth2:
        secret:
            _secretName: localdev/HEROKU_OAUTH_SECRET

hubspot:
    apiKey:
        _secretName: graveyard/HUBSPOT_API_KEY
    oauth2:
        secret:
            _secretName: secrets/HUBSPOT_OAUTH_SECRET

intercom:
    identityVerificationSecret:
        _secretName: secrets/INTERCOM_IDENTITY_SECRET

    api:
        accessToken:
            _secretName: graveyard/INTERCOM_ACCESS_TOKEN

jira:
    oauth2:
        secret:
            _secretName: localdev/JIRA_SECRET

jwt:
    secret: "It's a secret to everybody"
    expiresIn: 28800 # seconds in 8 hours

karmacheck:
    api:
        key:
            _secretName: secrets/KC_API_KEY

knock:
    api:
        key:
            _secretName: secrets/KNOCK_API_KEY

launchdarkly:
    # configs to use ld local server needs to download and start manually
    localServer:
        enabled: false
        projectName: drata-app
        uris:
            streamUri: 'http://localhost:8765'
            baseUri: 'http://localhost:8765'
            eventsUri: 'http://localhost:8765'
    # Read Feature Flags from FILE locally, do not use this and local server at the same time
    useLocalDefinition: false
    redis:
        enabled: false
    secret:
        _secretName: localdev/LAUNCHDARKLY_SDK_KEY
    apiServiceToken:
        _secretName: localdev/LAUNCHDARKLY_API_SERVICE_TOKEN

linear:
    oauth2:
        key: '271331031be6ccf1ed50da0d1df5d587'
        secret:
            _secretName: secrets/LINEAR_SECRET

localEmail:
    host: 'localhost'
    port: 1025
    secure: false
    from: '"Drata" <<EMAIL>>'

localstack:
    endpoint: http://0.0.0.0:4566
    enabled: true

mailgun:
    key:
        _secretName: secrets/ESP_KEY

mergedev:
    api:
        token:
            _secretName: secrets/MERGEDEV_API_TOKEN

leen:
    api:
        key:
            _secretName: secrets/LEEN_API_KEY

microsoft365GccHigh:
    oauth2:
        secret:
            _secretName: secrets/M365_GCC_HIGH_OAUTH_SECRET

microsoftTeams:
    oauth2:
        secret:
            _secretName: secrets/TEAMS_OAUTH_SECRET

mtls:
    ca: |-
        -----BEGIN CERTIFICATE-----
        MIIDAjCCAeoCCQDkdKsDKFQyLjANBgkqhkiG9w0BAQsFADBCMR8wHQYDVQQDDBZE
        cmF0YSBMb2NhbGRldiBSb290IENBMQswCQYDVQQGEwJVUzESMBAGA1UEBwwJU2Fu
        IERpZWdvMCAXDTI0MDIxMzIzMjQzM1oYDzIxMjEwODAzMjMyNDMzWjBCMR8wHQYD
        VQQDDBZEcmF0YSBMb2NhbGRldiBSb290IENBMQswCQYDVQQGEwJVUzESMBAGA1UE
        BwwJU2FuIERpZWdvMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAotZ1
        5oNPUOCmFqY358XKwr/L7L8wgzdLjatdTo0xefUY2vXTMJR6FjsYHneMZ23es4ze
        ioTvqboDk0hAgxeUf8BSdfvv48H7UIb+fucGZQhC4wbmPQK4F31mIo/JF3sqVw2c
        Wwd+MDljk0giZT8pDzTBr7670qgM0KxgpKV0uhYKHUQT16ZN84Szi/xaRM+gT1gK
        siwFsw6MjThCkDHn84Sz/6QpsolYGF7z59tTXlVcSgKO1CeN8g6tjrvIBG41kh5l
        jxBcSLLR2K9qeUtErFwnPDGaHBGB+KQHb8NHx1+/lGe4IM1wAU8jC/SIDicYRfJg
        AlTIsm71WF3gOo3pXwIDAQABMA0GCSqGSIb3DQEBCwUAA4IBAQAViEtwlWAgMFW6
        F1oKwLKrFxeFoAKaI0ND4oRoEGrh3LL2i57ExWJMOxfKkAPmEZTMih6tBuOsahLY
        v8c0Fw1o+83YbrQN9wFTHG4yxqi2m7zh6hZvH1WNT9C24hdtRYLqJvdQk0NooTHJ
        B//hXCnsGuSNP3nIN32SWZlV22GKzMmj52+Kh6ZH4ZZ1sk0ptbq1DPOD28UQdu8r
        kqUj7KhjzUenBuJYZuQaono4J1hID9zP+ml7fbHmj0NODtbI6pqb8z6yZQhdMwz0
        A0g2cV/PHrQVvBiOqlk2BhnI59rvrrUTVTZ7hXWxr5ZzmVnA1gAL6wO/3gaxLVLD
        DQJNHu8K
        -----END CERTIFICATE-----
    clientKey: |-
******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    clientCert: |-
        -----BEGIN CERTIFICATE-----
        MIIDtzCCAp+gAwIBAgIJAPBcM43J6xuvMA0GCSqGSIb3DQEBCwUAMEIxHzAdBgNV
        BAMMFkRyYXRhIExvY2FsZGV2IFJvb3QgQ0ExCzAJBgNVBAYTAlVTMRIwEAYDVQQH
        DAlTYW4gRGllZ28wIBcNMjQwMjE0MDAxODMzWhgPMjEyNDAxMjEwMDE4MzNaMFox
        CzAJBgNVBAYTAlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRIwEAYDVQQHDAlTYW4g
        RGllZ28xDjAMBgNVBAoMBURyYXRhMRIwEAYDVQQDDAlsb2NhbGhvc3QwggEiMA0G
        CSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQCYmKv9VgS2iAUB/TjahcFo/1WMLK36
        APDeQ6/hr3ZicB247YJ+8Va6KgjAY73PfAhKFbaZSGtBBhPXtbNCDbnAB9BcKiRN
        7hI4q42d46trFsqjtWzTNxzQs6XJcwsyeqGb0oYJjXoxO/yTtqvNkeVioMTuMyjY
        kiXaCfv70q3gqygkaedWZwTsQGUj4l5/ftVqJ2nFThOH1iY87gTZHtPR0DDNpqca
        +S9+zV3Z1wfdHPLE5tki9OjXuwVZDkKSVEQDrHKLnZcvyHNJJMBU3rpB/7cVbvsZ
        L20LJI5at8z+ru4iMyH0+fFv4jkgrHuQ7tqUmr6pLIYGiVrF5i0V2xE9AgMBAAGj
        gZUwgZIwXAYDVR0jBFUwU6FGpEQwQjEfMB0GA1UEAwwWRHJhdGEgTG9jYWxkZXYg
        Um9vdCBDQTELMAkGA1UEBhMCVVMxEjAQBgNVBAcMCVNhbiBEaWVnb4IJAOR0qwMo
        VDIuMAkGA1UdEwQCMAAwCwYDVR0PBAQDAgTwMBoGA1UdEQQTMBGCCWxvY2FsaG9z
        dIcEAAAAADANBgkqhkiG9w0BAQsFAAOCAQEAiwMrMU3ikJfxhSp01AevXJAzfd8J
        TNUTAe3NKu1oXv/Ov48QMAve2hWd2cUMDdwjmLnv6OdE1Qnspk6AxwBOGbmNvw+F
        58EULdsW9b6WtCwosF7QqwQRsO+IGS8/1G2agYPD93/DMzLsSwEyn9gO2daS7p99
        TttfxmGe5xbfA1QTolpwmzB/dqEEsOfQUa7ZfzVrQcAcccIiVYg8z57gAA262KWv
        I8YCGhOTzgGbsYqsPbbmb6cWcUBNcAQJ+S4A09EnOCzT3QkBZ68EFJH3yXW8Xek2
        GmaUEF1C4NUXPov08lONAqOTEpCzI3MAmwqRkjrFrT0jikCinEtYHwTRVQ==
        -----END CERTIFICATE-----

notion:
    oauth2:
        key:
            _secretName: secrets/NOTION_KEY
        secret:
            _secretName: secrets/NOTION_SECRET
        drataPrivateKey:
            _secretName: secrets/DRATA_NOTION_PRIVATE_KEY

office365:
    oauth2:
        secret:
            _secretName: secrets/OFFICE365_OAUTH_SECRET
        secretNonProdDrataDev: # NON-PROD on dratadev
            _secretName: localdev/office365/drata_dev/OFFICE365_OAUTH_SECRET

opensearch:
    enable: true # setting to false since a few teams will start working on this and it's not ready yet
    connectionType: hosted # can be hosted, serverless, or provisioned
    createIndexesDuringNukeAndPave: true # set to true to try and index during nukeAndPave
    monitoring:
        index: '%s.monitoring.results'
        indexerEnabled: true # enable the indexer which will automatically update the monitoring results index based upon monitoring related drata platform actions
    hosted:
        node: 'http://127.0.0.1:9201'

pusher:
    secret:
        _secretName: secrets/PUSHER_SECRET
    encryptionMasterKeyBase64:
        _secretName: localdev/PUSHER_ENCRYPTION_KEY

queue:
    assessmentCompleted:
        name: 'ap-dev-assessment-completed-us-west-2-queue'
        url: 'https://localhost.localstack.cloud:4566/000000000000/ap-dev-assessment-completed-us-west-2-queue'
        region: 'us-west-2'
        enabled: false
    salesforce:
        name: 'dt-dev-tenant-creation'
        url: 'https://sqs.us-west-2.amazonaws.com/025187613910/dt-dev-tenant-creation'
        region: 'us-west-2'
        enabled: false

redis:
    host: 'localhost'
    port: 6379

rippling:
    oauth2:
        secret:
            _secretName: secrets/RIPPLING_OAUTH_SECRET

region:
    NA:
        api: http://localhost:3000
        adminApp: http://localhost:8000
    EU:
        api: http://localhost:3001
        adminApp: http://localhost:8001
    APAC:
        api: http://localhost:3002
        adminApp: http://localhost:8002

safebase:
    sso:
        key:
            _secretName: secrets/SAFEBASE_SSO_PRIVATE_KEY
    api:
        headers:
            cfAccessClientSecret:
                _secretName: secrets/SAFEBASE_CF_ACCESS_CLIENT_SECRET
            cfAccessClientId:
                _secretName: secrets/SAFEBASE_CF_ACCESS_CLIENT_ID
            authorization:
                _secretName: secrets/SAFEBASE_AUTHORIZATION_HEADER
        webhook:
            secret:
                _secretName: secrets/SAFEBASE_WEBHOOK_SECRET
        vrm:
            secret:
                _secretName: secrets/SAFEBASE_VRM_PRIVATE_SECRET
            keyId:
                _secretName: secrets/SAFEBASE_VRM_PRIVATE_KEY

salesforce:
    api:
        # NOTE: the clientId is changed in the Salesforce environment on a regular basis
        #       this value should be updated when it changes
        clientId: 3MVG9EJ2FoGDnkgVqtYmb..iNefBQjLO5t_d4h8p3cFeSgMS0ByQPfDhnpdW.nBc9weJFLZSiQV1bHGq9vb.7
        key:
            _secretName: secrets/JSFORCE_SALESFORCE
        url: https://drata--dratafull1.sandbox.lightning.force.com/lightning
    connection:
        oauth2:
            key:
                _secretName: secrets/SALESFORCE_OAUTH_KEY
            secret:
                _secretName: secrets/SALESFORCE_OAUTH_SECRET
    report:
        privateKeyId:
            _secretName: secrets/WAAS_SALESFORCE_REPORT_PRIVATE_KEY_ID
        privateKey:
            _secretName: secrets/WAAS_SALESFORCE_REPORT_PRIVATE_KEY

security:
    # this needs to be a 128-bit HEX key
    encryptionKey:
        _secretName: localdev/DB_ENCRYPTION_KEY
    contexts:
        defaultContext:
            keys:
                _secretName: localdev/TEMPORAL_ENCRYPTION_KEY
                _module: ap_secrets
            defaultKeyId:
                _secretName: localdev/TEMPORAL_DEFAULT_KEY_ID
                _module: ap_secrets
        temporalContext:
            keys:
                _secretName: localdev/TEMPORAL_ENCRYPTION_KEY
                _module: ap_secrets
            defaultKeyId:
                _secretName: localdev/TEMPORAL_DEFAULT_KEY_ID
                _module: ap_secrets

segment:
    secret:
        _secretName: localdev/SEGMENT_WRITE_KEY

server:
    allowedCns:
        - localhost
        - autopilot

services:
    vendorDirectory:
        domain: 'http://localhost:8787' # Testing vendor-api cloudflare locally https://github.com/drata/cloudflare/tree/main/workers/vendors-api
        useMock: true # Use mock data instead of calling the vendor directory API

siteAdmin:
    email: <EMAIL>
    guid: a1b2c3d4-e5f6-4ac7-8def-123456789abc

slack:
    enabled: true
    auth0:
        token:
            _secretName: secrets/SLACK_AUTH0_TOKEN
    hmac:
        secret:
            _secretName: secrets/SLACK_HMAC_SIGNING_SECRET
    webhook:
        secret:
            _secretName: secrets/SLACK_AUTOPILOT_NOTIFICATION_WEBHOOK_URL
    salesforce:
        testWebhook:
            url:
                _secretName: secrets/SLACK_SALESFORCE_TEST_WEBHOOK_URL
    oauth2:
        secret:
            _secretName: secrets/SLACK_OAUTH_SECRET
        redirect: https://localhost:5000/callback/slack
    platformAlerts:
        readToken:
            _secretName: localdev/PLATFORM_ALERTS_SLACK_READ_TOKEN
        writeToken:
            _secretName: localdev/PLATFORM_ALERTS_SLACK_WRITE_TOKEN
        webhook:
            internal-temporal-support:
                _secretName: secrets/INTERNAL_TEMPORAL_SUPPORT_PLATFORM_ALERTS_WEBHOOK_URL
            prod-alerts:
                _secretName: secrets/PROD_PLATFORM_ALERTS_WEBHOOK_URL
            qa-alerts:
                _secretName: secrets/QA_PLATFORM_ALERTS_WEBHOOK_URL
            team-the-notorious-bugs-alerts:
                _secretName: secrets/PLATFORM_ALERTS_WEBHOOK_URL
            team-the-notorious-bugs-alerts-noisy:
                _secretName: secrets/PLATFORM_ALERTS_WEBHOOK_URL_STAGING
stackOne:
    api:
        key:
            _secretName: secrets/STACKONE_API_KEY
        url: https://api.stackone.com
        headers:
            accountId: x-account-id
        endpoints:
            connectSessions: connect_sessions
            account: accounts/%s
            iam:
                users: unified/iam/users

storageDriver:
    aws:
        s3:
            apEvidenceAssessmentBucket: 'ap-dev-assessment-result-us-west-2-bucket'
            apEvidenceBucket: 'ap-dev-evidence-us-west-2-bucket'
            region: 'us-west-2'

svix:
    api:
        key:
            _secretName: secrets/SVIX_API_KEY

swagger:
    enabled: true

trustcenter:
    partnerClientKey:
        _secretName: secrets/TC_PARTNER_SECRET

temporal:
    enabled: false
    taskQueues:
        temporal-default: tw_queue
        temporal-slow-pool: tw_queue

typeform:
    secret:
        _secretName: localdev/TYPEFORM_WEBHOOK_SECRET
    responsesToken:
        _secretName: localdev/TYPEFORM_RESPONSES_TOKEN
    formIds:
        vendorRiskQuestionnaire: 'eN455zTA'
        riskAssessment:
            ENGINEERING: 'E5wwgkOv'
            LEGAL: 'OaOjUCQe'
            HR: 'LMlz2y79'
            SECURITY: 'bDOGkU9Q'
            FINANCE: 'mlqGYeBT'
            SALES: 'zdyHUtLm'

vellum:
    apiKey:
        _secretName: secrets/VELLUM_API_KEY
    recipe:
        validator:
            secret:
                _secretName: secrets/VELLUM_RECIPE_VALIDATOR_SECRET_KEY

vitally:
    resources:
        secret:
            _secretName: secrets/VITALLY_RESOURCES_SECRET

workos:
    apiKey:
        _secretName: secrets/WORK_OS_API_KEY
    webhookSecret:
        _secretName: localdev/WORK_OS_WEBHOOK_SECRET

zoho:
    api:
        url: https://bugtracker.zoho.com/restapi/
        endpoints:
            portals: 'portals/'
    oauth2:
        secret:
            _secretName: secrets/ZOHO_OAUTH_SECRET
        redirect: 'http://localhost:5000/callback/zoho'
        tokenUrl: https://accounts.zoho.com/oauth/v2/token
        accessToken:
            grantType: 'authorization_code'

zoom:
    api:
        url: https://api.zoom.us
        endpoints:
            account: v2/users/me
            users: v2/users
    oauth2:
        key: AIgxTswBSS6i2aj9F935ig
        secret:
            _secretName: secrets/ZOOM_OAUTH_SECRET
        redirect: http://localhost:5000/callback/zoom
        tokenUrl: https://zoom.us/oauth/token
        accessToken:
            grantType: authorization_code
        refreshToken:
            grantType: refresh_token
        scopes: account:read:admin user:read:admin

tenantDeletionWorkflow:
    deleteForms: false
    deleteS3Objects: false
    deleteDBData: false
    hardDeleteRegionalData: false

flatfile:
    apiKey:
        _secretName: secrets/FLATFILE_API_KEY
    environmentId:
        _secretName: secrets/FLATFILE_ENVIRONMENT_ID
    baseUrl: 'https://platform.flatfile.com/api'
    namespace: 'portal'

aiAgent:
    api:
        baseUrl: 'http://127.0.0.1:9000'

okta:
    siteAdmin:
        clientCredentials:
            issuer: 'https://drata-automation.okta.com/oauth2/default'
            retool:
                clientId: '0oa1w9h0d09Ec5yRo1d8'

url:
    vendorHubApp: 'http://localhost:8781'
