api:
    name: '[QA] Drata'
    connectionMetricsLogging: true

apideck:
    oauth:
        redirect: 'https://app.qa.drata.com/callback/cloud'
asana:
    oauth2:
        redirect: 'https://app.qa.drata.com/callback/asana'

atlassian:
    oauth2:
        redirect: 'https://app.qa.drata.com/callback/atlassian'

aws:
    accountId: '************'
    eventbridge:
        enabled: true
        busName: 'dt-qa-drata-shared-eb-bus'
        region: 'eu-central-1'
    s3:
        appBucket: 'dt-qa-app-eu-central-1-bucket'
        cdnBucket: 'dt-qa-cdn-eu-central-1-bucket'
        eventBucket: 'dt-qa-event-eu-central-1'
        autopilotBucket: 'dt-qa-autopilot-data-eu-central-1-bucket'
    runner:
        subnets:
            ['subnet-05170f009f09b450c', 'subnet-0142013c703227430', 'subnet-002dd6c6cf43369ed']
        securityGroups: 'sg-01189951148ee3d71'
        name: 'api'
        region: 'eu-central-1'

azureDevops:
    boards:
        oauth2:
            redirect: 'https://app.qa.drata.com/callback/azuredevops-boards'
    repos:
        oauth2:
            redirect: 'https://app.qa.drata.com/callback/azuredevops-repos'
    devops:
        oauth2:
            redirect: 'https://app.qa.drata.com/callback/azuredevops'

bitbucket:
    oauth2:
        key: 'x8beN2vSLHdkwUPCm9'
    code:
        oauth2:
            key: 'cxmn8kzqQqfCRMyDKY'

checkr:
    oauth2:
        key: 'b470426fc7f7a8488a9c1f4c'

clickup:
    oauth2:
        redirect: 'https://app.qa.drata.com/callback/clickup'

confluence:
    oauth2:
        key: '43h0boXqwJ6mv24ogXI5CF7ShHvzCYkV'
        redirect: 'https://app.qa.drata.com/callback/confluence'

curricula:
    api:
        url: https://dev.curricula.com/api/v1
    oauth2:
        key: '4odpzNRQ7bLGjVMNvM9KZW6Xgn12Dywv'
        redirect: 'https://app.qa.drata.com/callback/curricula'
        tokenUrl: 'https://dev.curricula.com/oauth/token'

datadog:
    enabled: true

db:
    ssl: true

digitalocean:
    oauth2:
        key: '****************************************************************'
        redirect: 'https://app.qa.drata.com/callback/digitalocean'

docusign:
    api:
        clientId: 83650467-38fa-4601-8fc9-7f187fa901ef
        webhookBaseUrl: https://api.eu.qa.drata.com
        ndaBaseUrl: https://appdemo.docusign.com/documents/details

email:
    trustCenterName: '[QA] Trust Center'

github:
    versionControl:
        applicationId: 101802 # Drata [ACME] Free Org [DEV/TESTING]
        # applicationId: 117129 # Drata [ACME] Paid Org [DEV/TESTING]
    issues:
        applicationId: 101804 # Drata [ACME] Free Org [DEV/TESTING]
        # applicationId: 117132 # Drata [ACME] Paid Org [DEV/TESTING]
    code:
        applicationId: 861155 # Drata [ACME] Free Org [DEV/TESTING]

gitlab:
    oauth2:
        redirect: 'https://app.qa.drata.com/callback/gitlab'

gitlabIssues:
    oauth2:
        redirect: 'https://app.qa.drata.com/callback/gitlab-issues'

hud:
    enabled: true

jira:
    oauth2:
        key: 'RJVl92La3xGxsMzChxLVtHYM2KteVIak'
        redirect: 'https://app.qa.drata.com/callback/jira'

jwt:
    cookie:
        domain: '.drata.com'
        webRefreshTokenKey: 'web-refresh-token-qa'
        siteAdminRefreshTokenKey: 'admin-refresh-token-qa'

karmacheck:
    api:
        webhookDomain: 'https://api.eu.qa.drata.com'

linear:
    oauth2:
        redirect: 'https://app.qa.drata.com/callback/linear'

lightrun:
    enabled: true

# mtls:
#     commonName: api-ap.int.euc1.qa.drata.net

opensearch:
    enable: false # setting to true for this env
    # monitoring:
    #     indexerEnabled: true # enable the indexer which will automatically update the monitoring results index based upon monitoring related drata platform actions
    # aws:
    #     region: eu-central-1
    #     node: https://opensearch.int.euc1.qa.drata.net

riskManagement:
    assessmentReport: 'dist/app/risk-management/docs/assessmentReportTemplate.docx'

riskAssessment:
    assessmentReport: 'dist/app/users/risk-assessment/docs/assessmentReportTemplate.docx'

redis:
    enable_ssl: true

segment:
    enabled: true

# server:
#     allowedCns:
#         - 'orchestrator.int.euc1.qa.drata.net'

slack:
    enabled: true

temporal:
    clusterAddress: 'eu-central-1.aws.api.temporal.io:7233'
    namespace: 'qa-euc1.wb2on'
    autopilot:
        queueName: 'autopilot_queue_qa'
    tracingEnabled: true

# queue:
#     assessmentCompleted:
#         name: 'ap-qa-eu-central-1-api-ap2-queue'
#         url: 'https://sqs.eu-central-1.amazonaws.com/************/ap-qa-eu-central-1-api-ap2-queue'

region:
    NA:
        api: https://api.qa.drata.com
        adminApp: https://admin.qa.drata.com
    EU:
        api: https://api.eu.qa.drata.com
        adminApp: https://admin.eu.qa.drata.com
    APAC:
        api: https://api.apac.qa.drata.com
        adminApp: 'https://admin.apac.qa.drata.com'

safebase:
    sso:
        enabled: true
        jwtIssuer: 'https://api.eu.qa.drata.com'

salesforce:
    connection:
        oauth2:
            redirect: https://app.qa.drata.com/callback/salesforce

services:
    vendorDirectory:
        domain: 'https://vendors-api.drata-dev.workers.dev'

typeform:
    formIds:
        vendorRiskQuestionnaire: 'QJAyyTsr'
        riskAssessment:
            ENGINEERING: 'etvN2M1z'
            LEGAL: 'N6u4PdvL'
            HR: 'K85Drv9N'
            SECURITY: 'TAq1df5m'
            FINANCE: 'm4lPqipy'
            SALES: 'wKuoWdDV'
    webhook:
        url: https://api.eu.qa.drata.com
    defaultQuestionnaire:
        workspaceId: gFCM8N # <NAME_EMAIL>

url:
    publicApi: 'https://public-api.eu.qa.drata.com'
    trustCenterPartnerApi: 'https://tc-api.qa.drata.com'
    webApp: 'https://app.qa.drata.com'
    dratApp: 'https://app-multiverse.qa.drata.com'
    adminApp: 'https://admin.eu.qa.drata.com'
    appCdn: 'https://img-qa.eu.dratacdn.com'
    vendorHubApp: 'https://vendorhub.qa.drata.com'

workos:
    redirectUri: 'https://api.eu.qa.drata.com/auth/sso/callback'

zoho:
    oauth2:
        redirect: 'https://app.qa.drata.com/callback/zoho'

zoom:
    oauth2:
        key: W7QUgTDOSMGVv55bgGdyjg
        redirect: https://app.qa.drata.com/callback/zoom
notion:
    oauth2:
        redirect: 'https://app.qa.drata.com/callback/notion'

# complianceAsCode:
#     s3:
#         findingsBucket: ac-qa-finding-eu-central-1-bucket
#         iacUploadBucket: ac-qa-metamodel-eu-central-1-bucket
#     sqs:
#         iacValidationStarted:
#             name: 'LaunchValidationQueue'
#             url: 'https://sqs.eu-central-1.amazonaws.com/448354310742/LaunchValidationQueue'
#             region: 'eu-central-1'
#             enabled: true
bambooHR:
    oauth2:
        redirect: https://app.qa.drata.com/callback/bambooHr
hubspot:
    oauth2:
        key: ************************************

# Time at which the tenant reminder workflow should run. Temporarily set for 1 am CTZ
remindersSchedule:
    hour: 7
    minute: 0
    timezone: Europe/Berlin

okta:
    siteAdmin:
        clientCredentials:
            issuer: 'https://drata-automation.okta.com/oauth2/default'
            retool:
                clientId: '0oa1w9h0d09Ec5yRo1d8'
