########################################################
# This file is similar to dev.yml except the url block #
########################################################
api:
    pinoLogLevel: 'debug'

datadog:
    enabled: true

karmacheck:
    api:
        webhookDomain: 'https://api.dev.drata.com'

riskManagement:
    assessmentReport: 'dist/app/risk-management/docs/assessmentReportTemplate.docx'

riskAssessment:
    assessmentReport: 'dist/app/users/risk-assessment/docs/assessmentReportTemplate.docx'

slack:
    enabled: true
    oauth2:
        redirect: 'https://release-oauth.int.infra.drata.net/callback'

microsoft365GccHigh:
    oauth2:
        redirect: 'https://release-oauth.int.infra.drata.net/callback'

microsoftTeams:
    oauth2:
        redirect: 'https://release-oauth.int.infra.drata.net/callback'
heroku:
    oauth2:
        key: '************************************'
        redirect: 'https://release-oauth.int.infra.drata.net/callback'
jira:
    oauth2:
        key: 'QGQa0cNNq65ybvRTY5xPI63Ya4YL5CQA'
        redirect: 'https://release-oauth.int.infra.drata.net/callback'
notion:
    oauth2:
        redirect: 'https://release-oauth.int.infra.drata.net/callback/notion'

opensearch:
    enable: true # setting to false since a few teams will start working on this and it's not ready yet
    connectionType: hosted # can be hosted, serverless, or provisioned
    monitoring:
        index: '%s.monitoring.results'
        indexerEnabled: true # enable the indexer which will automatically update the monitoring results index based upon monitoring related drata platform actions
    vendors:
        index: '%s.vendors'
        indexerEnabled: true
    controls:
        indexerEnabled: true
    hosted:
        node: 'http://opensearch:9201'

salesforce:
    api:
        # NOTE: the clientId is changed in the Salesforce environment on a regular basis
        #       this value should be updated when it changes
        clientId: 3MVG9EJ2FoGDnkgVqtYmb..iNefBQjLO5t_d4h8p3cFeSgMS0ByQPfDhnpdW.nBc9weJFLZSiQV1bHGq9vb.7
        key:
            _secretName: secrets/JSFORCE_SALESFORCE
        url: https://drata--dratafull1.sandbox.lightning.force.com/lightning
    connection:
        oauth2:
            key:
                _secretName: secrets/SALESFORCE_OAUTH_KEY
            secret:
                _secretName: secrets/SALESFORCE_OAUTH_SECRET

services:
    vendorDirectory:
        domain: 'https://vendors-api.drata-dev.workers.dev'

lightrun:
    enabled: false
bambooHR:
    oauth2:
        redirect: 'https://app-ted6015.envs.drata.net/callback/bambooHr'
        key: 'developer_portal-f4b2a132ee8a4e1917e7c3a5f47146af12b544cf-08_08_2024_10:09:45'
swagger:
    enabled: true

safebase:
    sso:
        enabled: true

confluence:
    oauth2:
        key: '6A4Zti78wUx57mY4TFxsFCBVMgJzjNcn'
        redirect: 'https://app-tedd05d.envs.drata.net/callback/confluence'

url:
    vendorHubApp: 'https://vendor-hub-rh.drata.workers.dev'

temporal:
    clusterAddress: temporal:7233
    taskQueues:
        temporal-default: tw_queue
        temporal-slow-pool: tw_queue

# Time at which the tenant reminder workflow should run. Temporarily set for 1 am CTZ
remindersSchedule:
    hour: 6
    minute: 0
    timezone: America/Chicago
