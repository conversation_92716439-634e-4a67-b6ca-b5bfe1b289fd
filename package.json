{"name": "drata-api", "version": "0.0.1", "private": true, "license": "UNLICENSED", "packageManager": "yarn@1.22.22", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "node --max-old-space-size=8192 ./node_modules/.bin/nest build", "build-with-ci": "node --max-old-space-size=16384 ./node_modules/.bin/nest build", "build-with-ci:worker": "node --max-old-space-size=16384 ./node_modules/.bin/nest build --builder swc", "build-with-swc": "NO_SWAGGER=true node --max-old-space-size=8192 ./node_modules/.bin/nest build --builder swc", "format": "prettier --write --ignore-path .gitignore \"src/**/*.ts\" \"test/**/*.ts\"", "check-format": "prettier --ignore-path .gitignore --list-different \"src/**/*.ts\"", "check-types": "tsc", "start:dev": "NODE_ENV=localdev NODE_CONFIG_ENV=localdev NODE_OPTIONS=\"--max-old-space-size=8192\" nodemon --config nodemon.json", "start:mocked": "NODE_ENV=localdev,localmocks NODE_CONFIG_ENV=localdev,localmocks nodemon --config nodemon.json", "start:debug": "NODE_ENV=localdev NODE_CONFIG_ENV=localdev nodemon --config nodemon.json -- --debug", "start:prod": "node dist/main", "start": "yarn start:dev", "start:swc": "NODE_ENV=localdev NODE_CONFIG_ENV=localdev nest start --exec \"node --require ./dist/hud-init.js\" --builder swc --watch", "start:no-swagger": "NODE_ENV=localdev NODE_CONFIG_ENV=localdev NO_SWAGGER=true nest start --exec \"node --require ./dist/hud-init.js\" --builder swc --watch", "start:no-swagger:debug": "NODE_ENV=localdev NODE_CONFIG_ENV=localdev NO_SWAGGER=true nest start --builder swc --watch --debug", "start:test": "bin/drata-cli swagger:docs && NODE_ENV=localtest NODE_CONFIG_ENV=localtest node dist/main", "start:test:public": "API_TYPE=public PORT=4000 NODE_ENV=localtest NODE_CONFIG_ENV=localtest node dist/main", "start:mocks": "docker compose -f docker-compose.mocked.yml up --build", "cli": "node dist/app/drata-cli", "start:public": "API_TYPE=public PORT=4000 yarn start:dev", "start:public:swc": "API_TYPE=public PORT=4000 yarn start:swc", "start:public:no-swagger": "API_TYPE=public PORT=4000 yarn start:no-swagger", "start:public:no-swagger:debug": "API_TYPE=public PORT=4000 yarn start:no-swagger:debug", "start:scanner-uploader-consumer": "NODE_ENV=localdev NODE_CONFIG_ENV=localdev nest start scanner-listener", "start:public-mocked": "API_TYPE=public PORT=4000 yarn start:mocked", "start:region:eu": "node ./scripts/region/prepare-localdev.region.js eu && (trap 'kill 0' SIGINT; docker compose -f ./region/eu/docker-compose.yml up & NODE_ENV=localdev.region.eu NODE_CONFIG_ENV=localdev.region.eu nodemon --config nodemon.json & wait)", "start:region:apac": "node ./scripts/region/prepare-localdev.region.js apac && (trap 'kill 0' SIGINT; docker compose -f ./region/apac/docker-compose.yml up & NODE_ENV=localdev.region.apac NODE_CONFIG_ENV=localdev.region.apac nodemon --config nodemon.json & wait)", "start:region:eu:no-swagger": "node ./scripts/region/prepare-localdev.region.js eu && (trap 'kill 0' SIGINT; docker compose -f ./region/eu/docker-compose.yml up & NODE_ENV=localdev.region.eu NODE_CONFIG_ENV=localdev.region.eu NO_SWAGGER=true nest start --builder swc --watch & wait)", "start:region:apac:no-swagger": "node ./scripts/region/prepare-localdev.region.js apac && (trap 'kill 0' SIGINT; docker compose -f ./region/apac/docker-compose.yml up & NODE_ENV=localdev.region.apac NODE_CONFIG_ENV=localdev.region.apac NO_SWAGGER=true nest start --builder swc --watch & wait)", "start:temporal": "temporal server start-dev --namespace localdev --namespace localdev-eu --namespace localdev-apac --ui-codec-endpoint https://codecserver-dev.drata.net --dynamic-config-value frontend.enableUpdateWorkflowExecution=true", "start:temporal:history": "yarn start:temporal --db-filename ./db-backups/temporal.db", "start:temporal:ci": "temporal server start-dev --namespace localdev --headless", "ld-flags": "NODE_ENV=localdev NODE_CONFIG_ENV=localdev ts-node -r tsconfig-paths/register ./src/scripts/feature-flag/run-cli.ts", "localdev:build": "./bin/localdev-builder", "localdev:build:replace": "yarn localdev:build && cp config/localdev.gen.yml config/localdev.yml", "localdev:build:region": "node ./scripts/region/prepare-localdev.region.js", "postman:build": "./bin/postman-builder", "postman:build:replace": "./bin/postman-builder && cp ./config/local-drata.postman_environment.json ./.postman/environments/local-drata.postman_environment.json", "postman:blueprint": "yarn postman:build:replace && .postman/scripts/blueprint-builder.sh sync default && .postman/scripts/blueprint-builder.sh connections", "postman:blueprint:local": "yarn postman:build:replace && .postman/scripts/blueprint-builder.sh use && .postman/scripts/blueprint-builder.sh connections", "postman:tdac": "postman collection run .postman/collections/tdac-blueprint.postman_collection.json -e .postman/environments/local-drata.postman_environment.json", "featureflag:build": "./bin/featureflag-builder", "featureflag:build:replace": "./bin/featureflag-builder && cp ./config/feature-flag.gen.json ./config/feature-flag.json ", "lint": "NODE_OPTIONS='--max-old-space-size=8192' eslint --ignore-path .gitignore --ignore-path .eslintignore --ext .ts --fix ./src/", "lint-no-fix": "NODE_OPTIONS='--max-old-space-size=8192' eslint --ignore-path .gitignore --ignore-path .eslintignore --ext .ts ./src/", "lint:ci": "node --max-old-space-size=8192 ./node_modules/eslint/bin/eslint.js --ignore-path .gitignore --ignore-path .eslintignore --ext .ts", "validate": "npm-run-all --parallel check-types check-format lint test build", "test": "NODE_CONFIG_ENV=test AWS_SDK_JS_SUPPRESS_MAINTENANCE_MODE_MESSAGE=1 node --max-old-space-size=4096 ./node_modules/.bin/jest", "test:verbose": "NODE_CONFIG_ENV=test AWS_SDK_JS_SUPPRESS_MAINTENANCE_MODE_MESSAGE=1 node --max-old-space-size=4096 ./node_modules/.bin/jest", "test:junit": "NODE_CONFIG_ENV=test AWS_SDK_JS_SUPPRESS_MAINTENANCE_MODE_MESSAGE=1 JEST_JUNIT_REPORT_TEST_SUITE_ERRORS=true jest --reporters=default --reporters=jest-junit --silent", "test:watch": "NODE_CONFIG_ENV=test AWS_SDK_JS_SUPPRESS_MAINTENANCE_MODE_MESSAGE=1 jest --watch", "test:cov": "NODE_CONFIG_ENV=test AWS_SDK_JS_SUPPRESS_MAINTENANCE_MODE_MESSAGE=1 jest --coverage", "test:debug": "NODE_CONFIG_ENV=test node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e:all": "./bin/drata-cli typeorm:init; find src/tests  -maxdepth 1 -mindepth 1 -type d -print | egrep -v utils |cut -c11- | xargs -n1 yarn test:e2e", "test:e2e": "NODE_ENV=localdev  NODE_CONFIG_ENV=localdev AWS_SDK_JS_SUPPRESS_MAINTENANCE_MODE_MESSAGE=1 node --max-old-space-size=8192 --stack-size=3000 ./node_modules/.bin/jest  --runInBand --config ./src/tests/jest-e2e.json", "test:e2e:workflows": "API_TYPE=temporal-default NODE_ENV=localdev NODE_CONFIG_ENV=localdev,localdev-waas AWS_SDK_JS_SUPPRESS_MAINTENANCE_MODE_MESSAGE=1 node --max-old-space-size=8192 --stack-size=3000 ./node_modules/.bin/jest --runInBand --config ./src/tests/jest-e2e.workflows.json", "test:e2e:workflows:debug": "DB_RESTORATION=false yarn test:e2e:workflows", "test:e2e:workflows:no-backup": "SKIP_E2E_CUSTOM_BACKUP=true yarn test:e2e:workflows", "test:e2e:all:debug": "./bin/drata-cli typeorm:init; find src/tests  -maxdepth 1 -mindepth 1 -type d -print | egrep -v utils | cut -c11- | xargs -n1 yarn test:e2e:debug", "test:e2e:debug": ">|ormlogs.log && yarn prebuild && jest --runInBand --logHeapUsage --config ./src/tests/jest-e2e.json", "test:e2e:debug:gc": "node --max-old-space-size=8192 --stack-size=3000 --expose-gc ./node_modules/.bin/jest --w=50% --logHeapUsage --config ./src/tests/jest-e2e.json", "test:e2e:performance": "node --max-old-space-size=8192 --stack-size=3000 --expose-gc ./node_modules/.bin/jest --logHeapUsage --w=50% --forceExit --passWithNoTests", "test:e2e:autopilot": "yarn test:e2e:performance --config ./src/tests/autopilot-config-files/jest.config.js", "test:e2e:autopilot:separately": "./bin/ormconfig.sh | yarn test:e2e:autopilot agent && yarn test:e2e:autopilot ticketing && yarn test:e2e:autopilot identity && yarn test:e2e:autopilot in_drata && yarn test:e2e:autopilot infrastructure && yarn test:e2e:autopilot observability && yarn test:e2e:autopilot policy && yarn test:e2e:autopilot version-control", "test:integration:workflows": "API_TYPE=temporal-default NODE_ENV=localdev NODE_CONFIG_ENV=localdev,localdev-waas AWS_SDK_JS_SUPPRESS_MAINTENANCE_MODE_MESSAGE=1 node --max-old-space-size=8192 --stack-size=3000 ./node_modules/.bin/jest  --runInBand --config ./src/tests/jest-integration.workflows.json --forceExit", "worker:dev": "API_TYPE=temporal-default NODE_ENV=localdev NODE_CONFIG_ENV=localdev,localdev-waas nodemon --config nodemon-worker.json", "worker": "yarn worker:dev", "worker:ci": "AWS_PROFILE=api AWS_SDK_LOAD_CONFIG=true API_TYPE=temporal-default NODE_ENV=localdev NODE_CONFIG_ENV=localdev,localdev-waas node ./dist/app/worker/main.js > api.log", "worker:swc": "API_TYPE=temporal-default NODE_ENV=localdev NODE_CONFIG_ENV=localdev,localdev-waas nest start worker --builder swc --watch", "worker:no-swagger": "NO_SWAGGER=true yarn worker:swc", "worker:eu:swc": "API_TYPE=temporal-default NODE_ENV=localdev.region.eu NODE_CONFIG_ENV=localdev.region.eu,localdev-waas nest start worker --builder swc --watch", "worker:eu:no-swagger": "NO_SWAGGER=true yarn worker:eu:swc", "worker:apac:swc": "API_TYPE=temporal-default NODE_ENV=localdev.region.apac NODE_CONFIG_ENV=localdev.region.apac,localdev-waas nest start worker --builder swc --watch", "worker:apac:no-swagger": "NO_SWAGGER=true yarn worker:apac:swc", "worker:slow-pool:swc": "API_TYPE=temporal-slow-pool NODE_ENV=localdev NODE_CONFIG_ENV=localdev,localdev-temporal-slow-pool nest start worker --builder swc --watch", "worker:slow-pool:no-swagger": "NO_SWAGGER=true yarn worker:slow-pool:swc", "prepare": "node ./scripts/prepare.mjs", "postinstall": "patch-package", "generate:nukeMigrations": "ts-node -r tsconfig-paths/register ./src/scripts/run-generate-nuke-migrations.ts", "generate:workflow:component": "ts-node ./src/waas/scripts/workflow-generator.ts", "generate:workflowHistories": "NODE_ENV=localdev NODE_CONFIG_ENV=localdev ts-node -r tsconfig-paths/register ./src/waas/scripts/generate-workflow-histories-for-replay.ts", "replay:workflowHistories": "NODE_ENV=localdev NODE_CONFIG_ENV=localdev ts-node -r tsconfig-paths/register ./src/waas/scripts/run-replay-workflow-histories.ts", "replay:allWorkflowHistories": "NODE_ENV=localdev NODE_CONFIG_ENV=localdev ts-node -r tsconfig-paths/register ./src/waas/scripts/replay-all-workflow-histories.ts", "run:workflow": "./src/scripts/workflows.ts execute", "ignore": "ls", "atlas:yaml:format": "prettier --write \"deploy/**/*.{yml,yaml}\"", "atlas:yaml:check": "prettier --check \"deploy/**/*.{yml,yaml}\""}, "dependencies": {"@aws-sdk/client-api-gateway": "^3.787.0", "@aws-sdk/client-appsync": "^3.787.0", "@aws-sdk/client-cloudfront": "^3.787.0", "@aws-sdk/client-cloudtrail": "^3.787.0", "@aws-sdk/client-cloudwatch": "^3.787.0", "@aws-sdk/client-codecommit": "^3.787.0", "@aws-sdk/client-dynamodb": "3.699.0", "@aws-sdk/client-ec2": "^3.787.0", "@aws-sdk/client-ecr": "^3.787.0", "@aws-sdk/client-ecs": "^3.804.0", "@aws-sdk/client-eks": "^3.787.0", "@aws-sdk/client-elastic-load-balancing": "^3.787.0", "@aws-sdk/client-elastic-load-balancing-v2": "^3.787.0", "@aws-sdk/client-elasticache": "^3.787.0", "@aws-sdk/client-elasticsearch-service": "^3.787.0", "@aws-sdk/client-eventbridge": "^3.787.0", "@aws-sdk/client-guardduty": "^3.787.0", "@aws-sdk/client-iam": "^3.787.0", "@aws-sdk/client-identitystore": "^3.787.0", "@aws-sdk/client-inspector2": "^3.556.0", "@aws-sdk/client-kms": "3.699.0", "@aws-sdk/client-marketplace-entitlement-service": "^3.556.0", "@aws-sdk/client-marketplace-metering": "^3.556.0", "@aws-sdk/client-organizations": "^3.787.0", "@aws-sdk/client-rds": "^3.787.0", "@aws-sdk/client-s3": "^3.804.0", "@aws-sdk/client-secrets-manager": "3.699.0", "@aws-sdk/client-sns": "^3.787.0", "@aws-sdk/client-sqs": "^3.787.0", "@aws-sdk/client-sso-admin": "^3.787.0", "@aws-sdk/client-sts": "^3.787.0", "@aws-sdk/client-waf": "^3.787.0", "@aws-sdk/client-waf-regional": "^3.787.0", "@aws-sdk/client-wafv2": "^3.787.0", "@aws-sdk/credential-provider-node": "^3.749.0", "@aws-sdk/credential-providers": "^3.744.0", "@aws-sdk/lib-storage": "^3.804.0", "@aws-sdk/s3-request-presigner": "^3.804.0", "@aws-sdk/smithy-client": "3.358.0", "@aws-sdk/types": "^3.734.0", "@aws-sdk/util-dynamodb": "3.699.0", "@azure/arm-monitor": "^7.0.0", "@azure/arm-resourcegraph": "^4.2.1", "@azure/arm-resources": "^6.0.0", "@azure/arm-storage": "^18.4.0", "@azure/identity": "^4.8.0", "@azure/msal-node": "^3.4.0", "@cantoo/pdf-lib": "^2.3.2", "@casl/ability": "^6.7.3", "@cubejs-client/core": "^0.35.23", "@datadog/datadog-api-client": "^1.27.0", "@drata/common": "0.0.84", "@drata/enums": "0.0.154", "@drata/expression-evaluator": "2.0.3", "@drata/json-rules-engine": "0.0.7", "@drata/logger": "0.0.16", "@drata/pod-area": "0.0.31", "@drata/recipes": "0.0.46", "@drata/recurring-schedule": "^0.0.10", "@drata/resources-schemas": "0.0.111", "@drata/storage-driver": "0.0.9", "@drata/xlsx": "^0.20.2", "@faker-js/faker": "^7.6.0", "@flatfile/api": "1.19.0", "@flatfile/hooks": "^1.6.0", "@flatfile/listener": "1.1.2", "@flatfile/listener-driver-pubsub": "^2.1.0", "@flatfile/plugin-record-hook": "^2.0.2", "@flatfile/plugin-zip-extractor": "^0.8.1", "@inquirer/prompts": "^7.4.0", "@knocklabs/node": "^0.6.19", "@launchdarkly/node-server-sdk": "^9.7.5", "@launchdarkly/node-server-sdk-redis": "^4.2.2", "@linear/sdk": "^31.0.0", "@mergeapi/merge-node-client": "1.1.2", "@mhoc/axios-digest-auth": "^0.8.0", "@microsoft/microsoft-graph-client": "^3.0.7", "@nest-lab/or-guard": "^2.6.0", "@nestjs-modules/mailer": "^2.0.2", "@nestjs/axios": "^3.0.1", "@nestjs/cache-manager": "^3.0.1", "@nestjs/common": "^10.4.15", "@nestjs/core": "^10.4.15", "@nestjs/cqrs": "^10.0.1", "@nestjs/jwt": "^10.2.0", "@nestjs/microservices": "^10.4.15", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.4.15", "@nestjs/swagger": "^8.1.1", "@nestjs/terminus": "^10.2.3", "@nestjs/testing": "^10.4.15", "@nestjs/typeorm": "^10.0.2", "@octokit/auth-app": "^2.10.5", "@octokit/plugin-throttling": "^3.4.1", "@octokit/rest": "^20.1.0", "@okta/jwt-verifier": "^4.0.1", "@opensearch-project/opensearch": "^2.13.0", "@opentelemetry/api": "1.9.0", "@slack/bolt": "^4.2.1", "@slack/web-api": "^7.9.1", "@slack/webhook": "^7.0.5", "@ssut/nestjs-sqs": "^2.2.0", "@temporalio/activity": "1.11.7", "@temporalio/client": "1.11.7", "@temporalio/common": "1.11.7", "@temporalio/interceptors-opentelemetry": "1.11.7", "@temporalio/proto": "1.11.7", "@temporalio/testing": "1.11.7", "@temporalio/worker": "1.11.7", "@temporalio/workflow": "1.11.7", "@trycourier/courier": "^6.4.0", "@urql/core": "^5.1.1", "@useshortcut/client": "1.1.0", "@workos-inc/node": "^7.44.0", "adm-zip": "^0.5.16", "airtable": "^0.12.2", "ajv": "^8.17.1", "amply.js": "^0.0.4", "analytics-node": "^6.2.0", "app-root-path": "^3.1.0", "archiver": "7.0.1", "async-mutex": "^0.5.0", "aws4-axios": "^3.3.0", "axios": "1.7.9", "axios-rate-limit": "^1.4.0", "axios-retry": "^4.5.0", "body-parser": "^1.20.3", "botbuilder": "^4.23.2", "cache-manager": "^6.4.3", "case": "^1.6.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "commander": "^6.2.0", "compression": "1.7.5", "config": "^3.3.12", "convertapi": "^1.15.0", "cookie-parser": "^1.4.7", "cross-fetch": "^4.1.0", "csv-stringify": "^5.5.1", "data-urls": "^5.0.0", "date-fns": "^2.30.0", "dd-trace": "^5.17.0", "deep-object-diff": "^1.1.9", "docusign-esign": "^8.0.1", "docxtemplater": "^3.61.0", "dots-wrapper": "^3.11.16", "elastic-builder": "^2.31.0", "entities": "^4.5.0", "express": "^4.21.2", "fast-csv": "^5.0.2", "fast-redact": "^3.5.0", "file-type": "16.5.4", "flatted": "^3.3.3", "google-auth-library": "^9.15.1", "googleapis": "^148.0.0", "graphql-request": "^3.6.1", "hbs": "^4.2.0", "helmet": "^4.1.1", "http-errors": "^2.0.0", "hud-sdk": "^1.6.25", "inline-css": "^4.0.3", "ioredis": "^5.4.2", "isomorphic-dompurify": "2.20.0", "jira2md": "3.0.1", "js-yaml": "^4.1.0", "jsforce": "3.6.4", "json-schema-diff-validator": "^0.4.2", "json-stringify-safe": "^5.0.1", "jsonwebtoken": "^9.0.2", "lightrun": "^1.55.1", "lodash": "^4.17.21", "long": "^4.0.0", "marked": "4.1.1", "mime-types": "^3.0.1", "minimatch": "^10.0.1", "moment": "^2.30.1", "ms": "^2.1.3", "mustache": "^4.2.0", "mysql": "^2.18.1", "nest-commander": "^3.17.0", "nest-csv-parser": "^2.0.4", "nestjs-cls": "^5.4.0", "nestjs-ddtrace": "^5.0.0", "nestjs-i18n": "^10.5.1", "nestjs-pino": "^4.4.0", "nock": "^13.3.1", "node-fetch": "^2.7.0", "officeparser": "^4.0.8", "pako": "^2.1.0", "parse-full-name": "^1.2.6", "passport": "^0.7.0", "passport-client-cert": "^2.1.0", "passport-custom": "^1.1.1", "passport-headerapikey": "^1.2.2", "passport-jwt": "^4.0.1", "patch-package": "^8.0.0", "pino": "^9.6.0", "pino-http": "^10.4.0", "pizzip": "^3.1.8", "pluralize": "^8.0.0", "postinstall-postinstall": "^2.1.0", "pusher": "^5.2.0", "qs": "^6.14.0", "redis": "^3.0.2", "reflect-metadata": "^0.2.2", "rimraf": "^6.0.1", "rxjs": "^7.8.2", "sanitize-filename": "^1.6.3", "slack-block-builder": "^2.8.0", "source-map-support": "^0.5.19", "sqs-producer": "^3.1.1", "stream": "^0.0.3", "svix": "^1.61.0", "ts-node": "^8.6.2", "tsconfig-paths": "^3.9.0", "typeorm": "0.3.21-dev.83567f5", "typeorm-seeding": "^1.6.1", "undici": "6.21.1", "uuid": "^10.0.0", "vellum-ai": "^0.14.29", "xml2js": "^0.6.2", "yargs": "^17.7.2", "zod": "^3.24.2"}, "devDependencies": {"@automock/adapters.nestjs": "^2.1.0", "@automock/jest": "^2.1.0", "@drata/types": "0.0.77", "@jest/globals": "^29.7.0", "@nestjs/cli": "^10.4.9", "@nestjs/schematics": "^10.1.1", "@slack/types": "^2.14.0", "@swc/cli": "^0.6.0", "@swc/core": "^1.11.18", "@swc/jest": "^0.2.37", "@types/adm-zip": "^0.5.7", "@types/analytics-node": "^3.1.14", "@types/archiver": "^6.0.3", "@types/compression": "^1.7.5", "@types/cookie-parser": "^1.4.8", "@types/data-urls": "^3.0.4", "@types/express": "^4.17.9", "@types/hbs": "^4.0.1", "@types/http-errors": "^2.0.4", "@types/inquirer": "^9.0.7", "@types/jest": "29.5.14", "@types/jest-when": "^3.5.5", "@types/js-yaml": "^4.0.9", "@types/jsforce": "^1.11.5", "@types/lodash": "4.14.202", "@types/ms": "^2.1.0", "@types/multer": "^1.4.12", "@types/node": "^20.17.30", "@types/supertest": "^6.0.2", "@types/uuid": "^10.0.0", "@types/xml2js": "^0.4.14", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "@typescript-eslint/rule-tester": "^7.18.0", "@typescript-eslint/type-utils": "^7.18.0", "aws-sdk-client-mock": "^4.1.0", "eslint": "^8.56.0", "eslint-config-prettier": "^6.10.0", "eslint-plugin-local-rules": "^3.0.2", "generate-template-files": "^3.2.1", "husky": "^9.1.7", "jest": "29.7.0", "jest-environment-node-single-context": "29.4.0", "jest-junit": "^16.0.0", "jest-mock": "^29.7.0", "jest-mock-extended": "3.0.7", "jest-when": "3.7.0", "nodemon": "^3.1.9", "npm-run-all": "^4.1.5", "pino-pretty": "13.0.0", "prettier": "^3.5.3", "sqlite3": "^5.0.2", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^8.0.2", "tsconfig-paths-webpack-plugin": "^4.1.0", "typescript": "^4.9.0"}, "resolutions": {"docusign-esign/**/cookiejar": "2.1.4", "string-width": "4.2.3", "@temporalio/interceptors-opentelemetry/**/@opentelemetry/resources": "1.30.1", "@babel/traverse": "^7.26.9"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "moduleDirectories": ["node_modules", "src"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": ["ts-jest", {"isolatedModules": true}]}, "coverageDirectory": "../coverage", "testEnvironment": "node", "testTimeout": 30000, "maxWorkers": 4, "workerIdleMemoryLimit": "2.5G", "workerThreads": true, "setupFiles": ["./jest.setup.ts"]}, "scarfSettings": {"enabled": false}}